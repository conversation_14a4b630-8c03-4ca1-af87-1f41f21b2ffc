package com.ucmed.thirdparty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2017/11/14 15:31
 */
public class ThirdPartyUser implements Serializable {

    private static final long serialVersionUID = -5613483607549041559L;

    private String userId;

    private String openId;

    private Integer appCode;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Integer getAppCode() {
        return appCode;
    }

    public void setAppCode(Integer appCode) {
        this.appCode = appCode;
    }

    @Override
    public String toString() {
        return "ThirdPartyUser{" +
                "userId='" + userId + '\'' +
                ", openId='" + openId + '\'' +
                '}';
    }
}
