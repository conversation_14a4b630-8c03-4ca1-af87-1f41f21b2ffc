package com.ucmed.service;

import com.ucmed.bean.SecurityUserApp;
import com.ucmed.mapper.SecurityUserAppMapper;
import com.ucmed.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2017/11/9 9:32
 */
@Repository
public class SecurityUserAppServiceImpl implements SecurityUserAppService {

    @Autowired
    private SecurityUserAppMapper userAppMapper;

    @Override
    public List<SecurityUserApp> getApps(String userId) {
        return userAppMapper.selectByUserId(userId);
    }

    @Override
    public boolean isUserInApp(String userId, int appCode) {
        List<SecurityUserApp> userApps = userAppMapper.selectByUserIdAndAppCode(userId, appCode);
        return userApps.size() > 0;
    }

    @Override
    public void saveUserApp(String userId, int appCode) {
        SecurityUserApp userApp = new SecurityUserApp();
        userApp.setUserId(userId);
        userApp.setAppCode(appCode);
        userApp.setOperDate(TimeUtil.getCurrentTime());
        userApp.setOpenId(UUID.randomUUID().toString());
        userAppMapper.insert(userApp);
    }
}
