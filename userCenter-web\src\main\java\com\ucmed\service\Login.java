package com.ucmed.service;

import com.google.common.util.concurrent.ListeningExecutorService;
import com.google.common.util.concurrent.MoreExecutors;
import com.ucmed.authc.EnumProductKey;
import com.ucmed.bean.*;
import com.ucmed.bean.redisbean.UserPwdErrorTimes;
import com.ucmed.common.constant.AppMemoryInfo;
import com.ucmed.common.service.CommonService;
import com.ucmed.dto.UserDTO;
import com.ucmed.dto.UserProjectDTO;
import com.ucmed.exception.BusinessException;
import com.ucmed.mapper.JcUserMapper;
import com.ucmed.mapper.SecurityProjectMapper;
import com.ucmed.mapper.SecurityUserProjectMapper;
import com.ucmed.util.ThreadPoolUtil;
import com.ucmed.util.TimeUtil;
import com.ucmed.util.TokenUtil;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DataAccessException;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisCallback;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.serializer.RedisSerializer;
import org.springframework.stereotype.Service;
import redis.clients.jedis.Protocol;
import redis.clients.util.SafeEncoder;

import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.UUID;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;

import static com.ucmed.common.constant.CommonConstant.*;

/**
 * Created by XXB-QJH-1303.
 * Date: 2017/9/25 14:42
 */
@Service
public class Login extends CommonService {
    private static Logger log4j = Logger.getLogger(Login.class.getName());

    @Autowired
    private GetAppInfoService getAppInfoService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private CommonService commonService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private JcUserMapper jcUserMapper;
    @Autowired
    private AuthorityService authorityService;
    @Autowired
    private JcUserPushService userPushService;
    @Autowired
    SecurityProjectMapper securityProjectMapper;
    @Autowired
    SecurityUserProjectMapper securityUserProjectMapper;
    @Autowired
    AppMemoryInfo appMemoryInfo;

    final static ListeningExecutorService SERVICE = MoreExecutors.listeningDecorator(ThreadPoolUtil.pool);

    @Value("${pwd.errorLockTimes}")
    Integer pwdErrorLockTimes;

    public UCResponse login(String userId, String password, int appCode, String roleName, boolean sms) {
        try {
            boolean b = setIfAbsent("logining:" + appCode + userId, 1, 30);
            if (!b) {
                return new UCResponse(-4, "该用户登录中");
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        Application application = getAppInfoService.getAppByCode(appCode);
        String loginId = userId;
        if (application == null) {
//            redisTemplate.delete("logining:" + appCode + loginId);
            redisTemplate.delete("logining:" + appCode + loginId);
            return new UCResponse(-1, "应用未注册");
        }

        UserDTO user = userInfoService.getUserDTO(userId);
        if (user == null) {
            redisTemplate.delete("logining:" + appCode + loginId);
            return new UCResponse(4, "账号未注册");
        }
        userId = user.getUserId();
        //是否锁定
        if ("y".equals(user.getLock())) {
            redisTemplate.delete("logining:" + appCode + loginId);
            return new UCResponse(2, "账号被锁定,请联系管理员");
        }

        JSONObject param = new JSONObject();
        param.put(USERID, user.getUserId());
        param.put(PHONE, commonService.ThreeDESDecrypt(user.getPhone()));
        // 用户是否已授权
        if (!isUserInApp(user, appCode)) {
            redisTemplate.delete("logining:" + appCode + loginId);
            return new UCResponse(5, "用户未授权，是否确定使用该应用？", param);
        }
        if (!isUserInRole(user, roleName, appCode)) {
            redisTemplate.delete("logining:" + appCode + loginId);
            return new UCResponse(6, "角色未授权", param);
        }

        UserPwdErrorTimes userPwdErrorTimes = (UserPwdErrorTimes) redisTemplate.boundHashOps(USER_PWD_ERROR_TIMES + userId)
                .get(application.getProj_code());
        if (!sms && userPwdErrorTimes != null && userPwdErrorTimes.getFailedTimes() >= pwdErrorLockTimes) {
            redisTemplate.delete("logining:" + appCode + loginId);
            return new UCResponse(EnumRetCode.USER_PWD_ERROR_TIMES_CODE);
        }

        UC_UserInfo userInfo = new UC_UserInfo();
        //判断是否独立密码
        if (!sms) {
            boolean indPwd = appMemoryInfo.getAppIndependentPwd(appCode);
            if (indPwd) {
                dlSetPassAndFlat(user, appCode);
            }
        }
        userInfo.setPassword(user.getPassword());
        userInfo.setSecuritykey(user.getSecurityKey());
        // 判断密码是否正确
        if (!sms && !passwordMatched(password, userInfo)) {
            if (userPwdErrorTimes == null) {
                userPwdErrorTimes = new UserPwdErrorTimes(userId, user.getPhone(), application.getProj_code());
            } else {
                userPwdErrorTimes.setLoginTime(TimeUtil.getCurrentTime());
            }

            userPwdErrorTimes.setFailedTimes(userPwdErrorTimes.getFailedTimes() + 1);
            redisHashPut(USER_PWD_ERROR_TIMES + userId, application.getProj_code(),
                    userPwdErrorTimes, 1, TimeUnit.HOURS);

            log4j.info(userPwdErrorTimes.errorLog());
            redisTemplate.delete("logining:" + appCode + loginId);
            return new UCResponse(1, "密码错误");
        }

        if (userPwdErrorTimes != null) {
            redisTemplate.boundHashOps(USER_PWD_ERROR_TIMES + userId).delete(application.getProj_code());
        }


        String retParam = null;

        user.setAppCode(appCode);
        user.setOpenId(getUserOpenIdByAppCode(user, appCode));
        user.setProjCode(getUserProjCodeByAppCode(user, appCode));
        retParam = loginSuccess(user, loginId);
        return new UCResponse(0, "登录成功", retParam);
    }

    /**
     * 免密登录
     *
     * @param userId
     * @param appCode
     * @param roleName
     * @return
     */
    public UCResponse SecretLogin(String userId, int appCode, String roleName) {
        return login(userId, "", appCode, roleName, true);
    }

    public String loginSuccess(UserDTO user, String loginId) {
//        log4j.info("测试用：用户登录成功 token删除前：token：" + user.getToken());
        deleteLastLoginToken(user);
        String currentTime = TimeUtil.getCurrentTime();

        // 、更新token值最后登录时间、登录次数

        JcUser jcUser = new JcUser();
        jcUser.setUid(user.getUid());
        jcUser.setToken(TokenUtil.generateToken(currentTime, true));//生成token
        jcUser.setTokenTime(currentTime);
        jcUser.setLatesttime(currentTime);
        jcUser.setLoginTimes(user.getLoginTimes() + 1);

        jcUser.setUserId(user.getUserId());
        jcUser.setPhone(user.getPhone());
        // 登录成功返回值
        JSONObject param = new JSONObject();
        param.put(USERID, user.getUserId());
        String phone = commonService.ThreeDESDecrypt(user.getPhone());
        // 区分华侨手机号，将华侨手机号显示初始化
        phone = phone != null && phone.startsWith(OC_PHONE_MARK) ?
                phone.substring(OC_PHONE_MARK.length()) : phone;
        param.put(PHONE, phone);
        param.put(TOKEN, jcUser.getToken());
        param.put("proj_code", user.getProjCode());
        param.put(APPCODE, user.getAppCode());
        param.put("id", user.getUid());
        param.put("openId", user.getOpenId());
        param.put("permission", authorityService.getPermission(user.getUserId(), user.getAppCode()));
        String newPushId = UUID.randomUUID().toString();

        param.put("pushId", newPushId);
        // 返回用户名和身份证号码
        UserInfo userInfo = null;
        try {
            userInfo = userInfoService.getUserInfoByUserId(user.getUserId());
            param.put("realName", userInfo.getThe_name());
            param.put("idCard", userInfo.getCard_id());
        } catch (BusinessException e) {
            e.printStackTrace();
            param.put("realName", "");
            param.put("idCard", "");
        }
        param.put(LAST_ACTIVE_TIME, LocalDateTime.now().toString());
        redisTemplate.boundHashOps(jcUser.getToken()).put(EnumProductKey.GENERAL.name(), param.toString());
        redisTemplate.boundHashOps(jcUser.getToken()).expire(appMemoryInfo.getExpiredTime(user.getAppCode()), TimeUnit.HOURS);
        UserAppToken UserAppToken = new UserAppToken(user.getAppCode(), jcUser.getToken(),
                (int) appMemoryInfo.getExpiredTime(user.getAppCode()));

        JSONObject loginMassage = new JSONObject();
        loginMassage.put(USERID, user.getUserId());
        loginMassage.put(APPCODE, user.getAppCode());
        SERVICE.submit(new Callable<Boolean>() {
            @Override
            public Boolean call() throws Exception {
                jcUserMapper.updateByPrimaryKeySelective(jcUser);
                if (getPushIdByProjCode(user, user.getProjCode()) == null) {
                    userPushService.insert(user.getOpenId(), user.getUserId(), user.getAppCode(), newPushId);
                } else {
                    userPushService.updateByOpenId(user.getOpenId(), newPushId, TimeUtil.getCurrentTime());
                }
                BoundHashOperations<String, Object, Object> userTokenMap = null;
                userTokenMap = redisTemplate.boundHashOps(USERTOKEN_CACHE + user.getUserId());
                addUserToken(userTokenMap, UserAppToken, loginMassage);
                redisTemplate.delete("logining:" + user.getAppCode() + loginId);
                return true;
            }
        });

//        log4j.info("测试用：用户登录成功 token生成后：token：" + jcUser.getToken());
        return param.toString();
    }

    private boolean isUserInRole(UserDTO user, String roleName, int appCode) {
        List<UserProjectDTO> userProjects = user.getUserProjects();
        for (UserProjectDTO userProject : userProjects) {
            if (appCode == userProject.getAppCode() && roleName.equals(userProject.getRoleName())) {
                return true;
            }
        }
        return false;
    }

    private boolean isUserInApp(UserDTO user, int appCode) {
        List<UserProjectDTO> userProjects = user.getUserProjects();
        for (UserProjectDTO userProject : userProjects) {
            if (appCode == userProject.getAppCode()) {
                return true;
            }
        }
        return false;
    }

    private void dlSetPassAndFlat(UserDTO user, int appCode) {
        List<UserProjectDTO> userProjects = user.getUserProjects();
        Integer projCode = getProjCode(appCode);
        for (UserProjectDTO userProject : userProjects) {
            if (projCode.equals(userProject.getProjCode())) {
                boolean hasdlpwd = userProject.getDl_pwd() != null && userProject.getSecurityKey() != null
                        && !userProject.getDl_pwd().isEmpty() && !userProject.getSecurityKey().isEmpty();
                if (hasdlpwd) {
                    user.setPassword(userProject.getDl_pwd());
                    user.setSecurityKey(userProject.getSecurityKey());
                } else {
                    userProject.setDl_pwd(user.getPassword());
                    userProject.setSecurityKey(user.getSecurityKey());
                    securityUserProjectMapper.adddlPwd(userProject);
                }
            }
        }
    }

    private Integer getUserProjCodeByAppCode(UserDTO user, int appCode) {
        List<UserProjectDTO> userProjects = user.getUserProjects();
        for (UserProjectDTO userProject : userProjects) {
            if (appCode == userProject.getAppCode()) {
                return userProject.getProjCode();
            }
        }
        return -1;
    }

    private String getUserOpenIdByAppCode(UserDTO user, int appCode) {
        List<UserProjectDTO> userProjects = user.getUserProjects();
        for (UserProjectDTO userProject : userProjects) {
            if (appCode == userProject.getAppCode()) {
                return userProject.getOpenId();
            }
        }
        return null;
    }

    /**
     * 获取pushId
     *
     * @param user
     * @param projCode
     * @return
     */
    private String getPushIdByProjCode(UserDTO user, Integer projCode) {
        List<UserProjectDTO> userProjects = user.getUserProjects();
        for (UserProjectDTO userProject : userProjects) {
            if (Objects.equals(projCode, userProject.getProjCode())) {
                return userProject.getPushId();
            }
        }
        return null;
    }

    /**
     * 密码匹配
     *
     * @param inputPassword
     * @param user
     * @return
     */
    public boolean passwordMatched(String inputPassword, UC_UserInfo user) {
        String securityKey = user.getSecuritykey();
        String password = user.getPassword();
        if (inputPassword == null) {
            return false;
        }
        return password.equals(commonService.encryptPassword(inputPassword, securityKey)) ||
                password.equals(commonService.encryptPasswordDoubleHash(inputPassword, securityKey));

    }


    public void deleteLastLoginToken(UserDTO user) {
        UserAppToken userAppToken = (UserAppToken) redisTemplate.opsForHash().get(USERTOKEN_CACHE + user.getUserId(), user.getAppCode().toString());
        if (userAppToken != null) {
            redisTemplate.delete(userAppToken.getToken());
        } else if (redisTemplate.opsForHash().keys(USERTOKEN_CACHE + user.getUserId()).size() < 2 && StringUtils.isNotBlank(user.getToken())) {
            /**
             * userTokenMap.size() < 2是由于可能有独立token实现之前登录的账号且激活过token，
             * 导致usertokenmap里有一条数据，不为空但有一条数据（token最大过期时间），所以这里为 < 2 而不是 == 0
             */
            redisTemplate.delete(user.getToken());
        }
    }

    public void addUserToken(BoundHashOperations<String, Object, Object> userTokenMap, UserAppToken userAppToken, JSONObject loginMessage) {
        userTokenMap.put(String.valueOf(userAppToken.getAppCode()), userAppToken);
        sendActivityMessage(userTokenMap, loginMessage);
        long expiredTime = appMemoryInfo.getExpiredTime(userAppToken.getAppCode());
        try {
            LocalDateTime tokenMapExpireTime = LocalDateTime.now().plusHours(userTokenMap.getExpire() / 3600);
            LocalDateTime appTokenExpireTime = LocalDateTime.now().plusHours(expiredTime);
            if (tokenMapExpireTime.compareTo(appTokenExpireTime) < 0) {
                userTokenMap.expire(expiredTime, TimeUnit.HOURS);
                userTokenMap.put(MAX_EXPIRED_TIME, appTokenExpireTime.toString());
            }
        } catch (NullPointerException e) {
            log4j.info(e.getStackTrace());
        }
    }

    public boolean setIfAbsent(final String key, final Serializable value, final long exptime) {
        long startTime = System.currentTimeMillis();
        Boolean b = (Boolean) redisTemplate.execute(new RedisCallback<Boolean>() {
            @Override
            public Boolean doInRedis(RedisConnection connection) throws DataAccessException {
                RedisSerializer valueSerializer = redisTemplate.getValueSerializer();
                RedisSerializer keySerializer = redisTemplate.getKeySerializer();
                Object obj = connection.execute("set", keySerializer.serialize(key),
                        valueSerializer.serialize(value),
                        SafeEncoder.encode("NX"),
                        SafeEncoder.encode("EX"),
                        Protocol.toByteArray(exptime));
                return obj != null;
            }
        });
        long endTime = System.currentTimeMillis();
        log4j.info("登录锁上锁完毕，耗时：" + (endTime - startTime) + "ms");
        return b;
    }
}
