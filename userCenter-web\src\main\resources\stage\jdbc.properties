jdbc.driverClassName=org.postgresql.Driver
jdbc.url = ******************************************
jdbc.username = postgres
jdbc.password = VLOzhbfo@rcm9sl!

#------------ redis ------------
#最大分配的对象数  
redis.pool.maxActive=1024
#最大能够保持idel状态的对象数  
redis.pool.maxIdle=200
#当池内没有返回对象时，最大等待时间  
redis.pool.maxWait=1000
#当调用borrow Object方法时，是否进行有效性检查  
redis.pool.testOnBorrow=true
#当调用borrow Object方法时，是否进行有效性检查  
redis.pool.testOnReturn=true

redis服务端ip、端口、密码、超时时间
redis.host=*************
redis.port=16479
redis.password=iCl6bH@Zyyev9%qH
redis.timeout=6000
