package com.ucmed.dao;

import com.ucmed.bean.UserInfo;
import com.ucmed.common.dao.BaseDaoImpl;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

import static com.ucmed.common.constant.TableName.JCUSERINFO;

/**
 * jc_user
 * Created by QIUJIAHAO on 2016/8/16.
 */
@Repository
public class JcUserInfoDaoImpl extends BaseDaoImpl implements JcUserInfoDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    @Override
    public UserInfo getUserInfoByUserId(String userId) {
        String sql = "SELECT " +
                "t_id,user_id,the_name,sex,age,birthday,card_type,card_id,medicare,medicare_id,city_id,address,w_chat,mobile,e_mail,source_ids," +
                "create_time,update_time,ucmed_id " +
                "FROM " + JCUSERINFO + " " +
                "WHERE user_id=?";
        List<UserInfo> list = jdbcTemplate.query(sql, new Object[]{userId}, new BeanPropertyRowMapper<>(UserInfo.class));
        if(list.size() == 0) {
            return null;
        }
        return list.get(0);
    }

    @Override
    public int updateUserInfo(final UserInfo userInfo) {
        String sql = "UPDATE " + JCUSERINFO + " SET " +
                "the_name=?,sex=?,birthday=?,card_type=?,card_id=?,medicare=?,medicare_id=?,city_id=?,address=?,w_chat=?,mobile=?," +
                "e_mail=?,source_ids=?,update_time=? " +
                "WHERE user_id=?";

        return jdbcTemplate.update(sql, new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                ps.setString(1, userInfo.getThe_name());
                ps.setString(2, userInfo.getSex());
                ps.setString(3, userInfo.getBirthday());
                ps.setString(4, userInfo.getCard_type());
                ps.setString(5, userInfo.getCard_id());
                ps.setString(6, userInfo.getMedicare());
                ps.setString(7, userInfo.getMedicare_id());
                ps.setString(8, userInfo.getCity_id());
                ps.setString(9, userInfo.getAddress());
                ps.setString(10, userInfo.getW_chat());
                ps.setString(11, userInfo.getMobile());
                ps.setString(12, userInfo.getE_mail());
                ps.setString(13, userInfo.getSource_ids());
                ps.setString(14, userInfo.getUpdate_time());
                ps.setString(15, userInfo.getUser_id());
            }
        });
    }

    @Override
    public int addUserInfo(final UserInfo userInfo) {
        String sql = "INSERT INTO " + JCUSERINFO + "(user_id,the_name,sex,birthday,card_type,card_id,medicare,medicare_id,city_id,address," +
                "w_chat,mobile,e_mail,source_ids,create_time,ucmed_id) " +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        return jdbcTemplate.update(sql, new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                ps.setString(1, userInfo.getUser_id());
                ps.setString(2, userInfo.getThe_name());
                ps.setString(3, userInfo.getSex());
                ps.setString(4, userInfo.getBirthday());
                ps.setString(5, userInfo.getCard_type());
                ps.setString(6, userInfo.getCard_id());
                ps.setString(7, userInfo.getMedicare());
                ps.setString(8, userInfo.getMedicare_id());
                ps.setString(9, userInfo.getCity_id());
                ps.setString(10, userInfo.getAddress());
                ps.setString(11, userInfo.getW_chat());
                ps.setString(12, userInfo.getMobile());
                ps.setString(13, userInfo.getE_mail());
                ps.setString(14, userInfo.getSource_ids());
                ps.setString(15, userInfo.getCreate_time());
                ps.setString(16, userInfo.getUcmed_id());
            }
        });
    }

    @Override
    public int deleteUserInfo(String userId) {
        return 0;
    }
}
