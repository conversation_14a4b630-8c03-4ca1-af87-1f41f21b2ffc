package com.ucmed.bean;

import java.io.Serializable;

/**
 * Created by XXB-QJH-1303.
 * Date: 2017/9/25 11:54
 */
public class UserDO implements Serializable {

    private static final long serialVersionUID = -4234832403686323603L;
    private Integer uid;
    private String userId;
    private String password;
    private String passChangeTime;
    private String phone;
    private String token;
    private String lock;
    private String securityKey;
    private Integer loginTimes;
    private Integer appCode;
    private String roleName;
    private Integer roleId;
    private Integer projCode;
    private String openId;
    private String pushId;
    private String dl_pwd;
    private String dl_securityKey;
    private String dlPwdChangeTime;

    public String getPassChangeTime() {
        return passChangeTime;
    }

    public void setPassChangeTime(String passChangeTime) {
        this.passChangeTime = passChangeTime;
    }

    public String getDlPwdChangeTime() {
        return dlPwdChangeTime;
    }

    public void setDlPwdChangeTime(String dlPwdChangeTime) {
        this.dlPwdChangeTime = dlPwdChangeTime;
    }

    public String getDl_pwd() {
        return dl_pwd;
    }

    public void setDl_pwd(String dl_pwd) {
        this.dl_pwd = dl_pwd;
    }

    public String getDl_securityKey() {
        return dl_securityKey;
    }

    public void setDl_securityKey(String dl_securityKey) {
        this.dl_securityKey = dl_securityKey;
    }

    public Integer getUid() {
        return uid;
    }

    public void setUid(Integer uid) {
        this.uid = uid;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getLock() {
        return lock;
    }

    public void setLock(String lock) {
        this.lock = lock;
    }

    public String getSecurityKey() {
        return securityKey;
    }

    public void setSecurityKey(String securityKey) {
        this.securityKey = securityKey;
    }

    public Integer getLoginTimes() {
        return loginTimes;
    }

    public void setLoginTimes(Integer loginTimes) {
        this.loginTimes = loginTimes;
    }

    public Integer getAppCode() {
        return appCode;
    }

    public void setAppCode(Integer appCode) {
        this.appCode = appCode;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public Integer getProjCode() {
        return projCode;
    }

    public void setProjCode(Integer projCode) {
        this.projCode = projCode;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }
}
