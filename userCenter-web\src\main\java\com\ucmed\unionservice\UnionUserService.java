package com.ucmed.unionservice;

import cn.ucmed.common.constants.GlobalConstants;
import com.ucmed.bean.UCResponse;
import com.ucmed.common.constant.CommonConstant;
import com.ucmed.common.exception.IllegalPatternException;
import com.ucmed.common.service.CommonService;
import com.ucmed.api.SendSmsApi;
import com.ucmed.dto.MessageBody;
import com.ucmed.dto.MessageResult;
import com.ucmed.dto.UserDTO;
import com.ucmed.message.dao.SmsHistoryMapper;
import com.ucmed.message.model.SMSHistory;
import com.ucmed.service.CaptchaService;
import com.ucmed.service.Login;
import com.ucmed.service.UserInfoService;
import com.ucmed.service.UserService;
import net.sf.json.JSONObject;
import org.apache.http.HttpEntity;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.io.*;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.*;
@Service("unionUserService")
public class UnionUserService extends CommonService {
    private static Logger log4j = Logger.getLogger(UnionUserService.class.getName());

    private String retCode = "ret_code";
    private String retInfo = "ret_info";
    private String retData = "ret_data";

    @Value("#{configProperties['BdUrl']}")
    String urlStr;
    @Value("#{configProperties['BdAppId']}")
    String bAppId;
    @Value("#{configProperties['bcsmstype']}")
    private String bcsmstype;

    @Autowired
    UserService userService;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private Login login;
    @Autowired
    CaptchaService captchaService;
    @Autowired
    private RedisTemplate redisTemplate;

    @Autowired
    private SmsHistoryMapper smsHistoryMapper;


    public UCResponse login(Integer cAppId, String username, String password, String phone, String roleName){
        boolean app = (bAppId == null || "".equals(bAppId)) && cAppId == null;
        if (app){
            return new UCResponse(-3,"appId不能为空");
        }
        boolean userInfo = (username == null || username.trim().isEmpty()) || (phone == null || phone.trim().isEmpty());
        if (userInfo){
            return new UCResponse(-8,"用户名和手机不能为空");
        }
        if (!username.equals(username.trim()) || !phone.equals(phone.trim())){
            return new UCResponse(-9,"用户名或密码不能有空格");
        }
        UserDTO user = userInfoService.getUserDTO(username);
        if (user == null) {
            user = userInfoService.getUserDTO(phone);
        }
        BCResponse bcResponse = new BCResponse();
        JSONObject ucResponseJson;
        boolean b = false;
        boolean c = false;
        boolean a = cherkDJUser(bAppId, phone);
        String cInfo = null;
        String bInfo = null;
        if(a && user != null){
            UCResponse ucResponse = userService.login(phone, password, cAppId, roleName);
            if (ucResponse.getRetCode() == 4){
                ucResponse = userService.login(username, password, cAppId, roleName);
            }
            if(ucResponse.getRetCode() == 5 || ucResponse.getRetCode() == 6){
                userService.setPermission(phone, cAppId, roleName);
                ucResponse = userService.login(phone, password, cAppId, roleName);
            }
            cInfo = ucResponse.getRetInfo();
            if (ucResponse.getRetCode() == 0) {
                bcResponse.setCToken((String) ucResponse.getParam());
                bcResponse.setCFlags(1);
                c = true;
                ucResponseJson = djSecretLogin(username, bAppId, phone);
                bInfo = ucResponseJson.getString(retInfo);
                if (ucResponseJson.getInt(retCode) == 0) {
                    bcResponse.setBToken(ucResponseJson.getString(retData));
                    bcResponse.setBFlags(1);
                    b = true;
                }
            }else {
                ucResponseJson = djlogin(username, password, bAppId, phone);
                bInfo = ucResponseJson.getString(retInfo);
                if (ucResponseJson.getInt(retCode) == 0){
                    bcResponse.setBToken(ucResponseJson.getString(retData));
                    bcResponse.setBFlags(1);
                    b = true;

                    ucResponse = login.SecretLogin(phone, cAppId, roleName);
                    if (ucResponse.getRetCode() == 4){
                        ucResponse = login.SecretLogin(username, cAppId, roleName);;
                    }
                    if(ucResponse.getRetCode() == 5 || ucResponse.getRetCode() == 6){
                        userService.setPermission(phone, cAppId, roleName);
                        ucResponse = login.SecretLogin(phone, cAppId, roleName);
                    }
                    cInfo = ucResponse.getRetInfo();
                    if (ucResponse.getRetCode() == 0) {
                        bcResponse.setCToken((String) ucResponse.getParam());
                        bcResponse.setCFlags(1);
                        c = true;
                    }
                }else {
                    return new UCResponse(-4,
                            "登录失败,C端：" + ucResponse.getRetInfo() + ",B端：" + ucResponseJson.getString(retInfo), bcResponse);
                }
            }
        }else if(a){
            ucResponseJson = djlogin(username, password, bAppId, phone);
            bInfo = ucResponseJson.getString(retInfo);
            cInfo = "账号不存在";
            if (ucResponseJson.getInt(retCode) == 0){
                bcResponse.setBToken(ucResponseJson.getString(retData));
                bcResponse.setBFlags(1);
                b = true;
            }else {
                return new UCResponse(ucResponseJson.getInt(retCode),
                        "登录失败,B端:" + ucResponseJson.getString(retInfo) + ",C端：账号不存在", bcResponse);
            }
        }else if (user != null){
            UCResponse ucResponse = userService.login(phone, password, cAppId, roleName);
            if (ucResponse.getRetCode() == 4){
                ucResponse = userService.login(username, password, cAppId, roleName);
            }
            if(ucResponse.getRetCode() == 5 || ucResponse.getRetCode() == 6){
                userService.setPermission(phone, cAppId, roleName);
                ucResponse = userService.login(phone, password, cAppId, roleName);
            }
            cInfo = ucResponse.getRetInfo();
            bInfo = "账号不存在";
            if (ucResponse.getRetCode() == 0) {
                bcResponse.setCToken((String) ucResponse.getParam());
                bcResponse.setCFlags(1);
                c = true;
            }else {
                return new UCResponse(ucResponse.getRetCode(),
                        "登录失败,C端:" + ucResponse.getRetInfo() + ",B端：账号不存在", bcResponse);
            }
        }else {
            return new UCResponse(-1,"账号未注册");
        }
        if (b || c){
            return new UCResponse(0,"登录成功,B端：" + bInfo + ",C端：" + cInfo, bcResponse);
        }else {
            return new UCResponse(-4,"登录异常", bcResponse);
        }
    }

    public UCResponse smsLogin(Integer cAppId, String username, String phone, String roleName, String msgCode){
        boolean userInfo = username == null || "".equals(username) && (phone == null || "".equals(phone));
        if (userInfo){
            return new UCResponse(-8,"用户名和手机不能为空");
        }
        UCResponse ucResponse = userService.verifyMessageCode(phone, msgCode, bcsmstype, false);
        if (ucResponse.getRetCode() != 0) {
            log4j.info("smsLogin service output:" + ucResponse.getRetInfo());
            return new UCResponse(-2, ucResponse.getRetInfo());
        }
        ucResponse = nopwdLogin(cAppId, username, phone, roleName);
        log4j.info("smsLogin service output:" + ucResponse.getRetInfo());
        if (ucResponse.getRetCode() == 0) {
            redisTemplate.delete(CommonConstant.MSG_CATCH + phone);
        }
        return ucResponse;
    }

    public UCResponse nopwdLogin(Integer cAppId, String username, String phone, String roleName){
        boolean app = (bAppId == null || "".equals(bAppId)) && cAppId == null;
        if (app){
            return new UCResponse(-3,"appId不能为空");
        }
        boolean userInfo = (username == null || username.trim().isEmpty()) || (phone == null || phone.trim().isEmpty());
        if (userInfo){
            return new UCResponse(-8,"用户名和手机不能为空");
        }
        if (!username.equals(username.trim()) || !phone.equals(phone.trim())){
            return new UCResponse(-9,"用户名或密码不能有空格");
        }

        UserDTO user = userInfoService.getUserDTO(username);
        if (user == null) {
            user = userInfoService.getUserDTO(phone);
        }
        boolean a = cherkDJUser(bAppId, phone);
        BCResponse bcResponse = new BCResponse();
        JSONObject ucResponseJson;
        String cInfo = null;
        String bInfo = null;
        if (a && user != null){
            ucResponseJson = djSecretLogin(username, bAppId, phone);
            BResponse(bcResponse, ucResponseJson);
            bInfo = ucResponseJson.getString(retInfo);
            UCResponse ucResponse = login.SecretLogin(phone, cAppId, roleName);
            if (ucResponse.getRetCode() == 4){
                ucResponse = login.SecretLogin(username, cAppId, roleName);;
            }
            CResponse(bcResponse, ucResponse, phone, cAppId, roleName);
            cInfo = ucResponse.getRetInfo();
            if (bcResponse.getBFlags() == 0 && bcResponse.getCFlags() == 0){
                return new UCResponse(-1,"登录失败,B端：" + bInfo + ",C端：" + cInfo, bcResponse);
            }
        }else if (a){
            ucResponseJson = djSecretLogin(username, bAppId, phone);
            BResponse(bcResponse, ucResponseJson);
            bInfo = ucResponseJson.getString(retInfo);
            cInfo = "账号不存在";
            if (bcResponse.getBFlags() == 0){
                return new UCResponse(ucResponseJson.getInt(retCode), "登录失败,B端：" + bInfo + ",C端：" + cInfo, bcResponse);
            }
        }else if (user != null){
            UCResponse ucResponse = login.SecretLogin(phone, cAppId, roleName);
            if (ucResponse.getRetCode() == 4){
                ucResponse = login.SecretLogin(username, cAppId, roleName);;
            }
            CResponse(bcResponse, ucResponse, phone, cAppId, roleName);
            cInfo = ucResponse.getRetInfo();
            bInfo = "账号不存在";
            if (bcResponse.getCFlags() == 0){
                return new UCResponse(ucResponse.getRetCode(), "登录失败,C端："+ cInfo + ",B端" + bInfo, bcResponse);
            }
        }else {
            return new UCResponse(4,"账号未注册");
        }
        return new UCResponse(0,"登录成功,B端：" + bInfo + ",C端" + cInfo, bcResponse);
    }

    public UCResponse sendMsgCode(String smsToken, String phone, String type) {
        if (isNotPhonePattern(phone)) {
            try {
                throw new IllegalPatternException(641, "手机号不合法");
            } catch (IllegalPatternException e) {
                log4j.error(e);
                return new UCResponse(e.getCode(), e.getMessage());
            }
        }
        UCResponse ucResponse = captchaService.generateMsgCode(phone, type, 6, 300);
        if (0 != ucResponse.getRetCode()) {
            return ucResponse;
        }
        String code = (String) ucResponse.getParam();
        String content = "您正在申请手机验证码登录，验证码 " + code + " 请在5分钟内使用，如非本人操作请勿理会";
        MessageBody body = new MessageBody();
        body.setToken(smsToken);
        body.setPhone(phone);
        body.setContent(content);
        MessageResult result = SendSmsApi.sendMessage(body);
        if (result.getResultCode() != 100) {
            addSmsHistory(smsToken, phone, content, result.getResultInfo(), "0");
            return new UCResponse(result.getResultCode(), result.getResultInfo());
        }
        addSmsHistory(smsToken, phone, content, result.getResultInfo(), "1");
        return new UCResponse(0, "获取验证码成功，请查看您的手机短信");
    }


    private void BResponse(BCResponse bcResponse, JSONObject ucResponseJson) {
        if((int)ucResponseJson.get(retCode) == 0){
            bcResponse.setBFlags(1);
            bcResponse.setBToken(ucResponseJson.getString(retData));
        }else {
            bcResponse.setBFlags(0);
        }
    }

    private void CResponse(BCResponse bcResponse, UCResponse ucResponse, String phone, Integer cAppId, String roleName) {
        if(ucResponse.getRetCode() == 5 || ucResponse.getRetCode() == 6){
            userService.setPermission(phone, cAppId, roleName);
            ucResponse = login.SecretLogin(phone, cAppId, roleName);
        }
        if (ucResponse.getRetCode() == 0){
            bcResponse.setCFlags(1);
            bcResponse.setCToken((String) ucResponse.getParam());
        }else {
            bcResponse.setCFlags(0);
        }
    }



    public JSONObject djSecretLogin(String username, String appId, String phone){
        String url = urlStr + "/api/DjUser/SecretLogin";
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);

        String stamp = String.valueOf(System.currentTimeMillis());

        SecrrerLogin secrrerLogin = new SecrrerLogin();
        secrrerLogin.setPhone(phone);
        secrrerLogin.setUsername(username);
        JSONObject jsonObject = JSONObject.fromObject(secrrerLogin);

        String sourcestr = appId + stamp + jsonObject.toString();
        char[] arr = sourcestr.toLowerCase().toCharArray();
        Arrays.sort(arr);
        sourcestr = new String(arr);
        String strEncrypt = null;
        try {
            MessageDigest md = MessageDigest.getInstance("MD5");
            md.update(sourcestr.getBytes());
            strEncrypt = new BigInteger(1, md.digest()).toString(16);
        } catch (NoSuchAlgorithmException e) {
            e.printStackTrace();
        }
        try {
            post.setEntity(new StringEntity(jsonObject.toString()));

        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Accept", "application/json");
        post.setHeader("appid", appId);
        post.setHeader("stamp", stamp);
        post.setHeader("scret",strEncrypt);
        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(post);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return responseToResult(response);
    }


    public boolean cherkDJUser(String appId, String phone){
        String url = urlStr + "/api/DjUser/CheckUserExist" + "?phone=" + phone;
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpGet get = new HttpGet(url);
        CloseableHttpResponse response = null;
        get.setHeader("appid", appId);
        try {
            response = httpClient.execute(get);
        } catch (IOException e) {
            e.printStackTrace();
        }
        JSONObject result = responseToResult(response);
        if ((int)result.get(retCode) == 0){
            return true;
        }else {
            return false;
        }
    }

    public JSONObject djlogin(String username, String password, String appId, String phone){
        String url = urlStr + "/api/DjUser/UserLogin";
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost post = new HttpPost(url);
        JSONObject postJson = new JSONObject();
        postJson.put("username", username);
        postJson.put("pwd", password);
        postJson.put("phone", phone);
        try {
            post.setEntity(new StringEntity(postJson.toString()));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        post.setHeader("Content-Type", "application/json");
        post.setHeader("Accept", "application/json");
        post.setHeader("appid", appId);

        CloseableHttpResponse response = null;
        try {
            response = httpClient.execute(post);
        } catch (IOException e) {
            e.printStackTrace();
        }
        return responseToResult(response);
    }


    public JSONObject responseToResult(HttpResponse response){
        String result = "";
        try {
            HttpEntity entity =  response.getEntity();
            result = EntityUtils.toString(entity, "UTF-8");
        } catch (IOException e) {
            e.printStackTrace();
        }
        JSONObject jsonObject = JSONObject.fromObject(result);
        return jsonObject;
    }

    private void addSmsHistory(String smsToken, String phone, String content, String resultInfo, String status) {
        SMSHistory model = new SMSHistory();
        model.setContent(content);
        model.setCreatedby(smsToken);
        model.setCreatedon(new Date());
        model.setReceiverMobile(phone);
        model.setSenderMobile(phone);
        model.setSenderScyUserId(phone);
        model.setVcProjectId("755");
        model.setSmsHistoryId(UUID.randomUUID().toString());
        model.setSendStatus(status);
        model.setModifiedby(smsToken);
        model.setModifiedon(new Date());
        model.setDescription(resultInfo);
        smsHistoryMapper.insertSelective(model);
    }

}
