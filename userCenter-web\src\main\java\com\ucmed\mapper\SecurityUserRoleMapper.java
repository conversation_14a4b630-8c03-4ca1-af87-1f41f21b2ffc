package com.ucmed.mapper;

import com.ucmed.bean.SecurityUserRole;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface SecurityUserRoleMapper {

    @Delete({
            "delete from security_user_role",
            "where t_id = #{tId,jdbcType=INTEGER}"
    })
    int deleteByPrimaryKey(Integer tId);

    @Insert({
            "insert into security_user_role (role_id, user_id, ",
            "valid, oper_user, ",
            "oper_date)",
            "values (#{roleId,jdbcType=INTEGER}, #{userId,jdbcType=VARCHAR}, ",
            "#{valid,jdbcType=VARCHAR}, #{operUser,jdbcType=VARCHAR}, ",
            "#{operDate,jdbcType=VARCHAR})"
    })
    int insert(SecurityUserRole record);

    @Select({
            "select",
            "t_id, role_id, user_id, valid, oper_user, oper_date",
            "from security_user_role",
            "where user_id = #{userId,jdbcType=INTEGER}",
            "and role_id = #{roleId,jdbcType=INTEGER}"
    })
    @Results({
            @Result(column = "t_id", property = "tId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "role_id", property = "roleId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "valid", property = "valid", jdbcType = JdbcType.VARCHAR),
            @Result(column = "oper_user", property = "operUser", jdbcType = JdbcType.VARCHAR),
            @Result(column = "oper_date", property = "operDate", jdbcType = JdbcType.VARCHAR)
    })
    List<SecurityUserRole> selectByUserIdAndRoleId(@Param("userId") String userId, @Param("roleId") Integer roleId);

    @Select({
            "select",
            "t_id, role_id, user_id, valid, oper_user, oper_date",
            "from security_user_role",
            "where t_id = #{tid,jdbcType=INTEGER}"
    })
    @Results({
            @Result(column = "t_id", property = "tId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "role_id", property = "roleId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "valid", property = "valid", jdbcType = JdbcType.VARCHAR),
            @Result(column = "oper_user", property = "operUser", jdbcType = JdbcType.VARCHAR),
            @Result(column = "oper_date", property = "operDate", jdbcType = JdbcType.VARCHAR)
    })
    SecurityUserRole selectByPrimaryKey(@Param("tid") Integer tid);
}