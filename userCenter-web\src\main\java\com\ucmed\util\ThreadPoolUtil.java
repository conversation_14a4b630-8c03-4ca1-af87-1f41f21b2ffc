package com.ucmed.util;


import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

public class ThreadPoolUtil {
    private static final Logger log = LoggerFactory.getLogger(HttpClientUtils.class);

    public static ThreadPoolExecutor pool = new ThreadPoolExecutor(
            6,
            10,
            30,
            TimeUnit.MINUTES,
            new ArrayBlockingQueue<Runnable>(10),
            new CustomThreadFactory(),
            new CustomRejectedExecutionHandler());

    private static class CustomThreadFactory implements ThreadFactory {

        private AtomicInteger count = new AtomicInteger(0);

        @Override
        public Thread newThread(Runnable r) {
            Thread t = new Thread(r);
            String threadName = ThreadPoolUtil.class.getSimpleName() + count.addAndGet(1);
            log.info("创建线程了");
            t.setName(threadName);
            return t;
        }
    }


    private static class CustomRejectedExecutionHandler implements RejectedExecutionHandler {

        @Override
        public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
            // 记录异常
            if (!pool.isShutdown()) {
                log.info("线程满队列了，自己去执行口巴.............");
                //直接执行run方法
                r.run();
            }
        }
    }
}
