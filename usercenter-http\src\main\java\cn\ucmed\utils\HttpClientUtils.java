package cn.ucmed.utils;

import cn.hutool.http.HttpUtil;
import cn.hutool.http.Method;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ucmed.bean.UCResponse;
import org.apache.commons.httpclient.HttpClient;
import org.apache.commons.httpclient.NameValuePair;
import org.apache.commons.httpclient.URIException;
import org.apache.commons.httpclient.methods.GetMethod;
import org.apache.commons.httpclient.methods.PostMethod;
import org.apache.commons.httpclient.methods.StringRequestEntity;
import org.apache.commons.httpclient.params.HttpMethodParams;
import org.apache.commons.httpclient.util.URIUtil;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.*;
import org.springframework.stereotype.Component;
import org.springframework.web.client.RestTemplate;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.URL;
import java.net.URLConnection;
import java.util.*;

@Component
public class HttpClientUtils {
    private static Logger log = Logger.getLogger(HttpClientUtils.class.getName());

//    private static RestTemplate restTemplate;
//
//    @Autowired
//    public void setRestTemplate(RestTemplate restTemplate) {
//        HttpClientUtils.restTemplate = restTemplate;
//    }

    public static String doGet(String url, String queryString, Map<String, String> headers) {
        try {
            url = URIUtil.encodeQuery(url, "UTF-8");
        } catch (URIException e) {
            e.printStackTrace();
        }
        String response = null;
        HttpClient client = new HttpClient();
        org.apache.commons.httpclient.HttpMethod method = new GetMethod(url);
        try {

            if (StringUtils.isNotBlank(queryString)) {
                method.setQueryString(URIUtil.encodeQuery(queryString, "UTF-8"));
            }
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    method.addRequestHeader(entry.getKey(), entry.getValue());
                }
            }
            client.executeMethod(method);
//            if (method.getStatusCode() == HttpStatus.SC_OK) {
            response = method.getResponseBodyAsString();
//            }
        } catch (URIException e) {
//            log.error("执行HTTP Get请求时，编码查询字符串“" + queryString + "”发生异常！", e);
        } catch (IOException e) {
//            log.error("执行HTTP Get请求" + url + "时，发生异常！", e);
        } finally {
            method.releaseConnection();
            client.getHttpConnectionManager().closeIdleConnections(0);
        }
        return response;
    }


    public static String doPost(String url, Map<String, String> params, Map<String, String> headers) {
        try {
            url = URIUtil.encodeQuery(url, "UTF-8");
        } catch (URIException e) {
            e.printStackTrace();
        }
        String response = null;
        HttpClient client = new HttpClient();
        PostMethod method = new PostMethod(url);
        if (headers == null || !headers.containsKey("Content-Type")) {
            method.setRequestHeader("Content-Type", "application/x-www-form-urlencoded");
            if (params != null) {
                for (Map.Entry<String, String> entry : params.entrySet()) {
                    method.setParameter(entry.getKey(), String.valueOf(entry.getValue()));
                }
            }
        } else {
            StringRequestEntity stringRequestEntity = new StringRequestEntity(JSONObject.toJSONString(params));
            method.setRequestEntity(stringRequestEntity);
        }
        //设置Http Post数据

        if (headers != null) {
            for (Map.Entry<String, String> entry : headers.entrySet()) {
                method.addRequestHeader(entry.getKey(), entry.getValue());
            }
        }
        method.getParams().setParameter(HttpMethodParams.HTTP_CONTENT_CHARSET, "UTF-8");
        try {
            client.executeMethod(method);

//            if (method.getStatusCode() == HttpStatus.SC_OK) {
            response = method.getResponseBodyAsString();
//            }
        } catch (IOException e) {
//            log.error("执行HTTP Post请求" + url + "时，发生异常！", e);
        } finally {
            method.releaseConnection();
            client.getHttpConnectionManager().closeIdleConnections(0);
        }

        return response;
    }


    /**
     * 向指定 URL 发送JSON数据格式POST方法的请求
     *
     * @param url   发送请求的 URL
     * @param param 请求参数，请求参数应该是 {"a":a,"b":b,....} 的形式。
     * @return 所代表远程资源的响应结果
     */

    public static String sendPostByJson(String url, Object param, Map<String, String> headers) {
//        PrintWriter out = null;
        BufferedReader in = null;
        String result = "";
        OutputStream outputStream = null;
        try {
            URL realUrl = new URL(url);
            // 打开和URL之间的连接
            URLConnection conn = realUrl.openConnection();
            // 设置通用的请求属性
            conn.setRequestProperty("accept", "*/*");
            conn.setRequestProperty("connection", "Keep-Alive");
            conn.setRequestProperty("user-agent",
                    "Mozilla/4.0 (compatible; MSIE 6.0; Windows NT 5.1;SV1)");
            conn.setRequestProperty("Content-Type", "application/json;charset=utf-8");
            conn.setRequestProperty("Accept-Charset", "utf-8");
//            conn.setRequestProperty("contentType", "utf-8");
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    conn.setRequestProperty(entry.getKey(), entry.getValue());
                }
            }
            // 发送POST请求必须设置如下两行
            conn.setDoOutput(true);
            conn.setDoInput(true);
            if (param != null) {
                //将类实体转为json字符串
                String strObj = JSONObject.toJSONString(param);
                // 获取URLConnection对象对应的输出流
//                out = new PrintWriter(conn.getOutputStream());
                // 发送请求参数
                outputStream = conn.getOutputStream();
                outputStream.write(strObj.getBytes("utf-8"));

                // flush输出流的缓冲
//                out.flush();
            }
            // 定义BufferedReader输入流来读取URL的响应
            in = new BufferedReader(
                    new InputStreamReader(conn.getInputStream(), "UTF-8"));
            String line;
            while ((line = in.readLine()) != null) {
                result += line;
            }

        } catch (Exception e) {
            System.out.println("发送 POST 请求出现异常！" + e);
            e.printStackTrace();
        }
        // 使用finally块来关闭输出流、输入流
        finally {
            try {
//                if(out != null) {
//                    out.close();
//                }

                if (outputStream != null) {
                    outputStream.close();
                }

                if (in != null) {
                    in.close();
                }
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }
        return result;
    }

    public static String doGet(String url, JSONObject bodyParam, Map<String, String> headers) {
        // body无效了……
        try {
            HttpHeaders header = new HttpHeaders();
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    header.add(entry.getKey(), entry.getValue());
                }
            }
            header.setContentType(MediaType.APPLICATION_JSON_UTF8);
            String result = HttpUtil.createGet(url).header(header).execute().body();
            return result;
        } catch (Exception e) {
            UCResponse ucResponse = new UCResponse();
            ucResponse.setRetCode(500);
            ucResponse.setRetInfo(e.getMessage());
            log.error(e);
            return JSON.toJSONString(ucResponse);
        }
    }

    public static Object doGet(String url, JSONObject body, Map<String, String> headers, Class<?> retClazz) {
        // body无效了……
        try {
            HttpHeaders header = new HttpHeaders();
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    header.add(entry.getKey(), entry.getValue());
                }
            }
            header.setContentType(MediaType.APPLICATION_JSON_UTF8);
            String result = HttpUtil.createRequest(Method.GET, url).header(header).execute().body();
            return JSONObject.parseObject(result, retClazz);
        } catch (Exception e) {
            return getErrorRet(retClazz, e);
        }
    }


    public static Object doPost(String url, JSONObject body, Map<String, String> headers, Class<?> retClazz) {
        if (body == null) {
            body = new JSONObject();
        }
        if (body.size() > 0) {
            Map map = JSON.parseObject(body.toJSONString(), Map.class);
            return JSON.parseObject(doPost(url, map, headers), UCResponse.class);
        } else {
            Map<String, String> map = new HashMap<>();
            if (url.contains("?")) {
                String[] split = url.split("[?]")[1].split("&");
                url = url.split("[?]")[0];
                for (int i = 0; i < split.length; i++) {
                    String[] keyValue = split[i].split("=");
                    map.put(keyValue[0], keyValue[1]);
                }
            }

            return JSON.parseObject(doPost(url, map, headers), UCResponse.class);
        }

    }

    public static Object doPost(String url, JSONArray body, Map<String, String> headers, Class<?> retClazz) {
        if (body == null) {
            body = new JSONArray();
        }
        return doPost(url, headers, retClazz, body.toString());
    }

    private static Object doPost(String url, Map<String, String> headers, Class<?> retClazz, String s) {
        try {
            HttpHeaders header = new HttpHeaders();
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    header.add(entry.getKey(), entry.getValue());
                }
            }
            String body = HttpUtil.createPost(url).body(s).header(header).execute().body();
            return JSONObject.parseObject(body, retClazz);
        } catch (Exception e) {
            return getErrorRet(retClazz, e);
        }
    }

    public static Object doPut(String url, JSONObject body, Map<String, String> headers, Class<?> retClazz) {
        if (body == null) {
            body = new JSONObject();
        }
        try {
            HttpHeaders header = new HttpHeaders();
            if (headers != null) {
                for (Map.Entry<String, String> entry : headers.entrySet()) {
                    header.add(entry.getKey(), entry.getValue());
                }
            }
            header.setContentType(MediaType.APPLICATION_JSON_UTF8);
            String result = HttpUtil.createRequest(Method.PUT, url).body(body.toJSONString()).header(header).execute().body();
            return JSONObject.parseObject(result, retClazz);
        } catch (Exception e) {
            return getErrorRet(retClazz, e);
        }
    }

    private static Object getErrorRet(Class<?> retClazz, Exception e) {
        if (retClazz.equals(UCResponse.class)) {
            UCResponse ucResponse = new UCResponse();
            ucResponse.setRetCode(500);
            ucResponse.setRetInfo(e.getMessage());
            e.printStackTrace();
            log.error(e);
            return ucResponse;
        } else {
            return null;
        }
    }
}
