package com.ucmed.thirdparty;

import com.ucmed.bean.NUCResponse;
import com.ucmed.bean.UCResponse;
import com.ucmed.bean.retbean.UserBindInfoVO;

import java.util.List;

/**
 * 第三方用户接口
 *
 * <AUTHOR>
 * @date 2017/11/14 15:04
 */
public interface ThirdPartyService {

    /**
     * 绑定第三方账号
     *
     * @param phone          手机号
     * @param openId         第三方openid
     * @param appCode        应用ID
     * @param thirdPartyType 第三方应用类型：请查看com.ucmed.thirdparty.ThirdPartyEnum类
     * @return UCResponse<br>
     * retCode:0, retInfo:绑定成功<br>
     * retCode:1, retInfo:该用户已绑定，请直接登录<br>
     * retCode:500, retInfo:绑定失败原因<br>
     */
    UCResponse bind(String phone, String openId, int appCode, String roleName, String thirdPartyType);

    /**
     * 使用第三方账号登录
     *
     * @param openId   第三方openid
     * @param appCode  应用ID
     * @param roleName 角色名称
     * @return UCResponse<br>
     * retCode:0, retInfo:登录成功, param:user_id（用户唯一标识）, token, realName（真实姓名）, idCard（身份证号码）, pushId（推送ID）, openId（用户中心openid）<br>
     * retCode:404, retInfo:用户未绑定
     * retCode:500, retInfo:登录失败原因<br>
     */
    UCResponse login(String openId, int appCode, String roleName);

    /**
     * 没有手机号时绑定第三方账号
     *
     * @param id             第三方open_id或邮箱地址等账号
     * @param appCode        应用ID
     * @param thirdPartyType 第三方应用类型：请查看com.ucmed.thirdparty.ThirdPartyEnum类
     * @param roleName       角色名
     * @return UCResponse<br>
     * retCode:0, retInfo:绑定成功<br>
     * retCode:1, retInfo:该用户已绑定，请直接登录<br>
     * retCode:500, retInfo:绑定失败原因<br>
     */
    UCResponse bind(String id, int appCode, String roleName, String thirdPartyType);

    /**
     * 短信验证绑定第三方账号
     *
     * @param phone          手机号
     * @param openId         第三方openid
     * @param appCode        应用ID
     * @param roleName       角色
     * @param thirdPartyType 第三方应用类型：请查看com.ucmed.thirdparty.ThirdPartyEnum类
     * @param msgCode        短信验证码
     * @return UCResponse<br>
     * retCode:0, retInfo:绑定成功<br>
     * retCode:1, retInfo:该用户已绑定，请直接登录<br>
     * retCode:500, retInfo:绑定失败原因<br>
     */
    UCResponse bind(String phone, String openId, int appCode, String roleName, String thirdPartyType, String msgCode);


    /**
     * 更改绑定手机号
     *
     * @param phone          手机号
     * @param openId         第三方openid
     * @param appCode        应用ID
     * @param thirdPartyType 第三方应用类型：请查看com.ucmed.thirdparty.ThirdPartyEnum类
     * @return UCResponse<br>
     * retCode:0, retInfo:绑定成功<br>
     * retCode:1, retInfo:用户还未绑定任何手机号或账号<br>
     * retCode:500, retInfo:绑定失败原因<br>
     */
    UCResponse changePhone(String phone, String openId, int appCode, String roleName, String thirdPartyType);

    /**
     * 解绑
     *
     * @param phone          手机号
     * @param openId         第三方openid
     * @param thirdPartyType 第三方应用类型：请查看com.ucmed.thirdparty.ThirdPartyEnum类
     * @return UCResponse<br>
     * retCode:0, retInfo:解绑成功<br>
     * retCode:500, retInfo:绑定失败原因<br>
     */
    UCResponse unBind(String openId, String phone, String thirdPartyType);


    /**
     * 用于没有手机号的华侨用户,手机号非必填（绑定接口请用上面的bind(String phone, String openId, int appCode, String roleName, String thirdPartyType)）。
     *
     * @param phone          手机号，非必填
     * @param openId         第三方openId
     * @param appCode        应用id
     * @param roleName       角色
     * @param thirdPartyType 第三方类型
     * @return
     */
    UCResponse bindIgnorePhone(String phone, String openId, int appCode, String roleName, String thirdPartyType);

    /**
     * 根据应用id获取用户绑定信息
     *
     * @param phone          手机号，必填
     * @param appCode        应用id
     * @return
     */
    NUCResponse<List<UserBindInfoVO>> getBindInfo(String phone, String appCode);
}
