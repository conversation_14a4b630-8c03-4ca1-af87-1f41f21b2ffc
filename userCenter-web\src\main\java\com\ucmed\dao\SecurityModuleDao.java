package com.ucmed.dao;

import com.ucmed.bean.SecurityModule;

import java.util.List;

/**
 * Created by QIUJIAHAO on 2016/11/15.
 */
public interface SecurityModuleDao {

    List<SecurityModule> getModuleByAppCode(int appCode);

    SecurityModule getModuleById(int moduleId);

    int updateModule(SecurityModule module);

    int addModule(SecurityModule module);

    int deleteModuleById(int moduleId);

    int addAuthorityToRole(int roleId, int moduleId, int appCode, String time);

    int removeAuthorityRromRole(int roleId, int moduleId);
}
