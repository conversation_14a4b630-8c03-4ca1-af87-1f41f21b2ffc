package com.ucmed.common.dao;

import java.util.List;

/**
 * Created by QIUJIAHAO on 2016/7/25.
 */
public interface BaseDao<T> {
    List<T> query(String table, String[] conditions, Object[] parameters, Class<T> clazz);

    int count(String table, String[] conditions, Object[] parameters);

    int count(String sql);

    int update(String table, String[] fields, String[] conditions, Object[] parameters);

    int insert(String table, String[] fields, Object[] parameters);

    int delete(String table, String[] conditions, Object[] parameters);

}
