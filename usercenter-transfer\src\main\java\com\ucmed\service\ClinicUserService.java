package com.ucmed.service;

import com.ucmed.bean.UCResponse;
import com.ucmed.dto.ClinicResponse;
import com.ucmed.dto.ClinicUserInfo;

/**
 * 线下门诊管理系统相关接口
 *
 * <AUTHOR>
 * @date 2017/10/25 16:28
 */
public interface ClinicUserService {

    /**
     * 用户建档
     *
     * @param phone    手机号
     * @param password 密码，明文hash一次后传入
     * @param appCode  应用ID
     * @param roleName 角色名称（传入基础角色名，如患者、普通用户等）
     * @param userInfo ClinicUserInfo 对象
     *                 idCard        身份证号码
     *                 age           年龄
     *                 sex           性别 （1：男，2：女）
     *                 familyPhone   家属电话
     *                 address       家庭地址
     * @return UCResponse<br>
     * retCode:-3, retInfo:角色不存在<br>
     * retCode:-1, retInfo:应用未注册<br>
     * retCode:0, retInfo:注册成功<br>
     * retCode:1, retInfo:用户已注册<br>
     * retCode:2, retInfo:该用户已在别的医院建档，请授权。 param:已授权的应用ID列表，List<Integer>类型<br>
     * retCode:3, retInfo:密码太简单，建议使用大小写字母、数字和特殊字符<br>
     * retCode:5, retInfo:手机号不合法<br>
     * retCode:6, retInfo:身份证号码不能为空<br>
     * retCode:412, retInfo:身份证规则错误<br>
     */
    UCResponse registration(String phone, String password, int appCode, String roleName, ClinicUserInfo userInfo);

    /**
     * 按条分页获取用户列表
     * 非必填项不填传null
     *
     * @param appCode        应用ID, 必填
     * @param clinicUserInfo 查询条件
     *                       pageNo   页码
     *                       pageSize 页长
     *                       theName  姓名
     *                       phone    手机号
     *                       idCard   身份证号码
     * @return ClinicResponse<br>
     * retCode:0, retInfo:查询成功, param: ClinicResponse<br>
     */
    ClinicResponse listUser(int appCode, ClinicUserInfo clinicUserInfo);

    /**
     * 按条分页获取用户列表
     *
     * @param appCode    应用ID, 必填
     * @param pageNo     页码
     * @param pageSize   页长
     * @param conditions 查询条件
     * @return ClinicResponse<br>
     * retCode:0, retInfo:查询成功, param: ClinicResponse<br>
     */
    ClinicResponse listUser(int appCode, Integer pageNo, Integer pageSize, String conditions);

    /**
     * 修改用户信息
     * 只修改不为null的属性
     *
     * @param phone          手机号
     * @param appCode        应用ID
     * @param clinicUserInfo 用户信息
     * @return ClinicResponse<br>
     * retCode:0, retInfo:修改成功<br>
     * retCode:-1, retInfo:用户不存在<br>
     * retCode:-2, retInfo:用户未授权<br>
     * retCode:412, retInfo:身份证规则错误<br>
     */
    UCResponse updateUserInfo(String phone, int appCode, ClinicUserInfo clinicUserInfo);
}
