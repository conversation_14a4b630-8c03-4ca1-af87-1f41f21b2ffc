package com.ucmed.service;

import com.ucmed.bean.UCResponse;

/**
 * 验证码服务接口
 * Created by XXB-QJH-1303.
 * Date: 2017/9/4 9:01
 */
public interface CaptchaService {

    /**
     * 生成图形验证码
     *
     * @param length 验证码长度
     * @return UCResponse<br>
     * retCode:0, retInfo:成功获取图片验证码, param: validate_code_url, token<br>
     * retCode:500, retInfo:获取验证码失败<br>
     * <p>
     * UCResponse{retCode=0, retInfo='成功获取图片验证码', param={"validate_code_url":"https://test.yhzx.ucmed.cn/userCenterUCMED/validatecode/picturecode.htm?token=48efad99-970f-4786-bdd9-bf91b66d77c9&length=4&random=0.9795090177677795","token":"48efad99-970f-4786-bdd9-bf91b66d77c9"}}
     */
    UCResponse getImageCaptcha(int length);

    /**
     * 校验图形验证码
     *
     * @param token       token
     * @param captchaCode 验证码
     * @return UCResponse<br>
     * retCode:0, retInfo:图形验证码验证成功<br>
     * retCode:-1, retInfo:图形验证码过期，请刷新后重试, param: validate_code_url<br>
     * retCode:-2, retInfo:验证码输入错误，请重新输入, param: validate_code_url<br>
     */
    UCResponse verifyImageCaptcha(String token, String captchaCode);

    /**
     * 生成图形验证码
     * 此接口与手机号绑定，生成之前会对手机号规则、账号状态等进行校验
     *
     * @param phone 手机号
     * @param type  验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用
     * @return UCResponse<br>
     * retCode:641, retInfo:手机号不合法<br>
     * retCode:641, retInfo:应用未注册<br>
     * retCode:641, retInfo:账号已注册<br>
     * retCode:641, retInfo:账号未注册<br>
     * retCode:643, retInfo:type类型错误<br>
     * retCode:0, retInfo:成功获取图片验证码, param: validate_code_url<br>
     */
    UCResponse getImageCaptcha(String phone, String type);

    /**
     * 生成图形验证码 可指定验证码长度
     * 此接口与手机号绑定，生成之前会对手机号规则、账号状态等进行校验
     *
     * @param phone  手机号
     * @param type   验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用
     * @param length 验证码长度
     * @return UCResponse<br>
     * retCode:641, retInfo:手机号不合法<br>
     * retCode:641, retInfo:应用未注册<br>
     * retCode:641, retInfo:账号已注册<br>
     * retCode:641, retInfo:账号未注册<br>
     * retCode:643, retInfo:type类型错误<br>
     * retCode:0, retInfo:成功获取图片验证码, param: validate_code_url<br>
     */
    UCResponse getImageCaptcha(String phone, String type, int length);


    /**
     * 生成图形验证码 可指定验证码长度(附带appCode，对独立密码项目账号注册进行校验)
     * 此接口与手机号绑定，生成之前会对手机号规则、账号状态等进行校验
     *
     * @param phone  手机号
     * @param type   验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用
     * @param length 验证码长度
     * @return UCResponse<br>
     * retCode:641, retInfo:手机号不合法<br>
     * retCode:641, retInfo:应用未注册<br>
     * retCode:641, retInfo:账号已注册<br>
     * retCode:641, retInfo:账号未注册<br>
     * retCode:643, retInfo:type类型错误<br>
     * retCode:0, retInfo:成功获取图片验证码, param: validate_code_url<br>
     */
    UCResponse getImageCaptcha(int appCode, String phone, String type, int length);


    /**
     * 校验图形验证码
     *
     * @param phone   手机号
     * @param type    验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号
     * @param picCode 图形验证码
     * @return UCResponse<br>
     * retCode:0, retInfo:图形验证码验证成功<br>
     * retCode:-1, retInfo:图形验证码过期，请刷新后重试, param: validate_code_url<br>
     * retCode:-2, retInfo:验证码输入错误，请重新输入, param: validate_code_url<br>
     */
    UCResponse verifyImageCaptcha(String phone, String type, String picCode);

    /**
     * 生成短信验证码
     * 该接口只生成验证码，不发送短信
     *
     * @param phone  手机号
     * @param type   验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号
     * @param length 验证码长度
     * @return UCResponse<br>
     * retCode:0, retInfo:生成验证码成功, param:验证码<br>
     * retCode:-1, retInfo:生成验证码失败
     */
    UCResponse generateMsgCode(String phone, String type, int length);

    /**
     * 生成短信验证码
     * 该接口只生成验证码，不发送短信
     *
     * @param phone  手机号
     * @param type   验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号
     * @param length 验证码长度
     * @return UCResponse<br>
     * retCode:0, retInfo:生成验证码成功, param:验证码<br>
     * retCode:-1, retInfo:生成验证码失败
     */
    UCResponse generateMsgCode(String phone, String type, int length, long timeOut);
    /**
     * 生成图形验证码
     * 与手机号绑定，但不校验账号信息
     *
     * @param phone  手机号
     * @param type   验证码类型 0：注册 1：忘记密码 2：登录 3：修改手机号 4：其他
     * @param length 验证码长度
     * @return
     */
    UCResponse getCommonImageCaptcha(String phone, String type, int length);


    /**
     * 发送短信验证码（通过短信平台发送）
     * 不校验账号信息
     *
     * @param smsToken 短信平台token
     * @param phone    手机号
     * @param type     验证码类型 0：注册 1：忘记密码 2：登录 3：修改手机号 4：其他
     * @param picCode  图形验证码
     * @return
     */
    UCResponse sendMsgCode(String smsToken, String phone, String type, String picCode);

    /**
     * 发送短信验证码（通过短信平台发送）
     * 不校验账号信息
     *
     * @param smsToken 短信平台token
     * @param phone    手机号
     * @param type     验证码类型 0：注册 1：忘记密码 2：登录 3：修改手机号 4：绑定第三方 5：绑定就诊人
     * @return
     */
    UCResponse sendMsgCode(int appCode, String smsToken, String phone, String type, int length);
}
