package com.ucmed.util;

import net.sf.json.JSONObject;
import org.apache.http.HttpHost;
import org.apache.http.HttpRequest;
import org.apache.http.HttpResponse;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.apache.log4j.Logger;

import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.ArrayList;
import java.util.Properties;


public class SendMsgUtil {
    private static final Logger LOG = Logger.getLogger("SendMsgUtil");

    private static String url = null;
    private static String key = null;

    static {
        Properties prop = new Properties();
        InputStreamReader in;
        try {
            in = new InputStreamReader(SendMsgUtil.class.getResourceAsStream("/configure.properties"), "UTF-8");
            prop.load(in);
            url = prop.getProperty("msg.url",
                    "http://msg.zwjk.com/api/exec/1.htm");
            key = prop.getProperty("msg.key",
                    "ZW5sNWVWOWhibVJ5YjJsaw==");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static JSONObject httpRequest(JSONObject req) {
        LOG.info("request data is >>>>>>>>>>>>>>>>>>>>>>>>"
                + req.toString());
        req.put("TX", "SendMsg");
        req.put("V", "1.0");
        req.put("T", "0");
        req.put("D", "All");
        JSONObject res = new JSONObject();
        String mUrl = url;
        ArrayList<BasicNameValuePair> pairs = new ArrayList<BasicNameValuePair>();
        pairs.add(new BasicNameValuePair("requestData", req.toString()));
        UrlEncodedFormEntity entity = null;
        HttpRequest httpRequest = null;
        HttpResponse response = null;
        HttpHost target = null;
        String resStr = null;
        try {
            URL url = getURL();
            String protocol = url.getProtocol();
            int port = url.getPort();
            if (port == -1)
                port = url.getDefaultPort();
            target = new HttpHost(url.getHost(), port, protocol);
            if (pairs != null) {
                httpRequest = new HttpPost(mUrl);
                entity = new UrlEncodedFormEntity(pairs, "utf-8");
                ((HttpPost) httpRequest).setEntity(entity);
            }
            httpRequest.addHeader("K", key);
            httpRequest.addHeader("Content-type",
                    "application/x-www-form-urlencoded;charset=utf-8");
            httpRequest
                    .addHeader(
                            "Accept",
                            "application/xml,application/xhtml+xml,text/html;q=0.9,text/plain;q=0.8,image/png,*/*;q=0.5");
            response = HttpConnectionManager.getHttpClient().execute(
                    target, httpRequest);
            if (response != null
                    && response.getStatusLine().getStatusCode() == 200) {
                resStr = EntityUtils.toString(response.getEntity());
            }
            LOG.info("return data is <<<<<<<<<<<<<<<<<<<<<<<<<<" + resStr);
        } catch (Exception e) {
            LOG.error("http请求错误" + e);
        } finally {
            if (response != null) {
                try {
                    EntityUtils.consume(response.getEntity());// 会自动释放连接
                } catch (IOException e) {
                    LOG.error("释放连接错误" + e);
                }
            }
        }
        if (null == resStr) {
//            res.put(ReturnMsg.R, ReturnMsg.UNKNOWN_ERROR);
//            res.put(ReturnMsg.I, ReturnMsg.UNKNOWN_ERROR_MSG);
            return res;
        }
        JSONObject tmp = JSONObject.fromObject(resStr);
        return tmp;
    }

    private static URL getURL() {
        URL qz_url = null;
        try {
            qz_url = new URL(url);
        } catch (Exception e) {
        }

        return qz_url;
    }

}