package com.ucmed.specialservice;

import cn.hutool.core.date.DateUtil;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.poi.excel.ExcelWriter;
import cn.ucmed.common.constants.GlobalConstants;
import cn.ucmed.rubik.sms.view.SmsInfo;
import com.ucmed.bean.*;
import com.ucmed.bean.controllerbean.ThridUserTestBean;
import com.ucmed.bean.retbean.UnderlineUCResponse;
import com.ucmed.common.service.CommonService;
import com.ucmed.dto.UserCancelAuthDTO;
import com.ucmed.exception.BusinessException;
import com.ucmed.mapper.*;
import com.ucmed.message.client.SmsClient;
import com.ucmed.service.*;
import com.ucmed.util.SHA256Util;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

import static com.ucmed.common.constant.CommonConstant.SPECIAL_LOGIN_FLAG;
import static com.ucmed.common.constant.CommonConstant.USERTOKEN_CACHE;
import static com.ucmed.common.constant.TableName.*;
import static com.ucmed.transfer.UserServiceImpl.MTKEY_INVITE_MESSAGE_CODE;

@Service
public class SpecialService extends CommonService {
    private static Logger log4j = Logger.getLogger(SpecialService.class.getName());
    @Autowired
    private UserService userService;
    @Autowired
    private GetAppInfoService getAppInfoService;
    @Autowired
    private Login login;
    @Autowired
    private SecurityUserProjectMapper securityUserProjectMapper;
    @Autowired
    private SecurityUserAppMapper securityUserAppMapper;
    @Autowired
    private SecurityUserRoleMapper securityUserRoleMapper;
    @Autowired
    private SecurityUserProjectService securityUserProjectService;
    @Autowired
    private JcUserMapper jcUserMapper;
    @Autowired
    private JcUserInfoMapper jcUserInfoMapper;
    @Autowired
    private JcUserThirdPartyService JcUserThirdPartyService;
    @Autowired
    private SmsClient smsClient;
    @Autowired
    private UserRecycleBinMapper userRecycleBinMapper;
    @Autowired
    private RedisTemplate redisTemplate;

    public static final String USER_LOG_OUT_MESSAGE_CODE = "user_log_out";

    String defaultRoleName = "患者";

    public UCResponse login(int appid, String phone, String roleName) {
        Application application = getAppInfoService.getAppByCode(appid);
        if (StringUtils.isEmpty(application.getSpecial_flag())) {
            return new UCResponse(-1, "应用id错误");
        }
        String flag = application.getSpecial_flag();
        if (!flag.contains(SPECIAL_LOGIN_FLAG)) {
            return new UCResponse(-1, "应用id错误");
        }
        if (roleName == null) {
            roleName = defaultRoleName;
        }
        UCResponse response = login.SecretLogin(phone, appid, roleName);
        if (response.getRetCode() == 0) {
            return response;
        }
        if (response.getRetCode() == 4) {
            UCResponse ucResponse = userService.registration(phone, phone, SHA256Util.hash("UCMED@123456"), appid, roleName);
            if (ucResponse.getRetCode() != 0) {
                return ucResponse;
            }
            ucResponse = userService.setPermission(phone, appid, roleName);
            if (ucResponse.getRetCode() != 0) {
                return ucResponse;
            }
            response = login.SecretLogin(phone, appid, roleName);
        }
        if (response.getRetCode() == 5 || response.getRetCode() == 6) {
            UCResponse ucResponse = userService.setPermission(phone, appid, roleName);
            if (ucResponse.getRetCode() != 0) {
                return ucResponse;
            }
            response = login.SecretLogin(phone, appid, roleName);
        }
        return response;
    }

    @Transactional()
    public UnderlineUCResponse cancelAuthorization(Integer projectId, String phone, String appName) throws BusinessException {
        log4j.info("用户进行注销，phone：" + phone + "，项目：" + projectId);
        UC_UserInfo user = getUser(phone);
        if (user == null) {
            log4j.info("返回用户不存在，phone：" + phone + "，项目：" + projectId);
            return UnderlineUCResponse.createByError(0, "用户不存在");
        }
        List<UserCancelAuthDTO> userCancelAuthDTOS = securityUserProjectService.listUserAuthInfo(user.getUser_id(), projectId);
        if (CollectionUtils.isEmpty(userCancelAuthDTOS)) {
            log4j.info("返回用户不存在该项目的授权信息，phone：" + phone + "，项目：" + projectId);
            return UnderlineUCResponse.createByError(0, "用户不存在该项目的授权信息");
        }
        List<Application> appsByProjCode = getAppInfoService.getAppsByProjCode(projectId);
        List<Integer> appCodes = appsByProjCode.stream().map(application -> Integer.valueOf(application.getApp_code())).collect(Collectors.toList());
        for (int i = 0; i < userCancelAuthDTOS.size(); i++) {
            UserCancelAuthDTO userCancelAuthDTO = userCancelAuthDTOS.get(i);
            if (projectId.equals(userCancelAuthDTO.getProjCode())
                    && projectId.equals(userCancelAuthDTO.getAppProjCode())
                    && appCodes.contains(userCancelAuthDTO.getAppCode())
                    && user.getUser_id().equals(userCancelAuthDTO.getProjUserId())) {
                try {
                    UserAppToken cachedToken = (UserAppToken) redisTemplate.opsForHash().get(USERTOKEN_CACHE + user.getUser_id(),
                            userCancelAuthDTO.getAppCode().toString());
                    if (cachedToken != null) {
                        userService.logout(cachedToken.getToken());
                    }
                } catch (Exception e) {
                    log4j.info("该用户token在" + userCancelAuthDTO.getAppCode() + "下不存在token");
                }
                SecurityUserProject securityUserProject = new SecurityUserProject();
                securityUserProject.setUserProjectId(userCancelAuthDTO.getUserProjectId());
                securityUserProject.setDeletion("1");
                securityUserProject.setUpdateTime(DateUtil.now());
                securityUserProjectMapper.updateDelete(securityUserProject);
                UserRecycleBin userRecycleBinForUserApp = new UserRecycleBin(user.getUser_id(), user.getPhone(),
                        SECURITYUSERAPP, securityUserAppMapper.selectByPrimaryKey(userCancelAuthDTO.getUserAppId())
                );
                userRecycleBinMapper.insertUserRecycleBin(userRecycleBinForUserApp);
                securityUserAppMapper.deleteByPrimaryKey(userCancelAuthDTO.getUserAppId());
                UserRecycleBin userRecycleBinForUserRole = new UserRecycleBin(user.getUser_id(), user.getPhone(),
                        SECURITYUSERROLE, securityUserRoleMapper.selectByPrimaryKey(userCancelAuthDTO.getUserRoleId())
                );
                userRecycleBinMapper.insertUserRecycleBin(userRecycleBinForUserRole);
                securityUserRoleMapper.deleteByPrimaryKey(userCancelAuthDTO.getUserRoleId());
                JcUserThirdParty jcUserThirdParty = new JcUserThirdParty();
                jcUserThirdParty.setCreateBy(String.valueOf(userCancelAuthDTO.getAppCode()));
                jcUserThirdParty.setUserId(user.getUser_id());
                jcUserThirdParty.setDeletion("1");
                jcUserThirdParty.setUpdateTime(new Date());
                jcUserThirdParty.setUpdateBy(String.valueOf(userCancelAuthDTO.getAppCode()));
                JcUserThirdPartyService.unbindUserProject(jcUserThirdParty);
            }
        }
        List<SecurityUserProject> openIdByUserId = securityUserProjectService.getOpenIdByUserId(user.getUser_id());
        if (CollectionUtils.isEmpty(openIdByUserId)) {
            JcUser jcUser = new JcUser();
            jcUser.setUserId(user.getUser_id());
            jcUser.setPhone(user.getPhone());
            List<JcUser> jcUserList = jcUserMapper.select(jcUser);
            if (CollectionUtils.isNotEmpty(jcUserList)) {
                JcUser toDeleteJcUser = jcUserList.get(0);
                UserRecycleBin userRecycleBin = new UserRecycleBin(user.getUser_id(), user.getPhone(), JCUSER, toDeleteJcUser);
                userRecycleBinMapper.insertUserRecycleBin(userRecycleBin);
                jcUserMapper.deleteByPrimaryKey(toDeleteJcUser);
            }
            UserInfo userInfo = new UserInfo();
            userInfo.setUser_id(user.getUser_id());
            userInfo.setMobile(user.getPhone());
            List<UserInfo> userInfoList = jcUserInfoMapper.select(userInfo);
            if (CollectionUtils.isNotEmpty(userInfoList)) {
                UserInfo toDeleteJcUserInfo = userInfoList.get(0);
                UserRecycleBin userRecycleBin = new UserRecycleBin(user.getUser_id(), user.getPhone(), JCUSERINFO, toDeleteJcUserInfo);
                userRecycleBinMapper.insertUserRecycleBin(userRecycleBin);
                jcUserInfoMapper.deleteByPrimaryKey(toDeleteJcUserInfo);
            }
        }

        String templateId = USER_LOG_OUT_MESSAGE_CODE;
        String phoneStar = phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        String content = phoneStar + "，您好，您在 " + appName + " 提交的账号注销申请已处理完成。此账号" + phoneStar + "已无法登陆，若需继续使用，您可以重新注册账号，谢谢您一直以来的支持。";

        Map<String, String> smsParams = new HashMap<String, String>();
        smsParams.put("phone", phoneStar);
        smsParams.put("appName", appName);
        List<String> paramList = new ArrayList<>();
        paramList.add(phoneStar);
        paramList.add(appName);
        paramList.add(phoneStar);
        if (!smsClient.sendMessage(String.valueOf(appCodes.get(0)), new SmsInfo(phone, phone, content, templateId, smsParams, paramList))) {
            throw new BusinessException(0, "发送短信失败");
        }
        log4j.info("用户进行注销完成，phone：" + phone + "，项目：" + projectId);
        UnderlineUCResponse<Object> bySuccess = UnderlineUCResponse.createBySuccess();
        bySuccess.setRet_info("返回成功");
        return bySuccess;
    }

    public NUCResponse checkDeleteUser(String phone, String projectId, String appName) throws BusinessException {
//        JcUser jcUser = new JcUser();
//        jcUser.setPhone("delete-" + ThreeDESEncrypt(phone));
//        List<JcUser> select = jcUserMapper.select(jcUser);
//        System.out.println(select);
//        if (CollectionUtils.isNotEmpty(select) && select.size() == 1) {
        List<Application> appsByProjCode = getAppInfoService.getAppsByProjCode(Integer.valueOf(projectId));
        List<Integer> appCodes = appsByProjCode.stream().map(application -> Integer.valueOf(application.getApp_code())).collect(Collectors.toList());

        String templateId = MTKEY_INVITE_MESSAGE_CODE;
        String phoneStar = phone.replaceAll("(\\d{3})\\d{4}(\\d{4})", "$1****$2");
        String content = phoneStar + "，您好，您在 " + appName + " 提交的账号注销申请已处理完成。此账号" + phoneStar + "已无法登陆，若需继续使用，您可以重新注册账号，谢谢您一直以来的支持。";
        Map<String, String> smsParams = new HashMap<String, String>();
        smsParams.put("phone", phoneStar);
        smsParams.put("appName", appName);
        List<String> paramList = new ArrayList<>();
        paramList.add(phoneStar);
        paramList.add(appName);
        paramList.add(phoneStar);
        if (!smsClient.sendMessage(String.valueOf(appCodes.get(0)), new SmsInfo(phone, phone, content, templateId, smsParams, paramList))) {
            throw new BusinessException(642, "发送短信失败");
        }
//            return NUCResponse.createBySuccess();
//        }
        return NUCResponse.createBySuccess();
    }

    public NUCResponse infoAuthorization(Integer projCode, String phones) {
        List<String> phoneList = Arrays.asList(phones.split(","));
        System.out.println("共有：" + phoneList.size());
        for (int i = 0; i < phoneList.size(); i++) {
            System.out.println(i);
            String phone = phoneList.get(i);
            UC_UserInfo user = getUser(phone);
            if (user == null) {
                System.out.println("phone：" + phone + " 不存在");
                continue;
            }
            List<SecurityUserProject> openIdListByUserId = securityUserProjectMapper.getOpenIdListByUserId(user.getUser_id());
            if (CollectionUtils.isEmpty(openIdListByUserId)) {
                System.out.println("phone：" + phone + " 不存在授权");
            }
            StringBuilder s = new StringBuilder();
            s.append(phone);
            for (int j = 0; j < openIdListByUserId.size(); j++) {
                SecurityUserProject securityUserProject = openIdListByUserId.get(j);
                if (securityUserProject.getProjCode().equals(projCode)) {
                    s.append(" 存在于").append(projCode);
                } else {
                    s.append(" 不仅存在目标项目还存在于").append(securityUserProject.getProjCode());
                }
            }
            System.out.println(s.toString());
        }
        return NUCResponse.createBySuccess();
    }

    public void exportUserThridInfo() {
        List<ThridUserTestBean> thridUserTestBeans = jcUserMapper.listThridUserTestBean();
        for (ThridUserTestBean thridUserTestBean : thridUserTestBeans) {
            thridUserTestBean.setPhone(ThreeDESDecrypt(thridUserTestBean.getPhone()));
//            switch (thridUserTestBean.getType().trim()) {
//                case "1":
//                    thridUserTestBean.setType("微信");
//                    break;
//                case "2":
//                    thridUserTestBean.setType("企业微信");
//                    break;
//                case "3":
//                    thridUserTestBean.setType("支付宝");
//                    break;
//                case "4":
//                    thridUserTestBean.setType("其他");
//                    break;
//                case "11":
//                    thridUserTestBean.setType("小程序");
//                    break;
//                default:
//                    thridUserTestBean.setType("其他");
//                    break;
//            }
        }
        ExcelWriter writer = ExcelUtil.getWriter("d:/用户中心第三方绑定信息.xlsx");
        writer.write(thridUserTestBeans, true);
        writer.close();
    }

}
