package com.ucmed.message.web;

import cn.ucmed.common.util.PaginationResult;
import cn.ucmed.common.util.ResultUtil;
import com.ucmed.common.constant.SmsGatewayConstants;
import com.ucmed.message.model.TSmsGateway;
import com.ucmed.message.service.SmsGateway;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;

/**
 * <AUTHOR>
 * @Description 短信网关
 * @date 2016年4月27日 下午2:07:27
 */
@Controller
public class SmsGatewayController {

    private static final Logger LOG = Logger.getLogger(SmsGatewayController.class);

    private static final String SUCCESS = "success";
    @Autowired
    private SmsGateway smsGateway;

    /**
     * 获取列表，带分页
     *
     * @param request
     * @param currentPageNo
     * @param pageSize
     * @return
     */
    @RequestMapping(method = RequestMethod.GET, value = SmsGatewayConstants.LIST)
    @ResponseBody
    public PaginationResult<TSmsGateway> getList(
            HttpServletRequest request, @RequestParam("currentPageNo") int currentPageNo,
            @RequestParam("pageSize") int pageSize, String appCode) {
        return smsGateway.getPaginatedList(appCode, currentPageNo, pageSize);
    }

    /**
     * 新增
     *
     * @param request
     * @param model
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = SmsGatewayConstants.ADD)
    @ResponseBody
    public JSONObject addPost(
            HttpServletRequest request, @RequestBody TSmsGateway model, String appCode) {
        int code = smsGateway.add(appCode, model);
        if (code != 1) {
            return ResultUtil.getResult(1, "添加失败");
        }
        return ResultUtil.getResult(0, SUCCESS);
    }

    /**
     * 修改
     *
     * @param request
     * @param model
     * @return
     */
    @RequestMapping(method = RequestMethod.PUT, value = SmsGatewayConstants.EDIT)
    @ResponseBody
    public JSONObject updatePut(
            HttpServletRequest request, @RequestBody TSmsGateway model) {
        int code = smsGateway.update(model);
        if (code != 1) {
            return ResultUtil.getResult(1, "更新失败");
        }
        return ResultUtil.getResult(0, SUCCESS);
    }

    /**
     * 设置：禁用和开启
     *
     * @param model
     * @return
     */
    @RequestMapping(method = RequestMethod.POST, value = SmsGatewayConstants.SETEFFICT)
    @ResponseBody
    public JSONObject setEffict(
            @RequestBody TSmsGateway model) {
        int code = smsGateway.update(model);
        if (code != 1) {
            return ResultUtil.getResult(1, "设置失败");
        }
        return ResultUtil.getResult(0, SUCCESS);
    }

}