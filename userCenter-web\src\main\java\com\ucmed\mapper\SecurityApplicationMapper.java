package com.ucmed.mapper;

import com.ucmed.bean.UserAppToken;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import java.util.Map;

public interface SecurityApplicationMapper {
    @Select({
            "select " +
            "proj_code from security_application " +
            "where app_code=#{appCode}"
    })
    @Results({
            @Result(column = "proj_code", jdbcType = JdbcType.INTEGER),
    })
    public int seclectProjCodeByAppCode(int appCode);

    @Select({
            "select " +
            "app_code as appCode, token_life_cycle as expiredTime from security_application "
    })
    @Results({
            @Result(column = "app_code", property = "appCode",jdbcType = JdbcType.INTEGER),
            @Result(column = "token_life_cycle", property = "expiredTime", jdbcType = JdbcType.INTEGER),
    })
    @MapKey("appCode")
    Map<Integer, UserAppToken> seclectAllAppTokenLife();

    @Select({
            "select " +
                    "app_code from security_application " +
                    "where app_token=#{appToken}"
    })
    @Results({
            @Result(column = "app_code", jdbcType = JdbcType.INTEGER),
    })
    Integer seclectAppCodeByappToken(Integer appToken);

    @Select({
            "select " +
                    "app_token from security_application " +
                    "where app_code=#{appCode}"
    })
    @Results({
            @Result(column = "app_token", jdbcType = JdbcType.INTEGER),
    })
    Integer seclectAppTokenByAppCode(Integer appCode);

}
