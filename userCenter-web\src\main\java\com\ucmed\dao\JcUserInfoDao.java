package com.ucmed.dao;

import com.ucmed.bean.UserInfo;

/**
 * Created by QIUJIAHAO on 2016/8/16.
 */
public interface JcUserInfoDao {

    /**
     * 根据userId查询用户个人信息
     * @param userId
     * @return
     */
    UserInfo getUserInfoByUserId(String userId);

    /**
     * 更新用户信息
     * @param userInfo
     * @return
     */
    int updateUserInfo(UserInfo userInfo);

    /**
     * 新增用户信息
     * @param userInfo
     * @return
     */
    int addUserInfo(UserInfo userInfo);

    /**
     * 删除用户个人信息
     * @param userId
     * @return
     */
    int deleteUserInfo(String userId);
}
