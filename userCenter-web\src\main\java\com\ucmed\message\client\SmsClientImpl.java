package com.ucmed.message.client;

import cn.ucmed.common.constants.model.DefaultDBValue;
import cn.ucmed.common.ratelimit.*;
import cn.ucmed.rubik.sms.view.SmsInfo;
import cn.ucmed.util.SendMsgUtil;
import cn.ucmed.util.SmsGatewayHttpClient;
import com.jfinal.json.Json;
import com.ucmed.dto.MessageResult;
import com.ucmed.message.dao.SmsHistoryMapper;
import com.ucmed.message.model.SMSHistory;
import com.ucmed.message.model.TSmsGateway;
import com.ucmed.message.service.SmsGateway;
import com.ucmed.util.HttpClientUtils;
import com.ucmed.util.HttpRequest;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

public class SmsClientImpl implements SmsClient {

    private static final Logger LOG = Logger.getLogger(SmsClientImpl.class);
    @Autowired
    private SmsGatewayHttpClient smsGatewayHttpClient;

    /**
     * 用于测试的手机号码，从配置获取
     */
    private String testPhone;
    @Autowired
    private SmsHistoryMapper smsHistoryMapper;

    //    private RequestData requestData;
    @Autowired
    private SmsGateway smsGateway;
    @Autowired
    private SendMsgUtil sendMsgUtil;

    @Autowired
    private SmsThirdService smsThirdWebService;
    @Autowired
    private SmsThirdService smsThirdDubboService;
    @Autowired
    private SmsThirdService smsThirdPlatSmsService;
    private SmsThirdService smsThirdCommService;
    @Autowired
    private SmsFilter dailyCountFilter;
    @Autowired
    private SmsFilter frequencyFilter;

    public void setTestPhone(String testPhone) {
        this.testPhone = testPhone;
    }

    public void setSmsGatewayHttpClient(SmsGatewayHttpClient smsGatewayHttpClient) {
        this.smsGatewayHttpClient = smsGatewayHttpClient;
    }


    @Override
    public Boolean sendMessage(String appCode, List<SmsInfo> lstSmsInfo) {
        Boolean sendStatus;
        MessageResult messageResult = null;
        if (org.springframework.util.CollectionUtils.isEmpty(lstSmsInfo)) {
            return false;
        }
        try {
            // 0设置短信流量限制 频度  总量
            LOG.warn(Json.getJson().toJson(lstSmsInfo) + ":SmsClientImpl.sendMessage:sms");
            smsDenyFilter(appCode, lstSmsInfo);

            //获取 原先是否设置了 Http 接口
            String mUrl = smsGateway.loadGatewayUrl(appCode);


            //1 查询设置了SmsgateWay 服务,
            TSmsGateway tSmsGateway = smsGateway.loadGateway(appCode);
            //2， 根据 已经设置的类型 Dubbo, smsPlat, Webservice, （http）  设置为对于的smsgateWay 服务 代理
            buildSmsThirdCommService(tSmsGateway);

            String platformHostpitalId = appCode;
            boolean thirdSmsGateWay = false;

            if (tSmsGateway != null && !SmsGateway.PROTOCOL_HTTP.equalsIgnoreCase(tSmsGateway.getProtocolType())) {
                thirdSmsGateWay = true;
                //platformHostpitalId = tSmsGateway.getPlatformHospitalId();
            }
            if (thirdSmsGateWay) {
                //3， 根据设置的代理 发送Sms短信
                messageResult = smsThirdCommService.sendMessage(tSmsGateway, platformHostpitalId, lstSmsInfo);
                sendStatus = (messageResult != null && messageResult.getResultCode() == 100) ? true : false;
            } else { //处理原先的发消息
                sendStatus = StringUtils.isNotEmpty(mUrl) ?
                        sendByUrl(mUrl, lstSmsInfo) :
                        defaultSend(lstSmsInfo);
            }
            /**
             * 记录短信流水
             */
            for (SmsInfo smsInfo : lstSmsInfo) {

                addMsg(smsInfo, sendStatus, appCode, messageResult);
            }

            return sendStatus;
        } catch (Exception e) {
            LOG.error(StringUtils.EMPTY, e);
            if (e instanceof SendSmsFailException) {
                throw e;
            }
            return false;
        }
    }

    /**
     * 设置 第三方发送短信代理服务
     * String PROTOCOL_HTTP = "http";     http 协议继续 原先协议
     * String PROTOCOL_PLATSMS = "platsms";
     * String PROTOCOL_WEBSERVICE = "webservice";
     * String PROTOCOL_DUBBO_RENDER = "dubbo_render";
     *
     * @param tSmsGateway
     */
    private void buildSmsThirdCommService(TSmsGateway tSmsGateway) {
        if (tSmsGateway == null) {
            return;
        }


        if (StringUtils.equalsIgnoreCase(SmsGateway.PROTOCOL_DUBBO_RENDER, tSmsGateway.getProtocolType())) {
            smsThirdCommService = smsThirdDubboService;
        } else if (StringUtils.equalsIgnoreCase(SmsGateway.PROTOCOL_PLATSMS, tSmsGateway.getProtocolType())) {
            smsThirdCommService = smsThirdPlatSmsService;
        } else if (StringUtils.equalsIgnoreCase(SmsGateway.PROTOCOL_WEBSERVICE, tSmsGateway.getProtocolType())) {
            smsThirdCommService = smsThirdWebService;
        }
    }

    /**
     * 验证是否达到发送短信的上限，是否短信发送频率过高
     *
     * @param appCode
     * @param lstSmsInfo
     * @return
     */
    private void smsDenyFilter(String appCode, List<SmsInfo> lstSmsInfo) {
        Sms rateSms;
        try {
            for (SmsInfo smsInfo : lstSmsInfo) {
                rateSms = new Sms(smsInfo.getReceiverMobile(), appCode, smsInfo.getTemplateKey(), smsInfo.getContent());
                frequencyFilter.filter(rateSms);
                dailyCountFilter.filter(rateSms);
            }
        } catch (SendSmsFailException e) {
            LOG.error("SendSmsFailException--Deny", e);
            if (e instanceof DailySendMuchException) {
                throw new DailySendMuchException("您已获取20次验证码，当天无法再获取验证码");
            }
            if (e instanceof FrequentlyException) {
                throw new FrequentlyException("您已获取3次验证码，请2分钟后重新获取");
            }
            throw e;
        }
    }

    @Override
    public Boolean sendMessage(SmsInfo smsInfo) {
        List<SmsInfo> lstSmsInfo = new ArrayList<SmsInfo>();
        lstSmsInfo.add(smsInfo);
        return sendMessage(lstSmsInfo);
    }

    @Override
    public Boolean sendMessageMQ(String vcProjectId, SmsInfo smsInfo) {
        List<SmsInfo> lstSmsInfo = new ArrayList<SmsInfo>();
        lstSmsInfo.add(smsInfo);
        try {
            String mUrl = null;
            Boolean sendStatus = StringUtils.isNotEmpty(mUrl) ?
                    sendByUrl(mUrl, lstSmsInfo) :
                    defaultSend(lstSmsInfo);

            /**
             * 记录短信流水
             */
            for (SmsInfo smsInf : lstSmsInfo) {
                addMsgMQ(smsInf, sendStatus, vcProjectId);
            }

            return sendStatus;
        } catch (Exception e) {
            LOG.error(StringUtils.EMPTY, e);
            return false;
        }

    }

    @Override
    public Boolean sendMessage(List<SmsInfo> lstSmsInfo) {
        return sendMessage(StringUtils.EMPTY, lstSmsInfo);
    }


    @Override
    public Boolean sendMessage(String appCode, SmsInfo smsInfo) {
        List<SmsInfo> lstSmsInfo = new ArrayList<SmsInfo>();
        lstSmsInfo.add(smsInfo);
        return sendMessage(appCode, lstSmsInfo);
    }


    /**
     * 短信发送实现通过外部url实现
     *
     * @param url        外部url
     * @param lstSmsInfo 短信手机号+消息列表
     * @return 发送是否成功，成功为true，失败为false
     */
    private Boolean sendByUrl(String url, List<SmsInfo> lstSmsInfo) {

        smsGatewayHttpClient.setmUrl(url);

        /**
         * 发送短信的实现由表sms_gateway配置的url对应的controller来完成；
         */
        String result = smsGatewayHttpClient.
                sendSynchronousRequest(requestInfo(lstSmsInfo));
        /**
         * 网关那边接收的信息：requestData={"code":"success","info":[{"phone":"18768491403","phone_info":"你的验证码：11"},{"phone":"15381130013","phone_info":"你的验证码：22"}]}
         * 网关那边返回的信息：{"info":"success"}，其中成功：success，失败：fail
         */
        LOG.info("smsGateway response info :" + result);
        JSONObject res = JSONObject.fromObject(result);
        return StringUtils.isNotEmpty(result) && null != res && "success".equals(res.optString("info"));
    }

    /**
     * TODO 现在使用的短信平台不支持群发功能，如果列表>1，只发送第一个
     * rubik 默认短信发送方式
     *
     * @param lstSmsInfo 短信手机号+消息列表
     * @return 发送是否成功，成功为true，失败为false
     */
    private Boolean defaultSend(List<SmsInfo> lstSmsInfo) {

        Map<String, String> lstMSG = new HashMap<String, String>();

        JSONObject msgJSON = new JSONObject();
        for (SmsInfo msg : lstSmsInfo) {
            // lstMSG.put(msg.getReceiverMobile(), msg.getContent());
            msgJSON.put("msg", msg.getContent());
            msgJSON.put("phone", msg.getReceiverMobile());
            break;
        }

        return "200".equals(sendMsgUtil.httpRequest(msgJSON).optString("R"));
    }

    private JSONObject requestInfo(List<SmsInfo> lstSmsInfo) {

        JSONArray arr = new JSONArray();

        for (SmsInfo smsInfo : lstSmsInfo) {
            JSONObject jsonSmsInfo = new JSONObject();
            jsonSmsInfo.put("phone", StringUtils.isEmpty(testPhone) ?
                    smsInfo.getReceiverMobile() : testPhone);
            jsonSmsInfo.put("phone_info", smsInfo.getContent());
            arr.add(jsonSmsInfo);
        }

        JSONObject json = new JSONObject();
        json.put("code", "success");
        json.put("info", arr.toString());
        return json;
    }

    private void addMsg(SmsInfo smsInfo, Boolean sendStatus, String vcProjectId, MessageResult messageResult) {
        SMSHistory model = setSmsHistoryModel(smsInfo, sendStatus, vcProjectId, messageResult);
        smsHistoryMapper.insertSelective(model);
    }


    private void addMsgMQ(SmsInfo smsInfo, Boolean sendStatus, String vcProjectId) {
        SMSHistory model = setSmsHistoryModel(smsInfo, sendStatus, vcProjectId, null);
        smsHistoryMapper.insertSelective(model);
    }


    private SMSHistory setSmsHistoryModel(SmsInfo smsInfo,
                                          Boolean sendStatus, String vcProjectId, MessageResult messageResult) {
        SMSHistory model = new SMSHistory();
        String senderId = smsInfo.getSenderMobile();
        model.setSenderScyUserId(senderId);
        model.setSenderMobile(smsInfo.getSenderMobile());
        model.setReceiverMobile(smsInfo.getReceiverMobile());
        model.setContent(smsInfo.getContent());
        model.setSendStatus(sendStatus ? "1" : "0");
        model.setVcProjectId(vcProjectId);
        model.setTemplateKey(smsInfo.getTemplateKey());
        model.setSmsHistoryId(UUID.randomUUID().toString());
        model.setCreatedby(senderId);
        model.setCreatedon(new Date());
        model.setModifiedby(senderId);
        model.setModifiedon(new Date());
        model.setDeletionState(DefaultDBValue.CREATE_DELETION_STATE);
        model.setDescription(Json.getJson().toJson(messageResult));
        return model;
    }


}
