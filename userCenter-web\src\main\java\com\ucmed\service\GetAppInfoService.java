package com.ucmed.service;

import com.ucmed.bean.Application;
import net.sf.json.JSONObject;

import java.util.List;


public interface GetAppInfoService {
	
	/**
	 * 获取用户注册的应用程序信息
	 */
	String getAppsByUser(JSONObject rcv);
	
	/**
	 * 获取用户注册的角色信息
	 */
	String getRolesByUser(JSONObject rcv);
	
	/**
	 * 获取某应用程序的所有角色信息
	 */
	String getRolesByApp(JSONObject rcv);
	
	/**
	 * 获取用户在某应用程序的所有角色信息
	 */
	String getRolesByAppAndUser(JSONObject rcv);

    /**
     * 应用是否注册
     * @param app_code
     * @return
     */
    boolean isAppExists(int app_code);

    Application getAppByCode(int appCode);

    List<Application> getAppsByProjCode(Integer projCode);

}
