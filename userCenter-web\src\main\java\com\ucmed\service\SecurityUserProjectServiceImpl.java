package com.ucmed.service;

import com.ucmed.bean.SecurityUserProject;
import com.ucmed.dto.UserCancelAuthDTO;
import com.ucmed.mapper.SecurityUserProjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
public class SecurityUserProjectServiceImpl implements SecurityUserProjectService {
    @Autowired
    private SecurityUserProjectMapper securityUserProjectMapper;

    @Override
    public SecurityUserProject getUserProject(String userId, int projectId) {
        List<SecurityUserProject> userProjects = securityUserProjectMapper.selectByUserIdAndProjCode(userId, projectId);
        if (userProjects.size() == 0) {
            return null;
        }
        return userProjects.get(0);
    }

    @Override
    public SecurityUserProject getUserProjectByOpenId(String openId) {
        List<SecurityUserProject> userProjects = securityUserProjectMapper.selectByOpenId(openId);
        if (userProjects.size() == 0) {
            return null;
        }
        return userProjects.get(0);
    }

    @Override
    public void addUserProject(SecurityUserProject userProject) {
        securityUserProjectMapper.insert(userProject);
    }

    @Override
    public void updateUserProject(SecurityUserProject userProject) {
        securityUserProjectMapper.updateByPrimaryKey(userProject);
    }

    @Override
    public void updatedlPwd(SecurityUserProject userProject) {
        securityUserProjectMapper.updatedlPwdByPrimaryKey(userProject);
    }

    @Override
    public List<SecurityUserProject> getOpenIdByUserId(String userId) {
        return securityUserProjectMapper.getOpenIdListByUserId(userId);
    }

    @Override
    public List<UserCancelAuthDTO> listUserAuthInfo(String userId, Integer projCode) {
        return securityUserProjectMapper.listUserAuthInfo(userId, projCode);
    }
}
