package com.ucmed.service;

import java.util.List;
import java.util.concurrent.TimeUnit;

import com.ucmed.bean.Role;
import com.ucmed.bean.UC_UserInfo;
import com.ucmed.common.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import com.ucmed.bean.AppAndRole;
import com.ucmed.bean.Application;
import com.ucmed.dao.GetAppInfoDao;
import com.ucmed.util.JsonFormat;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.transaction.annotation.Transactional;

import static com.ucmed.common.constant.CommonConstant.*;

@Service("getAppInfoService")
@Transactional
public class GetAppInfoServiceImpl extends CommonService implements GetAppInfoService {
	@Autowired
	private GetAppInfoDao getAppInfoDao;
	@Autowired
	private RedisTemplate redisTemplate;

	/**
	 * 获取用户注册的应用程序信息
	 */
	@Override
	public String getAppsByUser(JSONObject rcv) {
		String user_id = rcv.getString(USERID);
		//判断用户是否存在
        UC_UserInfo user = getUser(user_id);
		if(user == null) {
			return JsonFormat.retFormat(4,"账号未注册");
		}
		user_id = user.getUser_id();
		List<Application> list = getAppInfoDao.getApps(user_id);
		if (list == null || list.size() == 0) {
			return JsonFormat.retFormat(1, "该用户未绑定任何应用");
		}
		return JsonFormat.retFormat(0, "查询成功", JSONArray.fromObject(list));
	}

	/**
	 * 获取用户注册的角色信息
	 */
	@Override
	public String getRolesByUser(JSONObject rcv) {
		String user_id = rcv.getString(USERID);
		//判断用户是否存在
        UC_UserInfo user = getUser(user_id);
		if(user == null) {
			return JsonFormat.retFormat(4,"账号未注册");
		}
		user_id = user.getUser_id();
		List<AppAndRole> list = getAppInfoDao.getRoles(user_id);
		if (list == null || list.size() == 0) {
			return JsonFormat.retFormat(1, "该用户未绑定角色");
		}
		return JsonFormat.retFormat(0, "查询成功", JSONArray.fromObject(list));
	}

	/**
	 * 获取某应用程序的所有角色信息
	 */
	@Override
	public String getRolesByApp(JSONObject rcv) {
		String app_code = rcv.getString(APPCODE);
		List<Role> list = getAppInfoDao.getRolesByApp(app_code);
		if (list == null || list.size() == 0) {
			return JsonFormat.retFormat(1, "该应用下未绑定角色");
		}
		return JsonFormat.retFormat(0, "查询成功", JSONArray.fromObject(list));
	}

	/**
	 * 获取用户在某应用程序的所有角色信息
	 */
	@Override
	public String getRolesByAppAndUser(JSONObject rcv) {
		String user_id = rcv.getString(USERID);
		String app_code = rcv.getString(APPCODE);
        //判断用户是否存在
        UC_UserInfo user = getUser(user_id);
        if(user == null) {
            return JsonFormat.retFormat(4,"账号未注册");
        }
        user_id = user.getUser_id();
		List<Role> list = getAppInfoDao.getRolesByAppAndUser(app_code, user_id);
		if (list == null || list.size() == 0) {
			return JsonFormat.retFormat(1, "用户未在该应用下绑定角色");
		}
		return JsonFormat.retFormat(0, "查询成功", JSONArray.fromObject(list));
	}

    @Override
    public boolean isAppExists(int app_code) {
        Application app = getAppByCode(app_code);
        return app != null;
    }

	@Override
	public Application getAppByCode(int appCode) {
		Application application = (Application) redisTemplate.opsForValue().get(APP_CACHE + appCode);
		if (application == null) {
			application = getAppInfoDao.getAppByCode(appCode);
			if (application != null) {
				redisTemplate.opsForValue().set(APP_CACHE + appCode, application);
				redisTemplate.expire(APP_CACHE + appCode, 1, TimeUnit.DAYS);
			}
		}
		return application;
	}

	@Override
	public List<Application> getAppsByProjCode(Integer projCode) {
		List<Application> applications = (List<Application>) redisTemplate.opsForValue().get(PROJ_CACHE + projCode);
		if (applications == null) {
			applications = getAppInfoDao.getAppsByProjCode(projCode);
			if (applications != null) {
				redisTemplate.opsForValue().set(PROJ_CACHE + projCode, applications);
				redisTemplate.expire(PROJ_CACHE + projCode, 1, TimeUnit.DAYS);
			}
		}
		return applications;
	}
}
