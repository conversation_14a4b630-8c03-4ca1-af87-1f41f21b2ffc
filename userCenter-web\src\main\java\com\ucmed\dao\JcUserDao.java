package com.ucmed.dao;

import com.ucmed.bean.UC_UserInfo;
import com.ucmed.dto.ClinicUserInfo;

import java.util.List;

/**
 * Created by QIUJIAHAO on 2016/7/22.
 */
public interface JcUserDao {

    /**
     * 通过uid查询用户
     *
     * @param uid
     * @return
     */
    UC_UserInfo getUserByUid(int uid);

    /**
     * 根据UserId查询用户
     *
     * @return
     */
    UC_UserInfo getUserByUserId(String userId);

    /**
     * 根据手机号查询用户
     *
     * @param phone
     * @return
     */
    UC_UserInfo getUserByPhone(String phone);

    /**
     * 更新用户
     *
     * @param user
     * @return 1:成功 0:失败
     */
    int updateUser(UC_UserInfo user);

    /**
     * 新增用户
     *
     * @param user
     * @return 1:成功 0:失败
     */
    int addUser(UC_UserInfo user);

    /**
     * 根据UserId和projcetId查询用户
     *
     * @return
     */
    UC_UserInfo getUserByUserIdAndProjectId(String userId, int projectId);

    /**
     * 根据phone和projcetId查询用户
     *
     * @return
     */
    UC_UserInfo getUserByPhoneAndProjectId(String phone, int projectId);

    /**
     * 查询用户（手机号或用户名）
     *
     * @param userIdOrPhone 手机号或用户名
     * @return UC_UserInfo
     */
    UC_UserInfo getUser(String userIdOrPhone);

    /**
     * 查询用户列表
     *
     * @param appCode  应用ID
     * @param pageNo   页码
     * @param pageSize 页号
     * @param name     姓名
     * @param phone    手机号
     * @param idCard   身份证号码
     * @return ClinicUser数组
     */
    List<ClinicUserInfo> listUser(Integer appCode, Integer pageNo, Integer pageSize, String name, String phone, String idCard);

    /**
     * 查询符合条件的用户数
     *
     * @param appCode 应用ID
     * @param name    姓名
     * @param phone   手机号
     * @param idCard  身份证号码
     * @return 用户数int
     */
    int getTotalUser(Integer appCode, String name, String phone, String idCard);

    /**
     * 查询用户列表
     *
     * @param appCode    应用ID
     * @param pageNo     页码
     * @param pageSize   页号
     * @param conditions 查询条件
     * @return ClinicUser数组
     */
    List<ClinicUserInfo> listUser(Integer appCode, Integer pageNo, Integer pageSize, String conditions);

    /**
     * 查询符合条件的用户数
     *
     * @param appCode    应用ID
     * @param conditions 查询条件
     * @return 用户数int
     */
    int getTotalUser(Integer appCode, String conditions);
}
