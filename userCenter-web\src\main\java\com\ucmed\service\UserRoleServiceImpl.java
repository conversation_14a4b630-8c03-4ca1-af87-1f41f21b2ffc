package com.ucmed.service;

import com.ucmed.bean.SecurityUserRole;
import com.ucmed.mapper.SecurityUserRoleMapper;
import com.ucmed.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/11/17 13:44
 */
@Service
public class UserRoleServiceImpl implements UserRoleService {
    @Autowired
    private SecurityUserRoleMapper userRoleMapper;

    @Override
    public boolean isUserInRole(String userId, int roleId) {
        List<SecurityUserRole> userRole = userRoleMapper.selectByUserIdAndRoleId(userId, roleId);
        return userRole.size() != 0;
    }

    @Override
    public void saveUserRole(String userId, int roleId) {
        SecurityUserRole userRole = new SecurityUserRole();
        userRole.setUserId(userId);
        userRole.setRoleId(roleId);
        userRole.setOperDate(TimeUtil.getCurrentTime());
        userRole.setValid("1");
        userRole.setOperUser(userId);
        userRoleMapper.insert(userRole);
    }
}
