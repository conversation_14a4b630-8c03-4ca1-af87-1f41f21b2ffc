package com.ucmed.swagger;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.EnableWebMvc;
import springfox.documentation.builders.ApiInfoBuilder;
import springfox.documentation.builders.PathSelectors;
import springfox.documentation.builders.RequestHandlerSelectors;
import springfox.documentation.builders.ResponseMessageBuilder;
import springfox.documentation.schema.ModelRef;
import springfox.documentation.service.ApiInfo;
import springfox.documentation.service.Contact;
import springfox.documentation.service.ResponseMessage;
import springfox.documentation.spi.DocumentationType;
import springfox.documentation.spring.web.plugins.Docket;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.util.ArrayList;
import java.util.List;

@EnableWebMvc
@EnableSwagger2
@Configuration
@ComponentScan(basePackages = {"com.ucmed.controller"})
public class SwaggerConfig {
    @Value("${host}")
    String hostname;
    String sc = "https://yhzx.zwjk.com/userCenterUCMED";
    String test = "https://testyhzx.zwjk.com/userCenterUCMED"; 

    @Bean
    public Docket createRestApi() {
        if (sc.equals(hostname)) {
            return new Docket(DocumentationType.SWAGGER_2)
                    .apiInfo(wailai())
                    .select()
                    .paths(PathSelectors.none())
                    .build();
        } else if (test.equals(hostname)) {
            return new Docket(DocumentationType.SWAGGER_2)
                    .host("test.yhzx.ucmed.cn")
                    .apiInfo(apiInfo())
                    .select()
                    .apis(RequestHandlerSelectors.basePackage("com.ucmed.controller"))
                    .paths(PathSelectors.any())
                    .build();
        } else {
            return new Docket(DocumentationType.SWAGGER_2)
                    .apiInfo(apiInfo())
                    .select()
                    .apis(RequestHandlerSelectors.basePackage("com.ucmed.controller"))
                    .paths(PathSelectors.any())
                    .build();
        }
    }

    private ApiInfo apiInfo() {
        List<ResponseMessage> responseMessageList = new ArrayList<>();
        responseMessageList.add(new ResponseMessageBuilder().code(0).message("找不到资源").responseModel(new ModelRef("Success")).build());
        return new ApiInfoBuilder()
                .title("接口列表 v1.0")
                .description("接口信息")
                .termsOfServiceUrl("")
                .contact(new Contact("用户中心接口信息", "wwww.baidu.com", "<EMAIL>"))
                .version("1.1.0（版本号不会改的，不会的！）")
                .build();
    }

    private ApiInfo wailai() {
        List<ResponseMessage> responseMessageList = new ArrayList<>();
        responseMessageList.add(new ResponseMessageBuilder().code(0).message("找不到资源").responseModel(new ModelRef("Success")).build());
        return new ApiInfoBuilder()
                .title("接口列表 v1.0")
                .description("接口信息")
                .termsOfServiceUrl("")
                .contact(new Contact("666", "666", "666"))
                .version("1.1.0")
                .build();
    }
}