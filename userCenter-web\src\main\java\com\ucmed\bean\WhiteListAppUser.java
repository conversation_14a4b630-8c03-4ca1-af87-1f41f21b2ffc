package com.ucmed.bean;

import java.io.Serializable;

/**
 * Author: 黄一辛 HUANGYIXIN
 * CreateTime: 2018/5/17 16:53
 * Contract: <EMAIL>
 * Description:
 **/
public class WhiteListAppUser implements Serializable {

    private static final long serialVersionUID = -7974608119250633252L;

    private Integer id;

    private Integer appCode;

    private String user;

    private Integer status;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getAppCode() {
        return appCode;
    }

    public void setAppCode(Integer appCode) {
        this.appCode = appCode;
    }

    public String getUser() {
        return user;
    }

    public void setUser(String user) {
        this.user = user;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }
}
