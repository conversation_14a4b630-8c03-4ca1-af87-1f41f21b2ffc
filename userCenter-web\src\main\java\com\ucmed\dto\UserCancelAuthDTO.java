package com.ucmed.dto;


/**
 * <AUTHOR>
 * @version 1.0
 * @date 2019/12/30 16:36
 */
public class UserCancelAuthDTO {
    private Integer userProjectId;
    private Integer userRoleId;
    private Integer userAppId;
    private Integer projCode;
    private Integer appProjCode;
    private Integer appCode;
    private String projUserId;
    private String roleUserId;
    private String appUserId;


    public Integer getUserProjectId() {
        return userProjectId;
    }

    public void setUserProjectId(Integer userProjectId) {
        this.userProjectId = userProjectId;
    }

    public Integer getUserRoleId() {
        return userRoleId;
    }

    public void setUserRoleId(Integer userRoleId) {
        this.userRoleId = userRoleId;
    }

    public Integer getUserAppId() {
        return userAppId;
    }

    public void setUserAppId(Integer userAppId) {
        this.userAppId = userAppId;
    }

    public Integer getProjCode() {
        return projCode;
    }

    public void setProjCode(Integer projCode) {
        this.projCode = projCode;
    }

    public Integer getAppProjCode() {
        return appProjCode;
    }

    public void setAppProjCode(Integer appProjCode) {
        this.appProjCode = appProjCode;
    }

    public Integer getAppCode() {
        return appCode;
    }

    public void setAppCode(Integer appCode) {
        this.appCode = appCode;
    }

    public String getProjUserId() {
        return projUserId;
    }

    public void setProjUserId(String projUserId) {
        this.projUserId = projUserId;
    }

    public String getRoleUserId() {
        return roleUserId;
    }

    public void setRoleUserId(String roleUserId) {
        this.roleUserId = roleUserId;
    }

    public String getAppUserId() {
        return appUserId;
    }

    public void setAppUserId(String appUserId) {
        this.appUserId = appUserId;
    }
}
