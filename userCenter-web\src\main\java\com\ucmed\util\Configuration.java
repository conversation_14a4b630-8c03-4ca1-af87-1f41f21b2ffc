package com.ucmed.util;

import java.io.InputStreamReader;
import java.util.Properties;

public class Configuration {
    private static Configuration config;
    private static String special_role;

    static {
        config = new Configuration();
    }

    private Configuration() {
        init();
    }

    public static Configuration getInstance() {
        return config;
    }

    private void init() {
        Properties prop = new Properties();
        InputStreamReader in;
        try {
            in = new InputStreamReader(Configuration.class.getResourceAsStream("/configure.properties"), "UTF-8");
            prop.load(in);
            special_role = prop.getProperty("special_role");
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    public static Configuration getConfig() {
        return config;
    }

    public static void setConfig(Configuration config) {
        Configuration.config = config;
    }

    public static String getSpecial_role() {
        return special_role;
    }

    public static void setSpecial_role(String special_role) {
        Configuration.special_role = special_role;
    }

}
