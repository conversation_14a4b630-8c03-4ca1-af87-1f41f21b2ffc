package com.ucmed.service;

import com.ucmed.bean.JcUserThirdParty;
import com.ucmed.bean.JcUserThirdPartyExample;
import com.ucmed.common.service.CommonService;
import com.ucmed.dto.UserBindInfoDTO;
import com.ucmed.mapper.JcUserThirdPartyMapper;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/11/14 15:19
 */
@Service
public class JcUserThirdPartyServiceImpl implements JcUserThirdPartyService {

    private static Logger log = Logger.getLogger(JcUserThirdPartyServiceImpl.class.getName());
    @Autowired
    private JcUserThirdPartyMapper jcUserThirdPartyMapper;
    @Autowired
    CommonService commonService;

    @Override
    public void save(JcUserThirdParty jcUserThirdParty) {
        jcUserThirdParty.setCreateTime(new Date());
        jcUserThirdParty.setUpdateTime(new Date());
        jcUserThirdPartyMapper.insertSelective(jcUserThirdParty);
        log.info("用户：" + jcUserThirdParty.getUserId() + " 绑定第三方OPENID：" + jcUserThirdParty.getOpenId());
    }

    @Override
    public JcUserThirdParty getUserByOpenId(String openId) {
        JcUserThirdPartyExample example = new JcUserThirdPartyExample();
        JcUserThirdPartyExample.Criteria criteria = example.createCriteria();
        criteria.andOpenIdEqualTo(openId);
        criteria.andDeletionEqualTo("0");
        List<JcUserThirdParty> users = jcUserThirdPartyMapper.selectByExample(example);
        if (users.size() == 0) {
            return null;
        }
        return users.get(0);
    }

    @Override
    public boolean isThridPartyUserExists(String openId) {
        JcUserThirdPartyExample example = new JcUserThirdPartyExample();
        JcUserThirdPartyExample.Criteria criteria = example.createCriteria();
        criteria.andOpenIdEqualTo(openId);
        criteria.andDeletionEqualTo("0");
        List<JcUserThirdParty> users = jcUserThirdPartyMapper.selectByExample(example);
        return users.size() > 0;
    }

    @Override
    public void updatePhone(JcUserThirdParty jcUserThirdParty) {
        jcUserThirdParty.setCreateTime(new Date());
        jcUserThirdParty.setUpdateTime(new Date());
        jcUserThirdPartyMapper.updatePhone(jcUserThirdParty);
        log.info("用户：" + jcUserThirdParty.getUserId() + " 绑定第三方OPENID：" + jcUserThirdParty.getOpenId());
    }

    @Override
    public void unBind(JcUserThirdParty jcUserThirdParty) {
        jcUserThirdPartyMapper.unbind(jcUserThirdParty);
        log.info("用户:" + jcUserThirdParty.getUserId() + " 解绑第三方OPENID：" + jcUserThirdParty.getOpenId());
    }

    @Override
    public void unbindUserProject(JcUserThirdParty jcUserThirdParty) {
        jcUserThirdPartyMapper.unbindUserProject(jcUserThirdParty);
        log.info("用户:" + jcUserThirdParty.getUserId() + " 解绑第三方OPENID：userId：" + jcUserThirdParty.getUserId()
                                    + ", appCode：" + jcUserThirdParty.getCreateBy());
    }

    @Override
    public List<UserBindInfoDTO> getBindInfo(String userIdOrPhone, List<String> appCode) {
        List<UserBindInfoDTO> bindInfoByPhone = jcUserThirdPartyMapper.getBindInfo(userIdOrPhone, commonService.ThreeDESEncrypt(userIdOrPhone), appCode);
        if (bindInfoByPhone.size() == 0) {
            return null;
        }
        return bindInfoByPhone;
    }
}
