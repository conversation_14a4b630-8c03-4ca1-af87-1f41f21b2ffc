package com.ucmed.service;

import com.ucmed.bean.JcUserThirdParty;
import com.ucmed.dto.UserBindInfoDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/11/14 15:17
 */
public interface JcUserThirdPartyService {

    void save(JcUserThirdParty jcUserThirdParty);

    JcUserThirdParty getUserByOpenId(String openId);

    boolean isThridPartyUserExists(String openId);

    void updatePhone(JcUserThirdParty jcUserThirdParty);

    void unBind(JcUserThirdParty jcUserThirdParty);

    void unbindUserProject(JcUserThirdParty jcUserThirdParty);

    List<UserBindInfoDTO> getBindInfo(String userIdOrPhone, List<String> appCode);
}
