package com.ucmed.service;

import com.ucmed.bean.DoctorInfo;
import com.ucmed.bean.Hospital;
import com.ucmed.bean.UserInfo;
import com.ucmed.dao.MRDaoImpl;
import com.ucmed.util.GenerateRandomString;
import com.ucmed.util.TimeUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import java.util.List;
import java.util.Set;
import org.apache.log4j.Logger;
@Service
public class UserCenterMRServiceImpl implements UserCenterMRService{

    Logger logger = Logger.getLogger(UserCenterMRServiceImpl.class);

    @Autowired
	private MRDaoImpl mrDaoImpl;

    /**
     * 返回信息格式化
     *
     * @param retCode 返回码
     * @param retInfo 返回码说明信息
     * @param retData 返回信息
     * @return 格式化后的字符串
     */
    private static String JsonFormat(String retCode, String retInfo, String retData){
        return "{\"ret_code\":" + retCode + ",\"ret_info\":\"" + retInfo + "\",\"ret_data\":" + retData + "}";
    }

    /**
     * 返回信息格式化
     *
     * @param returnCode 返回码
     * @param returnData 返回信息
     * @return 格式化后的字符串
     */
    private static String JsonFormat(String returnCode, String returnData){
        return "{\"return_code\":" + returnCode + ",\"return_data\":" + returnData + "}";
    }


	/**
	 * 登陆
	 *
	 * @param rcv    登录名
	 */
	@Override
	public String login(JSONObject rcv) {
        if(!rcv.containsKey("user") || !rcv.containsKey("password")) {
            return JsonFormat("0", JsonFormat("103", "参数有误，请检查参数", null));
        }
		return JsonFormat("0",JsonFormat("0","返回成功",
                mrDaoImpl.login(rcv.getString("user").toString(), rcv.get("password").toString())));
	}

	/**
	 * 查询普通用户基本信息
	 *
	 * @param rcv 卓健ID
	 * @return 普通用户基本信息
	 */
	@Override
	public String getUserInfo(String rcv) {
	    String ucmedId = JSONObject.fromObject(rcv).get("ucmed_user_id").toString();
	    String strJson;
		UserInfo userInfo = mrDaoImpl.getUserInfo(ucmedId);
        if(userInfo != null){
            JSONObject json = JSONObject.fromObject(userInfo);
            json.put("is_delete","null");
            strJson = json.toString();
            strJson = strJson.replaceAll("ucmed_id", "ucmed_user_id");
            strJson = strJson.replaceAll("the_name", "name");
            strJson = strJson.replaceAll("mobile", "phone");
            strJson = strJson.replaceAll("e_mail", "email");
            return JsonFormat("0",JsonFormat("0","返回成功",strJson));
        }else{
            return JsonFormat("0",JsonFormat("4","找不到该id对应的用户信息",null));
        }
	}

	/**
	 * 更新普通用户基本信息
	 *
	 * @param rcv 新的用户基本信息
	 */
	@Override
    @SuppressWarnings("unchecked")
	public String updateUserInfo(String rcv) {
        JSONObject rcvJson = JSONObject.fromObject(rcv);
        String ucmedId = rcvJson.get("ucmed_user_id").toString();

        UserInfo userInfo = mrDaoImpl.getUserInfo(ucmedId);
        if(userInfo != null){
            String tmp;
            if((tmp = rcvJson.get("card_type").toString()) != null)
                userInfo.setCard_type(tmp);
            if((tmp = rcvJson.get("card_id").toString()) != null)
                userInfo.setCard_id(tmp);
            if((tmp = rcvJson.get("name").toString()) != null)
                userInfo.setThe_name(tmp);
            if((tmp = rcvJson.get("sex").toString()) != null)
                userInfo.setSex(tmp);
            if((tmp = rcvJson.get("birthday").toString()) != null)
                userInfo.setBirthday(tmp);
            if((tmp = rcvJson.get("phone").toString()) != null)
                userInfo.setMobile(tmp);
            if((tmp = rcvJson.get("email").toString()) != null)
                userInfo.setE_mail(tmp);
            if((tmp = rcvJson.get("address").toString()) != null)
                userInfo.setAddress(tmp);
            if((tmp = rcvJson.get("source_ids").toString()) != null)
                userInfo.setSource_ids(tmp);
            userInfo.setUpdate_time(TimeUtil.getCurrentTime());
            mrDaoImpl.updateUserInfo(userInfo);
            String result = "{\"State\":1,\"Message\":\"更新成功！\",\"Value\":\"\"}";
            return JsonFormat("0",JsonFormat("0","返回成功",result));
        }else{
            return JsonFormat("0",JsonFormat("4","找不到该id对应的用户基本信息",null));
        }

	}

    /**
     * 验证普通用户基本信息
     */
    @Override
    public String verifyUserInfo(String rcv) {
        JSONObject rcvJson = JSONObject.fromObject(rcv);
        String cardId = rcvJson.get("card_id").toString();
        String name = rcvJson.get("name").toString();
        String phone = rcvJson.get("phone").toString();
        UserInfo userInfo = mrDaoImpl.queryUserInfo(cardId, name, phone);
        if(userInfo == null){
            String ucmedId = GenerateRandomString.generateRandomString("UC", 3);
            rcv = rcv.replaceAll("ucmed_user_id", "ucmed_id");
            rcv = rcv.replaceAll("name", "the_name");
            rcv = rcv.replaceAll("phone", "mobile");
            rcv = rcv.replaceAll("email", "e_mail");
            System.out.println(rcv);
            rcvJson = JSONObject.fromObject(rcv);
            userInfo = (UserInfo) JSONObject.toBean(rcvJson, UserInfo.class);
            userInfo.setUcmed_id(ucmedId);
            userInfo.setUser_id(ucmedId);
            userInfo.setCreate_time(TimeUtil.getCurrentTime());
            userInfo.setUpdate_time(TimeUtil.getCurrentTime());
            if(mrDaoImpl.addUserInfo(userInfo) == 1)
                return JsonFormat("0", JsonFormat("0", "返回成功"), JSONObject.fromObject(userInfo).toString());
            else
                return null;
        }else{
            /*返回查询到的结果*/
            JSONObject json = JSONObject.fromObject(userInfo);
            json.put("is_delete","null");
            String strJson = json.toString();
            strJson = strJson.replaceAll("ucmed_id", "ucmed_user_id");
            strJson = strJson.replaceAll("the_name", "name");
            strJson = strJson.replaceAll("mobile", "phone");
            strJson = strJson.replaceAll("e_mail", "email");
            return JsonFormat("0", JsonFormat("0", "返回成功",strJson));
        }
    }

    private String compoundJson(JSONObject jsonObject, List<DoctorInfo> lDoctorInfo){
        jsonObject.remove("id");
        JSONArray jsonArray = new JSONArray();
        for(DoctorInfo doctorInfo : lDoctorInfo){
            JSONObject tmp = JSONObject.fromObject(doctorInfo);
            JSONObject tmpAll = new JSONObject();
            tmpAll.putAll(jsonObject);
            tmpAll.putAll(tmp);
            jsonArray.add(tmpAll);
            tmpAll.clear();
        }
        return jsonArray.toString();
    }
    /**
	 * 查询医生基本信息
	 *
	 * @param rcv 卓健ID
	 * @return 医生基本信息
	 */
	@Override
	public String getDoctorInfo(String rcv) {
	    String ucmedId = JSONObject.fromObject(rcv).get("ucmed_doctor_id").toString();
        String strJson;
        UserInfo userInfo = mrDaoImpl.getUserInfo(ucmedId);
        if(userInfo != null){
            JSONObject jsonUserInfo = JSONObject.fromObject(userInfo);
            jsonUserInfo.put("is_delete","null");
            List<DoctorInfo> lDoctorInfo = mrDaoImpl.queryDoctorInfo(ucmedId);
            if(lDoctorInfo != null){
                strJson = compoundJson(jsonUserInfo, lDoctorInfo);
            }else{
                strJson = jsonUserInfo.toString();

            }
            strJson = strJson.replaceAll("ucmed_id", "ucmed_doctor_id");
            strJson = strJson.replaceAll("the_name", "name");
            strJson = strJson.replaceAll("mobile", "phone");
            strJson = strJson.replaceAll("e_mail", "email");
            return JsonFormat("0",JsonFormat("0","返回成功",strJson));
        }else{
            return JsonFormat("0",JsonFormat("4","找不到该id对应的用户信息",null));
        }
	}

	/**
	 * 更新医生基本信息
	 *
	 * @param rcv 新的医生基本信息
	 * @return 执行结果
	 */
	@Override
    @SuppressWarnings("unchecked")
	public String updateDoctorInfo(String rcv) {
        JSONObject rcvJson = JSONObject.fromObject(rcv);
        String ucmedId = rcvJson.get("ucmed_user_id").toString();

        UserInfo userInfo = mrDaoImpl.getUserInfo(ucmedId);
        if(userInfo != null){
            String tmp;
            if((tmp = rcvJson.get("card_type").toString()) != null)
                userInfo.setCard_type(tmp);
            if((tmp = rcvJson.get("card_id").toString()) != null)
                userInfo.setCard_id(tmp);
            if((tmp = rcvJson.get("name").toString()) != null)
                userInfo.setThe_name(tmp);
            if((tmp = rcvJson.get("sex").toString()) != null)
                userInfo.setSex(tmp);
            if((tmp = rcvJson.get("birthday").toString()) != null)
                userInfo.setBirthday(tmp);
            if((tmp = rcvJson.get("phone").toString()) != null)
                userInfo.setMobile(tmp);
            if((tmp = rcvJson.get("email").toString()) != null)
                userInfo.setE_mail(tmp);
            if((tmp = rcvJson.get("address").toString()) != null)
                userInfo.setAddress(tmp);
            if((tmp = rcvJson.get("source_ids").toString()) != null)
                userInfo.setSource_ids(tmp);
            userInfo.setUpdate_time(TimeUtil.getCurrentTime());
            mrDaoImpl.updateUserInfo(userInfo);

            if((tmp = rcvJson.get("ucmed_unit_doctor_id").toString()) != null){
                DoctorInfo doctorInfo = mrDaoImpl.queryDoctorInfo(ucmedId, tmp);
                if(doctorInfo != null){
                    if((tmp = rcvJson.get("ucmed_hospital_id").toString()) != null)
                        doctorInfo.setUcmed_hospital_id(tmp);
                    if((tmp = rcvJson.get("hospital_org_code").toString()) != null)
                        doctorInfo.setHospital_org_code(tmp);
                    if((tmp = rcvJson.get("section_name").toString()) != null)
                        doctorInfo.setSection_name(tmp);
                    if((tmp = rcvJson.get("common_section_no").toString()) != null)
                        doctorInfo.setCommon_section_no(tmp);
                    if((tmp = rcvJson.get("work_no").toString()) != null)
                        doctorInfo.setWork_no(tmp);
                    doctorInfo.setUpdate_time(TimeUtil.getCurrentTime());
                    mrDaoImpl.updateDoctorInfo(doctorInfo);
                }
            }
            String result = "{\"State\":1,\"Message\":\"更新成功！\",\"Value\":\"\"}";
            return JsonFormat("0",JsonFormat("0","返回成功",result));
        }else{
            return JsonFormat("0",JsonFormat("4","找不到该id对应的用户基本信息",null));
        }
	}

    /**
     * 验证医生基本信息
     */
    @Override
    public String verifyDoctorInfo(String rcv) {
        JSONObject rcvJson = JSONObject.fromObject(rcv);
        UserInfo userInfo = new UserInfo();
        DoctorInfo doctorInfo = new DoctorInfo();
        userInfo.setCard_type(rcvJson.get("card_type").toString());
        userInfo.setCard_id(rcvJson.get("card_id").toString());
        userInfo.setThe_name(rcvJson.get("name").toString());
        userInfo.setSex(rcvJson.get("sex").toString());
        userInfo.setBirthday(rcvJson.get("birthday").toString());
        userInfo.setMobile(rcvJson.get("phone").toString());
        userInfo.setE_mail(rcvJson.get("email").toString());
        userInfo.setAddress(rcvJson.get("address").toString());
        userInfo.setSource_ids(rcvJson.get("source_ids").toString());
        userInfo.setCreate_time(rcvJson.get("create_time").toString());
        userInfo.setUpdate_time(rcvJson.get("update_time").toString());
        doctorInfo.setUcmed_hospital_id(rcvJson.get("ucmed_hospital_id").toString());
        doctorInfo.setHospital_org_code(rcvJson.get("hospital_org_code").toString());
        doctorInfo.setSection_name(rcvJson.get("section_name").toString());
        doctorInfo.setCommon_section_no(rcvJson.get("common_section_no").toString());
        doctorInfo.setWork_no(rcvJson.get("work_no").toString());
        doctorInfo.setCreate_time(rcvJson.get("create_time").toString());
        doctorInfo.setUpdate_time(rcvJson.get("update_time").toString());

        String strJson;
        /*首先验证用户是否存在，若存在获取ucmed_id,若不存在需要写入jc_user_info*/
        UserInfo queryUserInfo = mrDaoImpl.queryUserInfo(userInfo.getCard_id(),
                userInfo.getThe_name(), userInfo.getMobile());
        if(queryUserInfo  != null){
            JSONObject resultUserInfo = JSONObject.fromObject(queryUserInfo);
            resultUserInfo.put("is_delete","null");
            String ucmedId = queryUserInfo.getUcmed_id();
            System.out.println("ucmedId:" + ucmedId);
            List<DoctorInfo> lDoctorInfo = mrDaoImpl.queryDoctorInfo(ucmedId);
            if(lDoctorInfo != null && lDoctorInfo.size() != 0){
                /*jc_doctor中有信息这返回信息*/
                System.out.println(lDoctorInfo.size());
                strJson = compoundJson(resultUserInfo, lDoctorInfo);
            }else{
                /*jc_doctor中无信息则写入信息*/
                doctorInfo.setUcmed_unit_doctor_id(GenerateRandomString.generateRandomString("UH",3));
                doctorInfo.setUser_id(ucmedId);
                doctorInfo.setUcmed_id(ucmedId);
                doctorInfo.setCreate_time(TimeUtil.getCurrentTime());
                doctorInfo.setUpdate_time(TimeUtil.getCurrentTime());
                mrDaoImpl.addDoctorInfo(doctorInfo);

                JSONArray jsonArray = new JSONArray();
                JSONObject tmpDoctorInfo = JSONObject.fromObject(doctorInfo);
                JSONObject tmp = new JSONObject();
                tmp.putAll(resultUserInfo);
                tmp.putAll(tmpDoctorInfo);
                jsonArray.add(tmp);
                strJson = jsonArray.toString();
            }
        }else{
            /*不存在即需要写入jc_user_info和jc_doctor_info*/
            String ucmedId = GenerateRandomString.generateRandomString("UD", 3);
            userInfo.setUser_id(ucmedId);
            userInfo.setUcmed_id(ucmedId);
            userInfo.setCreate_time(TimeUtil.getCurrentTime());
            userInfo.setUpdate_time(TimeUtil.getCurrentTime());
            doctorInfo.setUcmed_unit_doctor_id(GenerateRandomString.generateRandomString("UH", 3));
            doctorInfo.setUcmed_id(ucmedId);
            doctorInfo.setUser_id(ucmedId);
            doctorInfo.setCreate_time(TimeUtil.getCurrentTime());
            doctorInfo.setUpdate_time(TimeUtil.getCurrentTime());
            mrDaoImpl.addUserInfo(userInfo);
            mrDaoImpl.addDoctorInfo(doctorInfo);
            JSONObject tmp = new JSONObject();
            tmp.putAll(JSONObject.fromObject(userInfo));
            tmp.putAll(JSONObject.fromObject(doctorInfo));
            JSONArray tmpJsonArray = new JSONArray();
            tmpJsonArray.add(tmp);
            strJson = tmpJsonArray.toString();
        }
        strJson = strJson.replaceAll("ucmed_id", "ucmed_doctor_id");
        strJson = strJson.replaceAll("the_name", "name");
        strJson = strJson.replaceAll("mobile", "phone");
        strJson = strJson.replaceAll("e_mail", "email");
        return JsonFormat("0",JsonFormat("0","返回成功",strJson));
    }

    /**
	 * 添加医院信息
	 *
	 * @param rcv 医院信息
	 * @return 执行结果
	 */
	@Override
	public String addHospitalInfo(String rcv) {
		JSONObject rcvJson = JSONObject.fromObject(rcv);
        JSONObject tmpRcvJson = new JSONObject();
        Set set = rcvJson.keySet();
        for(Object s : set){
            int len = s.toString().length();
            String tmpKey = s.toString().substring(0,1).toLowerCase() + s.toString().substring(1, len);
            tmpRcvJson.put(tmpKey, rcvJson.get(s.toString()).toString());
        }
        Hospital hospital = (Hospital) JSONObject.toBean(tmpRcvJson, Hospital.class);
        if(mrDaoImpl.addHospital(hospital) == 1)
            return JsonFormat("0",JsonFormat("0","返回成功",null));
        else
            return null;
	}

	/**
	 * 更新医院信息
	 *
	 * @param rcv 新的医院信息
	 * @return 执行结果
	 */
	@Override
	public String updateHospitalInfo(String rcv) {
        JSONObject rcvJson = JSONObject.fromObject(rcv);
        JSONObject tmpRcvJson = new JSONObject();
        Set set = rcvJson.keySet();
        for(Object s : set){
            int len = s.toString().length();
            String tmpKey = s.toString().substring(0,1).toLowerCase() + s.toString().substring(1, len);
            tmpRcvJson.put(tmpKey, rcvJson.get(s.toString()).toString());
        }
        Hospital hospital = (Hospital) JSONObject.toBean(tmpRcvJson, Hospital.class);
        if(mrDaoImpl.updateHospital(hospital) == 1)
            return JsonFormat("0", JsonFormat("0","返回成功",null));
        else
            return null;
	}

	/**
	 * 查询医院信息
	 *
	 * @param rcv 查询条件
	 * @return 医院信息
	 */
	@Override
	public String getHospitalInfo(String rcv) {
	    String orgCode = "(orgcode = 'ORGCODE' or 'ORGCODE' = '')";
        String keyWord = "(concat(id, allhosname, shorthosname, hospinyin, orgcode, hossite, hosttype, hoslevel," +
                " orgtype, healthorgaddressid, province, city, area, address, addresscode, postcode," +
                " detailcontent, remark, createid, createtime, updateid, updatetime) like '%KEYWORD%')";
        String provice = "(province = 'PROVINCE' or 'PROVINCE' = '')";
        String city = "(city = 'CITY' or 'CITY' = '')";
        String area = "(area = 'AREA' or 'AREA' = '')";

	    JSONObject rcvJson = JSONObject.fromObject(rcv);
        orgCode = orgCode.replaceAll("ORGCODE", rcvJson.get("OrgCode").toString());
        keyWord = keyWord.replaceAll("KEYWORD", rcvJson.get("KeyWord").toString());
        provice = provice.replaceAll("PROVINCE", rcvJson.get("Province").toString());
        city = city.replaceAll("CITY", rcvJson.get("City").toString());
        area = area.replaceAll("AREA", rcvJson.get("Area").toString());
        String condition = orgCode + " and " + keyWord + " and " + provice + " and " + city + " and " + area;
        List<Hospital> lHospital = mrDaoImpl.queryHospital(condition);
        if(lHospital != null) {
            JSONObject resultJson = new JSONObject();
            JSONArray jsonArray = new JSONArray();
            for(Hospital hospital : lHospital){
                JSONObject tmpJson = new JSONObject();
                tmpJson.put("ShortHosName", hospital.getShortHosName());
                tmpJson.put("OrgCode", hospital.getOrgCode());
                jsonArray.add(tmpJson);
            }
            resultJson.put("total_count", lHospital.size());
            resultJson.put("list", jsonArray);
            return JsonFormat("0",JsonFormat("0", "返回成功", resultJson.toString()));
        }else{
            return null;
        }
	}
}
