package com.ucmed.common.constant;

/**
 * 接口名称
 * Created by QIUJIAHAO on 2016/9/27.
 */
public class InterfaceName {
    // 注册
    public static final String REGISTRATION = "registration";
    // 登录
    public static final String LOGIN = "login";
    // 修改密码
    public static final String CHANGEPASSWORD = "changePwd";
    // 重置密码
    public static final String REINPUTPASSWORD = "reInputPassword";
    // 退出登录
    public static final String LOGOUT = "logout";
    // 发送短信
    public static final String SENDMSGCODE = "sendMsgCode";
    // 绑定微信
    public static final String WECHATBINDING = "bindWechat";
    // 微信登录
    public static final String WECHATLOGIN = "loginByWechat";
    // 微信解绑
    public static final String WECHATUNBINDING = "unbindWechat";
    // 查询用户信息
    public static final String GETUSERINFO = "getUserInfo";
    // 修改用户信息
    public static final String UPDATEUSERINFO = "updateUserInfo";
    // 验证token有效性
    public static final String ISTOKENVALID = "isTokenValid";
    // 用户是否注册某角色
    public static final String ISUSERINROLE = "isUserInRole";
    // 查询功能权限
    public static final String GETAUTHORITY = "getAuthority";
    // 查询数据权限
    public static final String GETDATAVALUE = "getDataValue";
    // 修改手机号
    public static final String CHANGEPHONE = "changePhone";
    // 获取用户注册的应用程序信息
    public static final String GETAPPSBYUSER = "getAppsByUser";
    // 获取用户注册的角色信息
    public static final String GETROLESBYUSER = "getRolesByUser";
    // 获取某应用程序的所有角色信息
    public static final String GETROLESBYAPP = "getRolesByApp";
    // 获取用户在某应用程序的所有角色信息
    public static final String GETROLESBYAPPANDUSER = "getRolesByAppAndUser";

    // 添加就诊人
    public static final String ADDPATIENT = "addPatient";
    // 修改就诊人
    public static final String UPDATEPATIENT = "updatePatient";
    // 查询就诊人
    public static final String GETPATIENTINFO = "getPatientInfo";
    // 根据应用和角色查用户
    public static final String FINDUSERBYAPPANDROLE = "findUserByAppAndRole";

    // 添加功能权限
    public static final String ADDAUTHORITY = "addAuthority";
    // 删除功能权限
    public static final String DELETEAUTHORITY = "deleteAuthority";
    // 更新功能权限
    public static final String UPDATEAUTHORITY = "updateAuthority";
    // 查询功能权限
    public static final String QUERYAUTHORITY = "queryAuthority";
    // 绑定功能权限
    public static final String ADDAUTHORITYTOROLE = "addAuthorityToRole";
    // 解绑功能权限
    public static final String REMOVEAUTHORITYFROMROLE = "removeAuthorityFromRole";

    // 添加数据权限
    public static final String ADDDATAVALUE = "addDataValue";
    // 删除数据权限
    public static final String DELETEDATAVALUE = "deleteDataValue";
    // 更新数据权限
    public static final String UPDATEDATAVALUE = "updateDataValue";
    // 查询数据权限
    public static final String QUERYDATAVALUE = "queryDataValue";
    // 绑定数据权限
    public static final String ADDDATAVALUETOROLE = "addDataValueToRole";
    // 解绑数据权限
    public static final String REMOVEDATAVALUEFROMROLE = "removeDataValueFromRole";

    // 添加角色
    public static final String ADDROLE = "addRole";
    // 更新角色
    public static final String UPDATEROLE = "updateRole";
    // 删除角色
    public static final String DELETEROLE = "deleteRole";
    // 查询角色
    public static final String QUERYROLE = "queryRole";

    // 生成短信验证码
    public static final String GENERATEMESSAGECODE = "generateMessageCode";
    // 验证短信验证码
    public static final String VERIFYMESSAGECODE = "verifyMessageCode";
    // 免密码登录
    public static final String NOPASSWORDLOGIN = "noPasswordLogin";

    // 授权登录
    public static final String SETPERMISSION = "setPermission";
}
