package com.ucmed.service;

import net.sf.json.JSONObject;

/**
 * Web Service 服务类
 * Created by QIUJIAHAO on 2016/9/18.
 */
public interface UserWebService {

    /**
     * 注册
     */
    String registration(JSONObject rcv);

    /**
     * 登录
     */
    String login(JSONObject rcv);

    /**
     * 修改密码
     */
    String changePwd(JSONObject rcv);

    /**
     * 重置密码
     */
    String reInputPassword(JSONObject rcv);

    /**
     * token有效性
     */
    String isTokenValid(JSONObject rcv);

    /**
     * 退出登录
     */
    String logout(JSONObject rcv);

    /**
     * 修改手机号
     */
    String changePhone(JSONObject rcv);

    /**
     * 用户是否注册某角色
     */
    String isUserInRole(JSONObject rcv);

    /**
     * 根据应用和角色查用户
     */
    String findUserByAppAndRole(JSONObject rcv);

    /**
     * 生成短信验证码
     */
    String generateMessageCode(JSONObject rcv);

    /**
     * 验证短信验证码
     */
    String verifyMessageCode(JSONObject rcv);

    /**
     * 无密码登录
     */
    String noPasswordLogin(JSONObject rcv);

    /**
     * 授权登录
     */
    String setPermission(JSONObject rcv);

    String getUserIdByUid(JSONObject rcv);

    /**
     * 根据openId查询用户信息
     * @param rcv
     * @return
     */
    String getUserInfoByOpenId(JSONObject rcv);

}
