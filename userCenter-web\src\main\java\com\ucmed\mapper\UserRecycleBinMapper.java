package com.ucmed.mapper;

import com.ucmed.bean.UserRecycleBin;
import org.apache.ibatis.annotations.Insert;
import tk.mybatis.mapper.common.Mapper;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/01/07 09:04
 */
public interface UserRecycleBinMapper extends Mapper<UserRecycleBin> {
    @Insert({
            "insert into user_recycle_bin (user_id, phone, ",
            "table_data, create_time, ",
            "table_name)",
            "values (#{userId,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, ",
            "#{tableData,jdbcType=OTHER, typeHandler=com.ucmed.common.handler.JSONTypeHandlerPg}," +
            " #{createTime,jdbcType=VARCHAR}, ",
            "#{tableName,jdbcType=VARCHAR})"
    })
    int insertUserRecycleBin(UserRecycleBin record);
}
