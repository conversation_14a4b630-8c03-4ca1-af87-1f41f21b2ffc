package com.ucmed.dao;

import com.ucmed.bean.Patient;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

import static com.ucmed.common.constant.TableName.JCUSERPATIENT;

/**
 * jc_user_patient
 * Created by QIUJIAHAO on 2016/9/27.
 */
@Repository
public class JcUserPatientDaoImpl implements JcUserPatientDao {

    @Resource private JdbcTemplate jdbcTemplate;

    @Override
    public int updatePatient(final Patient patient) {

        String sql = "UPDATE " + JCUSERPATIENT + " SET " +
                "user_id=?,patient_name=?,patient_sex=?,patient_birthday=?,patient_card_type=?,patient_card_id=?,patient_medicare=?," +
                "patient_medicare_id=?,patient_city_id=?,patient_address=?,patient_w_chat=?,patient_mobile=?,patient_e_mail=?,update_time=? " +
                "WHERE patient_id=?";

        return jdbcTemplate.update(sql, new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                ps.setString(1, patient.getUser_id());
                ps.setString(2, patient.getPatient_name());
                ps.setString(3, patient.getPatient_sex());
                ps.setString(4, patient.getPatient_birthday());
                ps.setString(5, patient.getPatient_card_type());
                ps.setString(6, patient.getPatient_card_id());
                ps.setString(7, patient.getPatient_medicare());
                ps.setString(8, patient.getPatient_medicare_id());
                ps.setString(9, patient.getPatient_city_id());
                ps.setString(10, patient.getPatient_address());
                ps.setString(11, patient.getPatient_w_chat());
                ps.setString(12, patient.getPatient_mobile());
                ps.setString(13, patient.getPatient_e_mail());
                ps.setString(14, patient.getUpdate_time());
                ps.setInt(15, patient.getPatient_id());
            }
        });
    }

    @Override
    public int addPatient(final Patient patient) {
        String sql = "INSERT INTO " + JCUSERPATIENT + "(user_id,patient_name,patient_sex,patient_birthday,patient_card_type,patient_card_id,patient_medicare,patient_medicare_id,patient_city_id,patient_address,patient_w_chat,patient_mobile,patient_e_mail,create_time) " +
                "VALUES(?,?,?,?,?,?,?,?,?,?,?,?,?,?)";

        return jdbcTemplate.update(sql, new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                ps.setString(1, patient.getUser_id());
                ps.setString(2, patient.getPatient_name());
                ps.setString(3, patient.getPatient_sex());
                ps.setString(4, patient.getPatient_birthday());
                ps.setString(5, patient.getPatient_card_type());
                ps.setString(6, patient.getPatient_card_id());
                ps.setString(7, patient.getPatient_medicare());
                ps.setString(8, patient.getPatient_medicare_id());
                ps.setString(9, patient.getPatient_city_id());
                ps.setString(10, patient.getPatient_address());
                ps.setString(11, patient.getPatient_w_chat());
                ps.setString(12, patient.getPatient_mobile());
                ps.setString(13, patient.getPatient_e_mail());
                ps.setString(14, patient.getCreate_time());
            }
        });
    }

    @Override
    public List<Patient> getPatientByUser(String user_id) {
        String sql = "SELECT " +
                "patient_id,user_id,patient_name,patient_sex,patient_birthday,patient_card_type,patient_card_id,patient_medicare,patient_medicare_id,patient_city_id,patient_address,patient_w_chat,patient_mobile,patient_e_mail,create_time,update_time " +
                "FROM " + JCUSERPATIENT + " " +
                "WHERE user_id=?";
        List<Patient> list = jdbcTemplate.query(sql, new Object[]{user_id}, new BeanPropertyRowMapper<>(Patient.class));
        if(list.size() == 0) {
            return null;
        }
        return list;
    }

    @Override
    public Patient getPatientById(int patient_id) {
        String sql = "SELECT " +
                "patient_id,user_id,patient_name,patient_sex,patient_birthday,patient_card_type,patient_card_id,patient_medicare,patient_medicare_id,patient_city_id,patient_address,patient_w_chat,patient_mobile,patient_e_mail,create_time,update_time " +
                "FROM " + JCUSERPATIENT + " " +
                "WHERE patient_id=?";
        List<Patient> list = jdbcTemplate.query(sql, new Object[]{patient_id}, new BeanPropertyRowMapper<>(Patient.class));
        if(list.size() == 0) {
            return null;
        }
        return list.get(0);
    }
}
