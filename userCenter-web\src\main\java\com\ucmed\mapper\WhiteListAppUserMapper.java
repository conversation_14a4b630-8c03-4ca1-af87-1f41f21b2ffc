package com.ucmed.mapper;

import com.ucmed.bean.WhiteListAppUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * Author: 黄一辛 HUANGYIXIN
 * CreateTime: 2018/5/17 16:56
 * Contract: <EMAIL>
 * Description:
 **/
public interface WhiteListAppUserMapper {

    @Select({
            "select",
            "id",
            "from whitelist_app_user",
            "where app_code = #{appCode,jdbcType=INTEGER} and user_id = #{userId,jdbcType=VARCHAR} and status = '1'"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true)
    })
    List<WhiteListAppUser> getIdByAppCodeAndUser(@Param("appCode") Integer appCode, @Param("userId") String userId);
}
