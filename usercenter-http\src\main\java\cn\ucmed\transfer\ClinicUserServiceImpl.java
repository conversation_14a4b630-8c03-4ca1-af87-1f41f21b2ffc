package cn.ucmed.transfer;

import cn.ucmed.utils.HttpClientUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ucmed.bean.UCResponse;
import com.ucmed.dto.ClinicResponse;
import com.ucmed.dto.ClinicUserInfo;
import com.ucmed.service.ClinicUserService;

import static cn.ucmed.constant.UserCenterConstant.USER_CENTER_URL;

public class ClinicUserServiceImpl implements ClinicUserService {
    @Override
    public UCResponse registration(String phone, String password, int appCode, String roleName, ClinicUserInfo userInfo) {
        String url = USER_CENTER_URL + "/clinicUser/registration?phone=" + phone
                + "&appCode=" + appCode + "&password=" + password + "&roleName=" + roleName + "&userInfo=" + userInfo;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public ClinicResponse listUser(int appCode, ClinicUserInfo clinicUserInfo) {
        String url = USER_CENTER_URL + "/clinicUser/listUserByDetailedConditions?clinicUserInfo=" + clinicUserInfo
                + "&appCode=" + appCode;
        return (ClinicResponse) HttpClientUtils.doGet(url, new JSONObject(), null, ClinicResponse.class);
    }

    @Override
    public ClinicResponse listUser(int appCode, Integer pageNo, Integer pageSize, String conditions) {
        String url = USER_CENTER_URL + "/clinicUser/listUserByConditions?appCode=" + appCode
                + "&appCode=" + appCode + "&pageNo=" + pageNo + "&pageSize=" + pageSize;
        return (ClinicResponse) HttpClientUtils.doGet(url, new JSONObject(), null, ClinicResponse.class);
    }

    @Override
    public UCResponse updateUserInfo(String phone, int appCode, ClinicUserInfo clinicUserInfo) {
        String url = USER_CENTER_URL + "/clinicUser/updateUserInfo?appCode=" + appCode
                + "&appCode=" + appCode + "&clinicUserInfo=" + clinicUserInfo;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }
}
