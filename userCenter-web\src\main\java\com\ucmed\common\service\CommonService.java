package com.ucmed.common.service;

import com.ucmed.authc.EnumProductKey;
import com.ucmed.bean.*;
import com.ucmed.common.constant.AppMemoryInfo;
import com.ucmed.common.constant.CommonConstant;
import com.ucmed.dao.JcUserDao;
import com.ucmed.dao.JcUserInfoDao;
import com.ucmed.dao.SecurityRoleDao;
import com.ucmed.exception.BusinessException;
import com.ucmed.mapper.JcUserPushMapper;
import com.ucmed.mapper.SecurityApplicationMapper;
import com.ucmed.mapper.SecurityUserAppMapper;
import com.ucmed.mapper.SecurityUserRoleMapper;
import com.ucmed.service.*;
import com.ucmed.util.*;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.BoundHashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeParseException;
import java.time.temporal.ChronoUnit;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.logging.Logger;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.ucmed.common.constant.CommonConstant.*;


/**
 * 公共服务类
 * Created by QIUJIAHAO on 2016/8/15.
 */

@Service("commonService")
public class CommonService {

    private static Logger log4j = Logger.getLogger(CommonService.class.getName());
    @Autowired
    private JcUserDao jcUserDao;
    @Autowired
    private SecurityRoleDao securityRoleDao;
    @Autowired
    private JcUserInfoDao jcUserInfoDao;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private JcUserPushMapper jcUserPushMapper;
    @Autowired
    private SecurityUserAppMapper securityUserAppMapper;
    @Autowired
    private JcUserPushService jcUserPushService;
    @Autowired
    private SecurityUserRoleMapper securityUserRoleMapper;
    @Autowired
    private SecurityUserProjectService securityUserProjectService;
    @Autowired
    private PermissionService permissionService;
    @Autowired
    private GetAppInfoService getAppInfoService;
    @Autowired
    private RoleService roleService;
    @Autowired
    SecurityApplicationMapper securityApplicationMapper;
    @Autowired
    AppMemoryInfo appMemoryInfo;
    @Autowired
    private RocketMQUtil rocketMQUtil;

    LocalDateTime localDateTime = LocalDateTime.now();

    public JcUserDao getJcUserDao() {
        return jcUserDao;
    }

    public SecurityRoleDao getSecurityRoleDao() {
        return securityRoleDao;
    }

    public JcUserInfoDao getJcUserInfoDao() {
        return jcUserInfoDao;
    }

    /**
     * 用户名合法性（只能包含数字、字母、下划线、中划线）
     */
    public boolean isNotUsernamePattern(String userId) {
        if (userId == null || "".equals(userId)) {
            log4j.info(userId + " 用户名不合法");
            return true;
        }
        Pattern p = Pattern.compile("^[a-zA-Z0-9_]{4,16}$");
        Matcher m = p.matcher(userId);
        return !m.matches();
    }

    /**
     * 手机号合法性
     */
    public boolean isNotPhonePattern(String phone) {
        if (phone == null || "".equals(phone)) {
            log4j.info(phone + " 手机号不合法");
            return true;
        }
//		Pattern p = Pattern.compile("^((13[0-9])|(15[^4,\\D])|(17[6-8])|(18[0,5-9]))\\d{8}$");
        Pattern p = Pattern.compile("^1\\d{10}$");
        Matcher m = p.matcher(phone);
        return !(m.matches() || phone.startsWith(CommonConstant.OC_PHONE_MARK));
    }

    /**
     * 弱密码检测
     * 弱密码:true
     */
    public boolean isSimplePwd(String password) {
        if (password == null) {
            return true;
        }
        boolean flag = true;
        if (password.length() <= 6) {
            return true; //密码太短
        }
        int Modes = 0;
        boolean num = true;
        boolean upper = true;
        boolean lower = true;
        boolean other = true;
        for (int i = 0; i < password.length(); i++) {
            char each = password.charAt(i);
            if (each > '0' && each < '9') {
                if (num) {
                    Modes++;
                    num = false;
                }
            } else if (each > 'a' && each < 'z') {
                if (lower) {
                    Modes++;
                    lower = false;
                }
            } else if (each > 'A' && each < 'Z') {
                if (upper) {
                    Modes++;
                    upper = false;
                }
            } else {
                if (other) {
                    Modes++;
                    other = false;
                }
            }
        }
        if (Modes >= 2) {
            flag = false;
        }
        return flag;
    }

    public static boolean isEmail(String string) {
        if (string == null) {
            return false;
        }
        String regEx1 = "^([a-z0-9A-Z]+[-|\\.]?)+[a-z0-9A-Z]@([a-z0-9A-Z]+(-[a-z0-9A-Z]+)?\\.)+[a-zA-Z]{2,}$";
        Pattern p;
        Matcher m;
        p = Pattern.compile(regEx1);
        m = p.matcher(string);
        if (m.matches()) {
            return true;
        } else {
            return false;
        }
    }

    /**
     * 获取用户uid
     *
     * @param userIdOrPhone
     * @return
     */
    public int getUid(String userIdOrPhone) {
        int uid = 0;
        String userId = userIdOrPhone;
        String phone = ThreeDESEncrypt(userIdOrPhone);
        UC_UserInfo userInfo = jcUserDao.getUserByUserId(userId);
        if (userInfo == null) {
            userInfo = jcUserDao.getUserByPhone(phone);
            if (userInfo != null) {
                uid = userInfo.getUid();
            }
        } else {
            uid = userInfo.getUid();
        }
        return uid;
    }

    /**
     * 判断用户是否已注册
     *
     * @param userId
     * @return
     */
    public boolean isUserExist(String userId) {
        int uid = getUid(userId);
        return uid > 0;
    }

    public boolean isUserNotExist(String userId) {
        return !isUserExist(userId);
    }

    /**
     * 判断用户名是否存在
     *
     * @param userId
     * @return
     */
    public boolean isUserIdExist(String userId) {
        UC_UserInfo userInfo = jcUserDao.getUserByUserId(userId);
        return userInfo != null;
    }

    /**
     * 判断手机号是否存在
     *
     * @param phone
     * @return
     */
    public boolean isPhoneExist(String phone) {
        UC_UserInfo userInfo = jcUserDao.getUserByPhone(ThreeDESUtil.get3DESEncrypt(phone, SPKEY));
        return userInfo != null;
    }

    public boolean isPhoneNotExist(String phone) {
        return !isPhoneExist(phone);
    }

    /**
     * 根据roleName和appCode得到roleId
     *
     * @param roleName 角色名称
     * @param appCode  应用ID
     * @return roleId 角色ID
     */
    public int getRoleId(String roleName, int appCode) {
        List<Role> roles = roleService.listByAppCode(appCode);
        for (Role role : roles) {
            if (roleName.equals(role.getRoleName())) {
                return role.getRoleId();
            }
        }
        return 0;
    }

    /**
     * 判断用户是否与角色绑定
     *
     * @param userId 用户名
     * @param roleId 角色ID
     * @return
     */
    public boolean isUserInRole(String userId, int roleId) {
        List<SecurityUserRole> userRole = securityUserRoleMapper.selectByUserIdAndRoleId(userId, roleId);
        return userRole.size() != 0;
    }

    /**
     * 判断应用下是否有角色
     *
     * @param roleName 角色名称
     * @param appCode  应用ID
     * @return
     */
    public boolean isRoleInApp(String roleName, int appCode) {
        return securityRoleDao.getRoleId(appCode, roleName) > 0;
    }

    /**
     * 应用与角色绑定
     *
     * @param appCode  应用ID
     * @param roleName 角色名称
     */
    public void addRoleToApp(int appCode, String roleName) {
        if (!isRoleInApp(roleName, appCode)) {
            Role role = new Role();
            role.setAppCode(appCode);
            role.setRoleName(roleName);
            role.setOperDate(TimeUtil.getCurrentTime());
            role.setOperUser(String.valueOf(appCode));
            securityRoleDao.addRole(role);
        }
    }

    /**
     * 用户与角色绑定
     *
     * @param userId 用户名
     * @param roleId 角色ID
     */
    public void addUserToRole(String userId, int roleId) {
        if (!isUserInRole(userId, roleId)) {
//            baseDao.insert(SECURITYUSERROLE, new String[]{"userId", "roleId", "oper_user", "oper_date"}, new Object[]{userId, roleId, "usercenter", TimeUtil.getCurrentTime()});
            SecurityUserRole userRole = new SecurityUserRole();
            userRole.setUserId(userId);
            userRole.setRoleId(roleId);
            userRole.setOperDate(TimeUtil.getCurrentTime());
            userRole.setValid("1");
            userRole.setOperUser(userId);
            securityUserRoleMapper.insert(userRole);
        }
    }

    /**
     * 判断用户是否与应用绑定
     *
     * @param userId
     * @param appCode
     * @return
     */
    public SecurityUserApp getUserApp(String userId, int appCode) {
        List<SecurityUserApp> userApps = securityUserAppMapper.selectByUserIdAndAppCode(userId, appCode);
        if (userApps.size() == 0) {
            return null;
        }
        return userApps.get(0);
    }

    /**
     * 用户与应用绑定
     *
     * @param userId  用户名
     * @param appCode 应用ID
     */
    public SecurityUserApp addUserToApp(String userId, int appCode) {
        SecurityUserApp userApp = getUserApp(userId, appCode);
        if (userApp == null) {
            SecurityUserApp newUserApp = new SecurityUserApp();
            newUserApp.setAppCode(appCode);
            newUserApp.setOperDate(TimeUtil.getCurrentTime());
            newUserApp.setUserId(userId);
            newUserApp.setOpenId(UUID.randomUUID().toString());
            securityUserAppMapper.insert(newUserApp);
            return newUserApp;
        } else if (userApp.getOpenId() == null) {
            userApp.setOpenId(UUID.randomUUID().toString());
            securityUserAppMapper.updateByPrimaryKey(userApp);
        }
        return userApp;
    }

    /**
     * 密码加密
     *
     * @param password    接收到的密码
     * @param securityKey
     * @return
     */
    public String encryptPassword(String password, String securityKey) {
        return SHA256Util.hash(password + securityKey);
    }

    /**
     * 两次hash的密码加密规则，适用于掌医的历史数据
     *
     * @param password    接收到的密码
     * @param securityKey
     * @return
     */
    public String encryptPasswordDoubleHash(String password, String securityKey) {
        return SHA256Util.hash(SHA256Util.hash(password) + securityKey);
    }


    /**
     * 特殊角色判断
     *
     * @param roleName 角色名称
     * @return
     */
    public boolean isSpecialRole(String roleName) {
        String[] specials = Configuration.getSpecial_role().split(",");
        for (String role : specials) {
            if (roleName.equals(role)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 登录成功 - 更新token值、最后登录时间、登录次数
     *
     * @param user
     */
    public String loginSuccess(UC_UserInfo user) {
        return loginSuccess(user, null);
    }

    public String loginSuccess(UC_UserInfo user, String token) {
//        log4j.info("commonservice测试用：用户登录成功：删除前：token" + user.getToken());
        if (StringUtils.isNotBlank(user.getToken())) {
            redisTemplate.delete(user.getToken());
        }

        String currentTime = TimeUtil.getCurrentTime();

        // 更新token值、最后登录时间、登录次数
        user.setToken(StringUtils.isNotBlank(token) ? token :
                TokenUtil.generateToken(currentTime, true));//生成token
        user.setToken_time(currentTime);
        user.setLatestTime(currentTime);
        user.setLogin_times(user.getLogin_times() + 1);
        jcUserDao.updateUser(user);

        int appCode = Integer.parseInt(user.getApp_code());

        // 登录成功返回值
        JSONObject param = new JSONObject();
        param.put(USERID, user.getUser_id());
        param.put(PHONE, ThreeDESDecrypt(user.getPhone()));
        param.put(TOKEN, user.getToken());
        param.put("proj_code", user.getProject_code());
        param.put("id", user.getUid());
        try {
            param.put("permission", permissionService.getPermission(user.getUser_id(), appCode));
        } catch (BusinessException e) {
            e.printStackTrace();
            param.put("permission", null);
        }
//        param.put("roles", roleService.queryUserRole(user.getUserId(), appCode));
//        param.put("authority", authorityService.getAuthority(user.getUserId(), appCode));
//        param.put("datavalue", authorityService.getDataValue(user.getUserId(), appCode));
        // 返回用户名和身份证号码
        UserInfo userInfo = jcUserInfoDao.getUserInfoByUserId(user.getUser_id());
        param.put("realName", userInfo.getThe_name());
        if (!"".equals(userInfo.getCard_id()) && userInfo.getCard_id() != null) {
            userInfo.setCard_id(ThreeDESDecrypt(userInfo.getCard_id()));
        }
        param.put("idCard", userInfo.getCard_id());
        // 返回pushId
        JcUserPush userPush = updateUserPush(user.getUser_id(), appCode);
        if (userPush != null) {
            param.put("pushId", userPush.getPushId());
            param.put("openId", userPush.getOpenId());
        }
        redisTemplate.boundHashOps(user.getToken()).put(EnumProductKey.GENERAL.name(), param.toString());
//        log4j.info("commonservice测试用：用户登录成功：生成后：token" + user.getToken());
        return param.toString();
    }


    public void loginSuccess(UC_UserInfo user, String token, Boolean isRubikLogin) {

        if (!isRubikLogin) {
            loginSuccess(user, token);
        }

        if (StringUtils.isNotBlank(user.getToken())) {
            redisTemplate.delete(user.getToken());
        }

        String currentTime = TimeUtil.getCurrentTime();

        // 更新token值、最后登录时间、登录次数
        user.setToken(StringUtils.isNotBlank(token) ? token :
                TokenUtil.generateToken(currentTime, true));//生成token
        user.setToken_time(currentTime);
        user.setLatestTime(currentTime);
        user.setLogin_times(user.getLogin_times() + 1);
        jcUserDao.updateUser(user);

        Application app = null;
        if (StringUtils.isNumeric(user.getApp_code())) {
            app = getAppInfoService.getAppByCode(Integer.parseInt(user.getApp_code()));
        }

        StringBuilder retJson = new StringBuilder("{\'user_id\':\'");
        retJson.append(user.getUser_id())
                .append("\',\'phone\':\'").append(ThreeDESDecrypt(user.getPhone()))
                .append("\',\'token\':\'").append(user.getToken())
                .append("\',\'id\':\'").append(user.getUid());

        if (app != null) {
            retJson.append("\',\'proj_code\':\'").append(app.getProj_code());
        }

        retJson.append("\'}");
        redisTemplate.boundHashOps(user.getToken()).put(EnumProductKey.GENERAL.name(), retJson);
    }

//    /**
//     * 更新推送信息
//     *
//     * @param userId
//     * @param appCode
//     * @return
//     */
//    public JcUserPush updateUserPush(String userId, int appCode) {
//        JcUserPush userPush = null;
//        SecurityUserApp userApp = getUserApp(userId, appCode);
//        if (userApp != null) {
//            userPush = jcUserPushMapper.selectByOpenId(userApp.getOpenId());
//            if (userPush != null) {
//                userPush.setPushId(UUID.randomUUID().toString());
//                userPush.setModifiedon(TimeUtil.getCurrentTime());
//                userPush.setModifiedby(userId);
//                jcUserPushMapper.updateByPrimaryKey(userPush);
//            } else {
//                if (userApp.getOpenId() == null) {
//                    userApp.setOpenId(UUID.randomUUID().toString());
//                    securityUserAppMapper.updateByPrimaryKey(userApp);
//                }
//                userPush = new JcUserPush();
//                userPush.setUserPushId(UUID.randomUUID().toString());
//                userPush.setOpenId(userApp.getOpenId());
//                userPush.setPushId(UUID.randomUUID().toString());
//                userPush.setAppCode(appCode);
//                userPush.setCreatedby(userApp.getUserId());
//                userPush.setModifiedby(userApp.getUserId());
//                userPush.setDeletionState("0");
//                userPush.setCreatedon(TimeUtil.getCurrentTime());
//                userPush.setModifiedon(TimeUtil.getCurrentTime());
//                jcUserPushMapper.insert(userPush);
//            }
//        }
//        return userPush;
//    }

    /**
     * 更新推送信息
     *
     * @param userId
     * @param appCode
     * @return
     */
    public JcUserPush updateUserPush(String userId, int appCode) {
        JcUserPush userPush = null;
        int projCode = getProjCode(appCode);
        SecurityUserProject userProject = securityUserProjectService.getUserProject(userId, projCode);
        if (userProject != null) {
            userPush = jcUserPushMapper.selectByOpenId(userProject.getOpenId());
            if (userPush != null) {
                userPush.setPushId(UUID.randomUUID().toString());
                userPush.setModifiedon(TimeUtil.getCurrentTime());
                userPush.setModifiedby(userId);
                jcUserPushMapper.updateByPrimaryKey(userPush);
            } else {
                if (userProject.getOpenId() == null) {
                    userProject.setOpenId(UUID.randomUUID().toString());
                    securityUserProjectService.updateUserProject(userProject);
                }
                userPush = new JcUserPush();
                userPush.setUserPushId(UUID.randomUUID().toString());
                userPush.setOpenId(userProject.getOpenId());
                userPush.setPushId(UUID.randomUUID().toString());
                userPush.setAppCode(appCode);
                userPush.setCreatedby(userProject.getUserId());
                userPush.setModifiedby(userProject.getUserId());
                userPush.setDeletionState("0");
                userPush.setCreatedon(TimeUtil.getCurrentTime());
                userPush.setModifiedon(TimeUtil.getCurrentTime());
                jcUserPushMapper.insert(userPush);
            }
        }
        return userPush;
    }

    /**
     * 获取用户对象
     *
     * @param userId 用户名
     * @return
     */
    public UC_UserInfo getUser(String userId) {
        int uid = getUid(userId);
        return jcUserDao.getUserByUid(uid);
    }

    /**
     * 根据userId和projectId查询用户
     *
     * @param userId
     * @param projectId
     * @return
     */
    public UC_UserInfo getUserByUserIdAndProjcetId(String userId, int projectId) {
        UC_UserInfo user = jcUserDao.getUserByUserIdAndProjectId(userId, projectId);
        if (user == null) {
            user = jcUserDao.getUserByPhoneAndProjectId(ThreeDESEncrypt(userId), projectId);
        }
        return user;
    }

    /**
     * 3DES加密
     *
     * @param str
     * @return
     */
    public String ThreeDESEncrypt(String str) {
        if ("".equals(str)) {
            return "";
        }
        if (str == null) {
            return null;
        }
        return ThreeDESUtil.get3DESEncrypt(str, SPKEY);
    }

    /**
     * 3DES解密
     *
     * @param str
     * @return
     */
    public String ThreeDESDecrypt(String str) {
        if ("".equals(str)) {
            return "";
        }
        if (str == null) {
            return null;
        }
        return ThreeDESUtil.get3DESDecrypt(str, SPKEY);
    }

    /**
     * 判断是否移动远程用户
     *
     * @param rcv
     * @return
     */
    public boolean isRemoteMedical(JSONObject rcv) {
        return rcv.containsKey(USER_TYPE) && REMOTE_MEDICAL.equals(rcv.getString(USER_TYPE));
    }

    /**
     * 将移动远程用户信息存入redis
     *
     * @param retData
     */
    public void RMLoginSuccess(JSONObject retData) {
        redisTemplate.boundHashOps(retData.getString("token")).put(EnumProductKey.REMOTE_MEDICAL.name(), retData.toString());
    }

    /**
     * 统一返回参数的格式
     *
     * @param retData
     */
    public String transRetStr(JSONObject retData) {
        if (retData.containsKey("ret_code")) {
            retData.put("retCode", retData.getString("ret_code"));
            retData.discard("ret_code");
        }
        if (retData.containsKey("ret_info")) {
            retData.put("retInfo", retData.getString("ret_info"));
            retData.discard("ret_info");
        }
        if (retData.containsKey("ret_data")) {
            retData.put("param", retData.getString("ret_data"));
            retData.discard("ret_data");
        }
        return retData.toString();
    }

    /**
     * 修改密码 - 密码、securityKey、密码修改时间
     *
     * @param user
     * @param password
     */
    public void changePwd(UC_UserInfo user, String password, String currentTime) {
        // 生成新securityKey
        String SecurityKey = SecurityKeyUtil.generateSecurityKey();
        // 新密码加密
        password = encryptPassword(password, SecurityKey);

        // 更新密码
        user.setPassword(password);
        user.setPass_change_time(currentTime);
        user.setSecuritykey(SecurityKey);
        updateUser(user);
    }

    public void dlchangePwd(SecurityUserProject userProject, String password, String currentTime) {
        // 生成新securityKey
        String SecurityKey = SecurityKeyUtil.generateSecurityKey();
        // 新密码加密
        password = encryptPassword(password, SecurityKey);
        // 更新密码
        userProject.setPassword(password);
        userProject.setUpdateTime(currentTime);
        userProject.setSecuritykey(SecurityKey);
        securityUserProjectService.updatedlPwd(userProject);
    }

    /**
     * 更新用户
     *
     * @param user
     */
    public void updateUser(UC_UserInfo user) {
        getJcUserDao().updateUser(user);
    }

    /**
     * 用户是否注册角色
     *
     * @param userId
     * @param roleName
     * @return
     */
    public boolean isUserInRole(String userId, String roleName) {
        return getSecurityRoleDao().isUserInRole(userId, roleName) > 0;
    }

    /**
     * 新用户注册
     *
     * @param userId
     * @param phone
     * @param password
     * @param appCode
     * @param roleName
     * @param currentTime
     */
    public void normalRegistration(String userId,
                                   String phone,
                                   String password,
                                   int appCode,
                                   String roleName,
                                   String currentTime,
                                   String UCMEDID) {
        //添加用户登录信息
        addUser(userId, password, phone, currentTime, UCMEDID);
        //添加用户个人资料信息
        addUserInfo(userId, phone, currentTime);
        //添加挂靠关系
        addRelation(userId, appCode, roleName);
    }

    public void normalRegistration(String userId,
                                   String phone,
                                   String password,
                                   int appCode,
                                   String roleName,
                                   String currentTime,
                                   String UCMEDID,
                                   String name,
                                   String email, boolean dlpwd) {
        //添加用户登录信息
        addUser(userId, password, phone, currentTime, UCMEDID);
        //添加用户个人资料信息
        addUserInfo(userId, phone, currentTime, name, email);
        //添加挂靠关系
        if (dlpwd == true) {
            dladdRelation(userId, appCode, roleName, password);
        } else {
            addRelation(userId, appCode, roleName);
        }
    }

    public void dlnormalRegistration(String userId,
                                     String phone,
                                     String password,
                                     int appCode,
                                     String roleName,
                                     String currentTime,
                                     String UCMEDID) {
        //添加用户登录信息
        addUser(userId, password, phone, currentTime, UCMEDID);
        //添加用户个人资料信息
        addUserInfo(userId, phone, currentTime);
        //添加挂靠关系
        dladdRelation(userId, appCode, roleName, password);
    }

    /**
     * 添加挂靠关系
     *
     * @param userId
     * @param appCode
     * @param roleName
     */
    public void addRelation(String userId, int appCode, String roleName) {
        int projCode = getProjCode(appCode);
        int roleId = getRoleId(roleName, appCode);
        if (roleId != 0) {
            // 用户与角色挂靠
            addUserToRole(userId, roleId);
        }
        // 用户与项目挂靠
        SecurityUserProject userProject = addUserProject(userId, projCode);
        // 用户与应用挂靠
        SecurityUserApp userApp = addUserToApp(userId, appCode);
        // 添加推送信息
        JcUserPush userPush = jcUserPushMapper.selectByOpenId(userProject.getOpenId());
        if (userPush == null) {
            jcUserPushService.insert(userProject.getOpenId(), userId, projCode, UUID.randomUUID().toString());
        }
    }

    public void dladdRelation(String userId, int appCode, String roleName, String password) {
        int projCode = getProjCode(appCode);
        int roleId = getRoleId(roleName, appCode);
        if (roleId != 0) {
            // 用户与角色挂靠
            addUserToRole(userId, roleId);
        }
        // 用户与项目挂靠
        SecurityUserProject userProject = addUserProject(userId, projCode, password);
        // 用户与应用挂靠
        SecurityUserApp userApp = addUserToApp(userId, appCode);
        // 添加推送信息
        JcUserPush userPush = jcUserPushMapper.selectByOpenId(userProject.getOpenId());
        if (userPush == null) {
            jcUserPushService.insert(userProject.getOpenId(), userId, projCode, UUID.randomUUID().toString());
        }
    }

    /**
     * 校验token 返回userId
     *
     * @param token
     * @return
     */
    public String verifyToken(String token) {
        if (token == null) {
            return null;
        }
        String redisInfo = (String) redisTemplate.boundHashOps(token).get(EnumProductKey.GENERAL.name());
        if (redisInfo == null) {
            return null;
        }
        JSONObject userInfo = JSONObject.fromObject(redisInfo);
        return userInfo.optString(USERID);
    }

    public String getProjCodeByToken(String token) {
        if (token == null) {
            return null;
        }
        String redisInfo = (String) redisTemplate.boundHashOps(token).get(EnumProductKey.GENERAL.name());
        if (redisInfo == null) {
            return null;
        }
        JSONObject userInfo = JSONObject.fromObject(redisInfo);
        return userInfo.optString("proj_code");
    }

    public String getTokenInfo(String token) {
        if (token == null) {
            return null;
        }
        String redisInfo = (String) redisTemplate.boundHashOps(token).get(EnumProductKey.GENERAL.name());
        if (redisInfo == null) {
            return null;
        }
        JSONObject userInfo = JSONObject.fromObject(redisInfo);
        JSONObject retJson = new JSONObject();
        retJson.put(USERID, userInfo.optString(USERID));
        retJson.put("proj_code", userInfo.optString("proj_code"));
        retJson.put(APPCODE, userInfo.optString(APPCODE));
        retJson.put("openId", userInfo.getString("openId"));
        return retJson.toString();
    }

    /**
     * 生成6位随机数字
     */
    public String generateRandomCode() {
        String numStr = "1234567890";
        String code = "";
        for (int i = 0; i < 6; i++) {
            double r = Math.random() * 10;
            code += numStr.charAt((int) r);
        }
        return code;
    }


    /**
     * 添加用户验证信息
     *
     * @param userId
     * @param password
     * @param phone
     * @param currentTime
     */
    public void addUser(String userId, String password, String phone, String currentTime, String UCMEDID) {
        UC_UserInfo user = new UC_UserInfo();
        //生成securityKey
        String securityKey = SecurityKeyUtil.generateSecurityKey();
        //密码加密
        password = encryptPassword(password, securityKey);
        //手机号加密
        phone = ThreeDESEncrypt(phone);

        user.setUser_id(userId);
        user.setPassword(password);
        user.setPhone(phone);
        user.setCreate_time(currentTime);
        user.setSecuritykey(securityKey);
        user.setUcmed_id(UCMEDID);
        getJcUserDao().addUser(user);
    }


    /**
     * 添加用户信息
     *
     * @param userId
     * @param mobile
     */
    public void addUserInfo(String userId, String mobile, String currentTime) {
        mobile = ThreeDESEncrypt(mobile);

        UserInfo userInfo = new UserInfo();
        userInfo.setUser_id(userId);
        userInfo.setMobile(mobile);
        userInfo.setCreate_time(currentTime);
        getJcUserInfoDao().addUserInfo(userInfo);
    }

    /**
     * 添加用户信息
     *
     * @param userId
     * @param mobile
     */
    public void addUserInfo(String userId, String mobile, String currentTime, String name, String email) {
        mobile = ThreeDESEncrypt(mobile);

        UserInfo userInfo = new UserInfo();
        userInfo.setUser_id(userId);
        userInfo.setThe_name(name);
        userInfo.setMobile(mobile);
        userInfo.setE_mail(email);
        userInfo.setCreate_time(currentTime);
        getJcUserInfoDao().addUserInfo(userInfo);
    }


    /**
     * 密码匹配
     *
     * @param inputPassword
     * @param user
     * @return
     */
    public boolean passwordMatched(String inputPassword, UC_UserInfo user) {
        String securityKey = user.getSecuritykey();
        String password = user.getPassword();
        if (inputPassword == null) {
            return false;
        }
        return password.equals(encryptPassword(inputPassword, securityKey)) ||
                password.equals(encryptPasswordDoubleHash(inputPassword, securityKey));

    }

    public boolean dlpasswordMatched(String inputPassword, SecurityUserProject userProject, UC_UserInfo user) {
        boolean hasdlpwd = userProject.getPassword() != null && userProject.getSecuritykey() != null
                && !userProject.getPassword().isEmpty() && !userProject.getSecuritykey().isEmpty();
        String securityKey;
        String password;
        if (hasdlpwd) {
            securityKey = userProject.getSecuritykey();
            password = userProject.getPassword();
        } else {
            return passwordMatched(inputPassword, user);
        }
        if (inputPassword == null) {
            return false;
        }
        return password.equals(encryptPassword(inputPassword, securityKey)) ||
                password.equals(encryptPasswordDoubleHash(inputPassword, securityKey));

    }

    public UCResponse login(String userId, String password, int appCode, String roleName, boolean sms) {
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            return new UCResponse(-1, "应用未注册");
        }
        UC_UserInfo user = getUser(userId);

        if (user == null) {
            return new UCResponse(4, "账号未注册");
        }
        userId = user.getUser_id();
        //是否锁定
        if ("y".equals(user.getLock())) {
            return new UCResponse(2, "账号被锁定,请联系管理员");
        }
        // 判断密码是否正确
        if (!sms && !passwordMatched(password, user)) {
            return new UCResponse(1, "密码错误");

        }
        JSONObject param = new JSONObject();
        param.put(USERID, user.getUser_id());
        param.put(PHONE, ThreeDESDecrypt(user.getPhone()));
        // 用户是否已授权
//        UC_UserInfo projUser = getUserByUserIdAndProjcetId(userId, Integer.parseInt(application.getProj_code()));
        SecurityUserApp userApp = getUserApp(user.getUser_id(), appCode);
        if (userApp == null) {
            return new UCResponse(5, "用户未授权，是否确定使用该应用？", param);
        }
        int roleId = getRoleId(roleName, appCode);
        if (!isUserInRole(userId, roleId)) {
            return new UCResponse(6, "角色未授权", param);
        }
        // 更新用户状态
        user.setApp_code(String.valueOf(appCode));
        user.setRole(roleName);
        user.setProject_code(application.getProj_code());
        String retParam = loginSuccess(user);
        return new UCResponse(0, "登录成功", retParam);
    }

    /**
     * 获取项目ID
     *
     * @param appCode 应用ID
     * @return
     */
    public int getProjCode(int appCode) {
        Application app = getAppInfoService.getAppByCode(appCode);
        return app == null ? 0 : Integer.parseInt(app.getProj_code());
    }

    /**
     * 添加用户与项目的关系，若关系已存在则返回现有关系
     *
     * @param userId   用户ID
     * @param projCode 项目Code
     * @return SecurityUserProject
     */
    public SecurityUserProject addUserProject(String userId, int projCode) {
        SecurityUserProject userProject = securityUserProjectService.getUserProject(userId, projCode);
        if (userProject == null) {
            userProject = new SecurityUserProject();
            userProject.setUserId(userId);
            userProject.setProjCode(projCode);
            userProject.setCreateBy(userId);
            userProject.setOpenId(UUID.randomUUID().toString());
            userProject.setCreateTime(TimeUtil.getCurrentTime());
            userProject.setUpdateTime(TimeUtil.getCurrentTime());
            userProject.setUpdateBy(userId);
            userProject.setDeletion("0");
            securityUserProjectService.addUserProject(userProject);
        } else if (userProject.getOpenId() == null) {
            userProject.setOpenId(UUID.randomUUID().toString());
            userProject.setUpdateTime(TimeUtil.getCurrentTime());
            securityUserProjectService.updateUserProject(userProject);
        }
        return userProject;
    }


    public SecurityUserProject addUserProject(String userId, int projCode, String password) {
        SecurityUserProject userProject = securityUserProjectService.getUserProject(userId, projCode);
        String securityKey = SecurityKeyUtil.generateSecurityKey();
        //密码加密
        password = encryptPassword(password, securityKey);
        if (userProject == null) {
            userProject = new SecurityUserProject();
            userProject.setUserId(userId);
            userProject.setProjCode(projCode);
            userProject.setCreateBy(userId);
            userProject.setOpenId(UUID.randomUUID().toString());
            userProject.setCreateTime(TimeUtil.getCurrentTime());
            userProject.setUpdateTime(TimeUtil.getCurrentTime());
            userProject.setUpdateBy(userId);
            userProject.setDeletion("0");
            userProject.setPassword(password);
            userProject.setSecuritykey(securityKey);
            securityUserProjectService.addUserProject(userProject);
        } else if (userProject.getOpenId() == null) {
            userProject.setOpenId(UUID.randomUUID().toString());
            userProject.setUpdateTime(TimeUtil.getCurrentTime());
            securityUserProjectService.updateUserProject(userProject);
        }
        return userProject;
    }

    /**
     * 判断账号是否存在
     *
     * @param userId
     * @return
     */
    public boolean isAccountExists(String userId) {
        return jcUserDao.getUser(userId) != null;
    }

    public UCResponse activeToken(String token) {
        if (token == null) {
            return null;
        }
        String redisInfo = (String) redisTemplate.boundHashOps(token).get(EnumProductKey.GENERAL.name());
        if (redisInfo == null) {
            return new UCResponse(401, "会话已失效");
        }
        JSONObject userInfo = JSONObject.fromObject(redisInfo);

        /**
         * 获取应用token过期时间
         */
        long expiredTime;
        try {
            expiredTime = appMemoryInfo.getExpiredTime(userInfo.optInt(APPCODE));
        } catch (NullPointerException e) {
            log4j.info("用户缓存中不存在应用码");
            expiredTime = 720;
        }
        BoundHashOperations<String, Object, Object> userTokenMap;
        userTokenMap = redisTemplate.boundHashOps(USERTOKEN_CACHE + userInfo.optString(USERID));
        localDateTime = LocalDateTime.now();
        String lastTime = userInfo.optString(LAST_ACTIVE_TIME);
        JSONObject sendMessage = new JSONObject();
        sendMessage.put(APPCODE, userInfo.getInt(APPCODE));
        sendMessage.put(USERID, userInfo.get(USERID));
        // 判断是否发送活跃mq
        sendActivityMessage(userTokenMap, sendMessage);

        userInfo.put(LAST_ACTIVE_TIME, localDateTime.toString());
        if (lastTime.isEmpty()) {
            //lastActiveTime为redis新加字段，为了识别该token何时开始生效或重激活时间，若不存在则添加
            redisTemplate.boundHashOps(token).put(EnumProductKey.GENERAL.name(), userInfo.toString());
            redisTemplate.boundHashOps(token).expire(expiredTime, TimeUnit.HOURS);
            updateTokenMapExpireTime(userTokenMap, expiredTime);
            return new UCResponse(0, "重置token失效时间成功");
        }

        //需要更新token生效的时间
        LocalDateTime updateDate;
        try {
            //最多一天或过期时间的20%更新一次token过期时间
            long lExpireMill = Math.min(24 * 3600 * 1000, (long) (expiredTime * 3600 * 1000 * 0.2));
            updateDate = LocalDateTime.parse(lastTime).plus(lExpireMill, ChronoUnit.MILLIS);
        } catch (DateTimeParseException e) {
            /**
             * 没什么意义，以防万一 若userinfo中时间格式不对可更改。
             */
            lastTime = userInfo.optString(LAST_ACTIVE_TIME);
            updateDate = LocalDateTime.parse(lastTime).plus((long) (expiredTime * 3600 * 1000 * 0.2), ChronoUnit.MILLIS);
        }

        if (expiredTime > 0 && updateDate.compareTo(localDateTime) < 0) {
            //更新token时间
            redisTemplate.boundHashOps(token).put(EnumProductKey.GENERAL.name(), userInfo.toString());
            redisTemplate.boundHashOps(token).expire(expiredTime, TimeUnit.HOURS);
            updateTokenMapExpireTime(userTokenMap, expiredTime);
            return new UCResponse(0, "重置token失效时间成功");
        }
        return new UCResponse(9, "token未到需重置时间");
    }

    /**
     * 更新用户在缓存中存放所有token信息的失效时间
     *
     * @param userTokenMap
     * @param expiredTime
     */
    public void updateTokenMapExpireTime(BoundHashOperations<String, Object, Object> userTokenMap, long expiredTime) {
        LocalDateTime tokenMapExpireTime = localDateTime.plus(userTokenMap.getExpire(), ChronoUnit.SECONDS);
        LocalDateTime appTokenExpireTime = localDateTime.plus(expiredTime * 3600, ChronoUnit.SECONDS);
        if (tokenMapExpireTime.compareTo(appTokenExpireTime) < 0) {
            userTokenMap.put(MAX_EXPIRED_TIME, appTokenExpireTime.toString());
            userTokenMap.expire(expiredTime * 3600 + 10, TimeUnit.SECONDS);
        }
    }


    public void redisHashPut(String hashKey, String key, Object value, Integer expiredTime, TimeUnit timeUnit) {
        redisTemplate.boundHashOps(hashKey).put(key, value);
        if (expiredTime != null && timeUnit != null) {
            redisTemplate.boundHashOps(hashKey).expire(expiredTime, timeUnit);
        }

    }


    /**
     * 根据apptoken获取appCode （在过滤器中使用）
     *
     * @param appToken
     * @return
     */
    public Integer getAppCodeByAppToken(Integer appToken) {
        if (appToken == null || appToken.equals(0)) {
            return 0;
        }
        Integer appCode = null;

        try {
            appCode = (Integer) redisTemplate.opsForValue().get(APP_CACHE + appToken);
        } catch (Exception e) {
            appCode = null;
        }
        if (appCode == null) {
            appCode = securityApplicationMapper.seclectAppCodeByappToken(appToken);
            if (appCode != null) {
                redisTemplate.opsForValue().set(APP_CACHE + appToken, appCode);
                redisTemplate.expire(APP_CACHE + appToken, 1, TimeUnit.DAYS);
            } else {
                appCode = securityApplicationMapper.seclectAppTokenByAppCode(appToken);
                if (appCode != null && appCode != 0) {
                    return 0;
                }
            }
        }

        if (appCode != null) {
            // 返回根据token查出的appCode
            return appCode;
        } else {
            // 原本就是appCode，返回传入的参数
            return appToken;
        }
    }


    /**
     * 判断是否发送用户活跃mq;
     *
     * @param userTokenMap
     * @param loginMessage
     */

    public void sendActivityMessage(BoundHashOperations<String, Object, Object> userTokenMap, JSONObject loginMessage) {
        try {
            boolean sendMassage = false;
            Integer activeAppCode = loginMessage.getInt(APPCODE);
            List<Integer> activityProject = null;
            if (userTokenMap.hasKey(ACTIVITY_DAY)) {
                LocalDate activityDay = LocalDate.parse(userTokenMap.get(ACTIVITY_DAY).toString());
                if (activityDay.isBefore(LocalDate.now())) {
                    // 是今天之前活跃的，设置为今天活跃，并且清空活跃项目表，发送mq
                    activityDay = LocalDate.now();
//                    userTokenMap.put(ACTIVITY_DAY, activityDay);
                    activityProject = new ArrayList<>();
                    activityProject.add(activeAppCode);
                    userTokenMap.put(ACTIVITY_APPLICATIONS, activityProject);
                    userTokenMap.put(ACTIVITY_DAY, LocalDate.now().toString());
                    sendMassage = true;
                } else {
                    // 今日已经激活过 判断是否激活过该项目
                    boolean hasActivity = false;
                    if (userTokenMap.hasKey(ACTIVITY_APPLICATIONS)) {
                        activityProject = (List<Integer>) userTokenMap.get(ACTIVITY_APPLICATIONS);
                        for (Integer anActivityProject : activityProject) {
                            if (anActivityProject.equals(activeAppCode)) {
                                hasActivity = true;
                                break;
                            }
                        }
                    } else {
                        activityProject = new ArrayList<>();
                    }
                    if (!hasActivity) {
                        // 未激活过该项目
                        activityProject.add(activeAppCode);
                        userTokenMap.put(ACTIVITY_APPLICATIONS, activityProject);
                        sendMassage = true;
                    }
                }
            } else {
                activityProject = new ArrayList<>();
                activityProject.add(activeAppCode);
                userTokenMap.put(ACTIVITY_APPLICATIONS, activityProject);
                userTokenMap.put(ACTIVITY_DAY, LocalDate.now().toString());
                sendMassage = true;
            }
            if (sendMassage) {
                Application app = getAppInfoService.getAppByCode(activeAppCode);
                if (app != null){
                    JSONObject msgObject = new JSONObject();
                    msgObject.put(PROJCODE, app.getProj_code());
                    msgObject.put(STATIC_FIELD, CommonService.getAppDeviceMap(app.getApp_device()));
                    rocketMQUtil.activityMessage(msgObject);
                }
            }
        } catch (Exception e) {
            log4j.info("发送mq失败，错误信息：" + e.getMessage());
        }
    }

    public static String getAppDeviceMap(String appDevice){
        String retField = "other";
        switch (appDevice){
            case "Android":
                retField = "android";
                break;
            case "iPhone":
            case "ipad":
            case "iOS":
            case "iPhone-移动":
                retField = "iphone";
                break;
            case "支付宝服务窗":
                retField = "alipay";
                break;
            case "wechat":
            case "Wechat":
            case "微信公众号":
                retField = "wechat";
                break;
            case "Web":
                retField = "web";
                break;
            case "WP8":
            case "微信小程序":
                retField = "other";
                break;
            default:
                break;
        }
        return retField;
    }
}