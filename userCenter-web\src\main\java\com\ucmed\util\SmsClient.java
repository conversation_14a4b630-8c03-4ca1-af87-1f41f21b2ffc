package com.ucmed.util;

import com.ucmed.api.SendSmsApi;
import com.ucmed.dto.MessageBody;
import com.ucmed.dto.MessageResult;

/**
 * 短信平台
 * Created by XXB-QJH-1303 on 2017/6/14.
 */
public class SmsClient {

    private static final String token = "DUiu3yHGnqFzZ8b0YmN";

    public static int sendMessage(String phone, String content) {
        MessageBody messageBody = new MessageBody();
        messageBody.setToken(token);
        messageBody.setPhone(phone);
        messageBody.setContent(content);
        MessageResult result = SendSmsApi.sendMessage(messageBody);
        return result.getResultCode();
    }
}
