package com.ucmed.service;


import com.ucmed.bean.NUCResponse;
import com.ucmed.bean.UCResponse;
import com.ucmed.bean.UserInfo;
import com.ucmed.dto.User;
import com.ucmed.dto.UserPush;
import com.ucmed.exception.UnknownAccountException;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 用户服务接口类
 * Created by QIUJIAHAO on 2017/7/19.
 */
public interface UserService {

    /**
     * 注册
     *
     * @param userId   用户名（如是手机注册则填手机号）
     * @param phone    手机号
     * @param password 密码，明文hash一次后传入
     * @param appCode  应用ID
     * @param roleName 角色名称（传入基础角色名，如患者、普通用户等）
     * @return UCResponse<br>
     * retCode:-3, retInfo:角色不存在<br>
     * retCode:-1, retInfo:应用未注册<br>
     * retCode:0, retInfo:注册成功, param:user_id, openId<br>
     * retCode:1, retInfo:该账号已注册，请使用原账号密码登录, param:user_id, openId<br>
     * retCode:3, retInfo:密码太简单，建议使用大小写字母、数字和特殊字符<br>
     * retCode:4, retInfo:用户名不合法<br>
     * retCode:5, retInfo:手机号不合法<br>
     */
    UCResponse registration(String userId, String phone, String password, int appCode, String roleName);

    /**
     * 注册
     *
     * @param userId   用户名（如是手机注册则填手机号）
     * @param phone    手机号
     * @param password 密码，明文hash一次后传入
     * @param appCode  应用ID
     * @param roleName 角色名称（传入基础角色名，如患者、普通用户等）
     * @param name     用户姓名
     * @param email    用户邮箱
     * @return UCResponse<br>
     * retCode:-3, retInfo:角色不存在<br>
     * retCode:-1, retInfo:应用未注册<br>
     * retCode:0, retInfo:注册成功, param:user_id, openId<br>
     * retCode:1, retInfo:该账号已注册，请使用原账号密码登录, param:user_id, openId<br>
     * retCode:3, retInfo:密码太简单，建议使用大小写字母、数字和特殊字符<br>
     * retCode:4, retInfo:用户名不合法<br>
     * retCode:5, retInfo:手机号不合法<br>
     * retCode:8, retInfo:邮箱不合法<br>
     */
    UCResponse registration(String userId, String phone, String password, int appCode, String roleName, String name, String email);

    /**
     * 短信注册
     *
     * @param userId   用户名（如是手机注册则填手机号）
     * @param phone    手机号
     * @param password 密码，明文hash一次后传入
     * @param appCode  应用ID
     * @param roleName 角色名称（传入基础角色名，如患者、普通用户等）
     * @param msgCode  短信验证码
     * @return UCResponse<br>
     * retCode:-3, retInfo:角色不存在<br>
     * retCode:-2, retInfo:验证码不正确<br>
     * retCode:-1, retInfo:应用未注册<br>
     * retCode:0, retInfo:注册成功, param:user_id, openId<br>
     * retCode:1, retInfo:该账号已注册，请使用原账号密码登录, param:user_id, openId<br>
     * retCode:3, retInfo:密码太简单，建议使用大小写字母、数字和特殊字符<br>
     * retCode:4, retInfo:用户名不合法<br>
     * retCode:5, retInfo:手机号不合法<br>
     */
    UCResponse registration(String userId, String phone, String password, int appCode, String roleName, String msgCode);

    /**
     * 密码登录
     *
     * @param userId   用户名或手机号
     * @param password 密码，明文hash一次后传入
     * @param appCode  应用ID
     * @param roleName 角色名称
     * @return UCResponse<br>
     * retCode:-1, retInfo:应用未注册<br>
     * retCode:0, retInfo:登录成功, param:user_id（用户唯一标识）, token, realName（真实姓名）, idCard（身份证号码）, pushId（推送ID）, openId<br>
     * retCode:1, retInfo:密码错误<br>
     * retCode:2, retInfo:账号被锁定,请联系管理员<br>
     * retCode:4, retInfo:账号未注册<br>
     * retCode:5, retInfo:用户未授权，是否确定使用该应用？, param:user_id, phone<br>
     * retCode:6, retInfo:角色未授权<br>
     */
    UCResponse login(String userId, String password, int appCode, String roleName);

    /**
     * 短信登录
     *
     * @param phone    手机号
     * @param msgCode  短信验证码
     * @param appCode  应用ID
     * @param roleName 角色名称
     * @return UCResponse<br>
     * retCode:-2, retInfo:验证码不正确<br>
     * retCode:-1, retInfo:应用未注册<br>
     * retCode:0, retInfo:登录成功, param:user_id（用户唯一标识）, token, realName（真实姓名）, idCard（身份证号码）, pushId（推送ID）, openId<br>
     * retCode:2, retInfo:账号被锁定,请联系管理员<br>
     * retCode:4, retInfo:账号未注册<br>
     * retCode:5, retInfo:用户未授权，是否确定使用该应用？, param:user_id, phone<br>
     * retCode:6, retInfo:角色未授权<br>
     */
    UCResponse smsLogin(String phone, String msgCode, int appCode, String roleName);

    /**
     * 修改密码
     *
     * @param newPwd 新密码，明文hash一次后传入
     * @param oldPwd 原密码，明文hash一次后传入
     * @param token  token
     * @return UCResponse<br>
     * retCode:0, retInfo:修改密码成功<br>
     * retCode:1, retInfo:新密码太简单，建议使用大小写字母、数字和特殊字符<br>
     * retCode:2, retInfo:修改密码失败，旧密码错误<br>
     * retCode:3, retInfo:账号未注册<br>
     * retCode:401, retInfo:会话已失效<br>
     */
    UCResponse changePwd(String newPwd, String oldPwd, String token);

    /**
     * 重置密码
     * 将废弃
     *
     * @param phone       手机号
     * @param msgCode     短信验证码
     * @param newPassword 新密码，明文hash一次后传入
     * @return UCResponse<br>
     * retCode:-1, retInfo:验证码不正确<br>
     * retCode:0, retInfo:重置密码成功<br>
     * retCode:1, retInfo:密码太简单，建议使用大小写字母、数字和特殊字符<br>
     * retCode:2, retInfo:账号未注册<br>
     */
    UCResponse reInputPassword(String phone, String msgCode, String newPassword);

    /**
     * 重置密码
     *
     * @param phone       手机号
     * @param msgCode     短信验证码
     * @param newPassword 新密码，明文hash一次后传入
     * @return UCResponse<br>
     * retCode:-1, retInfo:验证码不正确<br>
     * retCode:0, retInfo:重置密码成功<br>
     * retCode:1, retInfo:密码太简单，建议使用大小写字母、数字和特殊字符<br>
     * retCode:2, retInfo:账号未注册<br>
     */
    UCResponse reInputPassword(int appCode, String phone, String msgCode, String newPassword);

    /**
     * 退出登录
     *
     * @param token token
     * @return UCResponse<br>
     * retCode:0, retInfo:退出登录成功<br>
     */
    UCResponse logout(String token);

    /**
     * 修改手机号
     *
     * @param token    token
     * @param msgCode  短信验证码
     * @param newPhone 新手机号
     * @return UCResponse<br>
     * retCode:-1, retInfo:验证码不正确<br>
     * retCode:0, retInfo:手机号修改成功<br>
     * retCode:2, retInfo:新手机号不合法<br>
     * retCode:3, retInfo:新手机号已注册<br>
     * retCode:401, retInfo:会话已失效<br>
     */
    UCResponse changePhone(String token, String msgCode, String newPhone);

    /**
     * 修改手机号
     *
     * @param openId   用户中心数据库表security_user_project表中的openId
     * @param msgCode  短信验证码
     * @param newPhone 新手机号
     * @return UCResponse<br>
     * retCode:-1, retInfo:验证码不正确<br>
     * retCode:0, retInfo:手机号修改成功<br>
     * retCode:2, retInfo:新手机号不合法<br>
     * retCode:3, retInfo:新手机号已注册<br>
     * retCode:401, retInfo:会话已失效<br>
     */
    UCResponse changePhoneByOpenId(String openId, String msgCode, String newPhone);

    /**
     * 授权
     *
     * @param userId   用户名或手机号
     * @param appCode  应用ID
     * @param roleName 角色名称
     * @return UCResponse<br>
     * retCode:-3, retInfo:角色不存在<br>
     * retCode:-1, retInfo:应用未注册<br>
     * retCode:1, retInfo:账号未注册<br>
     * retCode:0, retInfo:授权成功<br>
     */
    UCResponse setPermission(String userId, int appCode, String roleName);

    /**
     * 发送短信验证码
     *
     * @param phone   手机号
     * @param appCode 应用ID
     * @param type    验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号
     * @param picCode 图形验证码
     * @return UCResponse<br>
     * retCode:641, retInfo:手机号不合法<br>
     * retCode:641, retInfo:应用未注册<br>
     * retCode:641, retInfo:账号已注册<br>
     * retCode:641, retInfo:账号未注册<br>
     * retCode:643, retInfo:type类型错误<br>
     * retCode:642, retInfo:获取验证码失败<br>
     * retCode:0, retInfo:获取验证码成功，请查看您的手机短信<br>
     */
    UCResponse sendMsgCode(String phone, int appCode, String type, String picCode);

    /**
     * 发送短信验证码
     * 不需要图形验证码 - 2017-8-22 为了兼容更新
     *
     * @param phone   手机号
     * @param appCode 应用ID
     * @param type    验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号
     * @return UCResponse<br>
     * retCode:641, retInfo:手机号不合法<br>
     * retCode:641, retInfo:应用未注册<br>
     * retCode:641, retInfo:账号已注册<br>
     * retCode:641, retInfo:账号未注册<br>
     * retCode:643, retInfo:type类型错误<br>
     * retCode:642, retInfo:获取验证码失败<br>
     * retCode:0, retInfo:获取验证码成功，请查看您的手机短信<br>
     */
    UCResponse sendMsgCode(String phone, int appCode, String type);

    /**
     * 校验短信验证码
     *
     * @param phone             手机号
     * @param msgCode           短信验证码
     * @param type              验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号
     * @param invalidWhenVerify 验证后立即过期
     * @return UCResponse<br>
     * retCode:0, retInfo:验证成功<br>
     * retCode:-1, retInfo:验证码失效或者输入不正确,请重新获取<br>
     * retCode:-2, retInfo:验证验证码失败，电话号码不正确<br>
     */
    UCResponse verifyMessageCode(String phone, String msgCode, String type, boolean invalidWhenVerify);

    /**
     * 生成图形验证码
     *
     * @param phone 手机号
     * @param type  验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用
     * @return UCResponse<br>
     * retCode:641, retInfo:手机号不合法<br>
     * retCode:641, retInfo:应用未注册<br>
     * retCode:641, retInfo:账号已注册<br>
     * retCode:641, retInfo:账号未注册<br>
     * retCode:643, retInfo:type类型错误<br>
     * retCode:0, retInfo:成功获取图片验证码, param: validate_code_url<br>
     */
    UCResponse generatePictureValidateCode(String phone, String type);

    /**
     * 生成图形验证码
     *
     * @param phone  手机号
     * @param type   验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用
     * @param length 验证码长度
     * @return UCResponse<br>
     * retCode:641, retInfo:手机号不合法<br>
     * retCode:641, retInfo:应用未注册<br>
     * retCode:641, retInfo:账号已注册<br>
     * retCode:641, retInfo:账号未注册<br>
     * retCode:643, retInfo:type类型错误<br>
     * retCode:0, retInfo:成功获取图片验证码, param: validate_code_url<br>
     */
    UCResponse generatePictureValidateCode(String phone, String type, int length);

    /**
     * 生成图形验证码
     *
     * @param phone   手机号
     * @param appCode 应用ID
     * @param type    验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用
     * @param length  验证码长度
     * @return UCResponse<br>
     * retCode:641, retInfo:手机号不合法<br>
     * retCode:641, retInfo:应用未注册<br>
     * retCode:641, retInfo:账号已注册<br>
     * retCode:641, retInfo:账号未注册<br>
     * retCode:643, retInfo:type类型错误<br>
     * retCode:0, retInfo:成功获取图片验证码, param: validate_code_url<br>
     */
    UCResponse generatePictureValidateCode(String phone, int appCode, String type, int length);

    /**
     * 校验图形验证码
     *
     * @param phone   手机号
     * @param type    验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用
     * @param picCode 图形验证码
     * @return UCResponse<br>
     * retCode:0, retInfo:图形验证码验证成功<br>
     * retCode:-1, retInfo:图形验证码过期，请刷新后重试, param: validate_code_url<br>
     * retCode:-2, retInfo:验证码输入错误，请重新输入, param: validate_code_url<br>
     */
    UCResponse verifyPictureValidateCode(String phone, String type, String picCode);

    /**
     * 获取用户个人资料
     *
     * @param token token
     * @return UCResponse<br>
     * retCode:0, retInfo:查询成功, param: userInfo对象<br>
     * retCode:401, retInfo: 会话已失效<br>
     */
    UCResponse getUserInfo(String token);

    /**
     * 更新用户个人资料
     *
     * @param token    token
     * @param userInfo 用户信息对象
     * @return UCResponse<br>
     * retCode:0, retInfo:更新成功<br>
     * retCode:401, retInfo: 会话已失效<br>
     */
    UCResponse updateUserInfo(String token, UserInfo userInfo);

    /**
     * 更新用户个人资料
     *
     * @param openId   用户中心数据库表security_user_project表中的openId
     * @param userInfo 用户信息对象
     * @return UCResponse<br>
     * retCode:0, retInfo:更新成功<br>
     * retCode:401, retInfo: 会话已失效<br>
     */
    UCResponse updateUserInfoByOpenId(String openId, UserInfo userInfo);

    /**
     * 根据openId查询用户
     *
     * @param openId openId
     * @return User
     * @throws UnknownAccountException 账号未注册
     */
    User getUserByOpenId(String openId) throws UnknownAccountException;

    /**
     * 根据openId查询推送信息
     *
     * @param openIds openId列表
     * @return UserPush
     * @throws UnknownAccountException
     */
    List<UserPush> getUserPushByOpenId(List<String> openIds) throws UnknownAccountException;


    /**
     * 验证用户token有效性
     *
     * @param token
     * @return
     */

    UCResponse verifyUserToken(String token);

    /**
     * 获取用户所有的卓健openid
     *
     * @param token
     * @return UCResponse<br>
     * retCode:401, retInfo:会话已失效<br>
     * retCode:-2, retInfo:用户未授权任何应用<br>
     * retCode:0, retInfo:获取成功, param: validate_code_url<br>
     */
    UCResponse getOpenIdByToken(String token);

    /**
     * 获取用户所有的卓健openid
     *
     * @param token
     * @param appCode
     * @return UCResponse<br>
     */
    UCResponse getOpenIdByToken(int appCode, String token);

    /**
     * 重新获取app的token过期时间（后台用）
     *
     * @return UCResponse<br>
     */
    UCResponse resetCache(String password);

    /**
     * 获取该项目下的一段时间内注册的手机的excel文件下载地址
     *
     * @param startTime
     * @param endTime
     * @param appCode
     * @return
     */
    UCResponse downloadRegisteredPhoneExcel(String startTime, String endTime, Integer appCode, Integer mouth);

    /**
     * 分页查询注册的手机号（按时间降序）
     *
     * @param pageNum
     * @param pageSize
     * @param appCode
     * @return retCode:0, retInfo:获取成功, param:PageResultBean
     */
    UCResponse listRegisteredPhone(Integer pageNum, Integer pageSize, String startTime, String endTime, Integer appCode, Integer mouth);

    /**
     * 获取该项目下的一段时间内注册的手机的excel文件
     *
     * @param startTime
     * @param endTime
     * @param projCode
     * @param response
     */
    void downloadRegisteredPhoneExcel(String startTime, String endTime, String projCode, HttpServletResponse response);

    /**
     * 查询一段时间内注册的手机号数量
     *
     * @param startTime
     * @param endTime
     * @param appCode
     * @param pageSize
     * @param mouth
     * @return
     */
    UCResponse countRegisteredPhone(String startTime, String endTime, Integer appCode, Integer pageSize, Integer mouth);

    /**
     * 检查用户是否在该项目中
     * @param phone
     * @param appCode
     * @return
     */
    NUCResponse<Boolean> checkUserInProject(String phone, Integer appCode);
}
