package com.ucmed.dao;

import java.util.List;

import com.ucmed.bean.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

@Repository("getInfoDao")
@SuppressWarnings({ "unchecked", "rawtypes" })
public class GetInfoDaoImpl implements GetInfoDao {
	@Autowired
	private JdbcTemplate jdbcTemplate;

	/**
	 * 返回用户详细信息
	 */
	public List<UserInfo> getUserInfo(String user_id) {
		String sql = "select user_id,the_name,sex,card_type,"
				+ "card_id,medicare,medicare_id,city_id,address,"
				+ "w_chat,mobile,e_mail,birthday,source_ids,create_time,update_time "
				+ "from jc_user_info where user_id='" + user_id + "'";
		return jdbcTemplate.query(sql,new BeanPropertyRowMapper(UserInfo.class));
	}
	
	/**
	 * 获取医生信息
	 */
	public List<DoctorInfo> getDoctorInfo(String user_id) {
		String sql = "select user_id,ucmed_hospital_id,hospital_org_code,"
				+ "section_name,common_section_no,work_no "
				+ "from jc_doctor_info where user_id='" + user_id + "'";
		return jdbcTemplate.query(sql,new BeanPropertyRowMapper(DoctorInfo.class));
	}

	/**
	 * 科室信息
	 */
	public List<Section> getSectionInfo(String section_no) {
		String sql = "select section_no,section_name,fid,description,remark,rank,is_delete "
				+ "from jc_common_section where section_no='" + section_no + "'";
		return jdbcTemplate.query(sql,new BeanPropertyRowMapper(Section.class));
	}
	
	/**
	 * 医院信息
	 */
	public List<Hospital> getHospitalInfo(String ucmed_hospital_id) {
		String sql = "select ucmed_hospital_id,org_code,hospital_name,short_name,"
				+ "spell_code,hospital_isuure_code,grade_of_unit,level_of_unit,"
				+ "org_type_code,org_class_code,area_code,province,city,area,"
				+ "econ_code,address,street,zip_code,phone,e_mail,domain,"
				+ "create_date,set_unit_code,sub_ordinate,fund,deputy,is_nation,"
				+ "is_sub_unit,subunit_num,subcommunity_num,import_subjection,"
				+ "strong_subjection,source_id,create_time,update_time "
				+ "from jc_hospital where ucmed_hospital_id='" + ucmed_hospital_id + "'";
		return jdbcTemplate.query(sql,new BeanPropertyRowMapper(Hospital.class));
	}
	
	/**
	 * 数据来源信息
	 */
	public List<PlatformSource> getPlatformInfo(String source_id) {
		String sql = "select source_id,source_name,source_url,source_token "
				+ "from jc_platform_source where source_id='" + source_id + "'";
		return jdbcTemplate.query(sql,new BeanPropertyRowMapper(PlatformSource.class));
	}
	
	/**
	 * 医院科室信息
	 */
	public List<HospitalSection> getHospitalSectionInfo(String ucmed_hospital_id) {
		String sql = "select ucmed_hospital_id,hospital_name,org_code,section_no,section_name "
				+ "from jc_hospital_section where ucmed_hospital_id='" + ucmed_hospital_id + "'";
		return jdbcTemplate.query(sql,new BeanPropertyRowMapper(HospitalSection.class));
	}
	
	/**
	 * 就诊人信息
	 */
	public List<Patient> getPatientInfo(String user_id) {
		String sql = "select patient_id,patient_name,patient_sex,patient_birthday,patient_card_type,"
				+ "patient_card_id,patient_medicare,patient_medicare_id,patient_address,"
				+ "patient_w_chat,patient_mobile,patient_e_mail,create_time,update_time "
				+ "from jc_user_patient where user_id='" + user_id + "'";
		return jdbcTemplate.query(sql,new BeanPropertyRowMapper(Patient.class));
	}
	
	/**
	 * 用户登录信息
	 */
	public List<UC_UserInfo> getUC_UserInfo(String user_id) {
		String sql = "select uid,user_id,password,phone,latestTime,failNum,create_time,login_times,pass_change_time,lock,app_code,token,token_time"
				+ "from jc_user where user_id='" + user_id + "'";
		return jdbcTemplate.query(sql,new BeanPropertyRowMapper(UC_UserInfo.class));
	}
}
