package com.ucmed.mapper;

import com.ucmed.bean.JcUserInfo;
import com.ucmed.bean.UserInfo;
import com.ucmed.dto.UserInfoExcelDTO;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.common.Mapper;

import javax.naming.Name;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/10/24 15:26
 */
public interface JcUserInfoMapper extends Mapper<UserInfo> {

    @Select({
            "SELECT jc_user_info.user_id, jc_user_info.mobile ",
            "FROM jc_user_info INNER JOIN jc_user_third_party ",
            "ON jc_user_info.user_id = jc_user_third_party.user_id ",
            "WHERE jc_user_third_party.open_id = #{openId} AND jc_user_third_party.create_by = #{appCode} " +
            "and jc_user_third_party.deletion = '0'"
    })
    @Results({
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "phone", property = "phone", jdbcType = JdbcType.VARCHAR)
    })
    JcUserInfo getUserInfoByOpenId(@Param("openId") String openId, @Param("appCode") String appCode);

    /**
     * jc_user表后期若有用，可加
     *
     * @param startTime
     * @param endTime
     * @param projCode
     * @return
     */
    @Select({
            "SELECT " +
                    "jui.mobile," +
                    "sup.create_time " +
                    "FROM " +
                    "jc_user_info jui, " +
                    "security_user_project sup " +
                    "WHERE " +
                    "sup.create_time > #{startTime} " +
                    "AND sup.create_time < #{endTime} " +
                    "AND sup.user_id = jui.user_id " +
                    "AND sup.proj_code = #{projCode} "
    })
    @Results(value = {
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "mobile", property = "mobile", jdbcType = JdbcType.VARCHAR)
    })
    List<UserInfoExcelDTO> listUserInfoWithinTimeByProjCode(@Param("startTime") String startTime,
                                                            @Param("endTime") String endTime,
                                                            @Param("projCode") Integer projCode);

    /**
     * 分页获取一段时间的注册信息
     *
     * @param startTime
     * @param endTime
     * @param projCode
     * @param pageNum
     * @param pageSize
     * @return
     */
    @Select({
            "SELECT " +
                    "jui.mobile, " +
                    "sup.create_time " +
                    "FROM " +
                    "jc_user_info jui, " +
                    "security_user_project sup " +
                    "WHERE " +
                    "sup.user_id = jui.user_id " +
                    "AND sup.user_project_id IN ( " +
                    "SELECT " +
                    "user_project_id " +
                    "FROM " +
                    "security_user_project " +
                    "WHERE " +
                    "create_time >= #{startTime} " +
                    "AND create_time <= #{endTime} " +
                    "AND proj_code = #{projCode} " +
                    "ORDER BY create_time " +
                    "LIMIT #{pageSize} OFFSET #{pageNum} " +
                    ") " +
                    "ORDER BY sup.create_time"
    })
    @Results(value = {
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.VARCHAR),
            @Result(column = "mobile", property = "mobile", jdbcType = JdbcType.VARCHAR)
    })
    List<UserInfoExcelDTO> listUserInfoWithinTimeByProjCodeLimitPage(@Param("startTime") String startTime,
                                                                     @Param("endTime") String endTime,
                                                                     @Param("projCode") Integer projCode,
                                                                     @Param("pageNum") Integer pageNum,
                                                                     @Param("pageSize") Integer pageSize);


    /**
     * 查找一段时间内某项目的注册量
     *
     * @param startTime
     * @param endTime
     * @param projCode
     * @return
     */
    @Select({
            "SELECT " +
                    "count(user_project_id) " +
                    "FROM " +
                    "security_user_project " +
                    "WHERE " +
                    "create_time > #{startTime} " +
                    "AND create_time < #{endTime} " +
                    "AND proj_code = #{projCode} "
    })
    Integer countRegisteredPhone(@Param("startTime") String startTime,
                                 @Param("endTime") String endTime,
                                 @Param("projCode") Integer projCode);

}
