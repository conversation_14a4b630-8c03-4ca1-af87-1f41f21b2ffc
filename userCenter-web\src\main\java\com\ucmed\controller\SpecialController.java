package com.ucmed.controller;

import cn.hutool.crypto.SecureUtil;
import cn.ucmed.common.ratelimit.DailySendMuchException;
import cn.ucmed.common.ratelimit.FrequentlyException;
import cn.ucmed.common.ratelimit.SendSmsFailException;
import com.ucmed.bean.NUCResponse;
import com.ucmed.bean.controllerbean.CancelAuthorizationParam;
import com.ucmed.bean.retbean.UnderlineUCResponse;
import com.ucmed.exception.BusinessException;
import com.ucmed.specialservice.SpecialService;
import net.sf.json.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;


@RestController
@RequestMapping("/special")
@ApiIgnore
public class SpecialController {
    private static Logger log4j = Logger.getLogger(SpecialController.class.getName());
    @Autowired
    SpecialService specialService;
    String cancelAuthorizationKey = "e97469abb925e4de7751f3e2bfa5306b";
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public ResponseEntity<String> login(String phone, Integer appCode, String roleName) {
        if (appCode == null) {
            appCode = 0;
        }
        String responseJson;
        responseJson = JSONObject.fromObject(specialService.login(appCode, phone, roleName)).toString();
        return createResponseEntity(responseJson);

    }

    @RequestMapping(value = "/cancelAuthorization", method = RequestMethod.POST)
    public ResponseEntity<UnderlineUCResponse> cancelAuthorization(@RequestBody CancelAuthorizationParam cancelAuthorizationParam) {
        JSONObject paramJson = JSONObject.fromObject(cancelAuthorizationParam);
        log4j.info("来注销了！！！！：" + paramJson.toString());
        String strEncrypt = null;
        String sourceStr = cancelAuthorizationParam.getPhone() + cancelAuthorizationParam.getSource()
                                + cancelAuthorizationParam.getTimestamp() + cancelAuthorizationKey;
        try {
            if (StringUtils.isEmpty(sourceStr)) {
                log4j.info("签名错误了！！！！：" + paramJson.toString());
                return createResponseEntity(UnderlineUCResponse.createByError(0, "签名错误"));
            }
            strEncrypt = SecureUtil.md5(sourceStr).toUpperCase();
//            MessageDigest md = MessageDigest.getInstance("MD5");
//            md.update(sourceStr.getBytes());
//            strEncrypt = new BigInteger(1, md.digest()).toString(16).toUpperCase();
//            char[] charArray = sourceStr.toCharArray();
//            byte[] byteArray = new byte[charArray.length];
//            for (int j = 0; j < charArray.length; j++) {
//                byteArray[j] = (byte) charArray[j];
//            }
//            byte[] md5Bytes = md.digest(byteArray);
//            StringBuffer hexValue = new StringBuffer();
//            for (int j = 0; j < md5Bytes.length; j++){
//                int val = ((int) md5Bytes[j]) & 0xff;
//                if (val < 16) {
//                    hexValue.append("0");
//                }
//                hexValue.append(Integer.toHexString(val));
//            }
//            strEncrypt = hexValue.toString().toUpperCase();
        } catch (Exception e) {
            e.printStackTrace();
        }
        if (strEncrypt == null || !strEncrypt.equals(cancelAuthorizationParam.getSign())) {
            log4j.info("签名错误了！！！！：" + paramJson.toString());
            return createResponseEntity(UnderlineUCResponse.createByError(0, "签名错误"));
        }
        UnderlineUCResponse nucResponse = null;
        try {
            nucResponse = specialService.cancelAuthorization(cancelAuthorizationParam.getProject_id(),
                                            cancelAuthorizationParam.getPhone(), cancelAuthorizationParam.getApp_name());
        } catch (BusinessException e) {
            e.printStackTrace();
            log4j.info("短信发送失败：" + paramJson.toString());
            return createResponseEntity(UnderlineUCResponse.createByError(0,"发送短信失败"));
        } catch (SendSmsFailException e) {
            log4j.error("SendSmsFailException--Deny", e);
            if (e instanceof DailySendMuchException) {
                return createResponseEntity(UnderlineUCResponse.createByError(0,"已获取20次短信，当天无法再获取，注销失败"));
            }
            if (e instanceof FrequentlyException) {
                return createResponseEntity(UnderlineUCResponse.createByError(0,"您已获取3次短信，请2分钟后重新注销"));
            }
            return createResponseEntity(UnderlineUCResponse.createByError(0,"发送短信失败"));
        }
        log4j.info("注销结束：" + JSONObject.fromObject(nucResponse).toString());
        return createResponseEntity(nucResponse);
    }

//    @RequestMapping(value = "/checkDeleteUser", method = RequestMethod.GET)
//    public ResponseEntity<NUCResponse> checkDeleteUser(String phone, String projectId, String appName) {
//        NUCResponse responseJson = null;
//        try {
//            responseJson = specialService.checkDeleteUser(phone, projectId, appName);
//        } catch (BusinessException e) {
//            e.printStackTrace();
//        }
//        return createResponseEntity(responseJson);
//    }


//    @RequestMapping(value = "/infoAuthorization", method = RequestMethod.GET)
//    public ResponseEntity<String> infoAuthorization(Integer projCode, String phones) {
//        specialService.infoAuthorization(projCode, phones);
//        return createResponseEntity("111");
//    }

    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }
}
