package com.ucmed.controller;

import com.ucmed.bean.Permission;
import com.ucmed.exception.BusinessException;
import com.ucmed.service.PermissionService;
import io.swagger.annotations.*;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
@Api(value = "授权", description = "授权接口")
@RestController
public class PermissionController {
    @Autowired
    PermissionService permissionService;


    @ApiOperation(
            value = "获取授权",
            notes = "获取授权\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = 3, message = "该账号未注册"),
            @ApiResponse(code = -1, message = "应用未注册"),
    })
    @RequestMapping(value = "/permission/getPermission", method = RequestMethod.GET)
    public ResponseEntity<String> getPermission(String userId, Integer appCode) throws BusinessException {
        String responseJson = JSONObject.fromObject(permissionService.getPermission(userId, appCode)).toString();
        return createResponseEntity(responseJson);
    }


    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }

}
