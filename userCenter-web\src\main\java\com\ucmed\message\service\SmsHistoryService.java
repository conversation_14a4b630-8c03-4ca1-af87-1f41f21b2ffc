package com.ucmed.message.service;

import cn.ucmed.common.util.JSONUtils;
import cn.ucmed.common.util.PaginationResult;
import com.ucmed.message.dao.SmsHistoryMapper;
import com.ucmed.message.model.SMSHistory;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.List;

public class SmsHistoryService {

    @Autowired
    private SmsHistoryMapper smsHistoryMapper;

    /*
     * 获取短信列表
     */
    public JSONObject getSMSHistoryList(JSONObject obj) {
        Long pageNo = obj.getLong("pageNo");
        Long pageSize = obj.getLong("pageSize");
        Long count = smsHistoryMapper.countAll();
        PaginationResult<SMSHistory> res = new PaginationResult<SMSHistory>();
        Long pageCount = count % pageSize > 0 ? count / pageSize + 1 : count
                / pageSize;

        List<SMSHistory> msgFlowList = smsHistoryMapper.getSMSHistoryList((pageNo - 1L)
                * pageSize, pageSize);
        res.setList(msgFlowList);
        res.setPageCount(pageCount);
        res.setTotalCount(count);

        return JSONUtils.modelToJson(res);
    }

    /*
     * 根据电话号码查询短信列表
     */
    public JSONObject getSMSHistoryListByKeywords(JSONObject obj) {
        Long pageNo = obj.getLong("pageNo");
        Long pageSize = obj.getLong("pageSize");
        String key = obj.getString("key");
        Long count = smsHistoryMapper.countAllByKeywords(key);
        PaginationResult<SMSHistory> res = new PaginationResult<SMSHistory>();
        Long pageCount = count % pageSize > 0 ? count / pageSize + 1 : count
                / pageSize;

        List<SMSHistory> msgFlowList = smsHistoryMapper.
                getSMSHistoryByKeywords((pageNo - 1L) * pageSize, pageSize, key);
        res.setList(msgFlowList);
        res.setPageCount(pageCount);
        res.setTotalCount(count);

        return JSONUtils.modelToJson(res);
    }

}
