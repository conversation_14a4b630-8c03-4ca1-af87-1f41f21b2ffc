package com.ucmed.bean;

import cn.ucmed.common.cache.ClientSession;
import com.ucmed.authc.AuthenticationToken;
import com.ucmed.authc.EnumProductKey;

/**
 * 掌上医院用 token
 * Created by <PERSON> on 2017/1/17.
 */
public class HospitalToken implements AuthenticationToken {

    private static final long serialVersionUID = 964251951446159696L;

    private String tokenKey;

    private ClientSession value;

    public HospitalToken(String tokenKey) {
        this.tokenKey = tokenKey;
    }

    public HospitalToken(String tokenKey, ClientSession value) {
        this.tokenKey = tokenKey;
        this.value = value;
    }

    @Override
    public String getTokenKey() {
        return tokenKey;
    }

    @Override
    public Integer getTimeOut() {
        return null;
    }

    @Override
    public EnumProductKey getProdKey() {
        return EnumProductKey.HOSPITAL_ONLINE;
    }

    @Override
    public Object getValue() {
        return value;
    }


}
