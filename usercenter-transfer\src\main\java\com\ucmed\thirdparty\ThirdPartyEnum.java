package com.ucmed.thirdparty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2017/11/14 15:41
 */
public enum ThirdPartyEnum implements Serializable {

    /**
     * 微信
     */
    WX("1"),
    /**
     * 企业微信
     */
    WORK_WX("2"),
    /**
     * 支付宝
     */
    ALIPAY("3"),
    /**
     * 其他
     */
    OTHER("4"),

    /**
     * 小程序
     */
    MINI_PROGRAM("11"),


    /**
     * 药研云小程序
     */
    GCP_MINI_PROGRAM("88"),


    /**
     * 药研云公众号
     */
    GCP_WECHAT_OFFICIAL_ACCOUNT("89");


    private String key;

    ThirdPartyEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }
}
