package com.ucmed.transfer;

import cn.ucmed.common.constants.GlobalConstants;
import com.ucmed.bean.*;
import com.ucmed.bean.retbean.UserBindInfoVO;
import com.ucmed.common.constant.CommonConstant;
import com.ucmed.common.service.CommonService;
import com.ucmed.dao.JcUserDao;
import com.ucmed.dto.UserBindInfoDTO;
import com.ucmed.service.*;
import com.ucmed.thirdparty.ThirdPartyEnum;
import com.ucmed.thirdparty.ThirdPartyService;
import com.ucmed.util.GenerateRandomString;
import net.sf.json.JSONObject;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 第三方用户接口实现类
 *
 * <AUTHOR>
 * @date 2017/11/14 16:06
 */
@Service
@Transactional
public class ThirdPartyServiceImpl extends CommonService implements ThirdPartyService {

    private static Logger log = Logger.getLogger(ThirdPartyServiceImpl.class.getName());

    @Autowired
    private JcUserThirdPartyService jcUserThirdPartyService;
    @Autowired
    private JcUserDao userDao;
    @Autowired
    private Login login;
    @Autowired
    private UserService userService;
    @Autowired
    private WhiteAndBlackListService whiteAndBlackListService;
    @Autowired
    private JcUserAccountService jcUserAccountService;
    @Autowired
    CommonService commonService;

    @Override
    public UCResponse bind(String phone, String openId, int appCode, String roleName, String thirdPartyType) {
        log.info("第三方用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType);
        if (jcUserThirdPartyService.isThridPartyUserExists(openId)) {
            return new UCResponse(1, "用户已绑定，请直接登录");
        }

        if (IsThirdPartyTypeError(thirdPartyType.trim())) {
            return new UCResponse(EnumRetCode.THIRD_PARTY_TYPE_ERROR);
        }

        UC_UserInfo user = userDao.getUser(phone);
        String userId;
        if (user == null) {
            UCResponse response = userService.registration(phone, phone, UUID.randomUUID().toString(), appCode, roleName);
            if (6 == response.getRetCode()) {
                // 根据场景不同，提示语相应改变
                response.setRetInfo("此用户不在白名单内，无法绑定");
            }
            if (7 == response.getRetCode()) {
                response.setRetInfo("此用户在黑名单内，无法绑定");
            }
            if (0 != response.getRetCode()) {
                log.info("第三方用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType + " " + response.getRetInfo());
                return new UCResponse(500, response.getRetInfo());
            } else {
                JSONObject param = JSONObject.fromObject(response.getParam());
                userId = param.getString("user_id");
            }
        } else {
            if (!whiteAndBlackListService.isPassWhitList(appCode, phone)) {
                return new UCResponse(6, "此用户不在白名单内，无法绑定");
            }

            if (!whiteAndBlackListService.isPassBlackList(appCode, phone)) {
                return new UCResponse(7, "此用户在黑名单内，无法绑定");
            }

            userId = user.getUser_id();
            UCResponse response = userService.setPermission(userId, appCode, roleName);
            if (0 != response.getRetCode()) {
                log.info("第三方用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType + " " + response.getRetInfo());
                return new UCResponse(500, response.getRetInfo());
            }
        }
        JcUserThirdParty jcUserThirdParty = new JcUserThirdParty();
        jcUserThirdParty.setOpenId(openId);
        jcUserThirdParty.setUserId(userId);
        jcUserThirdParty.setThirdPartyType(thirdPartyType);
        jcUserThirdParty.setCreateBy(String.valueOf(appCode));
        jcUserThirdParty.setUpdateBy(String.valueOf(appCode));
        jcUserThirdPartyService.save(jcUserThirdParty);
        log.info("第三方用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType + " 绑定成功");
        return new UCResponse(0, "绑定成功");
    }

    @Override
    public UCResponse login(String openId, int appCode, String roleName) {
        log.info("第三方用户登录:openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName);
        JcUserThirdParty thirdPartyUser = jcUserThirdPartyService.getUserByOpenId(openId);
        if (thirdPartyUser == null) {
            return new UCResponse(404, "用户未绑定");
        }
        String userId = thirdPartyUser.getUserId();
        UCResponse response = userService.setPermission(userId, appCode, roleName);
        if (0 != response.getRetCode()) {
            return new UCResponse(500, response.getRetInfo());
        }
        response = login.login(userId, null, appCode, roleName, true);
        if (0 != response.getRetCode()) {
            return new UCResponse(500, response.getRetInfo());
        } else {
            log.info("第三方用户登录:openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyUser.getThirdPartyType() + " 登录成功");
//            log.info("第三方用户登录:openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyUser.getThirdPartyType() + " 登录成功" + "  测试用：" + response.getParam());
            return new UCResponse(0, "登录成功", response.getParam());
        }
    }

    @Override
    public UCResponse bind(String id, int appCode, String roleName, String thirdPartyType) {
        log.info("第三方用户绑定:openId=" + id + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType);
        if (StringUtils.length(id) >= 50) {
            return new UCResponse(500, "id长度超过50字节");
        }
        if (jcUserAccountService.isAccountIdExists(id, appCode, roleName)) {
            JcUserAccount jcUserAccount = jcUserAccountService.getJcUserAccount(id, appCode, roleName);
            /*此处为了rubiku代码不改动写的逻辑，如果此id已经注册返回用户的信息*/
            return new UCResponse(0, "用户已绑定", jcUserAccount);
        }

        JcUserAccount jcUserAccount = new JcUserAccount();
        String userId = GenerateRandomString.generateRandomString("UCMED", 5);
        String openId = UUID.randomUUID().toString();
        jcUserAccount.setUserId(userId);
        jcUserAccount.setAccountId(id);
        jcUserAccount.setOpenId(openId);
        jcUserAccount.setThirdPartyType(thirdPartyType);
        jcUserAccount.setCreateTime(new Date());
        jcUserAccount.setCreateBy(String.valueOf(appCode));
        jcUserAccount.setUpdateTime(new Date());
        jcUserAccount.setUpdateBy(String.valueOf(appCode));
        jcUserAccount.setRoleName(roleName);
        int result = jcUserAccountService.save(jcUserAccount);
        if (result == 1) {
            return new UCResponse(0, "绑定成功", jcUserAccount);
        } else {
            return new UCResponse(500, "绑定失败");
        }
    }

    @Override
    public UCResponse bind(String phone, String openId, int appCode, String roleName, String thirdPartyType, String msgCode) {
        UCResponse response = userService.verifyMessageCode(phone, msgCode, GlobalConstants.ValidatePhoneType.BINDTHIRDAPP.getKey(), false);
        if (response.getRetCode() != 0) {
            return new UCResponse(-2, response.getRetInfo());
        } else {
            return bind(phone, openId, appCode, roleName, thirdPartyType);
        }
    }

    @Override
    public UCResponse changePhone(String phone, String openId, int appCode, String roleName, String thirdPartyType) {
        log.info("第三方用户更改手机号:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType);
        if (!jcUserThirdPartyService.isThridPartyUserExists(openId)) {
            return new UCResponse(-1, "用户还没绑定任何账号");
        }
        if (isNotPhonePattern(phone)) {
            return new UCResponse(-2, "手机号不合法");
        }
        if (IsThirdPartyTypeError(thirdPartyType.trim())) {
            return new UCResponse(EnumRetCode.THIRD_PARTY_TYPE_ERROR);
        }
        UC_UserInfo user = userDao.getUser(phone);
        String userId;
        if (user == null) {
            UCResponse response = userService.registration(phone, phone, UUID.randomUUID().toString(), appCode, roleName);
            if (6 == response.getRetCode()) {
                // 根据场景不同，提示语相应改变
                response.setRetInfo("此用户不在白名单内，无法绑定");
            }
            if (7 == response.getRetCode()) {
                response.setRetInfo("此用户在黑名单内，无法绑定");
            }
            if (0 != response.getRetCode()) {
                log.info("第三方用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType + " " + response.getRetInfo());
                return new UCResponse(500, response.getRetInfo());
            } else {
                JSONObject param = JSONObject.fromObject(response.getParam());
                userId = param.getString("user_id");
            }
        } else {
            JcUserThirdParty jcUserThirdParty = jcUserThirdPartyService.getUserByOpenId(openId);
            if (jcUserThirdParty != null) {
                if (jcUserThirdParty.getUserId().equals(user.getUser_id())) {
                    return new UCResponse(5, "请勿重复绑定同一个账号");
                }
            }

            if (!whiteAndBlackListService.isPassWhitList(appCode, phone)) {
                return new UCResponse(6, "此用户不在白名单内，无法绑定");
            }

            if (!whiteAndBlackListService.isPassBlackList(appCode, phone)) {
                return new UCResponse(7, "此用户在黑名单内，无法绑定");
            }

            userId = user.getUser_id();
            UCResponse response = userService.setPermission(userId, appCode, roleName);
            if (0 != response.getRetCode()) {
                log.info("第三方用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType + " " + response.getRetInfo());
                return new UCResponse(500, response.getRetInfo());
            }
        }
        JcUserThirdParty jcUserThirdParty = new JcUserThirdParty();
        jcUserThirdParty.setOpenId(openId);
        jcUserThirdParty.setUserId(userId);
        jcUserThirdParty.setThirdPartyType(thirdPartyType);
        jcUserThirdParty.setCreateBy(String.valueOf(appCode));
        jcUserThirdParty.setUpdateTime(new Date());
        jcUserThirdParty.setUpdateBy(String.valueOf(appCode));
        jcUserThirdPartyService.updatePhone(jcUserThirdParty);
        log.info("第三方用户更改绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType + " 绑定成功");
        return new UCResponse(0, "更改绑定成功");
    }

    @Override
    public UCResponse unBind(String phone, String openId, String thirdPartyType) {
        log.info("第三方用户解除绑定:phone=" + phone + ",openId=" + openId + ",type=" + thirdPartyType);
        if (!jcUserThirdPartyService.isThridPartyUserExists(openId)) {
            return new UCResponse(-1, "该第三方用户未绑定任何账号");
        }

        if (isNotPhonePattern(phone)) {
            return new UCResponse(-2, "手机号不合法");
        }
        if (IsThirdPartyTypeError(thirdPartyType.trim())) {
            return new UCResponse(EnumRetCode.THIRD_PARTY_TYPE_ERROR);
        }

        UC_UserInfo user = userDao.getUser(phone);
        JcUserThirdParty jcUserThirdParty = jcUserThirdPartyService.getUserByOpenId(openId);
        if (!thirdPartyType.equals(jcUserThirdParty.getThirdPartyType().trim())) {
            return new UCResponse(-6, "绑定的第三方type与该第三方type不一致");
        }
        if (user == null) {
            return new UCResponse(-4, "账号未注册");
        }
        if (!user.getUser_id().equals(jcUserThirdParty.getUserId())) {
            return new UCResponse(-5, "第三方用户绑定的账号与该手机号不一致");
        }
        jcUserThirdParty.setDeletion("1");
        jcUserThirdPartyService.unBind(jcUserThirdParty);
        return new UCResponse(0, "解绑成功");
    }


    @Override
    public UCResponse bindIgnorePhone(String phone, String openId, int appCode, String roleName, String thirdPartyType) {
        log.info("第三方OC用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType);
        if (jcUserThirdPartyService.isThridPartyUserExists(openId)) {
            return new UCResponse(1, "用户已绑定，请直接登录");
        }
        if (IsThirdPartyTypeError(thirdPartyType.trim())) {
            return new UCResponse(EnumRetCode.THIRD_PARTY_TYPE_ERROR);
        }
        /**
         * todo 未对国外手机号进行验证 暂时不进行验证。
         */
        if (StringUtils.isEmpty(phone)) {
            LocalDateTime now = LocalDateTime.now();
            phone = now.getYear() % 100 + "" + now.getMonthValue() +
                    now.getDayOfMonth() + now.getHour() + now.getMinute() +
                    RandomStringUtils.randomAlphanumeric(2);
            phone = CommonConstant.OC_PHONE_MARK + phone;
        }
        if (isNotPhonePattern(phone)) {
            phone = CommonConstant.OC_PHONE_MARK + phone;
        }


        UC_UserInfo user = userDao.getUser(phone);
        if (user != null && phone.substring(0, CommonConstant.OC_PHONE_MARK.length()).equals(CommonConstant.OC_PHONE_MARK)) {
            phone = CommonConstant.OC_PHONE_MARK + RandomStringUtils.randomNumeric(11);
            user = userDao.getUser(phone);
        }
        UCResponse ucResponse = bindRegistration(user, phone, openId, appCode, roleName, thirdPartyType, false);
        if (ucResponse.getRetCode() != 0) {
            return ucResponse;
        }

        String userId = ucResponse.getParam().toString();

        JcUserThirdParty jcUserThirdParty = new JcUserThirdParty();
        jcUserThirdParty.setOpenId(openId);
        jcUserThirdParty.setUserId(userId);
        jcUserThirdParty.setThirdPartyType(thirdPartyType);
        jcUserThirdParty.setCreateBy(String.valueOf(appCode));
        jcUserThirdParty.setUpdateBy(String.valueOf(appCode));
        jcUserThirdPartyService.save(jcUserThirdParty);
        log.info("第三方用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType + " 绑定成功");
        return new UCResponse(0, "绑定成功");
    }


    private UCResponse bindRegistration(UC_UserInfo user, String phone, String openId, int appCode, String roleName, String thirdPartyType, Boolean registrat) {
        if (user == null) {
            UCResponse response = userService.registration(phone, phone, UUID.randomUUID().toString(), appCode, roleName);
            if (6 == response.getRetCode()) {
                // 根据场景不同，提示语相应改变
                response.setRetInfo("此用户不在白名单内，无法绑定");
            }
            if (7 == response.getRetCode()) {
                response.setRetInfo("此用户在黑名单内，无法绑定");
            }
            if (0 != response.getRetCode()) {
                log.info("第三方用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType + " " + response.getRetInfo());
                return new UCResponse(500, response.getRetInfo());
            } else {
                JSONObject param = JSONObject.fromObject(response.getParam());
                return new UCResponse(0, response.getRetInfo(), param.getString("user_id"));
            }
        } else if (registrat) {
            if (!whiteAndBlackListService.isPassWhitList(appCode, phone)) {
                return new UCResponse(6, "此用户不在白名单内，无法绑定");
            }

            if (!whiteAndBlackListService.isPassBlackList(appCode, phone)) {
                return new UCResponse(7, "此用户在黑名单内，无法绑定");
            }

            UCResponse response = userService.setPermission(user.getUser_id(), appCode, roleName);
            if (0 != response.getRetCode()) {
                log.info("第三方用户绑定:phone=" + phone + ",openId=" + openId + ", appCode=" + appCode + ",roleName=" + roleName + ",type=" + thirdPartyType + " " + response.getRetInfo());
                return new UCResponse(500, response.getRetInfo());
            } else {
                return new UCResponse(0, response.getRetInfo(), user.getUser_id());
            }
        }
        return new UCResponse(EnumRetCode.PHONEEGISTERED);
    }

    @Override
    public NUCResponse<List<UserBindInfoVO>> getBindInfo(String userIdOrPhone, String appCode) {
        if (StringUtils.isEmpty(userIdOrPhone)) {
            return NUCResponse.createByError(-1, "userId不能为空");
        }
        if (StringUtils.isEmpty(appCode)) {
            return NUCResponse.createByError(-2, "appCode不能为空");
        }
        List<String> appCodes = Arrays.stream(appCode.split(",")).collect(Collectors.toList());
        for (int i = 0; i < appCodes.size(); i++) {
            try {
                Integer appCodeByAppToken = commonService.getAppCodeByAppToken(Integer.valueOf(appCodes.get(i)));
                appCodes.set(i, String.valueOf(appCodeByAppToken));
            } catch (NumberFormatException e) {
                return NUCResponse.createByError(-3, "appCode不为数字");
            }
        }
        List <UserBindInfoDTO> bindInfoByPhone = jcUserThirdPartyService.getBindInfo(userIdOrPhone, appCodes);
        List<UserBindInfoVO> userBindInfoVOS = new ArrayList<>();
        if (bindInfoByPhone != null && bindInfoByPhone.size() > 0) {
            bindInfoByPhone.forEach(userBindInfoDTO -> {
                UserBindInfoVO userBindInfoVO = new UserBindInfoVO();
                BeanUtils.copyProperties(userBindInfoDTO, userBindInfoVO);
                userBindInfoVOS.add(userBindInfoVO);
            });
        }
        return NUCResponse.createBySuccess(userBindInfoVOS);
    }

    private Boolean IsThirdPartyTypeError(String thirdPartyType){
        boolean typeError = true;
        for (ThirdPartyEnum e : ThirdPartyEnum.values()) {
            if (e.getKey().equals(thirdPartyType)) {
                typeError = false;
            }
        }
        return typeError;
    }

}
