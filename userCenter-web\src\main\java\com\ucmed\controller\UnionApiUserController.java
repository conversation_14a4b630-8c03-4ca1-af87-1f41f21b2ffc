package com.ucmed.controller;

import com.ucmed.service.UserService;
import com.ucmed.unionservice.UnionUserService;
import io.swagger.annotations.*;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/unionapi")
@Api(value = "云学院医生与患者用户中心无缝对接", description = "云学院医生与患者用户中心无缝对接")
public class UnionApiUserController {
    private static Logger log4j = Logger.getLogger(UnionApiUserController.class.getName());

    @Autowired
    private UnionUserService unionUserService;

    @Autowired
    private UserService userService;

    @Value("#{configProperties['bcsmstype']}")
    private String bcsmsType;

    @ApiOperation(
            value = "用户登录",
            notes = "用户输入用户名，密码进行登录"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cAppId", value = "C端用户中心的appId", required = true, paramType = "query"),
            @ApiImplicitParam(name = "username", value = "用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "phone", value = "电话（主要用于用户中心登录）", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "roleName", value = "角色", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -3, message = "appid不能为空"),
            @ApiResponse(code = -4, message = "登录异常"),
            @ApiResponse(code = -1, message = "应用未注册"),
            @ApiResponse(code = 0, message = "登录成功"),
            @ApiResponse(code = 1, message = "密码错误"),
            @ApiResponse(code = 2, message = "账号被锁定,请联系管理员"),
            @ApiResponse(code = 4, message = "账号未注册"),
            @ApiResponse(code = 5, message = "用户未授权，是否确定使用该应用？"),
            @ApiResponse(code = 6, message = "角色未授权"),
    })
    @RequestMapping(value = "/user/login", method = RequestMethod.POST)
    public ResponseEntity<String> login(Integer cAppId, String username, String password, String phone, String roleName){
        log4j.info("云学院医生与患者用户中心普通登录： cAppId:" + cAppId + "  username:" + username + "  password:" + password + "  phone:" + phone + "  roleName:" + roleName);
        String responseJson = JSONObject.fromObject(unionUserService.login(cAppId, username, password, phone, roleName)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "用户短信登录",
            notes = "用户输入用户名，短信验证码进行登录"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cAppId", value = "C端用户中心的appId", required = true, paramType = "query"),
            @ApiImplicitParam(name = "username", value = "用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "phone", value = "电话（主要用于用户中心登录）", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "roleName", value = "角色", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "msgCode", value = "短信验证码", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -3, message = "appid不能为空"),
            @ApiResponse(code = -2, message = "验证验证码失败"),
            @ApiResponse(code = 0, message = "登录成功"),
            @ApiResponse(code = 4, message = "账号未注册"),
    })
    @RequestMapping(value = "/user/smsLogin", method = RequestMethod.POST)
    public ResponseEntity<String> smsLogin(Integer cAppId, String username, String phone, String roleName, String msgCode){
        log4j.info("云学院医生与患者用户中心短信登录： cAppId:" + cAppId + "  username:" + username  + "  phone:" + phone + "  roleName:" + roleName + " msgCode:" + msgCode);
        String responseJson = JSONObject.fromObject(unionUserService.smsLogin(cAppId, username, phone, roleName, msgCode)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "用户免密登录",
            notes = "输入用户名appid进行登录"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "cAppId", value = "C端用户中心的appId", required = true, paramType = "query"),
            @ApiImplicitParam(name = "username", value = "用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "phone", value = "电话（主要用于用户中心登录）", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "roleName", value = "角色", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -3, message = "appid不能为空"),
            @ApiResponse(code = 0, message = "登录成功"),
            @ApiResponse(code = 4, message = "账号未注册"),
    })
    @RequestMapping(value = "/user/nopwdlogin", method = RequestMethod.POST)
    public ResponseEntity<String> nopwdLogin(Integer cAppId, String username, String phone, String roleName){
        log4j.info("云学院医生与患者用户中心免密登录： cAppId:" + cAppId + "  username:" + username  + "  phone:" + phone + "  roleName:" + roleName);
        String responseJson = JSONObject.fromObject(unionUserService.nopwdLogin(cAppId, username, phone, roleName)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "获取短信验证码",
            notes = "获取短信验证码不用图形验证码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取验证码成功，请查看您的手机短信"),
    })
    @RequestMapping(value = "/getmessagecode", method = RequestMethod.GET)
    public ResponseEntity<String> getmessagecode(String phone) {
        String smsToken = "SxDw3j67Y8Vpl0p09vF";
        String responseJson;
        responseJson = JSONObject.fromObject(unionUserService.sendMsgCode(smsToken, phone, bcsmsType)).toString();
        return createResponseEntity(responseJson);
    }



    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }
}
