package com.ucmed.message.client;

import cn.ucmed.rubik.sms.view.SmsInfo;

import java.util.List;

public interface SmsClient {

    /**
     * 单发短信，带requestData
     * @param smsInfo
     * @return
     */
    Boolean sendMessage(SmsInfo smsInfo);

    /**
     * 发送消息SMSfrom MQ  withOut requestData 
     * @param smsInfo
     * @return
     */
    Boolean sendMessageMQ(String vcProjectId, SmsInfo smsInfo);
    /**
     * 多手机号码发送，带requestData
     * @param lstSmsInfo
     * @return
     */
    Boolean sendMessage(List<SmsInfo> lstSmsInfo);

    /**
     * 单发短信，不带requestData
     * @param appCode
     * @param smsInfo
     * @return
     */
    Boolean sendMessage(String appCode, SmsInfo smsInfo);

    /**
     * 多手机号码发送，不带requestData
     * @param appCode
     * @param lstSmsInfo
     * @return
     */
    Boolean sendMessage(String appCode, List<SmsInfo> lstSmsInfo);

}
