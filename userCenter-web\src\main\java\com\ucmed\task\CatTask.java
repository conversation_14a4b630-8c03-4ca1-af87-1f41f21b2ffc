//package com.ucmed.task;
//import com.dianping.cat.Cat;
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.scheduling.annotation.Scheduled;
//import org.springframework.stereotype.Component;
//import tk.mybatis.mapper.util.StringUtil;
//
//
//@Slf4j
//@Component
//public class CatTask {
//    @Value("${spring.application.name}")
//    private String appName;
//    @Scheduled(cron = "0/10 * * * * ? ")
//    public void eventTask(){
//        if (StringUtil.isEmpty(appName)) {
//            return;
//        }
//        Cat.logEvent("HeartBeat", this.appName);
//    }
//}