package com.ucmed.service;

/**
 * 用户与角色的关联
 *
 * <AUTHOR>
 * @date 2017/11/17 13:40
 */
public interface UserRoleService {

    /**
     * 判断用户是否与角色绑定
     *
     * @param userId 用户名
     * @param roleId 角色ID
     * @return
     */
    boolean isUserInRole(String userId, int roleId);

    /**
     * 添加用户与角色的关联
     *
     * @param userId 用户ID
     * @param roleId 角色ID
     */
    void saveUserRole(String userId, int roleId);
}
