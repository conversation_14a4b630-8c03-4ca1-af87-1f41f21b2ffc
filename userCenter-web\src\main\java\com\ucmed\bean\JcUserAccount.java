package com.ucmed.bean;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.io.Serializable;
import java.util.Date;

/**
 * Author: 黄一辛 HUANGYIXIN
 * CreateTime: 2018/7/9 20:22
 * Contract: <EMAIL>
 * Description:
 **/
@Table(name="jc_user_account")
public class JcUserAccount implements Serializable {
    private static final long serialVersionUID = 640910433269118506L;
    @Id
    private Integer id;
    @Column(name="user_id")
    private String userId;

    @Column(name="account_id")
    private String accountId;

    @Column(name="third_party_type")
    private String thirdPartyType;

    @Column(name="create_time")
    private Date createTime;

    @Column(name="create_by")
    private String createBy;

    @Column(name="update_time")
    private Date updateTime;

    @Column(name="update_by")
    private String updateBy;

    @Column(name="deletion")
    private String deletion;

    @Column(name="role_name")
    private String roleName;

    @Column(name="open_id")
    private String openId;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getAccountId() {
        return accountId;
    }

    public void setAccountId(String accountId) {
        this.accountId = accountId;
    }

    public String getThirdPartyType() {
        return thirdPartyType;
    }

    public void setThirdPartyType(String thirdPartyType) {
        this.thirdPartyType = thirdPartyType;
    }

    public Date getCreateTime() {
        return createTime;
    }

    public void setCreateTime(Date createTime) {
        this.createTime = createTime;
    }

    public String getCreateBy() {
        return createBy;
    }

    public void setCreateBy(String createBy) {
        this.createBy = createBy;
    }

    public Date getUpdateTime() {
        return updateTime;
    }

    public void setUpdateTime(Date updateTime) {
        this.updateTime = updateTime;
    }

    public String getUpdateBy() {
        return updateBy;
    }

    public void setUpdateBy(String updateBy) {
        this.updateBy = updateBy;
    }

    public String getDeletion() {
        return deletion;
    }

    public void setDeletion(String deletion) {
        this.deletion = deletion;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
