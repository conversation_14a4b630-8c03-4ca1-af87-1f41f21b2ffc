<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:dubbo="http://code.alibabatech.com/schema/dubbo"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
        http://www.springframework.org/schema/beans/spring-beans-3.0.xsd
        http://code.alibabatech.com/schema/dubbo
        http://code.alibabatech.com/schema/dubbo/dubbo.xsd">

    <dubbo:service interface="com.ucmed.authc.AuthenticationService"
                   ref="userCenterAuthenticationServiceImpl" version="1.0.0"/>

    <dubbo:reference interface="cn.ucmed.rubik.authentication.service.MobileHospitalLoginService"
                     id="mobileHospitalLoginService" version="1.0.0"/>

    <dubbo:provider filter="dubboAppCodeFilter" />

    <!-- 用户接口 -->
    <dubbo:service interface="com.ucmed.service.UserService" ref="userCenterService" version="1.0.0" timeout="5000">
        <dubbo:method name="registration" retries="0" />
        <dubbo:method name="login" retries="0" />
        <dubbo:method name="smsLogin" retries="0" />
        <dubbo:method name="changePwd" retries="0" />
        <dubbo:method name="reInputPassword" retries="0" />
        <dubbo:method name="logout" retries="0" />
        <dubbo:method name="changePhone" retries="0" />
        <dubbo:method name="setPermission" retries="0" />
        <dubbo:method name="sendMsgCode" retries="0" />
        <dubbo:method name="verifyMessageCode" retries="0" />
        <dubbo:method name="generatePictureValidateCode" retries="0" />
        <dubbo:method name="verifyPictureValidateCode" retries="0" />
        <dubbo:method name="updateUserInfo" retries="0" />
    </dubbo:service>
    <bean id="userCenterService" class="com.ucmed.transfer.UserServiceImpl" />

    <!-- 验证码接口 -->
    <dubbo:service interface="com.ucmed.service.CaptchaService" ref="captchaService" version="1.0.0" timeout="5000" />
    <bean id="captchaService" class="com.ucmed.transfer.CaptchaServiceImpl" />

    <!-- 权限接口 -->
    <dubbo:service interface="com.ucmed.service.PermissionService" ref="permissionService" version="1.0.0" timeout="5000" />
    <bean id="permissionService" class="com.ucmed.transfer.PermissionServiceImpl" />

    <!-- 线下门诊管理系统用户封装接口 -->
    <dubbo:service interface="com.ucmed.service.ClinicUserService" ref="clinicUserService" version="1.0.0" timeout="5000">
        <dubbo:method name="registration" retries="0" />
        <dubbo:method name="listUser" retries="0" />
    </dubbo:service>
    <bean id="clinicUserService" class="com.ucmed.transfer.ClinicUserServiceImpl" />

    <!-- 第三方用户接口 -->
    <dubbo:service interface="com.ucmed.thirdparty.ThirdPartyService" ref="thirdPartyService" version="1.0.0" timeout="5000">
        <dubbo:method name="bind" retries="0" />
        <dubbo:method name="login" retries="0" />
    </dubbo:service>
    <bean id="thirdPartyService" class="com.ucmed.transfer.ThirdPartyServiceImpl" />
</beans>
