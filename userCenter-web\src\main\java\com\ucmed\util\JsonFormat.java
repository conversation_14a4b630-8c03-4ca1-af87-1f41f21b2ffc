package com.ucmed.util;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

public class JsonFormat {
	
	public static String retFormat(int retCode, String retInfo) {
		String jsonStr = "{\"retCode\":" + retCode + ",\"retInfo\":\"" + retInfo + "\"}";
		return jsonStr;
	}
	
	public static String retFormat(int retCode, String retInfo, JSONObject param) {
		String jsonStr = "{\"retCode\":" + retCode + ",\"retInfo\":\"" + retInfo + "\",\"param\":" + param + "}";
		return jsonStr;
	}
	
	public static String retFormat(int retCode, String retInfo, JSONArray param) {
		String jsonStr = "{\"retCode\":" + retCode + ",\"retInfo\":\"" + retInfo + "\",\"param\":" + param + "}";
		return jsonStr;
	}
	
	public static String retFormat(int retCode, String retInfo, String param) {
		String jsonStr = "{\"retCode\":" + retCode + ",\"retInfo\":\"" + retInfo + "\",\"param\":\"" + param + "\"}";
		return jsonStr;
	}
}
