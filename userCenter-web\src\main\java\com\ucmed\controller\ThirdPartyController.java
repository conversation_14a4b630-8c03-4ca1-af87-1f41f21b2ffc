package com.ucmed.controller;

import com.ucmed.bean.NUCResponse;
import com.ucmed.bean.retbean.UserBindInfoVO;
import com.ucmed.thirdparty.ThirdPartyService;
import io.swagger.annotations.*;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;

@Api(value = "第三方应用", description = "第三方应用接口")
@RestController
public class ThirdPartyController {
    @Autowired
    ThirdPartyService thirdPartyService;


    @ApiOperation(
            value = "绑定第三方应用（非正常绑定）",
            notes = "绑定第三方应用\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "roleName", value = "角色名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "thirdPartyType", value = "第三方应用类型（1：微信 2：企业微信 3：支付宝 4：其他 11:小程序）", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 1, message = "用户已绑定，请直接登录"),
            @ApiResponse(code = 0, message = "绑定成功"),
            @ApiResponse(code = 500, message = "绑定失败"),
    })
    @RequestMapping(value = "/thirdParty/bind", method = RequestMethod.POST)
    public ResponseEntity<String> bind(String openId, Integer appCode, String roleName, String thirdPartyType) {
        String responseJson = JSONObject.fromObject(thirdPartyService.bind(openId, appCode, roleName, thirdPartyType)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "绑定第三方应用并且注册",
            notes = "绑定第三方应用并且注册\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "roleName", value = "角色名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "thirdPartyType", value = "第三方应用类型（1：微信 2：企业微信 3：支付宝 4：其他 11:小程序）", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 1, message = "用户已绑定，请直接登录"),
            @ApiResponse(code = 0, message = "绑定成功"),
            @ApiResponse(code = 500, message = "注册问题的各种信息"),
            @ApiResponse(code = 6, message = "此用户不在白名单内，无法绑定"),
            @ApiResponse(code = 7, message = "此用户在黑名单内，无法绑定"),
            @ApiResponse(code = 0, message = "绑定成功"),

    })
    @RequestMapping(value = "/thirdParty/bindAndRegistration", method = RequestMethod.POST)
    public ResponseEntity<String> bind(String phone, String openId, Integer appCode, String roleName, String thirdPartyType) {
        String responseJson = JSONObject.fromObject(thirdPartyService.bind(phone, openId, appCode, roleName, thirdPartyType)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "通过验证码绑定第三方应用并且注册",
            notes = "通过验证码绑定第三方应用并且注册\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "roleName", value = "角色名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "thirdPartyType", value = "第三方应用类型（1：微信 2：企业微信 3：支付宝 4：其他 11:小程序）", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "msgCode", value = "验证码", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 1, message = "用户已绑定，请直接登录"),
            @ApiResponse(code = 0, message = "绑定成功"),
            @ApiResponse(code = 500, message = "注册问题的各种信息"),
            @ApiResponse(code = 6, message = "此用户不在白名单内，无法绑定"),
            @ApiResponse(code = 7, message = "此用户在黑名单内，无法绑定"),
            @ApiResponse(code = 0, message = "绑定成功"),

    })
    @RequestMapping(value = "/thirdParty/bindAndRegistrationByMsgCode", method = RequestMethod.POST)
    public ResponseEntity<String> bind(String phone, String openId, Integer appCode, String roleName, String thirdPartyType, String msgCode) {
        String responseJson = JSONObject.fromObject(thirdPartyService.bind(phone, openId, appCode, roleName, thirdPartyType, msgCode)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "绑定第三方应用并且注册（针对海外华侨用户）",
            notes = "绑定第三方应用并且注册（针对海外华侨用户，手机号可为空，也可为非国内手机号格式）\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "roleName", value = "角色名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "thirdPartyType", value = "第三方应用类型（1：微信 2：企业微信 3：支付宝 4：其他 11:小程序）", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 1, message = "用户已绑定，请直接登录"),
            @ApiResponse(code = 0, message = "绑定成功"),
            @ApiResponse(code = 500, message = "注册问题的各种信息"),
            @ApiResponse(code = 6, message = "此用户不在白名单内，无法绑定"),
            @ApiResponse(code = 7, message = "此用户在黑名单内，无法绑定"),
            @ApiResponse(code = 0, message = "绑定成功"),

    })
    @RequestMapping(value = "/thirdParty/ocBindAndRegistration", method = RequestMethod.POST)
    public ResponseEntity<String> bindIgnorePhone(String phone, String openId, Integer appCode, String roleName, String thirdPartyType) {
        String responseJson = JSONObject.fromObject(thirdPartyService.bindIgnorePhone(phone, openId, appCode, roleName, thirdPartyType)).toString();
        return createResponseEntity(responseJson);
    }



    @ApiOperation(
            value = "第三方应用登录",
            notes = "第三方应用登录\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "roleName", value = "角色名", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 404, message = "用户未绑定"),
            @ApiResponse(code = 0, message = "登录成功"),
            @ApiResponse(code = 500, message = "未授权或登录失败"),

    })
    @RequestMapping(value = "/thirdParty/login", method = RequestMethod.POST)
    public ResponseEntity<String> login(String openId, Integer appCode, String roleName){
        String responseJson = JSONObject.fromObject(thirdPartyService.login(openId, appCode, roleName)).toString();
        return createResponseEntity(responseJson);
    }
    @ApiOperation(
            value = "更改绑定的手机号（账号）",
            notes = "更改绑定的手机号（账号）\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "roleName", value = "角色名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "thirdPartyType", value = "第三方应用类型（1：微信 2：企业微信 3：支付宝 4：其他 11:小程序）", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "用户还未绑定任何账号"),
            @ApiResponse(code = -2, message = "手机号不合法"),
            @ApiResponse(code = 0, message = "更改绑定成功"),
            @ApiResponse(code = 500, message = "注册问题的各种信息"),
            @ApiResponse(code = 5, message = "请勿重复绑定同一个账号"),
            @ApiResponse(code = 6, message = "此用户不在白名单内，无法绑定"),
            @ApiResponse(code = 7, message = "此用户在黑名单内，无法绑定"),
    })
    @RequestMapping(value = "/thirdParty/changeBindPhone", method = RequestMethod.POST)
    public ResponseEntity<String> changePhone(String phone, String openId, Integer appCode, String roleName, String thirdPartyType) {
        String responseJson = JSONObject.fromObject(thirdPartyService.changePhone(phone, openId, appCode, roleName, thirdPartyType)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "解除第三方绑定",
            notes = "解除第三方绑定\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "thirdPartyType", value = "第三方应用类型（1：微信 2：企业微信 3：支付宝 4：其他 11:小程序）", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "用户还未绑定任何账号"),
            @ApiResponse(code = -2, message = "手机号不合法"),
            @ApiResponse(code = -3, message = "第三方类型错误"),
            @ApiResponse(code = -4, message = "账号未注册"),
            @ApiResponse(code = -5, message = "该用户未绑定该第三方账号"),
            @ApiResponse(code = -6, message = "绑定的第三方type和传入的不一致"),
            @ApiResponse(code = 0, message = "解除绑定成功"),
    })
    @RequestMapping(value = "/thirdParty/unBind", method = RequestMethod.POST)
    public ResponseEntity<String> unBind(String phone, String openId, String thirdPartyType) {
        String responseJson = JSONObject.fromObject(thirdPartyService.unBind(phone, openId, thirdPartyType)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "获取用户绑定信息",
            notes = "获取用户绑定信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userIdOrPhone", value = "用户id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCodes", value = "appcode，英文逗号分隔", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "手机号不能为空"),
            @ApiResponse(code = -2, message = "appCode不能为空"),
    })
    @ApiIgnore
    @RequestMapping(value = "/thirdParty/getUserBindInfo", method = RequestMethod.GET)
    public ResponseEntity<NUCResponse<List<UserBindInfoVO>>> getBindInfo(String userIdOrPhone, String appCodes) {
        if (StringUtils.isEmpty(userIdOrPhone)) {
            return createResponseEntity(NUCResponse.createByError(-1, "手机号不能为空"));
        }
        if (StringUtils.isEmpty(appCodes)) {
            return createResponseEntity(NUCResponse.createByError(-2, "appCode不能为空"));
        }
        NUCResponse<List<UserBindInfoVO>> bindInfo = thirdPartyService.getBindInfo(userIdOrPhone, appCodes);
        return createResponseEntity(bindInfo);
    }

    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }
}
