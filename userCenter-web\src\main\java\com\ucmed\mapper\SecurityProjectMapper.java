package com.ucmed.mapper;

import com.ucmed.bean.AppIndPwd;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import java.util.Map;

public interface SecurityProjectMapper {


    @Select({
            "SELECT " +
            "security_project.dl_pwd " +
            "FROM " +
            "security_project,security_application " +
            "WHERE " +
            "security_application.app_code = #{appCode} and security_application.proj_code = security_project.proj_code"
    })
    public int selectIndePwdByAppCode(int appCode);


    @Select({
            "SELECT " +
                    "dl_pwd " +
                    "FROM " +
                    "security_project " +
                    "WHERE " +
                    "proj_code=#{projCode}"
    })
    public int selectIndePwdByprojCode(int projCode);

    @Select({
        "SELECT " +
                "proj_name " +
                "FROM " +
                "security_project " +
                "WHERE " +
                "proj_code=#{projCode}"
    })
    String getProjNameByProjCode(Integer projCode);

    @Select({
            "SELECT " +
            "sp.dl_pwd, " ,
            "sp.proj_code " ,
            "FROM ",
            "security_project sp ",
    })
    @Results({
//            @Result(column = "app_code", property = "appCode",jdbcType = JdbcType.INTEGER),
            @Result(column = "dl_pwd", property = "indPwd",jdbcType = JdbcType.BOOLEAN),
            @Result(column = "proj_code", property = "proCode", jdbcType = JdbcType.INTEGER),
    })
    @MapKey("proCode")
    Map<Integer, AppIndPwd> seclectAllProIndependentPwd();


    @Select({
            "SELECT " ,
            "sp.dl_pwd, " ,
            "sa.app_code," ,
            "sp.proj_code " ,
            "FROM " ,
            "security_project sp, security_application sa WHERE ",
            "sa.proj_code = sp.proj_code ",
            "ORDER BY sa.app_code",
    })
    @Results({
            @Result(column = "app_code", property = "appCode",jdbcType = JdbcType.INTEGER),
            @Result(column = "dl_pwd", property = "indPwd",jdbcType = JdbcType.BOOLEAN),
            @Result(column = "proj_code", property = "proCode", jdbcType = JdbcType.INTEGER),
    })
    @MapKey("appCode")
    Map<Integer, AppIndPwd> seclectAllAppIndependentPwd();
}
