package com.ucmed.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * 用户中心接口返回值
 */
@ApiModel(value="用户中心接口返回值",description="用户中心接口返回值")
public class UCResponse implements Serializable {

    private static final long serialVersionUID = -4345612602298647175L;
    @ApiModelProperty(value="响应码", name="retCode", example="0")
    int retCode;
    @ApiModelProperty(value="返回信息", name="retInfo", example="操作成功")
    String retInfo;
    @ApiModelProperty(value="返回数据", name="param", example="someMessage")
    Object param;

    public UCResponse() {}

    public UCResponse(int retCode, String retInfo, Object param) {
        this.retCode = retCode;
        this.retInfo = retInfo;
        this.param = param;
    }

    public UCResponse(int retCode, String retInfo) {
        this.retCode = retCode;
        this.retInfo = retInfo;
    }

    public UCResponse(EnumRetCode enumRetCode) {
        this.retCode = enumRetCode.getKey();
        this.retInfo = enumRetCode.getValue();
    }

    public UCResponse(EnumRetCode enumRetCode, Object param) {
        this.retCode = enumRetCode.getKey();
        this.retInfo = enumRetCode.getValue();
        this.param = param;
    }



    public int getRetCode() {
        return retCode;
    }

    public void setRetCode(int retCode) {
        this.retCode = retCode;
    }

    public String getRetInfo() {
        return retInfo;
    }

    public void setRetInfo(String retInfo) {
        this.retInfo = retInfo;
    }

    public Object getParam() {
        return param;
    }

    public void setParam(Object param) {
        this.param = param;
    }

    @Override
    public String toString() {
        return "UCResponse{" +
                "retCode=" + retCode +
                ", retInfo='" + retInfo + '\'' +
                ", param=" + param +
                '}';
    }
}
