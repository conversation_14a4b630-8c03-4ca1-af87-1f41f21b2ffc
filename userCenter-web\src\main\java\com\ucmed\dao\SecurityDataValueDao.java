package com.ucmed.dao;

import com.ucmed.bean.SecurityDataValue;

import java.util.List;

/**
 * Created by QIUJIAHAO on 2016/11/15.
 */
public interface SecurityDataValueDao {

    List<SecurityDataValue> getDataValueByAppCode(int appCode);

    SecurityDataValue getDataValueById(int dataValueId);

    int updateDataValue(SecurityDataValue dataValue);

    int addDataValue(SecurityDataValue dataValue);

    int deleteDataValueById(int dataValueId);

    int addDataValueToRole(int roleId, int dataValueId, int appCode, String time);

    int removeDataValueRromRole(int roleId, int dataValueId);

}
