package cn.ucmed.transfer;

import cn.ucmed.utils.HttpClientUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ucmed.bean.UCResponse;
import com.ucmed.service.CaptchaService;
import org.springframework.stereotype.Service;

import static cn.ucmed.constant.UserCenterConstant.USER_CENTER_URL;
@Service
public class CaptchaServiceImpl implements CaptchaService {
    @Override
    public UCResponse getImageCaptcha(int length) {
        String url = USER_CENTER_URL + "/captcha/image?length=" + length;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse verifyImageCaptcha(String token, String captchaCode) {
        String url = USER_CENTER_URL + "/captcha/image/verify?token=" + token + "&captchaCode=" + captchaCode;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse getImageCaptcha(String phone, String type) {
        String url = USER_CENTER_URL + "/picturecode?phone=" + phone
                + "&type=" + type + "&checkoutAccount=true";
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse getImageCaptcha(String phone, String type, int length) {
        String url = USER_CENTER_URL + "/picturecode?phone=" + phone
                + "&type=" + type + "&checkoutAccount=true&length=" + length;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse getImageCaptcha(int appCode, String phone, String type, int length) {
        String url = USER_CENTER_URL + "/picturecodeWithAppCode?phone=" + phone
                + "&type=" + type + "&length=" + length + "&appCode=" + appCode;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse verifyImageCaptcha(String phone, String type, String picCode) {
        String url = USER_CENTER_URL + "/picturecode/verify?phone=" + phone
                + "&type=" + type + "&picCode=" + picCode;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse generateMsgCode(String phone, String type, int length) {
        UCResponse ucResponse = new UCResponse();
        ucResponse.setRetCode(404);
        ucResponse.setRetInfo("该接口不可直接调用");
        return ucResponse;
    }

    @Override
    public UCResponse generateMsgCode(String phone, String type, int length, long timeOut) {
        UCResponse ucResponse = new UCResponse();
        ucResponse.setRetCode(404);
        ucResponse.setRetInfo("该接口不可直接调用");
        return ucResponse;
    }


    @Override
    public UCResponse getCommonImageCaptcha(String phone, String type, int length) {
        String url = USER_CENTER_URL + "/picturecode/verify?phone=" + phone
                + "&type=" + type + "&length=" + length + "&checkoutAccount=false";
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse sendMsgCode(String smsToken, String phone, String type, String picCode) {
        String url = USER_CENTER_URL + "/messagecode?phone=" + phone
                + "&smsToken=" + smsToken + "&type=" + type + "&picCode=" + picCode + "&checkoutAccount=false";
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse sendMsgCode(int appCode, String smsToken, String phone, String type, int length) {
        String url = USER_CENTER_URL + "/getmessagecode?phone=" + phone
                + "&type=" + type + "&appCode=" + appCode + "&smsToken=" + smsToken + "&length=" + length ;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }
}
