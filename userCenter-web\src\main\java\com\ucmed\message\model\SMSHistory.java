package com.ucmed.message.model;

import javax.persistence.Column;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

@Table(name = "sh_sms_history")
public class SMSHistory {
    /**
     * 主键，uuid
     */
    @Id
    @Column(name = "sh_sms_history_id")
    private String smsHistoryId;


    /**
     * template_key
     */
    @Column(name = "template_key")
    private String templateKey;


    /**
     * 发送者scyUserId
     */
    @Column(name = "sender_scy_user_id")
    private String senderScyUserId;

    /**
     * 发送者手机号码
     */
    @Column(name = "sender_mobile")
    private String senderMobile;

    /**
     * 接受者手机号码
     */
    @Column(name = "receiver_mobile")
    private String receiverMobile;

    /**
     * 发送内容
     */
    private String content;

    /**
     * 0：成功，1：失败
     */
    @Column(name = "send_status")
    private String sendStatus;

    /**
     * 新建者
     */
    private String createdby;

    /**
     * 新建日期
     */
    private Date createdon;

    /**
     * 修改者
     */
    private String modifiedby;

    /**
     * 修改日期
     */
    private Date modifiedon;


    /**
     * 删除状态,0未删除，1已删除
     */
    @Column(name = "deletion_state")
    private String deletionState;

    /**
     * 备注
     */
    private String description;


    /**
     *
     */
    private String vcProjectId;

    /**
     * 获取主键，uuid
     *
     * @return sh_sms_history_id - 主键，uuid
     */
    public String getSmsHistoryId() {
        return smsHistoryId;
    }

    /**
     * 设置主键，uuid
     *
     * @param smsHistoryId 主键，uuid
     */
    public void setSmsHistoryId(String smsHistoryId) {
        this.smsHistoryId = smsHistoryId;
    }

    /**
     * 获取发送者scyUserId
     *
     * @return sender_scy_user_id - 发送者scyUserId
     */
    public String getSenderScyUserId() {
        return senderScyUserId;
    }

    /**
     * 设置发送者scyUserId
     *
     * @param senderScyUserId 发送者scyUserId
     */
    public void setSenderScyUserId(String senderScyUserId) {
        this.senderScyUserId = senderScyUserId;
    }

    /**
     * 获取发送者手机号码
     *
     * @return sender_mobile - 发送者手机号码
     */
    public String getSenderMobile() {
        return senderMobile;
    }

    /**
     * 设置发送者手机号码
     *
     * @param senderMobile 发送者手机号码
     */
    public void setSenderMobile(String senderMobile) {
        this.senderMobile = senderMobile;
    }

    /**
     * 获取接受者手机号码
     *
     * @return receiver_mobile - 接受者手机号码
     */
    public String getReceiverMobile() {
        return receiverMobile;
    }

    /**
     * 设置接受者手机号码
     *
     * @param receiverMobile 接受者手机号码
     */
    public void setReceiverMobile(String receiverMobile) {
        this.receiverMobile = receiverMobile;
    }

    /**
     * 获取发送内容
     *
     * @return content - 发送内容
     */
    public String getContent() {
        return content;
    }

    /**
     * 设置发送内容
     *
     * @param content 发送内容
     */
    public void setContent(String content) {
        this.content = content;
    }

    /**
     * 获取0：成功，1：失败
     *
     * @return send_status - 0：成功，1：失败
     */
    public String getSendStatus() {
        return sendStatus;
    }

    /**
     * 设置0：成功，1：失败
     *
     * @param sendStatus 0：成功，1：失败
     */
    public void setSendStatus(String sendStatus) {
        this.sendStatus = sendStatus;
    }

    /**
     * 获取新建者
     *
     * @return createdby - 新建者
     */
    public String getCreatedby() {
        return createdby;
    }

    /**
     * 设置新建者
     *
     * @param createdby 新建者
     */
    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    /**
     * 获取新建日期
     *
     * @return createdon - 新建日期
     */
    public Date getCreatedon() {
        return createdon;
    }

    /**
     * 设置新建日期
     *
     * @param createdon 新建日期
     */
    public void setCreatedon(Date createdon) {
        this.createdon = createdon;
    }

    /**
     * 获取修改者
     *
     * @return modifiedby - 修改者
     */
    public String getModifiedby() {
        return modifiedby;
    }

    /**
     * 设置修改者
     *
     * @param modifiedby 修改者
     */
    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    /**
     * 获取修改日期
     *
     * @return modifiedon - 修改日期
     */
    public Date getModifiedon() {
        return modifiedon;
    }

    /**
     * 设置修改日期
     *
     * @param modifiedon 修改日期
     */
    public void setModifiedon(Date modifiedon) {
        this.modifiedon = modifiedon;
    }

    /**
     * 获取删除状态,0未删除，1已删除
     *
     * @return deletion_state - 删除状态,0未删除，1已删除
     */
    public String getDeletionState() {
        return deletionState;
    }

    /**
     * 设置删除状态,0未删除，1已删除
     *
     * @param deletionState 删除状态,0未删除，1已删除
     */
    public void setDeletionState(String deletionState) {
        this.deletionState = deletionState;
    }

    /**
     * 获取备注
     *
     * @return description - 备注
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置备注
     *
     * @param description 备注
     */
    public void setDescription(String description) {
        this.description = description;
    }

    /**
     * @return the vcProjectId
     */
    public String getVcProjectId() {
        return vcProjectId;
    }

    /**
     * @param vcProjectId the vcProjectId to set
     */
    public void setVcProjectId(String vcProjectId) {
        this.vcProjectId = vcProjectId;
    }


    /**
     * @return
     */
    public String getTemplateKey() {
        return templateKey;
    }

    /**
     * @param templateKey
     */
    public void setTemplateKey(String templateKey) {
        this.templateKey = templateKey;
    }
}