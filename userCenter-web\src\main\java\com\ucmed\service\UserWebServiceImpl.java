package com.ucmed.service;

import com.ucmed.bean.*;
import com.ucmed.common.service.CommonService;
import com.ucmed.mapper.JcUserInfoMapper;
import com.ucmed.mapper.SecurityProjectMapper;
import com.ucmed.util.JsonFormat;
import com.ucmed.util.TimeUtil;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.concurrent.TimeUnit;

import static com.ucmed.common.constant.CommonConstant.*;

@Service
public class UserWebServiceImpl extends CommonService implements UserWebService {

    @Autowired
    JcUserInfoMapper jcUserInfoMapper;

    @Autowired
    private YLXZService ylxzService;

    @Autowired
    private UserService userService;

    @Autowired
    SecurityProjectMapper securityProjectMapper;

    @Autowired
    private SecurityUserProjectService securityUserProjectService;

    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 注册
     */
    @Transactional
    public String registration(JSONObject rcv) {

        // 移动远程医生用户
        if (isRemoteMedical(rcv)) {
            return transRetStr(JSONObject.fromObject(ylxzService.registration(rcv)));
        }

        String password = rcv.optString(PASSWORD);
        String phone = rcv.optString(PHONE);
        int appCode = rcv.optInt(APPCODE);
        String roleName = rcv.optString(ROLENAME);
        String userId = rcv.optString(USERID);
        UCResponse response = userService.registration(userId, phone, password, appCode, roleName);
        return JSONObject.fromObject(response).toString();
    }

    /**
     * 登录
     */
    public String login(JSONObject rcv) {

        // 移动远程医生用户
        if (isRemoteMedical(rcv)) {
            JSONObject result = JSONObject.fromObject(ylxzService.login(rcv));
            if ("0".equals(result.get("ret_code"))) {
                JSONObject ret_data = result.getJSONObject("ret_data");
                RMLoginSuccess(ret_data);
            }
            return transRetStr(result);
        }

        String userId = rcv.optString(USERID);
        String password = rcv.optString(PASSWORD);
        int appCode = rcv.optInt(APPCODE);
        String roleName = rcv.optString(ROLENAME);

        UCResponse response = userService.login(userId, password, appCode, roleName);
        return JSONObject.fromObject(response).toString();
    }

    /**
     * 修改密码
     */
    public String changePwd(JSONObject rcv) {

        // 移动远程医生用户
        if (isRemoteMedical(rcv)) {
            return transRetStr(JSONObject.fromObject(ylxzService.changePwd(rcv)));
        }

        String userId = rcv.getString(USERID);
        String oldPwd = rcv.getString(PASSWORD);
        String newPwd = rcv.getString(NEWPASSWORD);
        String token = rcv.getString(TOKEN);

        String currentTime = TimeUtil.getCurrentTime();
        UC_UserInfo user = getUser(userId);
        if (user == null) {
            return JsonFormat.retFormat(3, "该账号未注册");
        }
        // token验证
        String tokenuserId = verifyToken(token);
        if (tokenuserId == null) {
            return JsonFormat.retFormat(104, "token验证失败");
        }
        activeToken(token);
//        if (!token.equals(user.getToken())) {
//            return JsonFormat.retFormat(104, "token验证失败");
//        }
        String projCode = getProjCodeByToken(token);
        int dlpwd = 0;
        try {
            dlpwd = securityProjectMapper.selectIndePwdByprojCode(Integer.parseInt(projCode));
        }catch (Exception e){
            e.printStackTrace();
        }

        if (dlpwd != 0){
            SecurityUserProject userProject = securityUserProjectService.getUserProject(user.getUser_id(), Integer.parseInt(projCode));
            if (userProject == null){
                return JsonFormat.retFormat(-1, "用户未授权该应用，修改密码失败");
            }
            if (dlpasswordMatched(oldPwd, userProject, user)) {
                // 检查新密码强度
                if (isSimplePwd(newPwd)) {
                    return JsonFormat.retFormat(1, "新密码太简单，建议使用大小写字母、数字和特殊字符");
                }
                dlchangePwd(userProject, newPwd, currentTime);
                return JsonFormat.retFormat(0, "修改密码成功");
            } else {
                return JsonFormat.retFormat(2, "修改密码失败，旧密码错误");
            }
        }
        // 验证密码是否正确
        if (passwordMatched(oldPwd, user)) {
            // 检查新密码强度
            if (isSimplePwd(newPwd)) {
                return JsonFormat.retFormat(1, "新密码太简单，建议使用大小写字母、数字和特殊字符");
            }
            changePwd(user, newPwd, currentTime);
            return JsonFormat.retFormat(0, "修改密码成功");
        } else {
            return JsonFormat.retFormat(2, "修改密码失败，旧密码错误");
        }
    }

    /**
     * 重置密码
     */
    public String reInputPassword(JSONObject rcv) {

        // 移动远程医生用户
        if (isRemoteMedical(rcv)) {
            return transRetStr(JSONObject.fromObject(ylxzService.reInputPassword(rcv)));
        }

        String userId = rcv.getString(USERID);
        String newPassword = rcv.getString(NEWPASSWORD);
        String currentTime = TimeUtil.getCurrentTime();

        UC_UserInfo user = getUser(userId);
        if (user == null) {
            return JsonFormat.retFormat(2, "该账号未注册");
        }
        if (isSimplePwd(newPassword)) {
            return JsonFormat.retFormat(1, "密码太简单，建议使用大小写字母、数字和特殊字符");
        }
        int appCode = 0;
        int dlpwd;
        try {
            appCode = rcv.getInt(APPCODE);
            dlpwd = securityProjectMapper.selectIndePwdByAppCode(appCode);
        }catch (Exception e){
            dlpwd = 0;
        }
        if (dlpwd != 0 && appCode != 0){
            int projCode = getProjCode(appCode);
            SecurityUserProject userProject = securityUserProjectService.getUserProject(user.getUser_id(), projCode);
            if (userProject == null){
                addUserProject(user.getUser_id(), projCode);
                userProject = securityUserProjectService.getUserProject(user.getUser_id(), projCode);
            }
            dlchangePwd(userProject, newPassword, currentTime);
        }else {
            changePwd(user, newPassword, currentTime);
        }
        return JsonFormat.retFormat(0, "重置密码成功");
    }

    /**
     * token有效性
     */
    public String isTokenValid(JSONObject rcv) {
        //用户名
//        String user_id = rcv.getString(USERID);
        String token = rcv.getString(TOKEN);
//        UC_UserInfo user = getUser(user_id);
        //用户是否存在
//        if (user == null) {
//            return JsonFormat.retFormat(2, "账号未注册");
//        }
//        String userToken = user.getToken();
        String userId = verifyToken(token);
        if (userId == null) {
            return JsonFormat.retFormat(0, "token已失效");
        }
        activeToken(token);
        return JsonFormat.retFormat(1, "token未失效", token);
//        if (userToken.equals(token)) {
//            return JsonFormat.retFormat(1, "token未失效", token);
//        } else {
//            return JsonFormat.retFormat(0, "token已失效");
//        }
    }

    /**
     * 退出登录
     */
    public String logout(JSONObject rcv) {

        // 移动远程医生用户
        if (isRemoteMedical(rcv)) {
            return transRetStr(JSONObject.fromObject(ylxzService.logout(rcv)));
        }

        //用户名
        String user_id = rcv.getString(USERID);
        //获取用户对象
        UC_UserInfo user = getUser(user_id);
        //用户是否存在
        if (user == null) {
            return JsonFormat.retFormat(1, "账号未注册");
        }
        String token = user.getToken();
        //redis删除
        redisTemplate.delete(token);
        //清空token
        user.setToken("");
        user.setToken_time(TimeUtil.getCurrentTime());
        updateUser(user);
        return JsonFormat.retFormat(0, "退出登录成功");
    }

    /**
     * 修改手机号
     */
    public String changePhone(JSONObject rcv) {
        //原手机号
        String phone = rcv.getString(PHONE);
        //新手机号
        String newPhone = rcv.getString(NEWPHONE);
        UC_UserInfo user = getUser(phone);
        //验证原手机号是否注册
        if (user == null) {
            return JsonFormat.retFormat(1, "旧手机号未注册");
        }
        //验证新手机号合法性
        if (isNotPhonePattern(newPhone)) {
            return JsonFormat.retFormat(2, "新手机号不合法");
        }
        //验证新手机号是否注册
        if (isPhoneExist(newPhone)) {
            return JsonFormat.retFormat(3, "新手机号已注册");

        }
        //手机号加密
        newPhone = ThreeDESEncrypt(newPhone);
        // 更新手机号
        user.setPhone(newPhone);
        updateUser(user);
        return JsonFormat.retFormat(0, "手机号修改成功");
    }

    /**
     * 用户是否注册某角色
     */
    public String isUserInRole(JSONObject rcv) {
        // 用户名
        String user_id = rcv.getString(USERID);
        // 角色名称
        String role_name = rcv.getString(ROLENAME);
        UC_UserInfo user = getUser(user_id);
        // 账号未注册
        if (user == null) {
            return JsonFormat.retFormat(1, "用户未注册" + role_name + "角色");
        }
        user_id = user.getUser_id();
        if (isUserInRole(user_id, role_name)) {
            return JsonFormat.retFormat(0, "用户已注册" + role_name + "角色");
        } else {
            return JsonFormat.retFormat(1, "用户未注册" + role_name + "角色");
        }
    }

    @Override
    public String findUserByAppAndRole(JSONObject rcv) {
        int app_code = rcv.getInt(APPCODE);
        String role_name = rcv.getString(ROLENAME);
        String user_id = rcv.getString(USERID);
        UC_UserInfo user = getUser(user_id);
        if (user == null) {
            return JsonFormat.retFormat(1, "账号未注册");
        }
        int role_id = getRoleId(role_name, app_code);
        if (isUserInRole(user.getUser_id(), role_id)) {
            String retJson = "{\'user_id\':\'" + user.getUser_id() + "\',\'phone\':\'" + ThreeDESDecrypt(user.getPhone()) + "\',\'id\':\'" + user.getUid() + "\'}";
            return JsonFormat.retFormat(0, "用户已注册该角色", JSONObject.fromObject(retJson));
        } else {
            return JsonFormat.retFormat(2, "用户未注册该角色");
        }

    }

    @Override
    public String generateMessageCode(JSONObject rcv) {
        String phone = rcv.getString(PHONE);
        //获取用户对象
        UC_UserInfo user = getUser(phone);
        //用户是否存在
        if (user == null) {
            return JsonFormat.retFormat(1, "账号未注册");
        }
        String messageCode = generateRandomCode();
        JSONObject retJson = new JSONObject();
        retJson.put("msg_code", messageCode);
        retJson.put("phone", ThreeDESDecrypt(user.getPhone()));
        retJson.put("user_id", user.getUser_id());
        redisTemplate.opsForValue().set(user.getUser_id(), retJson.toString(), 30 * 60, TimeUnit.SECONDS);
        return JsonFormat.retFormat(0, "验证码已生成", retJson);
    }

    @Override
    public String verifyMessageCode(JSONObject rcv) {
        String phone = rcv.getString(PHONE);
        String inputMessageCode = rcv.getString(MESSAGECODE);
        String invalidWhenVerify = "N";
        if (rcv.containsKey(INVALIDWHENVERIFY)) {
            invalidWhenVerify = rcv.getString(INVALIDWHENVERIFY);
        }
        //获取用户对象
        UC_UserInfo user = getUser(phone);
        //用户是否存在
        if (user == null) {
            return JsonFormat.retFormat(1, "账号未注册");
        }
        if (redisTemplate.hasKey(user.getUser_id())) {
            JSONObject redisMessage = JSONObject.fromObject(redisTemplate.opsForValue().get(user.getUser_id()));
            String messageCode = (String) redisMessage.get(MESSAGECODE);
            if ("Y".equals(invalidWhenVerify)) {
                redisTemplate.delete(user.getUser_id());
            }
            if (inputMessageCode.equals(messageCode)) {
                return JsonFormat.retFormat(0, "验证成功");
            }
        }
        return JsonFormat.retFormat(2, "验证失败");
    }

    @Override
    public String noPasswordLogin(JSONObject rcv) {

        // 移动远程医生用户
        if (isRemoteMedical(rcv)) {
            JSONObject result = JSONObject.fromObject(ylxzService.verifyCodeLogin(rcv));
            if ("0".equals(result.get("ret_code"))) {
                JSONObject ret_data = result.getJSONObject("ret_data");
                RMLoginSuccess(ret_data);
            }
            return transRetStr(JSONObject.fromObject(result.toString()));
        }

        int app_code = rcv.getInt(APPCODE);
        String role_name = rcv.getString(ROLENAME);
        String phone = rcv.getString(PHONE);
        String messageCode = rcv.getString(MESSAGECODE);
        String invalidWhenVerify = "N";
        if (rcv.containsKey(INVALIDWHENVERIFY)) {
            invalidWhenVerify = rcv.getString(INVALIDWHENVERIFY);
        }
        JSONObject request = new JSONObject();
        request.put(PHONE, phone);
        request.put(MESSAGECODE, messageCode);
        request.put(INVALIDWHENVERIFY, invalidWhenVerify);
        JSONObject response = JSONObject.fromObject(verifyMessageCode(request));
        if (response.getInt("retCode") == 0) {
            UC_UserInfo user = getUser(phone);
            UCResponse ucResponse = login(user.getUser_id(), "", app_code, role_name, true);
            return JSONObject.fromObject(ucResponse).toString();
        } else {
            return response.toString();
        }
    }

    public String setPermission(JSONObject rcv) {
        String userId = rcv.optString(USERID);
        int appCode = rcv.optInt(APPCODE);
        String roleName = rcv.optString(ROLENAME);
        UCResponse response = userService.setPermission(userId, appCode, roleName);
        return JSONObject.fromObject(response).toString();
    }

    @Override
    public String getUserIdByUid(JSONObject rcv) {
        int uid = rcv.optInt("id");
        UC_UserInfo user = getJcUserDao().getUserByUid(uid);
        return user == null ? null : user.getUser_id();
    }

    @Override
    public String getUserInfoByOpenId(JSONObject rcv){
        String openId = rcv.getString("open_id");
        String appCode = rcv.getString("app_code");
        JcUserInfo result = jcUserInfoMapper.getUserInfoByOpenId(openId, appCode);
        if(result != null){
            result.setMobile(ThreeDESDecrypt(result.getMobile()));
        }
        return JSONObject.fromObject(result).toString();
    }
}
