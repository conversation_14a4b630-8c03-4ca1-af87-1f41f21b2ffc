package com.ucmed.message.client.imp;

import cn.ucmed.rubik.dubbo.DubboReference;
import cn.ucmed.rubik.sms.service.ISmsSendService;
import cn.ucmed.rubik.sms.view.SmsInfo;
import com.jfinal.json.Json;
import com.ucmed.dto.MessageResult;
import com.ucmed.message.client.SmsThirdService;
import com.ucmed.message.model.TSmsGateway;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

@Service("smsThirdDubboService")
public class SmsThirdDubboServiceExecutor implements SmsThirdService {
    private static final Logger LOG = Logger.getLogger(SmsThirdDubboServiceExecutor.class);

    @Autowired
    private DubboReference dubboReference;

    @Override
    public MessageResult sendMessage(TSmsGateway tSmsGateway, String platformHospitalId, List<SmsInfo> lstSmsInfo) {
        LOG.debug("SmsThirdDubboServiceExecutor-sendMessage:" + platformHospitalId);
        Boolean ret = dubboReference.initReference(ISmsSendService.class,
                platformHospitalId).smsSend(lstSmsInfo);
        MessageResult messageResult = new MessageResult();
        messageResult.setChannelName("dubbo");
        messageResult.setResultCode(ret ? 100 : 500);
        messageResult.setResultInfo(Json.getJson().toJson(lstSmsInfo));
        return messageResult;
    }


}
