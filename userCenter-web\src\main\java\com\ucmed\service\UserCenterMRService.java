package com.ucmed.service;

import net.sf.json.JSONObject;

public interface UserCenterMRService {
	/**
	 * 登陆
	 *
	 * @param rcv 账号密码
	 */
	String login(JSONObject rcv);
	
	/**
	 * 查询普通用户基本信息
	 *
	 * @param rcv 卓健ID
	 * @return 普通用户基本信息
	 */
	String getUserInfo(String rcv);
	
	/**
	 * 更新普通用户基本信息
	 *
	 * @param rcv 新的用户基本信息
	 */
	String updateUserInfo(String rcv);
	
	/**
	 * 验证普通用户基本信息
	 *
	 *
	 */
	String verifyUserInfo(String rcv);
	
	/**
	 * 查询医生基本信息
	 *
	 * @param rcv 卓健ID
	 * @return 医生基本信息
	 */
	String getDoctorInfo(String rcv);

	/**
	 * 更新医生基本信息
	 *
	 * @param rcv 新的医生基本信息
	 * @return 执行结果
	 */
	String updateDoctorInfo(String rcv);
	
	/**
	 * 验证医生基本信息
	 */
	String verifyDoctorInfo(String rcv);

	/**
	 * 添加医院信息
	 *
	 * @param rcv 医院信息
	 * @return 执行结果
	 */
	String addHospitalInfo(String rcv);

	/**
	 * 更新医院信息
	 *
	 * @param rcv 新的医院信息
	 * @return 执行结果
	 */
	String updateHospitalInfo(String rcv);
	
	/**
	 * 查询医院信息
	 *
	 * @param rcv 部分医院信息
	 * @return 医院信息
	 */
	String getHospitalInfo(String rcv);
}
