package com.ucmed.bean.controllerbean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="第三方绑定",description="第三方绑定模型")
public class ThridPartyBindBean {
    @ApiModelProperty(value="手机号（账号）", name="phone", required = true, example="string")
    private String phone;
    @ApiModelProperty(value="第三方openId", name="openId", required = true, example="string")
    private String openId;
    @ApiModelProperty(value="角色", name="roleName", required = true, example="string")
    private String roleName;
    @ApiModelProperty(value="第三方类型（1：微信 2：企业微信 3：支付宝 4：其他）", name="thirdPartyType", required = true, example="string")
    private String thirdPartyType;


    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getThirdPartyType() {
        return thirdPartyType;
    }

    public void setThirdPartyType(String thirdPartyType) {
        this.thirdPartyType = thirdPartyType;
    }
}
