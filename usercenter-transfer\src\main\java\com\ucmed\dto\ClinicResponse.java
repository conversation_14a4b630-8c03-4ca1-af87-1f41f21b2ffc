package com.ucmed.dto;

import java.io.Serializable;
import java.util.List;

/**
 * 线下门诊管理系统
 * 按条件分页获取用户列表返回对象
 *
 * <AUTHOR>
 * @date 2017/10/25 15:53
 */
public class ClinicResponse implements Serializable {
    private static final long serialVersionUID = 3623830950825157098L;

    /**
     * 返回码 0代表成功
     */
    private int retCode;
    /**
     * 返回信息
     */
    private String retInfo;
    /**
     * 当前页数
     */
    private Integer pageCount;
    /**
     * 总记录数
     */
    private Integer totalCount;
    /**
     * 用户列表
     */
    List<ClinicUserInfo> users;

    public ClinicResponse(int retCode, String retInfo, Integer pageCount, Integer totalCount, List<ClinicUserInfo> users) {
        this.retCode = retCode;
        this.retInfo = retInfo;
        this.pageCount = pageCount;
        this.totalCount = totalCount;
        this.users = users;
    }

    public int getRetCode() {
        return retCode;
    }

    public void setRetCode(int retCode) {
        this.retCode = retCode;
    }

    public String getRetInfo() {
        return retInfo;
    }

    public void setRetInfo(String retInfo) {
        this.retInfo = retInfo;
    }

    public Integer getPageCount() {
        return pageCount;
    }

    public void setPageCount(Integer pageCount) {
        this.pageCount = pageCount;
    }

    public Integer getTotalCount() {
        return totalCount;
    }

    public void setTotalCount(Integer totalCount) {
        this.totalCount = totalCount;
    }

    public List<ClinicUserInfo> getUsers() {
        return users;
    }

    public void setUsers(List<ClinicUserInfo> users) {
        this.users = users;
    }
}
