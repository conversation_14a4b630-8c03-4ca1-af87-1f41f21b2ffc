package com.ucmed.common.dao;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.stereotype.Repository;

import java.beans.IntrospectionException;
import java.beans.PropertyDescriptor;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

/**
 * Created by QIUJIAHAO on 2016/7/22.
 */
@Repository("baseDao")
public class BaseDaoImpl<T> implements BaseDao<T> {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    public JdbcTemplate getJdbcTemplate() {
        return jdbcTemplate;
    }

    /**
     * 查询
     * @param table 表名
     * @param conditions 条件字段
     * @param parameters 参数值
     * @param clazz 映射对象
     * @return
     */
    public List<T> query(String table, String[] conditions, Object[] parameters, Class<T> clazz) {
        List<T> resultList = null;
        String sql = "SELECT ";
        // 获取实体类字段
        List<String> fields = getBeanField(clazz);
        // 拼接查询字段
        for (int i = 0; i < fields.size(); i++) {
            if(i != 0) sql += ",";
            sql += fields.get(i);
        }

        sql += " FROM " + table;

        try {
            if (parameters != null && parameters.length > 0 && conditions != null && conditions.length > 0) {
                sql += " WHERE ";
                // 拼接条件字段
                for (int i = 0; i < conditions.length; i++) {
                    if(i != 0) sql += " AND ";
                    sql += conditions[i] + " = ?";
                }
                resultList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(clazz), parameters);
            }
            else
                resultList = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(clazz));
        } catch (Exception e) {
            e.printStackTrace();
        }
        return resultList;
    }

    /**
     * 获取实体类属性名
     * @param clazz 实体类
     * @return
     */
    private List<String> getBeanField(Class clazz) {
        List<String> fields = new ArrayList<>();
        Field[] classFields = clazz.getDeclaredFields();
        for (Field field : classFields) {
            try {
                PropertyDescriptor pd =new PropertyDescriptor(field.getName(),clazz);
                Method getMethod = pd.getReadMethod();
            } catch (IntrospectionException e) {
               continue;
            }
            fields.add(field.getName());
        }
        return fields;
    }

    /**
     * 查询记录条数
     * @param table 表名
     * @param conditions 条件字段
     * @param parameters 参数值
     * @return
     */
    public int count(String table, String[] conditions, Object[] parameters) {
        int count;
        String sql = "SELECT COUNT(*) FROM " + table;
        if(conditions != null && conditions.length > 0 && parameters != null && parameters.length > 0) {
            sql += " WHERE ";
            for (int i = 0; i < conditions.length; i++) {
                if (i != 0) sql += " AND ";
                sql += conditions[i] + " = ?";
            }
            count = jdbcTemplate.queryForObject(sql, Integer.class, parameters);
        } else {
            count = jdbcTemplate.queryForObject(sql, Integer.class);
        }
        return count;
    }

    /**
     * 更新
     * @param table 表名
     * @param fields 更新字段
     * @param conditions 条件字段
     * @param parameters 参数值
     * @return
     */
    public int update(String table, String[] fields, String[] conditions, final Object[] parameters) {
        int result = -1;
        String sql = "UPDATE " + table + " SET ";

        if(fields != null && fields.length > 0) {
            // 拼接更新字段
            for (int i = 0; i < fields.length; i++) {
                if (i != 0) sql += ", ";
                sql += fields[i] + " = ?";
            }

            try {
                if (parameters != null && parameters.length > 0 && conditions != null && conditions.length > 0) {
                    sql += " WHERE ";
                    // 拼接条件字段
                    for (int i = 0; i < conditions.length; i++) {
                        if (i != 0) sql += " AND ";
                        sql += conditions[i] + " = ?";
                    }
                    result = jdbcTemplate.update(sql, new PreparedStatementSetter() {
                        @Override
                        public void setValues(PreparedStatement ps) throws SQLException {
                            for (int i = 0; i < parameters.length; i++) {
                                ps.setObject(i + 1, parameters[i]);
                            }
                        }
                    });
                }
                else
                    result = jdbcTemplate.update(sql);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        return result;
    }

    /**
     * 插入
     * @param table 表名
     * @param fields 字段名集合
     * @param parameters 参数值
     * @return
     */
    public int insert(String table, String[] fields, final Object[] parameters) {
        int result;
        String sql = "INSERT INTO " + table + "(";

        if(fields != null && fields.length > 0) {
            // 拼接插入字段
            for (int i = 0; i < fields.length; i++) {
                if (i != 0) sql += ", ";
                sql += fields[i];
            }
        }
        sql += ") VALUES(";
        for(int i = 0; i < fields.length; i++) {
            if(i != 0) sql += ",";
            sql += "?";
        }
        sql += ")";

        if (parameters != null && parameters.length > 0) {
            result = jdbcTemplate.update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement ps) throws SQLException {
                    for (int i = 0; i < parameters.length; i++) {
                        ps.setObject(i + 1, parameters[i]);
                    }
                }
            });
        } else{
            result = jdbcTemplate.update(sql);
        }
        return result;
    }

    /**
     * 删除
     * @param table 表名
     * @param conditions 条件字段
     * @param parameters 参数值
     * @return
     */
    public int delete(String table, String[] conditions, final Object[] parameters) {
        int result;
        String sql = "DELETE FROM " + table + " WHERE ";
        // 拼接条件字段
        for (int i = 0; i < conditions.length; i++) {
            if(i != 0) sql += " AND ";
            sql += conditions[i] + " = ?";
        }

        result = jdbcTemplate.update(sql, new PreparedStatementSetter() {
            @Override
            public void setValues(PreparedStatement ps) throws SQLException {
                for (int i = 0; i < parameters.length; i++) {
                    ps.setObject(i + 1, parameters[i]);
                }
            }
        });

        return result;
    }

    public int count(String sql) {
        return jdbcTemplate.queryForObject(sql, Integer.class);
    }

}
