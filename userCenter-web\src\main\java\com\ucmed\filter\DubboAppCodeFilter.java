package com.ucmed.filter;

import com.alibaba.dubbo.common.extension.Activate;
import com.alibaba.dubbo.rpc.*;
import com.alibaba.fastjson.JSONObject;
import com.ucmed.common.service.CommonService;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;

import java.lang.reflect.Method;
import java.lang.reflect.Parameter;
import java.util.ArrayList;
import java.util.List;

@Activate
public class DubboAppCodeFilter implements Filter {
    private static Logger log = Logger.getLogger(DubboAppCodeFilter.class.getName());
    @Autowired
    CommonService commonService;


    @Override
    public Result invoke(Invoker<?> invoker, Invocation invocation) throws RpcException {
        String name = invoker.getInterface().getName();
        Object[] args = invocation.getArguments();
        String method = invocation.getMethodName();
        String prefix = "日志：" + name + "." + method;
        List<String> paramterNames = null;
        try {
            paramterNames = getParameterName(Class.forName(name), method, args.length);
        } catch (ClassNotFoundException e) {
            e.printStackTrace();
        }
        if (paramterNames != null) {
            for (int i = 0; i < paramterNames.size(); i++) {
                if (paramterNames.get(i).equals("appCode")) {
                    args[i] = commonService.getAppCodeByAppToken((Integer) args[i]);
                    break;
                }
            }
        }
        Result r = invoker.invoke(invocation);
        if (r.hasException()) {
            Throwable e = r.getException();
            if (e.getClass().getName().equals("java.lang.RuntimeException")) {
                log.error(prefix + " 运行时异常=>" + JSONObject.toJSONString(r));
            } else {
                log.error(prefix + " 异常=>" + JSONObject.toJSONString(r));
            }
        } else {
//            log.info(prefix + " 结果=>" + JSONObject.toJSONString(r));
//            log.info(prefix + "正常调用成功");
        }
        return r;
    }

    public static List<String> getParameterName(Class clazz, String methodName, Integer paramCount) {
        List<String> paramterList = new ArrayList<>();
        Method[] methods = clazz.getDeclaredMethods();
        for (Method method : methods) {
            if (methodName.equals(method.getName())) {
                //直接通过method就能拿到所有的参数
                Parameter[] params = method.getParameters();
                if (paramCount.equals(params.length)) {
                    for (Parameter parameter : params) {
                        paramterList.add(parameter.getName());
                    }
                    break;
                }
            }
        }
        return paramterList;
    }

    public CommonService getCommonService() {
        return commonService;
    }

    public void setCommonService(CommonService commonService) {
        this.commonService = commonService;
    }
}
