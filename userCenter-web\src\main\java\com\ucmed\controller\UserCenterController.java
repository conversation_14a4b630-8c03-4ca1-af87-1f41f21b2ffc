package com.ucmed.controller;

import com.ucmed.bean.Application;
import com.ucmed.bean.NUCResponse;
import com.ucmed.bean.UCResponse;
import com.ucmed.bean.UserInfo;
import com.ucmed.bean.controllerbean.CancelAuthorizationParam;
import com.ucmed.bean.retbean.UserProjectInfoVO;
import com.ucmed.common.exception.UnknownAppException;
import com.ucmed.dto.UserPush;
import com.ucmed.exception.UnknownAccountException;
import com.ucmed.service.CaptchaService;
import com.ucmed.service.GetAppInfoService;
import com.ucmed.service.UserInfoService;
import com.ucmed.service.UserService;
import io.swagger.annotations.*;
import net.sf.json.JSONObject;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.math.BigInteger;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.util.List;

@RestController
@Api(value = "用户中心", description = "用户中心")
public class UserCenterController {
    @Autowired
    private UserService userService;
    @Autowired
    private CaptchaService captchaService;
    @Autowired
    private UserInfoService userInfoService;

    @ApiOperation(
            value = "普通用户注册",
            notes = "用户输入手机或用户名，密码，角色进行注册\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "roleName", value = "角色", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query", dataType = "int"),
    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "应用未注册"),
            @ApiResponse(code = -3, message = "角色不存在"),
            @ApiResponse(code = 3, message = "密码太简单，建议使用大小写字母、数字和特殊字符"),
            @ApiResponse(code = 4, message = "用户名不合法"),
            @ApiResponse(code = 5, message = "手机号不合法"),
            @ApiResponse(code = 6, message = "此用户不在白名单内，无法注册"),
            @ApiResponse(code = 7, message = "此用户在黑名单内，无法注册")
    })
    @RequestMapping(value = "/user/registration", method = RequestMethod.POST)
    public ResponseEntity<String> registration(HttpServletRequest request,
                                               String userId, String phone, String password, String roleName, Integer appCode) {
        if (appCode == null) {
            appCode = 0;
        }
        String responseJson = JSONObject.fromObject(userService.registration(userId, phone, password, appCode, roleName)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "普通用户注册(带姓名与邮箱)",
            notes = "用户输入手机或用户名，密码，角色进行注册\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "roleName", value = "角色", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "name", value = "用户姓名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "email", value = "邮箱", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "应用未注册"),
            @ApiResponse(code = -3, message = "角色不存在"),
            @ApiResponse(code = 3, message = "密码太简单，建议使用大小写字母、数字和特殊字符"),
            @ApiResponse(code = 4, message = "用户名不合法"),
            @ApiResponse(code = 5, message = "手机号不合法"),
            @ApiResponse(code = 6, message = "此用户不在白名单内，无法注册"),
            @ApiResponse(code = 7, message = "此用户在黑名单内，无法注册"),
            @ApiResponse(code = 8, message = "邮箱不合法")
    })
    @RequestMapping(value = "/user/registrationWithEmail", method = RequestMethod.POST)
    public ResponseEntity<String> registrationWithEmail(HttpServletRequest request,
                                                        String userId, String phone, String password, String roleName, Integer appCode, String name, String email) {
        if (appCode == null) {
            appCode = 0;
        }
        String responseJson = JSONObject.fromObject(userService.registration(userId, phone, password, appCode, roleName, name, email)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "普通用户短信注册",
            notes = "用户输入手机或用户名，密码，角色,短信验证码进行注册\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "roleName", value = "角色", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "msgCode", value = "短信验证码", required = true, paramType = "query", dataType = "String")
    })
    @ApiResponses({
            @ApiResponse(code = -2, message = "验证验证码失败"),
            @ApiResponse(code = 0, message = "验证成功"),
    })
    @RequestMapping(value = "/user/smsRegistration", method = RequestMethod.POST)
    public ResponseEntity<String> registrationWithMsgCode(HttpServletRequest request, String userId, String phone,
                                                          String password, String roleName, Integer appCode, String msgCode) {
        if (appCode == null) {
            appCode = 0;
        }
        String responseJson = JSONObject.fromObject(userService.registration(userId, phone, password, appCode, roleName, msgCode)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "普通用户登录",
            notes = "用户输入用户名，密码进行登录\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "roleName", value = "角色", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),

    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "应用未注册"),
            @ApiResponse(code = 0, message = "登录成功"),
            @ApiResponse(code = 1, message = "密码错误"),
            @ApiResponse(code = 2, message = "账号被锁定,请联系管理员"),
            @ApiResponse(code = 4, message = "账号未注册"),
            @ApiResponse(code = 5, message = "用户未授权，是否确定使用该应用？"),
            @ApiResponse(code = 6, message = "角色未授权"),
    })
    @RequestMapping(value = "/user/login", method = RequestMethod.POST)
    public ResponseEntity<String> login(HttpServletRequest request,
                                        String userId,
                                        String password,
                                        String roleName,
                                        Integer appCode) {
        if (appCode == null) {
            appCode = 0;
        }
        String responseJson = JSONObject.fromObject(userService.login(userId, password, appCode, roleName)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "普通用户短信登录",
            notes = "用户输入用户名，短信验证码进行登录\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "msgCode", value = "短信验证码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "roleName", value = "角色", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),

    })
    @ApiResponses({
            @ApiResponse(code = -2, message = "验证验证码失败"),
            @ApiResponse(code = -1, message = "应用未注册"),
            @ApiResponse(code = 0, message = "登录成功"),
            @ApiResponse(code = 1, message = "密码错误"),
            @ApiResponse(code = 2, message = "账号被锁定,请联系管理员"),
            @ApiResponse(code = 4, message = "账号未注册"),
            @ApiResponse(code = 5, message = "用户未授权，是否确定使用该应用？"),
            @ApiResponse(code = 6, message = "角色未授权"),
    })
    @RequestMapping(value = "/user/smsLogin", method = RequestMethod.POST)
    public ResponseEntity<String> smsLogin(HttpServletRequest request,
                                           String userId,
                                           String msgCode,
                                           String roleName,
                                           Integer appCode) {
        if (appCode == null) {
            appCode = 0;
        }
        String responseJson = JSONObject.fromObject(userService.smsLogin(userId, msgCode, appCode, roleName)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "普通用户授权",
            notes = "用户应用授权\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "roleName", value = "角色", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = -3, message = "角色不存在"),
            @ApiResponse(code = -1, message = "应用未注册"),
            @ApiResponse(code = 0, message = "授权成功"),
            @ApiResponse(code = 1, message = "账号未注册"),
    })
    @RequestMapping(value = "/user/setPermission", method = RequestMethod.POST)
    public ResponseEntity<String> setPermission(HttpServletRequest request,
                                                String userId,
                                                String roleName,
                                                Integer appCode) {
        if (appCode == null) {
            appCode = 0;
        }
        String responseJson = JSONObject.fromObject(userService.setPermission(userId, appCode, roleName)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "普通用户更改密码",
            notes = "用户输入新密码和旧密码，验证token\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "newPwd", value = "新密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "oldPwd", value = "旧密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "token", value = "就是token", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "修改密码成功"),
            @ApiResponse(code = 1, message = "新密码太简单，建议使用大小写字母、数字和特殊字符"),
            @ApiResponse(code = 2, message = "修改密码失败，旧密码错误"),
            @ApiResponse(code = 3, message = "该账号未注册"),
            @ApiResponse(code = 401, message = "会话已失效"),

    })
    @ApiIgnore
    @RequestMapping(value = "/user/changePassword", method = RequestMethod.PUT)
    public ResponseEntity<String> changePassword(HttpServletRequest request,
                                                 String newPwd, String oldPwd, String token) {
        String responseJson = JSONObject.fromObject(userService.changePwd(newPwd, oldPwd, token)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "普通用户更改密码",
            notes = "用户输入新密码和旧密码，验证token\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "newPwd", value = "新密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "oldPwd", value = "旧密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "token", value = "就是token", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "修改密码成功"),
            @ApiResponse(code = 1, message = "新密码太简单，建议使用大小写字母、数字和特殊字符"),
            @ApiResponse(code = 2, message = "修改密码失败，旧密码错误"),
            @ApiResponse(code = 3, message = "该账号未注册"),
            @ApiResponse(code = 401, message = "会话已失效"),

    })
    @RequestMapping(value = "/user/changeUserPassword", method = RequestMethod.POST)
    public ResponseEntity<String> changeUserPassword(HttpServletRequest request,
                                                 String newPwd, String oldPwd, String token) {
        String responseJson = JSONObject.fromObject(userService.changePwd(newPwd, oldPwd, token)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "普通用户通过短信验证码重置密码",
            notes = "用户输入新的密码和短信验证码（若不输入appcode无法使用万能验证码）\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机/用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "newPwd", value = "新密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "msgCode", value = "验证码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "验证验证码失败"),
            @ApiResponse(code = 0, message = "验证成功"),
    })
    @ApiIgnore
    @RequestMapping(value = "/user/reInputPassword", method = RequestMethod.PUT)
    public ResponseEntity<String> reInputPassword(HttpServletRequest request, Integer appCode,
                                                  String phone, String newPwd, String msgCode) {
        String responseJson;

        if (appCode == null) {
            responseJson = JSONObject.fromObject(userService.reInputPassword(phone, msgCode, newPwd)).toString();
        } else {
            responseJson = JSONObject.fromObject(userService.reInputPassword(appCode, phone, msgCode, newPwd)).toString();
        }
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "普通用户通过短信验证码重置密码",
            notes = "用户输入新的密码和短信验证码（若不输入appcode无法使用万能验证码）\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机/用户名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "newPwd", value = "新密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "msgCode", value = "验证码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "验证验证码失败"),
            @ApiResponse(code = 0, message = "验证成功"),
    })
    @RequestMapping(value = "/user/reInputUserPassword", method = RequestMethod.POST)
    public ResponseEntity<String> reInputUserPassword(HttpServletRequest request, Integer appCode,
                                                  String phone, String newPwd, String msgCode) {
        String responseJson;

        if (appCode == null) {
            responseJson = JSONObject.fromObject(userService.reInputPassword(phone, msgCode, newPwd)).toString();
        } else {
            responseJson = JSONObject.fromObject(userService.reInputPassword(appCode, phone, msgCode, newPwd)).toString();
        }
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "普通用户登出",
            notes = "用户退出登录\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "就是token", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "退出登录成功"),
    })
    @RequestMapping(value = "/user/logout", method = RequestMethod.POST)
    public ResponseEntity<String> logout(String token) {
        String responseJson = JSONObject.fromObject(userService.logout(token)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "普通用户更换手机号码",
            notes = "普通用户更换手机号码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "就是token", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "newPhone", value = "新手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "msgCode", value = "短信验证码", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "验证短信验证码失败"),
            @ApiResponse(code = 0, message = "手机号修改成功"),
            @ApiResponse(code = 2, message = "手机号不合法"),
            @ApiResponse(code = 3, message = "手机号已注册"),
            @ApiResponse(code = 401, message = "会话已失效"),
    })
    @RequestMapping(value = "/user/changePhone", method = RequestMethod.POST)
    public ResponseEntity<String> changePhone(String token, String newPhone, String msgCode) {
        String responseJson = JSONObject.fromObject(userService.changePhone(token, msgCode, newPhone)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "普通用户根据卓健项目openid更换手机号码",
            notes = "普通用户根据卓健项目openid更换手机号码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "卓健项目openid", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "newPhone", value = "新手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "msgCode", value = "短信验证码", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -1, message = "验证短信验证码失败"),
            @ApiResponse(code = 0, message = "手机号修改成功"),
            @ApiResponse(code = 2, message = "手机号不合法"),
            @ApiResponse(code = 3, message = "手机号已注册"),
            @ApiResponse(code = 401, message = "会话已失效"),
    })
    @RequestMapping(value = "/user/changePhoneByOpenId", method = RequestMethod.POST)
    public ResponseEntity<String> changePhoneByOpenId(String openId, String msgCode, String newPhone) {
        String responseJson = JSONObject.fromObject(userService.changePhoneByOpenId(openId, msgCode, newPhone)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "获取用户信息",
            notes = "获取用户信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "就是token", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "查询成功"),
    })
    @RequestMapping(value = "/user/userInfo", method = RequestMethod.GET)
    public ResponseEntity<String> getUserInfo(String token) {

        String responseJson = JSONObject.fromObject(userService.getUserInfo(token)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "更新用户信息（废用）",
            notes = "更新用户信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "就是token", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "更新成功"),
            @ApiResponse(code = 401, message = "会话已失效"),
            @ApiResponse(code = 412, message = "异常信息"),
    })
    @ApiIgnore
    @RequestMapping(value = "/user/updateUserInfo", method = RequestMethod.POST)
    public ResponseEntity<String> updateUserInfo(String token, UserInfo userInfo) {
        String responseJson = JSONObject.fromObject(userService.updateUserInfo(token, userInfo)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "更新用户信息(新)",
            notes = "更新用户信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "就是token", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "更新成功"),
            @ApiResponse(code = 401, message = "会话已失效"),
            @ApiResponse(code = 412, message = "异常信息"),
    })
    @RequestMapping(value = "/user/updateUserInfos", method = RequestMethod.POST)
    public ResponseEntity<String> updateUserInfos(String token, @RequestBody  UserInfo userInfo) {
        String responseJson = JSONObject.fromObject(userService.updateUserInfo(token, userInfo)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "用第三方的openId获取用户信息",
            notes = "用openId获取用户信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "就是token", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "查询成功"),
            @ApiResponse(code = 1, message = "此用户未注册"),
    })
    @RequestMapping(value = "/user/getUserInfoByOpenId", method = RequestMethod.POST)
    public ResponseEntity<String> getUserInfoByOpenId(String openId, String appCode) {
        return createResponseEntity(JSONObject.fromObject(userInfoService.getUserInfoByOpenId(openId, appCode)).toString());
    }

    /**
     * 获取图形验证码
     *
     * @param request
     * @param phone           手机号
     * @param type            验证码类型0:注册 1:忘记密码 2:登录 3:修改手机号 4：其他
     * @param checkoutAccount 是否校验账户，默认true，会校验账号是否已注册等
     * @return
     */
    @ApiOperation(
            value = "图形验证码",
            notes = "图形验证码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用 5：绑定就诊人 99:其他", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "checkoutAccount", value = "是否自定义长度", paramType = "query", dataType = "boolean"),
            @ApiImplicitParam(name = "length", value = "验证码长度长度", paramType = "query", dataType = "int"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "成功获取图片验证码"),
    })
    @RequestMapping(value = "/picturecode", method = RequestMethod.GET)
    public ResponseEntity<String> picturecode(HttpServletRequest request, String phone, String type, Boolean checkoutAccount) {
        if (checkoutAccount == null) {
            checkoutAccount = true;
        }
        String length = request.getParameter("length");
        String responseJson;
        if (checkoutAccount) {
            if (length == null) {
                responseJson = JSONObject.fromObject(userService.generatePictureValidateCode(phone, type)).toString();
            } else {
                responseJson = JSONObject.fromObject(userService.generatePictureValidateCode(phone, type, Integer.parseInt(length))).toString();
            }
        } else {
            if (length == null) {
                length = "4";
            }
            responseJson = JSONObject.fromObject(captchaService.getCommonImageCaptcha(phone, type, Integer.parseInt(length))).toString();
        }
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "获取图形验证码（带appCode）",
            notes = "获取图形验证码（带appCode）\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用 5：绑定就诊人 99:其他", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query", dataType = "int"),
            @ApiImplicitParam(name = "length", value = "长度", paramType = "query", dataType = "int"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "成功获取图片验证码"),
    })
    @RequestMapping(value = "/picturecodeWithAppCode", method = RequestMethod.GET)
    public ResponseEntity<String> picturecode(String phone, Integer appCode, String type, Integer length) {
        String responseJson;
        if (length == null || length == 0) {
            length = 4;
        }
        responseJson = JSONObject.fromObject(userService.generatePictureValidateCode(phone, appCode, type, length)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "验证图形验证码",
            notes = "验证图形验证码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用 5：绑定就诊人 99:其他", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "picCode", value = "图形验证码", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -2, message = "验证码输入错误，请重新输入"),
            @ApiResponse(code = -1, message = "图形验证码过期，请刷新后重试"),
            @ApiResponse(code = 0, message = "图形验证码验证成功"),

    })
    @RequestMapping(value = "/picturecode/verify", method = RequestMethod.GET)
    public ResponseEntity<String> verifyPictureValidateCode(String phone, String type, String picCode) {
        String responseJson = JSONObject.fromObject(userService.verifyPictureValidateCode(phone, type, picCode)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "获取短信验证码",
            notes = "获取短信验证码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用 5：绑定就诊人 99:其他", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "picCode", value = "图形验证码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "checkoutAccount", value = "是否检查用户为卓健用户", paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "smsToken", value = "短信token（checkoutAccount为false时生效）", paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -2, message = "验证码输入错误，请重新输入"),
            @ApiResponse(code = -1, message = "图形验证码过期，请刷新后重试"),
            @ApiResponse(code = 0, message = "获取验证码成功，请查看您的手机短信"),
    })
    @RequestMapping(value = "/messagecode", method = RequestMethod.GET)
    public ResponseEntity<String> messagecode(String phone,
                                              String type,
                                              Integer appCode,
                                              String picCode,
                                              Boolean checkoutAccount,
                                              String smsToken) {
        if (appCode == null) {
            appCode = 0;
        }
        if (checkoutAccount == null) {
            checkoutAccount = true;
        }
        String responseJson;
        if (checkoutAccount) {
            responseJson = JSONObject.fromObject(userService.sendMsgCode(phone, appCode, type, picCode)).toString();
        } else {
            responseJson = JSONObject.fromObject(captchaService.sendMsgCode(smsToken, phone, type, picCode)).toString();
        }
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "获取短信验证码（用下面那个，谢谢！！！messagecode那个）",
            notes = "获取短信验证码不用图形验证码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用 5：绑定就诊人 99:其他", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "smsToken", value = "smsToken", required = false, paramType = "query"),
            @ApiImplicitParam(name = "length", value = "仅在smsToken有效时有效", required = false, paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "获取验证码成功，请查看您的手机短信"),
    })

    @RequestMapping(value = "/getmessagecode", method = RequestMethod.GET)
    public ResponseEntity<String> getmessagecode(String phone,
                                                 String type,
                                                 Integer appCode,
                                                 String smsToken,
                                                 Integer length) {
        if (appCode == null) {
            appCode = 0;
        }
        if (length == null) {
            length = 6;
        }
        String responseJson;
        if (smsToken != null) {
            responseJson = JSONObject.fromObject(captchaService.sendMsgCode(appCode, smsToken, phone, type, length)).toString();
        } else {
            responseJson = JSONObject.fromObject(userService.sendMsgCode(phone, appCode, type)).toString();
        }

        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "验证短信验证码",
            notes = "验证短信验证码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "type", value = "0:注册 1:忘记密码 2:登录 3:修改手机号 4:绑定第三方应用 5：绑定就诊人 99:其他", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "msgCode", value = "短信验证码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "invalidWhenVerify", value = "若成功验证后，验证码是否失效（true：失效，false：继续生效）", paramType = "query", dataType = "boolean"),
    })
    @ApiResponses({
            @ApiResponse(code = -2, message = "验证验证码失败，电话号码不正确"),
            @ApiResponse(code = -1, message = "验证码失效或者输入不正确,请重新获取"),
            @ApiResponse(code = 0, message = "验证成功"),
    })
    @RequestMapping(value = "/messagecode/verify", method = RequestMethod.GET)
    public ResponseEntity<String> verifyMessagecode(HttpServletRequest request, String phone,
                                                    String type,
                                                    String msgCode,
                                                    boolean invalidWhenVerify) {
        if (!invalidWhenVerify) {
            invalidWhenVerify = Boolean.parseBoolean(request.getParameter("invalidWhenVerify"));
        }
        String responseJson = JSONObject.fromObject(userService.verifyMessageCode(phone, msgCode, type, invalidWhenVerify)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "用卓健项目openId获取用户推送信息",
            notes = "用openId获取用户推送信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openIds", value = "openId列表", required = true, paramType = "query", dataType = "list"),
    })
    @RequestMapping(value = "/user/getUserPushByOpenId", method = RequestMethod.POST)
    public ResponseEntity<String> getUserPushByOpenId(@RequestBody List<String> openIds) {
        //JSONObject.fromObject不能直接传送一个List变量，下面是在仿造一个json格式的数据
        StringBuilder stringBuilder = new StringBuilder("{\"userPush\":[");
        String responseJson = "";
        try {
            List<UserPush> userPushes = userService.getUserPushByOpenId(openIds);
            for (UserPush userPush : userPushes) {
                stringBuilder.append(JSONObject.fromObject(userPush).toString()).append(",");
            }
        } catch (UnknownAccountException e) {
            e.printStackTrace();
        }
        responseJson += stringBuilder;
        if (responseJson.lastIndexOf(",") != -1) {
            responseJson = responseJson.substring(0, responseJson.length() - 1);
        }
        responseJson += "]}";
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "用卓健项目的openId获取用户信息",
            notes = "用openId获取用户信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 401, message = "账号未注册"),
    })
    @RequestMapping(value = "/user/getUserByOpenId", method = RequestMethod.GET)
    public ResponseEntity<String> getUserByOpenId(String openId) {
        String responseJson = null;
        try {
            responseJson = JSONObject.fromObject(userService.getUserByOpenId(openId)).toString();
        } catch (UnknownAccountException e) {
            e.printStackTrace();
        }
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "用卓健项目openId更新用户信息(废用）",
            notes = "用openId更新用户信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "更新成功"),
            @ApiResponse(code = 401, message = "openId不存在"),
            @ApiResponse(code = 412, message = "异常信息"),
    })
    @ApiIgnore
    @RequestMapping(value = "/user/updateUserInfoByOpenId", method = RequestMethod.POST)
    public ResponseEntity<String> updateUserInfoByOpenId(String openId, UserInfo userInfo) {
        String responseJson;
        responseJson = JSONObject.fromObject(userService.updateUserInfoByOpenId(openId, userInfo)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "用卓健项目openId更新用户信息(新)",
            notes = "用openId更新用户信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "openId", value = "openId", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "更新成功"),
            @ApiResponse(code = 401, message = "openId不存在"),
            @ApiResponse(code = 412, message = "异常信息"),
    })
    @RequestMapping(value = "/user/updateUserInfosByOpenId", method = RequestMethod.POST)
    public ResponseEntity<String> updateUserInfosByOpenId(String openId, @RequestBody UserInfo userInfo) {
        String responseJson;
        responseJson = JSONObject.fromObject(userService.updateUserInfoByOpenId(openId, userInfo)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "验证用户token有效性",
            notes = "验证用户token有效性\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "token", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
    })
    @RequestMapping(value = "/user/verifyUserToken", method = RequestMethod.GET)
    public ResponseEntity<String> verifyUserToken(String token) {
        String responseJson = null;
        responseJson = JSONObject.fromObject(userService.verifyUserToken(token)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "获取该用户在卓健项目的所有openId",
            notes = "获取该用户的所有openId\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "token", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
    })
    @RequestMapping(value = "/user/getOpenIdListByToken", method = RequestMethod.GET)
    public ResponseEntity<String> getOpenIdListByToken(String token) {
        String responseJson = null;
        responseJson = JSONObject.fromObject(userService.getOpenIdByToken(token)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "获取该用户在该卓健项目的openId",
            notes = "获取该用户的所有openId\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "token", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
    })
    @RequestMapping(value = "/user/getOpenIdByToken", method = RequestMethod.GET)
    public ResponseEntity<String> getOpenIdByToken(String token, Integer appCode) {
        if (appCode == null) {
            appCode = 0;
        }
        String responseJson = null;
        responseJson = JSONObject.fromObject(userService.getOpenIdByToken(appCode, token)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiOperation(
            value = "清除内存属性缓存（用户中心后台用）",
            notes = "清除内存属性缓存（用户中心后台用）\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "password", value = "password", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
    })
    @RequestMapping(value = "/user/resetCache", method = RequestMethod.POST)
    public ResponseEntity<String> resetCache(String password) {

        String responseJson = null;
        responseJson = JSONObject.fromObject(userService.resetCache(password)).toString();
        return createResponseEntity(responseJson);
    }


    @ApiIgnore
    @RequestMapping(value = "/user/download/registeredPhoneExcel", method = RequestMethod.GET)
    public void downloadRegisteredPhoneExcel(String startTime, String endTime, String projCode, HttpServletResponse response) {
        userService.downloadRegisteredPhoneExcel(startTime, endTime, projCode, response);
    }

    @ApiOperation(
            value = "获取注册用户的手机号excel文件",
            notes = "获取注册用户的手机号excel文件\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "startTime", value = "开始时间 例：2018-12-01 00:00:00（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String", defaultValue = "2018-12-01 00:00:00"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 例：2018-12-01 00:00:00（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String", defaultValue = "2018-12-12 23:59:59"),
            @ApiImplicitParam(name = "appCode", value = "应用id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "mouth", value = "时间跨度限制，单位：月（默认为3,0为无限制）", paramType = "query", dataType = "String"),
    })
    @ApiResponses({
    })
    @RequestMapping(value = "/user/registeredPhoneExcel", method = RequestMethod.GET)
    public ResponseEntity<JSONObject> downloadRegisteredPhoneExcel(String startTime, String endTime, Integer appCode, Integer mouth) {
        JSONObject responseJson = null;
        if (mouth == null) {
            mouth = 3;
        }
        if (appCode == null) {
            appCode = 0;
        }
        responseJson = JSONObject.fromObject(userService.downloadRegisteredPhoneExcel(startTime, endTime, appCode, mouth));
        return createResponseEntityAllowOrigin(responseJson);
    }

    @ApiOperation(
            value = "列出该项目下一段时间内所有注册用户（仅手机号与注册时间）",
            notes = "列出该项目下一段时间内所有注册用户\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageNum", value = "页码（默认1）",  paramType = "query"),
            @ApiImplicitParam(name = "pageSize", value = "页大小（默认10）",  paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "开始时间 例：2018-12-01 00:00:00（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String", defaultValue = "2018-12-01 00:00:00"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 例：2018-12-01 00:00:00（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String", defaultValue = "2018-12-12 23:59:59"),
            @ApiImplicitParam(name = "appCode", value = "应用id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "mouth", value = "时间跨度限制，单位：月（默认为3,0为无限制）", paramType = "query", dataType = "String"),
    })
    @ApiResponses({
    })
    @RequestMapping(value = "/user/listRegisteredPhone", method = RequestMethod.GET)
    public ResponseEntity<JSONObject> listRegisteredPhone(Integer pageNum, Integer pageSize, String startTime,
                                                          String endTime, Integer appCode, Integer mouth) {
        if (pageNum == null || pageNum < 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize < 0) {
            pageSize = 10;
        }
        if (mouth == null || mouth < 0) {
            mouth = 3;
        }
        if (appCode == null) {
            appCode = 0;
        }
        JSONObject responseJson = null;
        responseJson = JSONObject.fromObject(userService.listRegisteredPhone(pageNum, pageSize, startTime, endTime, appCode, mouth));
        return createResponseEntityAllowOrigin(responseJson);
    }

    @ApiOperation(
            value = "列出该项目下一段时间内所有注册用户数量（用于分页）",
            notes = "列出该项目下一段时间内所有注册用户数量\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "pageSize", value = "页大小（默认10）",  paramType = "query"),
            @ApiImplicitParam(name = "startTime", value = "开始时间 例：2018-12-01 00:00:00（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String", defaultValue = "2018-12-01 00:00:00"),
            @ApiImplicitParam(name = "endTime", value = "结束时间 例：2018-12-01 00:00:00（yyyy-MM-dd HH:mm:ss）", required = true, paramType = "query", dataType = "String", defaultValue = "2018-12-12 23:59:59"),
            @ApiImplicitParam(name = "appCode", value = "应用id", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "mouth", value = "时间跨度限制，单位：月（默认为3,0为无限制）", paramType = "query", dataType = "String"),
    })
    @ApiResponses({
    })
    @RequestMapping(value = "/user/countRegisteredPhone", method = RequestMethod.GET)
    public ResponseEntity<JSONObject> countRegisteredPhone(String startTime, String endTime,
                                                           Integer appCode, Integer pageSize, Integer mouth) {
        if (pageSize == null || pageSize < 0) {
            pageSize = 10;
        }
        if (mouth == null || mouth < 0) {
            mouth = 3;
        }
        if (appCode == null) {
            appCode = 0;
        }
        JSONObject responseJson = null;
        responseJson = JSONObject.fromObject(userService.countRegisteredPhone(startTime, endTime, appCode, pageSize, mouth));
        return createResponseEntityAllowOrigin(responseJson);
    }


    @ApiOperation(
            value = "检查用户是否在该appCode所在的项目中任意app授权了",
            notes = "检查用户是否在这个项目中任意app授权了\n"
    )
    @RequestMapping(value = "/checkUserInProject", method = RequestMethod.POST)
    public ResponseEntity<NUCResponse<Boolean>> checkUserInProject(String phone, Integer appCode) {
        if (StringUtils.isEmpty(phone)) {
            return createResponseEntity(NUCResponse.createByError(-2, "手机号不能为空"));
        }
        return createResponseEntity(userService.checkUserInProject(phone, appCode));
    }


    @ApiOperation(
            value = "通过手机号获取用户的项目信息（仅项目openId和userId）",
            notes = "通过手机号获取用户的项目信息（仅项目openId和userId）\n"
    )
    @RequestMapping(value = "/getUserInProject", method = RequestMethod.POST)
    public NUCResponse<UserProjectInfoVO> getUserInProject(@ApiParam(value = "手机号", required = true) @RequestParam(value = "phone", required = false) String phone,
                                                           @ApiParam(value = "appCode", required = true) @RequestParam(value = "appCode", required = false) Integer appCode) {
        if (StringUtils.isEmpty(phone)) {
            return NUCResponse.createByError(-1, "手机号不能为空");
        }
        if (appCode == null || appCode == 0) {
            return NUCResponse.createByError(-2, "appCode不能为空");
        }
        return userService.getUserInProject(phone, appCode);
    }



    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }

    //跨域
    private <B> ResponseEntity<B> createResponseEntityAllowOrigin(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        // 指定允许其他域名访问
        headers.set("Access-Control-Allow-Origin", "*");
        // 响应类型
        headers.set("Access-Control-Allow-Methods", "POST, GET, DELETE, OPTIONS, DELETE");
        // 响应头设置
        headers.set("Access-Control-Allow-Headers", "Content-Type, x-requested-with, X-Custom-Header, HaiYi-Access-Token");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }
}
