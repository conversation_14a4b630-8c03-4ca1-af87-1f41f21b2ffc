package com.ucmed.authc;

import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * Created by <PERSON> on 2017/1/17.
 * 接入用户中心的产品线key
 */
public enum EnumProductKey implements Serializable {

    GENERAL("G"),
    SEE_DOCTOR_ONLINE("S"),//互联网医院
    HOSPITAL_ONLINE("H"),//掌上医院
    REMOTE_MEDICAL("R"),//移动远程
    MULTDISTRICT_MEDICAL_COLLABORATION("M"),//分级诊疗
    PATIENT_FLOWUP_MANAGEMENT_SYSTEM("P"),//院后患者管理系统
    DOC_CHAIN("D")//医链
    ;

    EnumProductKey(String value) {
        this.value = value;
    }

    private String value;

    public String getValue() {
        return value;
    }

    public static String getName(String value) {
        for (EnumProductKey type : values()) {
            if (type.value.equals(value)) {
                return type.name();
            }
        }
        return StringUtils.EMPTY;
    }

}
