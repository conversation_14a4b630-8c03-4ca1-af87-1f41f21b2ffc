package com.ucmed.dao;

import com.ucmed.bean.UC_UserInfo;
import com.ucmed.common.dao.BaseDaoImpl;
import com.ucmed.dto.ClinicUserInfo;
import com.ucmed.util.ThreeDESUtil;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import static com.ucmed.common.constant.CommonConstant.SPKEY;
import static com.ucmed.common.constant.TableName.JCUSER;

/**
 * <AUTHOR>
 * @date 2016/7/22
 */
@Repository
public class JcUserDaoImpl extends BaseDaoImpl implements JcUserDao {

    /**
     * 通过uid查询用户
     *
     * @param uid
     * @return
     */
    @Override
    public UC_UserInfo getUserByUid(int uid) {
        String sql = "SELECT "
                + "uid,user_id,password,phone,latesttime,failnum,create_time,login_times,pass_change_time,lock,token,token_time,wechat_id,valid,securitykey "
                + "FROM " + JCUSER + " "
                + "WHERE uid = ?";
        List<UC_UserInfo> result = getJdbcTemplate().query(sql, new Object[]{uid}, new BeanPropertyRowMapper<>(UC_UserInfo.class));
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    /**
     * 通过用户名查询用户
     *
     * @param userId
     * @return
     */
    @Override
    public UC_UserInfo getUserByUserId(String userId) {
        String sql = "SELECT "
                + "uid,user_id,password,phone,latesttime,failnum,create_time,login_times,pass_change_time,lock,token,token_time,wechat_id,valid,securitykey "
                + "FROM " + JCUSER + " "
                + "WHERE user_id = ?";
        List<UC_UserInfo> result = getJdbcTemplate().query(sql, new Object[]{userId}, new BeanPropertyRowMapper<>(UC_UserInfo.class));
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    /**
     * 通过手机号查询用户
     *
     * @param phone
     * @return
     */
    @Override
    public UC_UserInfo getUserByPhone(String phone) {
        String sql = "SELECT "
                + "uid,user_id,password,phone,latesttime,failnum,create_time,login_times,pass_change_time,lock,token,token_time,wechat_id,valid,securitykey "
                + "FROM " + JCUSER + " "
                + "WHERE phone = ?";
        List<UC_UserInfo> result = getJdbcTemplate().query(sql, new Object[]{phone}, new BeanPropertyRowMapper<>(UC_UserInfo.class));
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    /**
     * 更新用户
     *
     * @return
     */
    @Override
    public int updateUser(final UC_UserInfo user) {
        String sql = "UPDATE " + JCUSER + " SET "
                + "user_id = ?,password = ?,phone = ?,latesttime = ?,failnum = ?,create_time = ?,login_times = ?,pass_change_time = ?,lock = ?,token = ?,token_time = ?,wechat_id = ?,valid = ?,securitykey = ? "
                + "WHERE uid = ?";
        try {
            return getJdbcTemplate().update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setString(1, user.getUser_id());
                    preparedStatement.setString(2, user.getPassword());
                    preparedStatement.setString(3, user.getPhone());
                    preparedStatement.setString(4, user.getLatestTime());
                    preparedStatement.setInt(5, user.getFailNum());
                    preparedStatement.setString(6, user.getCreate_time());
                    preparedStatement.setInt(7, user.getLogin_times());
                    preparedStatement.setString(8, user.getPass_change_time());
                    preparedStatement.setString(9, user.getLock());
                    preparedStatement.setString(10, user.getToken());
                    preparedStatement.setString(11, user.getToken_time());
                    preparedStatement.setString(12, user.getWechat_id());
                    preparedStatement.setInt(13, user.getValid());
                    preparedStatement.setString(14, user.getSecuritykey());
                    preparedStatement.setInt(15, user.getUid());
                }
            });
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 新增用户
     *
     * @return
     */
    @Override
    public int addUser(final UC_UserInfo user) {
        String sql = "INSERT INTO " + JCUSER + "(user_id,password,phone,create_time,securitykey,ucmed_id) " +
                "VALUES(?, ?, ?, ?, ?, ?)";
        try {
            return getJdbcTemplate().update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setString(1, user.getUser_id());
                    preparedStatement.setString(2, user.getPassword());
                    preparedStatement.setString(3, user.getPhone());
                    preparedStatement.setString(4, user.getCreate_time());
                    preparedStatement.setString(5, user.getSecuritykey());
                    preparedStatement.setString(6, user.getUcmed_id());
                }
            });
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public UC_UserInfo getUserByUserIdAndProjectId(String userId, int projectId) {
        String sql = "SELECT u.uid,u.user_id,u.password,u.phone,u.latesttime,u.failnum,u.create_time,u.login_times,u.pass_change_time,u.lock,u.token,u.token_time,u.wechat_id,u.valid,u.securitykey FROM jc_user u INNER JOIN security_user_app app ON u.user_id = app.user_id AND u.user_id = ? AND app.app_code in (SELECT app_code FROM security_application WHERE proj_code = ?)";
        List<UC_UserInfo> result = getJdbcTemplate().query(sql, new Object[]{userId, projectId}, new BeanPropertyRowMapper<>(UC_UserInfo.class));
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    @Override
    public UC_UserInfo getUserByPhoneAndProjectId(String phone, int projectId) {
        String sql = "SELECT u.uid,u.user_id,u.password,u.phone,u.latesttime,u.failnum,u.create_time,u.login_times,u.pass_change_time,u.lock,u.token,u.token_time,u.wechat_id,u.valid,u.securitykey FROM jc_user u INNER JOIN security_user_app app ON u.user_id = app.user_id AND u.phone = ? AND app.app_code in (SELECT app_code FROM security_application WHERE proj_code = ?)";
        List<UC_UserInfo> result = getJdbcTemplate().query(sql, new Object[]{phone, projectId}, new BeanPropertyRowMapper<>(UC_UserInfo.class));
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    @Override
    public UC_UserInfo getUser(String userId) {
        String sql = "SELECT " +
                "uid,user_id,password,phone,latesttime,failnum,create_time,login_times,pass_change_time,lock,token,token_time,wechat_id,valid,securitykey FROM jc_user WHERE (user_id = ? or phone = ?)";
        List<UC_UserInfo> result = getJdbcTemplate().query(sql, new Object[]{userId, ThreeDESUtil.get3DESEncrypt(userId, SPKEY)}, new BeanPropertyRowMapper<>(UC_UserInfo.class));
        if (result.size() == 0) {
            return null;
        }
        return result.get(0);
    }

    @Override
    public List<ClinicUserInfo> listUser(Integer appCode, Integer pageNo, Integer pageSize, String name, String phone, String idCard) {
        String sql = "SELECT " +
                "u.phone, u.user_id, info.card_id as idCard, info.age, info.sex," +
                "info.family_phone, info.address, info.the_name " +
                "FROM " +
                "jc_user u " +
                "INNER JOIN jc_user_info info ON u.user_id = info.user_id " +
                "INNER JOIN security_user_app app ON u.user_id = app.user_id ";
        if (name != null) {
            sql += " AND info.the_name='" + name + "' ";
        }
        if (phone != null) {
            sql += " AND u.phone='" + ThreeDESUtil.get3DESEncrypt(phone, SPKEY) + "' ";
        }
        if (idCard != null) {
            sql += " AND info.card_id='" + ThreeDESUtil.get3DESEncrypt(idCard, SPKEY) + "' ";
        }
        sql += " WHERE app.app_code = ? ";
        if (pageNo != null && pageSize != null) {
            sql += " LIMIT " + pageSize + " OFFSET " + (pageNo - 1);
        }
        if (pageSize != null && pageNo == null) {
            sql += " LIMIT " + pageSize + " OFFSET 0 ";
        }

        List<ClinicUserInfo> result = getJdbcTemplate().query(sql, new Object[]{appCode}, new BeanPropertyRowMapper<>(ClinicUserInfo.class));
        return result;
    }

    @Override
    public List<ClinicUserInfo> listUser(Integer appCode, Integer pageNo, Integer pageSize, String conditions) {
        String sql;
        if (conditions == null) {
            sql = "SELECT " +
                    "u.phone, u.user_id, info.card_id as idCard, info.age, info.sex," +
                    "info.family_phone, info.address, info.the_name " +
                    "FROM " +
                    "jc_user u " +
                    "INNER JOIN jc_user_info info ON u.user_id = info.user_id " +
                    "INNER JOIN security_user_app app ON u.user_id = app.user_id";
        } else {
            String conditionsEncrypt = ThreeDESUtil.get3DESEncrypt(conditions, SPKEY);
            sql = "SELECT " +
                    "u.phone, u.user_id, info.card_id as idCard, info.age, info.sex," +
                    "info.family_phone, info.address, info.the_name " +
                    "FROM " +
                    "jc_user u " +
                    "INNER JOIN jc_user_info info ON u.user_id = info.user_id " +
                    "INNER JOIN security_user_app app ON u.user_id = app.user_id " +
                    " AND (info.the_name='" + conditions + "' OR u.phone='" + conditions + "' OR info.card_id='" + conditions + "' " +
                    " OR info.the_name='" + conditionsEncrypt + "' OR u.phone='" + conditionsEncrypt + "' OR info.card_id='" + conditionsEncrypt + "') ";
        }
        sql += " WHERE app.app_code = ? ";
        if (pageNo != null && pageSize != null) {
            sql += " LIMIT " + pageSize + " OFFSET " + (pageNo - 1);
        }
        if (pageSize != null && pageNo == null) {
            sql += " LIMIT " + pageSize + " OFFSET 0 ";
        }

        List<ClinicUserInfo> result = getJdbcTemplate().query(sql, new Object[]{appCode}, new BeanPropertyRowMapper<>(ClinicUserInfo.class));
        return result;
    }

    @Override
    public int getTotalUser(Integer appCode, String conditions) {
        String sql;
        if (conditions == null) {
            sql = "SELECT count(1) " +
                    "FROM " +
                    "jc_user u " +
                    "INNER JOIN jc_user_info info ON u.user_id = info.user_id " +
                    "INNER JOIN security_user_app app ON u.user_id = app.user_id ";
        } else {
            String conditionsEncrypt = ThreeDESUtil.get3DESEncrypt(conditions, SPKEY);
            sql = "SELECT count(1) " +
                    "FROM " +
                    "jc_user u " +
                    "INNER JOIN jc_user_info info ON u.user_id = info.user_id " +
                    "INNER JOIN security_user_app app ON u.user_id = app.user_id " +
                    " AND (info.the_name='" + conditions + "' OR u.phone='" + conditions + "' OR info.card_id='" + conditions + "' " +
                    " OR info.the_name='" + conditionsEncrypt + "' OR u.phone='" + conditionsEncrypt + "' OR info.card_id='" + conditionsEncrypt + "') ";
        }
        sql += " WHERE app.app_code = ? ";
        int result = getJdbcTemplate().queryForObject(sql, new Object[]{appCode}, Integer.class);
        return result;
    }

    @Override
    public int getTotalUser(Integer appCode, String name, String phone, String idCard) {
        String sql = "SELECT " +
                "count(1) " +
                "FROM " +
                "jc_user u " +
                "INNER JOIN jc_user_info info ON u.user_id = info.user_id " +
                "INNER JOIN security_user_app app ON u.user_id = app.user_id ";
        if (name != null) {
            sql += " AND info.the_name='" + name + "' ";
        }
        if (phone != null) {
            sql += " AND u.phone='" + ThreeDESUtil.get3DESEncrypt(phone, SPKEY) + "' ";
        }
        if (idCard != null) {
            sql += " AND info.card_id='" + ThreeDESUtil.get3DESEncrypt(idCard, SPKEY) + "' ";
        }
        sql += " WHERE app.app_code = ? ";
        int result = getJdbcTemplate().queryForObject(sql, new Object[]{appCode}, Integer.class);
        return result;
    }
}
