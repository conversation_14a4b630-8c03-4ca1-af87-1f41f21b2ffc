package com.ucmed.mapper;

import com.ucmed.bean.BlackListApp;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * Author: 黄一辛 HUANGYIXIN
 * CreateTime: 2018/5/25 10:15
 * Contract: <EMAIL>
 * Description:
 **/
public interface BlackListAppMapper {

    @Select({
            "select",
            "id",
            "from blacklist_app",
            "where app_code = #{appCode,jdbcType=INTEGER} and status = '1'"
    })
    @Results({
            @Result(column="id", property="id", jdbcType=JdbcType.INTEGER, id=true)
    })
    List<BlackListApp> getIdByAppCode(@Param("appCode") Integer appCode);
}
