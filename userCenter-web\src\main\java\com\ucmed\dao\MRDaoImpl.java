package com.ucmed.dao;

import com.ucmed.bean.DoctorInfo;
import com.ucmed.bean.Hospital;
import com.ucmed.bean.UserInfo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.stereotype.Repository;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

/**
 * Created by HUANGYIXIN on 2016/7/18.
 */
@Repository
public class MRDaoImpl implements MRDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;


    /**
     * 注册用户基本信息
     *
     * @param userInfo 用户Bean对象
     */
    @Override
    public int addUserInfo(final UserInfo userInfo) {
        String sql = "insert into jc_user_info(user_id, the_name, sex, birthday, card_type, card_id, medicare, medicare_id," +
                " city_id, address, w_chat, mobile, e_mail, source_ids, create_time, update_time, ucmed_id) values(?,?," +
                "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)" ;
        try{
            return jdbcTemplate.update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setString(1, userInfo.getUser_id());
                    preparedStatement.setString(2, userInfo.getThe_name());
                    preparedStatement.setString(3, userInfo.getSex());
                    preparedStatement.setString(4, userInfo.getBirthday());
                    preparedStatement.setString(5, userInfo.getCard_type());
                    preparedStatement.setString(6, userInfo.getCard_id());
                    preparedStatement.setString(7, userInfo.getMedicare());
                    preparedStatement.setString(8, userInfo.getMedicare_id());
                    preparedStatement.setString(9, userInfo.getCity_id());
                    preparedStatement.setString(10, userInfo.getAddress());
                    preparedStatement.setString(11, userInfo.getW_chat());
                    preparedStatement.setString(12, userInfo.getMobile());
                    preparedStatement.setString(13, userInfo.getE_mail());
                    preparedStatement.setString(14, userInfo.getSource_ids());
                    preparedStatement.setString(15, userInfo.getCreate_time());
                    preparedStatement.setString(16, userInfo.getUpdate_time());
                    preparedStatement.setString(17, userInfo.getUcmed_id());
                }
            });
        }catch (DataAccessException e){
            e.printStackTrace();
            return 0;
        }
    }


    /**
     * 登陆
     *
     * @param user      登录名
     * @param password  密码
     * @return ucmed_id 卓健ID
     */
    @Override
    public String login(String user, String password) {
        String sql = "select ucmed_id from jc_user where user_id = ? and password = ?";
        try {
            return jdbcTemplate.queryForObject(sql, java.lang.String.class, user, password);
        }catch (DataAccessException e){
            return null;
        }
    }

    /**
     * 根据ucmed_id查询普通用户基本信息
     *
     * @param ucmedId 卓健ID
     */
    @Override
    @SuppressWarnings("uncheck")
    public UserInfo getUserInfo(String ucmedId) {
        String sql = "select user_id, the_name, sex, card_type, card_id," +
                " medicare, medicare_id, city_id, address, w_chat, mobile, e_mail," +
                " birthday, source_ids, create_time, update_time, ucmed_id from jc_user_info where ucmed_id = '" +
                  ucmedId + "'";
        List<UserInfo> result = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(UserInfo.class));
        if (result == null || result.size() == 0)
            return null;
        return result.get(0);
    }

    /**
     * 更新普通用户基本信息
     *
     * @param userInfo 用户Bean对象
     */
    @Override
    public int  updateUserInfo(final UserInfo userInfo) {
        String sql = "update jc_user_info set the_name = ?, sex = ?, card_type = ?, card_id = ?, medicare = ?," +
                " medicare_id = ?, city_id = ?, address = ?, w_chat = ?, mobile = ?, e_mail = ?, birthday = ?," +
                " source_ids = ?, create_time = ?, update_time = ? where ucmed_id = ?";
        try {
            return jdbcTemplate.update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setString(1, userInfo.getThe_name());
                    preparedStatement.setString(2, userInfo.getSex());
                    preparedStatement.setString(3, userInfo.getCard_type());
                    preparedStatement.setString(4, userInfo.getCard_id());
                    preparedStatement.setString(5, userInfo.getMedicare());
                    preparedStatement.setString(6, userInfo.getMedicare_id());
                    preparedStatement.setString(7, userInfo.getCity_id());
                    preparedStatement.setString(8, userInfo.getAddress());
                    preparedStatement.setString(9, userInfo.getW_chat());
                    preparedStatement.setString(10, userInfo.getMobile());
                    preparedStatement.setString(11, userInfo.getE_mail());
                    preparedStatement.setString(12, userInfo.getBirthday());
                    preparedStatement.setString(13, userInfo.getSource_ids());
                    preparedStatement.setString(14, userInfo.getCreate_time());
                    preparedStatement.setString(15, userInfo.getUpdate_time());
                    preparedStatement.setString(16, userInfo.getUcmed_id());
                }
            });
        }catch (DataAccessException e){
            return 0;
        }

    }

    /**
     * 根据card_id/name/phone查询普通用户基本信息
     *
     * @param cardId 证件号码
     * @param name 姓名
     * @param phone 手机号码
     *
     */
    @Override
    public UserInfo queryUserInfo(String cardId, String name, String phone) {
        String cond1 = "(card_id = '" + cardId + "' or '" + cardId + "' = '')";
        String cond2 = "(the_name = '" + name + "' or '" + name + "' = '')";
        String cond3 = "(mobile = '" + phone + "' or '" + phone + "' = '')";

        String sql = "select user_id, the_name, sex, card_type, card_id," +
                " medicare, medicare_id, city_id, address, w_chat, mobile, e_mail," +
                " birthday, source_ids, create_time, update_time, ucmed_id from jc_user_info where " +
                cond1 + " and " + cond2 + " and " + cond3;
        List<UserInfo> result = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(UserInfo.class));
        if (result == null || result.size() == 0)
            return null;
        return result.get(0);
    }

    /**
     * 根据ucmed_id查询医生信息
     *
     * @param ucmedId 卓健ID
     */
    @Override
    public DoctorInfo getDoctorInfo(String ucmedId) {
        String sql = "select user_id, ucmed_hospital_id, hospital_org_code, section_name," +
                " common_section_no, work_no from jc_doctor_info where ucmed_id = '" + ucmedId + "'";
        List<DoctorInfo> result = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(DoctorInfo.class));
        if (result == null || result.size() == 0)
            return null;
        return result.get(0);
    }

    /**
     * 更新医生信息
     *
     * @param doctorInfo 医生Bean对象
     */
    @Override
    public int updateDoctorInfo(final DoctorInfo doctorInfo) {
        String sql = "update jc_doctor_info set ucmed_hospital_id = ?, hospital_org_code = ?, section_name = ?, "+
                "common_section_no = ?, work_no = ?, update_time = ? where ucmed_id = ? and ucmed_unit_doctor_id = ?";
        try {
            return jdbcTemplate.update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setInt(1,Integer.parseInt(doctorInfo.getUcmed_hospital_id()));
                    preparedStatement.setString(2, doctorInfo.getHospital_org_code());
                    preparedStatement.setString(3, doctorInfo.getSection_name());
                    preparedStatement.setString(4, doctorInfo.getCommon_section_no());
                    preparedStatement.setString(5, doctorInfo.getWork_no());
                    preparedStatement.setString(6, doctorInfo.getUpdate_time());
                    preparedStatement.setString(7, doctorInfo.getUcmed_id());
                    preparedStatement.setString(8, doctorInfo.getUcmed_unit_doctor_id());

                }
            });
        }catch (DataAccessException e){
            return 0;
        }
    }

    @Override
    public List<DoctorInfo> queryDoctorInfo(String ucmedId){
        String sql = "select * from jc_doctor_info where ucmed_id = '" + ucmedId + "'";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(DoctorInfo.class));
    }

    public DoctorInfo queryDoctorInfo(String ucmdeId, String ucmedUnitDoctorId){
        String sql = "select * from jc_doctor_info where ucmed_id = '" + ucmdeId + "' and ucmed_unit_doctor_id = '" +
                ucmedUnitDoctorId + "'";
        List<DoctorInfo> tmp = jdbcTemplate.query(sql, new BeanPropertyRowMapper<>(DoctorInfo.class));
        if(tmp == null || tmp.size() == 0)
            return null;
        return tmp.get(0);
    }

    @Override
    public int addDoctorInfo(final DoctorInfo doctorInfo) {
        String sql = "insert into jc_doctor_info(user_id, ucmed_hospital_id, hospital_org_code, section_name," +
                " common_section_no, work_no, ucmed_id, ucmed_unit_doctor_id, create_time, update_time) values(?,?,?," +
                "?,?,?,?,?,?,?)";
        try {
            return jdbcTemplate.update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setString(1, doctorInfo.getUser_id());
                    preparedStatement.setString(2,doctorInfo.getUcmed_hospital_id());
                    preparedStatement.setString(3, doctorInfo.getHospital_org_code());
                    preparedStatement.setString(4, doctorInfo.getSection_name());
                    preparedStatement.setString(5, doctorInfo.getCommon_section_no());
                    preparedStatement.setString(6, doctorInfo.getWork_no());
                    preparedStatement.setString(7, doctorInfo.getUcmed_id());
                    preparedStatement.setString(8, doctorInfo.getUcmed_unit_doctor_id());
                    preparedStatement.setString(9, doctorInfo.getCreate_time());
                    preparedStatement.setString(10, doctorInfo.getUpdate_time());
                }
            });
        }catch(DataAccessException e){
            e.printStackTrace();
            return 0;
        }
    }



    /**
     * 添加医院信息
     *
     * @param hospital 医院Bean对象
     */
    @Override
    public int addHospital(final Hospital hospital) {
        String sql = "insert into jc_hospital(allhosname, shorthosname, hospinyin, orgcode, hossite, hosttype," +
                " hoslevel, orgtype, healthorgaddressid, province, city, area, address, addresscode, postcode," +
                " detailcontent, remark, createid, createtime, updateid, updatetime, isdel) values(?,?,?,?,?,?," +
                "?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)";
        try{
            return jdbcTemplate.update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setString(1, hospital.getAllHosName());
                    preparedStatement.setString(2, hospital.getShortHosName());
                    preparedStatement.setString(3, hospital.getHosPinYin());
                    preparedStatement.setString(4, hospital.getOrgCode());
                    preparedStatement.setString(5, hospital.getHosSite());
                    preparedStatement.setString(6, hospital.getHostType());
                    preparedStatement.setString(7, hospital.getHosLevel());
                    preparedStatement.setString(8, hospital.getOrgType());
                    preparedStatement.setString(9, hospital.getHealthOrgAddressId());
                    preparedStatement.setString(10, hospital.getProvince());
                    preparedStatement.setString(11, hospital.getCity());
                    preparedStatement.setString(12, hospital.getArea());
                    preparedStatement.setString(13, hospital.getAddress());
                    preparedStatement.setString(14, hospital.getAddressCode());
                    preparedStatement.setString(15, hospital.getPostCode());
                    preparedStatement.setString(16, hospital.getDetailContent());
                    preparedStatement.setString(17, hospital.getRemark());
                    preparedStatement.setString(18, hospital.getCreateId());
                    preparedStatement.setString(19, hospital.getCreateTime());
                    preparedStatement.setString(20, hospital.getUpdateId());
                    preparedStatement.setString(21, hospital.getUpdateTime());
                    preparedStatement.setString(22, hospital.getIsDel());
                }
            });
        }catch (DataAccessException e){
            return 0;
        }
    }

    /**
     * 更新医院信息
     *
     * @param hospital 医院Bean对象
     */
    @Override
    public int  updateHospital(final Hospital hospital) {
        String sql = "update jc_hospital set allhosname = ?, shorthosname = ?, hospinyin = ?, hossite = ?," +
                " hosttype = ?, hoslevel = ?, orgtype = ?, healthorgaddressid = ?, province = ?, city = ?, area = ?," +
                " address = ?, addresscode = ?, postcode = ?, detailcontent = ?, remark = ?, createid = ?," +
                " createtime = ?, updateid = ?, updatetime = ?, isdel = ? where orgcode = ?";
        try{
            return jdbcTemplate.update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setString(1, hospital.getAllHosName());
                    preparedStatement.setString(2, hospital.getShortHosName());
                    preparedStatement.setString(3, hospital.getHosPinYin());
                    preparedStatement.setString(4, hospital.getHosSite());
                    preparedStatement.setString(5, hospital.getHostType());
                    preparedStatement.setString(6, hospital.getHosLevel());
                    preparedStatement.setString(7, hospital.getOrgType());
                    preparedStatement.setString(8, hospital.getHealthOrgAddressId());
                    preparedStatement.setString(9, hospital.getProvince());
                    preparedStatement.setString(10, hospital.getCity());
                    preparedStatement.setString(11, hospital.getArea());
                    preparedStatement.setString(12, hospital.getAddress());
                    preparedStatement.setString(13, hospital.getAddressCode());
                    preparedStatement.setString(14, hospital.getPostCode());
                    preparedStatement.setString(15, hospital.getDetailContent());
                    preparedStatement.setString(16, hospital.getRemark());
                    preparedStatement.setString(17, hospital.getCreateId());
                    preparedStatement.setString(18, hospital.getCreateTime());
                    preparedStatement.setString(19, hospital.getUpdateId());
                    preparedStatement.setString(20, hospital.getUpdateTime());
                    preparedStatement.setString(21, hospital.getIsDel());
                    preparedStatement.setString(22, hospital.getOrgCode());
                }
            });
        }catch (DataAccessException e){
            return 0;
        }
    }

    public List<Hospital> queryHospital(String condition){
        StringBuilder sql = new StringBuilder("select * from jc_hospital where ").append(condition);
        System.out.println(sql.toString());
        try{
            return jdbcTemplate.query(sql.toString(), new BeanPropertyRowMapper<>(Hospital.class));
        }catch (DataAccessException e){
            e.printStackTrace();
            return null;
        }
    }
}
