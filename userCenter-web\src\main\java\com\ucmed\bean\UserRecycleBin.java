package com.ucmed.bean;

import com.ucmed.util.TimeUtil;
import lombok.Data;

import javax.persistence.GeneratedValue;
import javax.persistence.GenerationType;
import javax.persistence.Id;
import javax.persistence.Table;
import java.util.Date;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/01/07 09:01
 */
@Data
@Table(name = "user_recycle_bin")
public class UserRecycleBin {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Integer id;

    private String userId;

    private String phone;

    private String tableName;

    private Object tableData;

    private String createTime;

    private Integer isDelete;


    public UserRecycleBin() {
    }

    public UserRecycleBin(String userId, String phone, String tableName, Object tableData) {
        this.userId = userId;
        this.phone = phone;
        this.tableName = tableName;
        this.tableData = tableData;
        this.createTime = TimeUtil.getCurrentTime();
        this.isDelete = 0;
    }


}
