package com.ucmed.util;

import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.connection.RedisConnection;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.UUID;

/**
 * Created by XXB-QJH-1303.
 * Date: 2017/9/26 9:39
 */
@Service
public class RedisLock {
    private static Logger log4j = Logger.getLogger(RedisLock.class.getName());
    private static final String LOCK_PREFIX = "LOCK_";

    @Autowired
    private RedisTemplate redisTemplate;

    public boolean lock(String lockName, long timeout, long requireTimeout) {
        String key = LOCK_PREFIX + lockName;
        String value = UUID.randomUUID().toString();
        long end = System.currentTimeMillis() + requireTimeout;
        RedisConnection conn = redisTemplate.getConnectionFactory().getConnection();
        while (System.currentTimeMillis() < end) {
            if (conn.setNX(key.getBytes(), value.getBytes())) {
                conn.expire(key.getBytes(), timeout);
                log4j.info("get lock, key: " + key + " , expire in " + timeout + " seconds.");
                conn.close();
                return true;
            }
//            log4j.info("key: " + key + " locked by another business：" + lockName);
            try {
                Thread.sleep(3 + new Random().nextInt(30));
            } catch (InterruptedException e) {
                e.printStackTrace();
                conn.close();
                return false;
            }
        }
        conn.close();
        return false;
    }

    public void unlock(String lockName) {
        redisTemplate.delete(LOCK_PREFIX + lockName);
    }
}
