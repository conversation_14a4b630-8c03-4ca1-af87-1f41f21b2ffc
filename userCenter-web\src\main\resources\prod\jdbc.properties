jdbc.driverClassName=org.postgresql.Driver
jdbc.url = **********************************************************
jdbc.username = postgres
jdbc.password = zhuojianchina
spring.datasource.initialSize=50
spring.datasource.minIdle=5
spring.datasource.maxActive=200
#------------ redis ------------
#最大分配的对象数  
redis.pool.maxActive=1024
#最大能够保持idel状态的对象数  
redis.pool.maxIdle=200
#当池内没有返回对象时，最大等待时间  
redis.pool.maxWait=1000
#当调用borrow Object方法时，是否进行有效性检查  
redis.pool.testOnBorrow=true
#当调用borrow Object方法时，是否进行有效性检查  
redis.pool.testOnReturn=true

redis服务端ip、端口、密码、超时时间
redis.host=uc-redis.config.zhuojian
redis.port=6379
redis.password=uX%MYWA%qzNbP6Gs
redis.timeout=1800
