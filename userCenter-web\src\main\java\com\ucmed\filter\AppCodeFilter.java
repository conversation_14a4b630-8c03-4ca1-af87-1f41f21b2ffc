package com.ucmed.filter;

import com.ucmed.common.service.CommonService;
import com.ucmed.mapper.SecurityApplicationMapper;
import com.ucmed.util.SpringUtils;
import com.ucmed.util.wrapper.ParameterRequestWrapper;
import org.apache.log4j.Logger;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.servlet.*;
import javax.servlet.http.HttpServletRequest;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.ucmed.common.constant.CommonConstant.APP_CACHE;

@Component
public class AppCodeFilter implements Filter {

    CommonService commonService;

    private static Logger log = Logger.getLogger(AppCodeFilter.class.getName());

    @Override
    public void init(FilterConfig filterConfig) {
        if (commonService == null) {
            commonService = (CommonService) SpringUtils.getBean("commonService");
        }
    }

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        String appToken = request.getParameter("appCode");
        if (appToken == null) {
            filterChain.doFilter(servletRequest, servletResponse);
            return;
        }
        Map<String, String[]> m = new HashMap<String, String[]>(request.getParameterMap());
        Integer appCode = null;
        try {
            appCode = commonService.getAppCodeByAppToken(Integer.valueOf(appToken));
        } catch (NumberFormatException e) {
            appCode = null;
        }


        if (appCode != null) {
            m.put("appCode", new String[]{String.valueOf(appCode)});
            ParameterRequestWrapper parameterRequestWrapper = new ParameterRequestWrapper(request, m);
            filterChain.doFilter(parameterRequestWrapper, servletResponse);
            return;
        } else {
            m.put("appCode", new String[]{"0"});
            ParameterRequestWrapper parameterRequestWrapper = new ParameterRequestWrapper(request, m);
            filterChain.doFilter(parameterRequestWrapper, servletResponse);
            log.info("appCode错误");
            return;
        }
    }

    @Override
    public void destroy() {

    }

    public CommonService getCommonService() {
        return commonService;
    }

    public void setCommonService(CommonService commonService) {
        this.commonService = commonService;
    }

}
