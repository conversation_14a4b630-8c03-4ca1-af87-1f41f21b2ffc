package com.ucmed.bean;

import com.ucmed.authc.AuthenticationToken;
import com.ucmed.authc.EnumProductKey;

/**
 * 值为字符串的token，默认超时时间为12小时
 * Created by <PERSON> on 2017/1/17.
 */
public class SimpleToken implements AuthenticationToken {

    private static final long serialVersionUID = 3291583263816779892L;

    private String tokenKey;

    private EnumProductKey productKey;

    private String value;

    private Integer timeOut;

    public void setTokenKey(String tokenKey) {
        this.tokenKey = tokenKey;
    }

    public EnumProductKey getProductKey() {
        return productKey;
    }

    public void setProductKey(EnumProductKey productKey) {
        this.productKey = productKey;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public void setTimeOut(Integer timeOut) {
        this.timeOut = timeOut;
    }

    public SimpleToken(String tokenKey) {
        this.tokenKey = tokenKey;
    }

    public SimpleToken(String tokenKey, EnumProductKey productKey) {
        this.tokenKey = tokenKey;
        this.productKey = productKey;
    }

    public SimpleToken(String tokenKey, EnumProductKey productKey, String value) {
        this.tokenKey = tokenKey;
        this.value = value;
        this.productKey = productKey;
    }

    public SimpleToken() {
    }

    public String getTokenKey() {
        return tokenKey;
    }

    /**
     * @return 默认超时时间为12小时
     */
    public Integer getTimeOut() {
        return timeOut;
    }

    public EnumProductKey getProdKey() {
        return productKey;
    }

    public Object getValue() {
        return value;
    }
}
