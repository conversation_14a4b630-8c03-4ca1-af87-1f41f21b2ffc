package com.ucmed.controller;

import cn.ucmed.common.util.SysUtil;
import com.ucmed.common.constants.controller.URLConstants;
import io.swagger.annotations.*;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.imageio.ImageIO;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.HttpSession;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.io.IOException;
import java.io.OutputStream;
import java.util.Random;
import java.util.concurrent.TimeUnit;

/**
 * Created by <PERSON><PERSON><PERSON>ian on 2017/7/5.
 */
@Api(value = "图形验证码", description = "图形验证码接口")
@Controller
public class ValidateCodeController {

    @Autowired
    private RedisTemplate redisTemplate;

    @Value("${testValidateCode}")
    private String testValidateCode;

    @Value("${validateCodeExpireTime}")
    private Long validateCodeExpireTime;

    /**
     * 生成验证码的工具
     *
     * @param request  request
     * @param response response
     * @throws ServletException
     * @throws IOException
     */
    @ApiOperation(
            value = "获取图形验证码",
            notes = "获取图形验证码\n"
    )
    @RequestMapping(method = RequestMethod.GET, value = URLConstants.PICTURE_VALIDATE_CODE)
    public void appValidateCode(HttpServletRequest request,
                                HttpServletResponse response) throws ServletException, IOException {
        /*
         * 一、绘图
         */
        /*
         * step1,创建一个内存映像对象(画布) BufferedImage(宽度,高度,类型)
         */
        String lengthStr = request.getParameter("length");
        int length = lengthStr == null ? 4 : Integer.parseInt(lengthStr);
        int width = length <= 4 ? 80 : 80 + (length - 4) * 5;

        BufferedImage image = new BufferedImage(width, 25, BufferedImage.TYPE_INT_RGB);

        setImageValues(request, image, width);

        /*
         * 二、将图片压缩并发送给浏览器
         */
        // 设置content-type消息头，告诉
        // 浏览器返回的是图片
        response.setContentType("image/jpeg");
        // 一定要获得字节输出流
        OutputStream ops = response.getOutputStream();
        // write方法会对原始图片(image)按照
        // 指定的压缩算法(jpeg)进行压缩，并且
        // 将压缩之后的数据输出到指定的流(ops)。
        ImageIO.write(image, "jpeg", ops);
        ops.close();
    }

    /**
     * @param request request
     * @param image   image
     */
    private void setImageValues(HttpServletRequest request, BufferedImage image, int width) {
    /*
     * step2,获得一个画笔
     */
        Graphics g = image.getGraphics();
        /*
         * step3,给笔设置颜色
         */
        Random r = new Random();
        g.setColor(new Color(239, 248, 255));
        /*
         * step4,给画布设置背景颜色 fillRect(x,y,width,height)
         */
        g.fillRect(0, 0, width, 25);
        /*
         * step5,绘图
         */
        g.setColor(new Color(r.nextInt(255), r.nextInt(255), r.nextInt(255)));
        // Font(字体,风格,大小)
        g.setFont(new Font(null, Font.ITALIC, 22));
        String length = request.getParameter("length");
        String number = StringUtils.isEmpty(testValidateCode) ? SysUtil.getRandomString(length == null ? 4 : Integer.parseInt(length), 0) : testValidateCode;
        String phoneNumber = request.getParameter("phoneNumber");
        String type = request.getParameter("type");
        String token = request.getParameter("token");
        // 将number存到redis中并设置过期时间

        if (phoneNumber != null && type != null) {
            redisTemplate.boundValueOps("validate_code:" + request.getParameter("phoneNumber") + request.getParameter("type")).set(number);
            redisTemplate.boundValueOps("validate_code:" + request.getParameter("phoneNumber") + request.getParameter("type")).expire(validateCodeExpireTime, TimeUnit.MINUTES);
            redisTemplate.boundValueOps("validate_code:" + request.getParameter("phoneNumber") + request.getParameter("type") + "-error").set("0");

            redisTemplate.boundValueOps("validate_code:" + request.getParameter("phoneNumber") + request.getParameter("type") + "-error").expire(validateCodeExpireTime, TimeUnit.MINUTES);
        } else if (token != null) {
            redisTemplate.boundValueOps(token).set(number);
            redisTemplate.boundValueOps(token).expire(validateCodeExpireTime, TimeUnit.MINUTES);
            redisTemplate.boundValueOps(token + "-error").set("0");

            redisTemplate.boundValueOps(token + "-error").expire(validateCodeExpireTime, TimeUnit.MINUTES);
        }

        // 将number绑订到session对象上
        HttpSession session = request.getSession();

        session.setMaxInactiveInterval(1 * 60);

        session.setAttribute("code", number);
        // drawString(String,x,y) x,y是左下角的坐标
        g.drawString(number, 2, 23);
        // step6,加一些干扰线
        for (int i = 0; i < 6; i++) {
            g.drawLine(r.nextInt(80), r.nextInt(30), r.nextInt(80), r.nextInt(30));
        }
    }
}
