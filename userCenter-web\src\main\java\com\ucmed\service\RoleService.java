package com.ucmed.service;

import com.ucmed.bean.Role;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * Created by HUANGYIXIN on 2016/11/15.
 */
public interface RoleService {


    /**
     * 添加角色
     * @param rcv
     */
    String addRole(JSONObject rcv);

    /**
     * 更新角色信息
     * @param rcv
     */
    String updateRole(JSONObject rcv);

    /**
     * 删除角色
     * @param rcv
     */
    String deleteRole(JSONObject rcv);

    /**
     * 根据appcode查询所有角色信息
     * @param rcv
     * @return
     */
    String queryRole(JSONObject rcv);

    List<Role> queryUserRole(String userId, int appCode);

    List<Role> listByAppCode(int appCode);

    boolean isExists(int appCode, String roleName);

    Role getRoleById(int roleId);

    Role getRoleByAppCodeAndName(int appCode, String roleName);
}
