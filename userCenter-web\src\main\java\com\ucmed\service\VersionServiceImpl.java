package com.ucmed.service;

import cn.hutool.core.io.resource.ClassPathResource;
import cn.hutool.core.io.resource.NoResourceException;
import com.ucmed.bean.NUCResponse;
import com.ucmed.bean.controllerbean.VersionVO;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/01/08 13:50
 */
@Service
public class VersionServiceImpl implements VersionService {

    @Value("${version:V3.0.0}")
    private String projectVersion;
    @Value("#{configProperties['BdUrl']}")
    String urlStr;

    @Override
    public VersionVO getVersion() {
        VersionVO versionVO = new VersionVO();
        try {
            ClassPathResource classPathResource = new ClassPathResource("version.txt");

            // 获得File对象，当然也可以获取输入流对象
            File file = classPathResource.getFile();
            BufferedReader bufferedReader = new BufferedReader(new FileReader(file));
            StringBuilder content = new StringBuilder();
            String line = null;

            if ((line = bufferedReader.readLine()) != null) {
                versionVO.setVersion(projectVersion + "." + line);
            }
            if ((line = bufferedReader.readLine()) != null) {
                versionVO.setSubmit_id(line);
            }
            if ((line = bufferedReader.readLine()) != null) {
                versionVO.setUpdate_time(line);
            }
            versionVO.setAbout_url(urlStr + "/index.html#/versionNumC");
            versionVO.setProduct_name("C端用户中心");
            versionVO.setReturn_code(0);
            versionVO.setReturn_msg("调用成功");
        } catch (NoResourceException e) {
            versionVO.setReturn_code(-1);
            versionVO.setReturn_msg("调用失败，版本文件不存在");
        } catch (IOException e) {
            versionVO.setReturn_code(-2);
            versionVO.setReturn_msg("调用失败，文件读取失败");
        } catch (Exception e) {
            versionVO.setReturn_code(99);
            versionVO.setReturn_msg("调用失败，未知错误");
        }
        return versionVO;
    }
}
