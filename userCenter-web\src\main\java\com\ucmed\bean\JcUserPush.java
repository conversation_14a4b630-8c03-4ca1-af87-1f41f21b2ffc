package com.ucmed.bean;

public class JcUserPush {
    private String userPushId;

    private String openId;

    private String pushId;

    private String isPush;

    private String isSound;

    private String isVibrate;

    private String isDisturb;

    private Integer appCode;

    private String createdby;

    private String createdon;

    private String modifiedby;

    private String modifiedon;

    private String deletionState;

    private String description;

    public String getUserPushId() {
        return userPushId;
    }

    public void setUserPushId(String userPushId) {
        this.userPushId = userPushId;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    public String getIsPush() {
        return isPush;
    }

    public void setIsPush(String isPush) {
        this.isPush = isPush;
    }

    public String getIsSound() {
        return isSound;
    }

    public void setIsSound(String isSound) {
        this.isSound = isSound;
    }

    public String getIsVibrate() {
        return isVibrate;
    }

    public void setIsVibrate(String isVibrate) {
        this.isVibrate = isVibrate;
    }

    public String getIsDisturb() {
        return isDisturb;
    }

    public void setIsDisturb(String isDisturb) {
        this.isDisturb = isDisturb;
    }

    public Integer getAppCode() {
        return appCode;
    }

    public void setAppCode(Integer appCode) {
        this.appCode = appCode;
    }

    public String getCreatedby() {
        return createdby;
    }

    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    public String getCreatedon() {
        return createdon;
    }

    public void setCreatedon(String createdon) {
        this.createdon = createdon;
    }

    public String getModifiedby() {
        return modifiedby;
    }

    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    public String getModifiedon() {
        return modifiedon;
    }

    public void setModifiedon(String modifiedon) {
        this.modifiedon = modifiedon;
    }

    public String getDeletionState() {
        return deletionState;
    }

    public void setDeletionState(String deletionState) {
        this.deletionState = deletionState;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }
}