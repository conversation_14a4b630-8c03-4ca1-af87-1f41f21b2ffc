package com.ucmed.bean;

import java.io.Serializable;
import java.util.List;

/**
 * Created by XXB-QJH-1303.
 * Date: 2017/9/6 15:09
 */
public class Permission implements Serializable {

    private static final long serialVersionUID = 5007795135407711816L;

    private List<String> roles;

    private List<Module> modules;

    private List<DataValue> dataValues;

    public List<String> getRoles() {
        return roles;
    }

    public void setRoles(List<String> roles) {
        this.roles = roles;
    }

    public List<Module> getModules() {
        return modules;
    }

    public void setModules(List<Module> modules) {
        this.modules = modules;
    }

    public List<DataValue> getDataValues() {
        return dataValues;
    }

    public void setDataValues(List<DataValue> dataValues) {
        this.dataValues = dataValues;
    }
}
