<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns="http://www.springframework.org/schema/beans"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd">
    <!--定义连接管理器-->
    <bean id="connectionManager" class="org.apache.http.impl.conn.PoolingHttpClientConnectionManager"
          destroy-method="close">
        <!-- 最大连接数 -->
        <property name="maxTotal" value="${http.maxTotal}"/>
        <!--设置每个主机最大的并发数-->
        <property name="defaultMaxPerRoute" value="${http.defaultMaxPerRoute}"/>
    </bean>

    <!--定义HttpClient构建器-->
    <bean id="httpClientBuilder" class="org.apache.http.impl.client.HttpClientBuilder" factory-method="create">
        <property name="connectionManager" ref="connectionManager"/>
    </bean>

    <!--定义httpClient对象，该bean一定是多例的-->
    <bean id="httpClient" class="org.apache.http.impl.client.CloseableHttpClient" factory-bean="httpClientBuilder"
          factory-method="build" scope="prototype"/>
    <!--定义requestConfig构建器-->
    <bean id="requestConfigBuilder" class="org.apache.http.client.config.RequestConfig.Builder">
        <!--设置创建连接的最长时间-->
        <property name="connectTimeout" value="${http.connectTimeout}"/>
        <!--从连接池中获取到连接的最长时间-->
        <property name="connectionRequestTimeout" value="${http.connectionRequestTimeout}"/>
        <!--数据传输的最长时间-->
        <property name="socketTimeout" value="${http.socketTimeout}"/>
    </bean>
    <!--请求参数对象-->
    <bean class="org.apache.http.client.config.RequestConfig" factory-bean="requestConfigBuilder"
          factory-method="build"/>
    <!--定期清理无效连接-->
    <bean class="org.apache.http.impl.client.IdleConnectionEvictor" destroy-method="shutdown">
        <constructor-arg index="0" ref="connectionManager"/>
        <constructor-arg index="1" value="${http.maxIdleTime}"/>
        <constructor-arg index="2" value="MINUTES"/>
    </bean>
</beans>