package com.ucmed.service;

import com.ucmed.common.service.CommonService;
import com.ucmed.util.TimeUtil;
import net.sf.json.JSONObject;
import org.apache.http.NameValuePair;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.client.entity.UrlEncodedFormEntity;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.message.BasicNameValuePair;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.Charset;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by XXB-QJH-1303 on 2017/5/15.
 */
@Service
public class YLXZServiceImpl extends CommonService implements YLXZService {

    @Autowired
    private CloseableHttpClient httpClient;

    @Autowired
    private RequestConfig requestConfig;

    @Value("#{configProperties['ylxz.domain']}")
    private String domain;

    @Override
    public String registration(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("user_id", rcv.containsKey("user_id") ? rcv.getString("user_id") : "");
        paramMap.put("name", rcv.containsKey("name") ? rcv.getString("name") : "");
        paramMap.put("phone", rcv.containsKey("phone") ? rcv.getString("phone") : "");
        paramMap.put("password", rcv.containsKey("password") ? rcv.getString("password") : "");
        paramMap.put("hospital_id", rcv.containsKey("hospital_id") ? rcv.getString("hospital_id") : "");
        paramMap.put("app_code", rcv.containsKey("app_code") ? rcv.getString("app_code") : "");
        paramMap.put("section", rcv.containsKey("section") ? rcv.getString("section") : "");
        paramMap.put("sex", rcv.containsKey("sex") ? rcv.getString("sex") : "");
        paramMap.put("role_list", rcv.containsKey("role_list") ? rcv.getString("role_list") : "");
        try {
            return doPost(domain + "Registration", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String login(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("user_id", rcv.containsKey("user_id") ? rcv.getString("user_id") : "");
        paramMap.put("password", rcv.containsKey("password") ? rcv.getString("password") : "");
        paramMap.put("app_code", rcv.containsKey("app_code") ? rcv.getString("app_code") : "");
        paramMap.put("hospital_id", rcv.containsKey("hospital_id") ? rcv.getString("hospital_id") : "");
        paramMap.put("union_id", rcv.containsKey("union_id") ? rcv.getString("union_id") : "");
        try {
            return doPost(domain + "Login", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String reInputPassword(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("user_id", rcv.containsKey("user_id") ? rcv.getString("user_id") : "");
        paramMap.put("newPwd", rcv.containsKey("newPwd") ? rcv.getString("newPwd") : "");
        paramMap.put("app_code", rcv.containsKey("app_code") ? rcv.getString("app_code") : "");
        try {
            return doPost(domain + "ReInputPassword", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String changePwd(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("password", rcv.containsKey("password") ? rcv.getString("password") : "");
        paramMap.put("newPwd", rcv.containsKey("newPwd") ? rcv.getString("newPwd") : "");
        paramMap.put("token", rcv.containsKey("token") ? rcv.getString("token") : "");
        try {
            return doPost(domain + "ChangePwd", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String logout(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("token", rcv.containsKey("token") ? rcv.getString("token") : "");
        try {
            return doPost(domain + "Logout", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String getUserInfo(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("token", rcv.containsKey("token") ? rcv.getString("token") : "");
        try {
            return doPost(domain + "GetUserInfo", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String getHospitalList(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("app_code", rcv.containsKey("app_code") ? rcv.getString("app_code") : "");
        paramMap.put("union_id", rcv.containsKey("union_id") ? rcv.getString("union_id") : "");
        paramMap.put("key_word", rcv.containsKey("key_word") ? rcv.getString("key_word") : "");
        try {
            return transRetStr(JSONObject.fromObject(doPost(domain + "GetHospitalList", paramMap)));
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String isUserExist(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("user_id", rcv.containsKey("user_id") ? rcv.getString("user_id") : "");
        paramMap.put("phone", rcv.containsKey("phone") ? rcv.getString("phone") : "");
        paramMap.put("app_code", rcv.containsKey("app_code") ? rcv.getString("app_code") : "");
        try {
            return transRetStr(JSONObject.fromObject(doPost(domain + "IsUserExist", paramMap)));
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String sendVerifyCode(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("phone", rcv.containsKey("phone") ? rcv.getString("phone") : "");
        paramMap.put("purpose", rcv.containsKey("purpose") ? rcv.getString("purpose") : "");
        paramMap.put("app_code", rcv.containsKey("app_code") ? rcv.getString("app_code") : "");
        paramMap.put("messagetemplate", rcv.containsKey("messagetemplate") ? rcv.getString("messagetemplate") : "");
        paramMap.put("hospital_id", rcv.containsKey("hospital_id") ? rcv.getString("hospital_id") : "");
        paramMap.put("union_id", rcv.containsKey("union_id") ? rcv.getString("union_id") : "");
        try {
            return doPost(domain + "SendVerifyCode", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    @Override
    public String verifyCodeLogin(JSONObject rcv) {
        Map<String, String> paramMap = new HashMap<>();
        paramMap.put("phone", rcv.containsKey("phone") ? rcv.getString("phone") : "");
        paramMap.put("code", rcv.containsKey("code") ? rcv.getString("code") : "");
        paramMap.put("app_code", rcv.containsKey("app_code") ? rcv.getString("app_code") : "");
        paramMap.put("hospital_id", rcv.containsKey("hospital_id") ? rcv.getString("hospital_id") : "");
        paramMap.put("union_id", rcv.containsKey("union_id") ? rcv.getString("union_id") : "");
        try {
            return doPost(domain + "VerifyCodeLogin", paramMap);
        } catch (IOException e) {
            e.printStackTrace();
            return null;
        }
    }

    private String doPost(String url, Map<String, String> paramMap) throws IOException {
        HttpPost httpPost = new HttpPost(url);
        //设置请求参数
        httpPost.setConfig(requestConfig);
        if (paramMap != null) {
            List<NameValuePair> parameters = new ArrayList<>();
            for (String s : paramMap.keySet()) {
                parameters.add(new BasicNameValuePair(s, paramMap.get(s)));
            }
            //构建一个form表单式的实体
            UrlEncodedFormEntity formEntity = new UrlEncodedFormEntity(parameters, Charset.forName("UTF-8"));
            //将请求实体放入到httpPost中
            httpPost.setEntity(formEntity);
        }
        //创建httpClient对象
        CloseableHttpResponse response = null;
        try {
            //执行请求
            response = httpClient.execute(httpPost);
            return EntityUtils.toString(response.getEntity());
        } finally {
            if (response != null) {
                response.close();
            }
        }
    }
}
