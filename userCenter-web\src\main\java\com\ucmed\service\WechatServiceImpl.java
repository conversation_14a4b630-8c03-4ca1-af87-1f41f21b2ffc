package com.ucmed.service;

import com.ucmed.bean.UCResponse;
import com.ucmed.common.service.CommonService;
import com.ucmed.thirdparty.ThirdPartyEnum;
import com.ucmed.thirdparty.ThirdPartyService;
import com.ucmed.util.JsonFormat;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import static com.ucmed.common.constant.CommonConstant.*;

/**
 * 微信服务实现类
 * Created by QIUJIAHAO on 2016/9/18.
 */
@Service
@Transactional
public class WechatServiceImpl extends CommonService implements WechatService {

    @Autowired
    private ThirdPartyService thirdPartyService;

    /**
     * 微信绑定
     */
    @Override
    public String bindWechat(JSONObject rcv) {
        //用户名
        String userId = rcv.getString(USERID);
        //微信ID
        String openId = rcv.getString(OPENID);
        int appCode = rcv.optInt(APPCODE);
        String roleName = rcv.optString(ROLENAME);

        UCResponse response = thirdPartyService.bind(userId, openId, appCode, roleName, ThirdPartyEnum.WX.getKey());
        return JSONObject.fromObject(response).toString();
    }

    /**
     * 微信登录
     */
    @Override
    public String loginByWechat(JSONObject rcv) {
        //微信OPENID
        String openId = rcv.getString(OPENID);
        //应用ID
        int appCode = rcv.getInt(APPCODE);
        //角色名称
        String roleName = rcv.getString(ROLENAME);
        UCResponse response = thirdPartyService.login(openId, appCode, roleName);
        return JSONObject.fromObject(response).toString();

    }
}
