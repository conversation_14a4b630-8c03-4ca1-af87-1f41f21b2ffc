log4j.rootCategory=INFO,stdout,host

log4j.appender.stdout.threshold=DEBUG
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p:  %m%n

#key infomation
log4j.appender.host.threshold=DEBUG
log4j.appender.host.Encoding=UTF-8
log4j.appender.host=org.apache.log4j.DailyRollingFileAppender
log4j.appender.host.file=/opt/logs/yhzx/default-8090
log4j.appender.host.datePattern='_'yyyy-MM-dd'.log'
log4j.appender.host.layout=org.apache.log4j.PatternLayout
log4j.appender.host.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %-5p:  %m%n

log4j.logger.com.ucmed.main.UserCenterImpl = INFO,usercenter
log4j.logger.com.dianping.cat=OFF

log4j.appender.UserCenterImpl.Encoding=UTF-8
log4j.appender.usercenter=org.apache.log4j.DailyRollingFileAppender
log4j.appender.usercenter.file=/opt/logs/yhzx/default-8090
log4j.appender.usercenter.datePattern='_'yyyy-MM-dd'.log'
log4j.appender.usercenter.layout=org.apache.log4j.PatternLayout
log4j.appender.usercenter.layout.ConversionPattern=%d{yyyy-MM-dd HH:mm:ss.SSS} %m%n