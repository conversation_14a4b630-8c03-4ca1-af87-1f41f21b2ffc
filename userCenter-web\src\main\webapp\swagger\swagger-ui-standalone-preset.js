!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.SwaggerUIStandalonePreset=e():t.SwaggerUIStandalonePreset=e()}(this,function(){return function(t){function e(r){if(n[r])return n[r].exports;var i=n[r]={i:r,l:!1,exports:{}};return t[r].call(i.exports,i,i.exports,e),i.l=!0,i.exports}var n={};return e.m=t,e.c=n,e.i=function(t){return t},e.d=function(t,n,r){e.o(t,n)||Object.defineProperty(t,n,{configurable:!1,enumerable:!0,get:r})},e.n=function(t){var n=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(n,"a",n),n},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="/dist",e(e.s=216)}([function(t,e,n){"use strict";function r(t){var e={};return null!==t&&Object.keys(t).forEach(function(n){t[n].forEach(function(t){e[String(t)]=n})}),e}function i(t,e){if(e=e||{},Object.keys(e).forEach(function(e){if(-1===o.indexOf(e))throw new s('Unknown option "'+e+'" is met in definition of "'+t+'" YAML type.')}),this.tag=t,this.kind=e.kind||null,this.resolve=e.resolve||function(){return!0},this.construct=e.construct||function(t){return t},this.instanceOf=e.instanceOf||null,this.predicate=e.predicate||null,this.represent=e.represent||null,this.defaultStyle=e.defaultStyle||null,this.styleAliases=r(e.styleAliases||null),-1===a.indexOf(this.kind))throw new s('Unknown kind "'+this.kind+'" is specified for "'+t+'" YAML type.')}var s=n(25),o=["kind","resolve","construct","instanceOf","predicate","represent","defaultStyle","styleAliases"],a=["scalar","sequence","mapping"];t.exports=i},function(t,e,n){var r=n(159)("wks"),i=n(81),s=n(2).Symbol;t.exports=function(t){return r[t]||(r[t]=s&&s[t]||(s||i)("Symbol."+t))}},function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(t,e){var n=t.exports={version:"2.4.0"};"number"==typeof __e&&(__e=n)},function(t,e){var n=t.exports="undefined"!=typeof window&&window.Math==Math?window:"undefined"!=typeof self&&self.Math==Math?self:Function("return this")();"number"==typeof __g&&(__g=n)},function(t,e,n){t.exports=!n(19)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},function(t,e,n){var r=n(18),i=n(65),s=n(48),o=Object.defineProperty;e.f=n(5)?Object.defineProperty:function(t,e,n){if(r(t),e=s(e,!0),r(n),i)try{return o(t,e,n)}catch(t){}if("get"in n||"set"in n)throw TypeError("Accessors not supported!");return"value"in n&&(t[e]=n.value),t}},function(t,e,n){var r=n(115),i=n(38);t.exports=function(t){return r(i(t))}},function(t,e){var n=Object;t.exports={create:n.create,getProto:n.getPrototypeOf,isEnum:{}.propertyIsEnumerable,getDesc:n.getOwnPropertyDescriptor,setDesc:n.defineProperty,setDescs:n.defineProperties,getKeys:n.keys,getNames:n.getOwnPropertyNames,getSymbols:n.getOwnPropertySymbols,each:[].forEach}},function(t,e,n){"use strict";function r(t,e,n,r,s,o,a,u){if(i(e),!t){var c;if(void 0===e)c=new Error("Minified exception occurred; use the non-minified dev environment for the full error message and additional helpful warnings.");else{var h=[n,r,s,o,a,u],l=0;c=new Error(e.replace(/%s/g,function(){return h[l++]})),c.name="Invariant Violation"}throw c.framesToPop=1,c}}var i=function(t){};t.exports=r},function(t,e,n){var r=n(4),i=n(3),s=n(63),o=n(12),a=function(t,e,n){var u,c,h,l=t&a.F,p=t&a.G,f=t&a.S,d=t&a.P,m=t&a.B,x=t&a.W,y=p?i:i[e]||(i[e]={}),v=y.prototype,D=p?r:f?r[e]:(r[e]||{}).prototype;p&&(n=e);for(u in n)(c=!l&&D&&void 0!==D[u])&&u in y||(h=c?D[u]:n[u],y[u]=p&&"function"!=typeof D[u]?n[u]:m&&c?s(h,r):x&&D[u]==h?function(t){var e=function(e,n,r){if(this instanceof t){switch(arguments.length){case 0:return new t;case 1:return new t(e);case 2:return new t(e,n)}return new t(e,n,r)}return t.apply(this,arguments)};return e.prototype=t.prototype,e}(h):d&&"function"==typeof h?s(Function.call,h):h,d&&((y.virtual||(y.virtual={}))[u]=h,t&a.R&&v&&!v[u]&&o(v,u,h)))};a.F=1,a.G=2,a.S=4,a.P=8,a.B=16,a.W=32,a.U=64,a.R=128,t.exports=a},function(t,e,n){var r=n(7),i=n(28);t.exports=n(5)?function(t,e,n){return r.f(t,e,i(1,n))}:function(t,e,n){return t[e]=n,t}},function(t,e,n){var r=n(46)("wks"),i=n(29),s=n(4).Symbol,o="function"==typeof s;(t.exports=function(t){return r[t]||(r[t]=o&&s[t]||(o?s:i)("Symbol."+t))}).store=r},function(t,e,n){var r=n(9),i=n(79);t.exports=n(53)?function(t,e,n){return r.setDesc(t,e,i(1,n))}:function(t,e,n){return t[e]=n,t}},function(t,e,n){"use strict";function r(t){return void 0===t||null===t}function i(t){return"object"==typeof t&&null!==t}function s(t){return Array.isArray(t)?t:r(t)?[]:[t]}function o(t,e){var n,r,i,s;if(e)for(s=Object.keys(e),n=0,r=s.length;n<r;n+=1)i=s[n],t[i]=e[i];return t}function a(t,e){var n,r="";for(n=0;n<e;n+=1)r+=t;return r}function u(t){return 0===t&&Number.NEGATIVE_INFINITY===1/t}t.exports.isNothing=r,t.exports.isObject=i,t.exports.toArray=s,t.exports.repeat=a,t.exports.isNegativeZero=u,t.exports.extend=o},function(t,e,n){"use strict";function r(t,e,n){var i=[];return t.include.forEach(function(t){n=r(t,e,n)}),t[e].forEach(function(t){n.forEach(function(e,n){e.tag===t.tag&&e.kind===t.kind&&i.push(n)}),n.push(t)}),n.filter(function(t,e){return-1===i.indexOf(e)})}function i(){function t(t){r[t.kind][t.tag]=r.fallback[t.tag]=t}var e,n,r={scalar:{},sequence:{},mapping:{},fallback:{}};for(e=0,n=arguments.length;e<n;e+=1)arguments[e].forEach(t);return r}function s(t){this.include=t.include||[],this.implicit=t.implicit||[],this.explicit=t.explicit||[],this.implicit.forEach(function(t){if(t.loadKind&&"scalar"!==t.loadKind)throw new a("There is a non-scalar type in the implicit list of a schema. Implicit resolving of such types is not supported.")}),this.compiledImplicit=r(this,"implicit",[]),this.compiledExplicit=r(this,"explicit",[]),this.compiledTypeMap=i(this.compiledImplicit,this.compiledExplicit)}var o=n(15),a=n(25),u=n(0);s.DEFAULT=null,s.create=function(){var t,e;switch(arguments.length){case 1:t=s.DEFAULT,e=arguments[0];break;case 2:t=arguments[0],e=arguments[1];break;default:throw new a("Wrong number of arguments for Schema.create function")}if(t=o.toArray(t),e=o.toArray(e),!t.every(function(t){return t instanceof s}))throw new a("Specified list of super schemas (or a single Schema object) contains a non-Schema object.");if(!e.every(function(t){return t instanceof u}))throw new a("Specified list of YAML types (or a single Type object) contains a non-Type object.");return new s({include:t,explicit:e})},t.exports=s},function(t,e,n){"use strict";function r(t){return void 0!==t.ref}function i(t){return void 0!==t.key}var s=n(36),o=n(88),a=(n(34),n(91),Object.prototype.hasOwnProperty),u=n(89),c={key:!0,ref:!0,__self:!0,__source:!0},h=function(t,e,n,r,i,s,o){var a={$$typeof:u,type:t,key:e,ref:n,props:o,_owner:s};return a};h.createElement=function(t,e,n){var s,u={},l=null,p=null;if(null!=e){r(e)&&(p=e.ref),i(e)&&(l=""+e.key),void 0===e.__self?null:e.__self,void 0===e.__source?null:e.__source;for(s in e)a.call(e,s)&&!c.hasOwnProperty(s)&&(u[s]=e[s])}var f=arguments.length-2;if(1===f)u.children=n;else if(f>1){for(var d=Array(f),m=0;m<f;m++)d[m]=arguments[m+2];u.children=d}if(t&&t.defaultProps){var x=t.defaultProps;for(s in x)void 0===u[s]&&(u[s]=x[s])}return h(t,l,p,0,0,o.current,u)},h.createFactory=function(t){var e=h.createElement.bind(null,t);return e.type=t,e},h.cloneAndReplaceKey=function(t,e){return h(t.type,e,t.ref,t._self,t._source,t._owner,t.props)},h.cloneElement=function(t,e,n){var u,l=s({},t.props),p=t.key,f=t.ref,d=(t._self,t._source,t._owner);if(null!=e){r(e)&&(f=e.ref,d=o.current),i(e)&&(p=""+e.key);var m;t.type&&t.type.defaultProps&&(m=t.type.defaultProps);for(u in e)a.call(e,u)&&!c.hasOwnProperty(u)&&(void 0===e[u]&&void 0!==m?l[u]=m[u]:l[u]=e[u])}var x=arguments.length-2;if(1===x)l.children=n;else if(x>1){for(var y=Array(x),v=0;v<x;v++)y[v]=arguments[v+2];l.children=y}return h(t.type,p,f,0,0,d,l)},h.isValidElement=function(t){return"object"==typeof t&&null!==t&&t.$$typeof===u},t.exports=h},function(t,e,n){var r=n(20);t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,e){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(t,e,n){var r=n(31);t.exports=function(t){if(!r(t))throw TypeError(t+" is not an object!");return t}},function(t,e){var n=t.exports={version:"1.2.6"};"number"==typeof __e&&(__e=n)},function(t,e,n){var r=n(51);t.exports=function(t,e,n){if(r(t),void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,i){return t.call(e,n,r,i)}}return function(){return t.apply(e,arguments)}}},function(t,e){t.exports={}},function(t,e,n){"use strict";function r(t,e){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=(new Error).stack||"",this.name="YAMLException",this.reason=t,this.mark=e,this.message=(this.reason||"(unknown reason)")+(this.mark?" "+this.mark.toString():"")}r.prototype=Object.create(Error.prototype),r.prototype.constructor=r,r.prototype.toString=function(t){var e=this.name+": ";return e+=this.reason||"(unknown reason)",!t&&this.mark&&(e+=" "+this.mark.toString()),e},t.exports=r},function(t,e,n){"use strict";var r=n(16);t.exports=new r({include:[n(83)],implicit:[n(196),n(189)],explicit:[n(181),n(191),n(192),n(194)]})},function(t,e,n){var r=n(71),i=n(39);t.exports=Object.keys||function(t){return r(t,i)}},function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},function(t,e){var n=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+r).toString(36))}},function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},function(t,e){t.exports=function(t){return"object"==typeof t?null!==t:"function"==typeof t}},function(t,e,n){var r=n(2),i=n(14),s=n(81)("src"),o=Function.toString,a=(""+o).split("toString");n(22).inspectSource=function(t){return o.call(t)},(t.exports=function(t,e,n,o){"function"==typeof n&&(n.hasOwnProperty(s)||i(n,s,t[e]?""+t[e]:a.join(String(e))),n.hasOwnProperty("name")||i(n,"name",e)),t===r?t[e]=n:(o||delete t[e],i(t,e,n))})(Function.prototype,"toString",function(){return"function"==typeof this&&this[s]||o.call(this)})},function(t,e,n){"use strict";function r(t){return function(){return t}}var i=function(){};i.thatReturns=r,i.thatReturnsFalse=r(!1),i.thatReturnsTrue=r(!0),i.thatReturnsNull=r(null),i.thatReturnsThis=function(){return this},i.thatReturnsArgument=function(t){return t},t.exports=i},function(t,e,n){"use strict";var r=n(33),i=r;t.exports=i},function(t,e,n){"use strict";var r=n(16);t.exports=r.DEFAULT=new r({include:[n(26)],explicit:[n(187),n(186),n(185)]})},function(t,e,n){"use strict";function r(t){if(null===t||void 0===t)throw new TypeError("Object.assign cannot be called with null or undefined");return Object(t)}/*
object-assign
(c) Sindre Sorhus
@license MIT
*/
var i=Object.getOwnPropertySymbols,s=Object.prototype.hasOwnProperty,o=Object.prototype.propertyIsEnumerable;t.exports=function(){try{if(!Object.assign)return!1;var t=new String("abc");if(t[5]="de","5"===Object.getOwnPropertyNames(t)[0])return!1;for(var e={},n=0;n<10;n++)e["_"+String.fromCharCode(n)]=n;if("**********"!==Object.getOwnPropertyNames(e).map(function(t){return e[t]}).join(""))return!1;var r={};return"abcdefghijklmnopqrst".split("").forEach(function(t){r[t]=t}),"abcdefghijklmnopqrst"===Object.keys(Object.assign({},r)).join("")}catch(t){return!1}}()?Object.assign:function(t,e){for(var n,a,u=r(t),c=1;c<arguments.length;c++){n=Object(arguments[c]);for(var h in n)s.call(n,h)&&(u[h]=n[h]);if(i){a=i(n);for(var l=0;l<a.length;l++)o.call(n,a[l])&&(u[a[l]]=n[a[l]])}}return u}},function(t,e,n){"use strict";function r(t){for(var e=arguments.length-1,n="Minified React error #"+t+"; visit http://facebook.github.io/react/docs/error-decoder.html?invariant="+t,r=0;r<e;r++)n+="&args[]="+encodeURIComponent(arguments[r+1]);n+=" for the full message or use the non-minified dev environment for full errors and additional helpful warnings.";var i=new Error(n);throw i.name="Invariant Violation",i.framesToPop=1,i}t.exports=r},function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},function(t,e){t.exports="constructor,hasOwnProperty,isPrototypeOf,propertyIsEnumerable,toLocaleString,toString,valueOf".split(",")},function(t,e){t.exports={}},function(t,e){t.exports=!0},function(t,e,n){var r=n(18),i=n(121),s=n(39),o=n(45)("IE_PROTO"),a=function(){},u=function(){var t,e=n(64)("iframe"),r=s.length;for(e.style.display="none",n(114).appendChild(e),e.src="javascript:",t=e.contentWindow.document,t.open(),t.write("<script>document.F=Object<\/script>"),t.close(),u=t.F;r--;)delete u.prototype[s[r]];return u()};t.exports=Object.create||function(t,e){var n;return null!==t?(a.prototype=r(t),n=new a,a.prototype=null,n[o]=t):n=u(),void 0===e?n:i(n,e)}},function(t,e){e.f={}.propertyIsEnumerable},function(t,e,n){var r=n(7).f,i=n(6),s=n(13)("toStringTag");t.exports=function(t,e,n){t&&!i(t=n?t:t.prototype,s)&&r(t,s,{configurable:!0,value:e})}},function(t,e,n){var r=n(46)("keys"),i=n(29);t.exports=function(t){return r[t]||(r[t]=i(t))}},function(t,e,n){var r=n(4),i=r["__core-js_shared__"]||(r["__core-js_shared__"]={});t.exports=function(t){return i[t]||(i[t]={})}},function(t,e){var n=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:n)(t)}},function(t,e,n){var r=n(20);t.exports=function(t,e){if(!r(t))return t;var n,i;if(e&&"function"==typeof(n=t.toString)&&!r(i=n.call(t)))return i;if("function"==typeof(n=t.valueOf)&&!r(i=n.call(t)))return i;if(!e&&"function"==typeof(n=t.toString)&&!r(i=n.call(t)))return i;throw TypeError("Can't convert object to primitive value")}},function(t,e,n){var r=n(4),i=n(3),s=n(41),o=n(50),a=n(7).f;t.exports=function(t){var e=i.Symbol||(i.Symbol=s?{}:r.Symbol||{});"_"==t.charAt(0)||t in e||a(e,t,{value:o.f(t)})}},function(t,e,n){e.f=n(13)},function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},function(t,e,n){var r=n(30),i=n(1)("toStringTag"),s="Arguments"==r(function(){return arguments}());t.exports=function(t){var e,n,o;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=(e=Object(t))[i])?n:s?r(e):"Object"==(o=r(e))&&"function"==typeof e.callee?"Arguments":o}},function(t,e,n){t.exports=!n(144)(function(){return 7!=Object.defineProperty({},"a",{get:function(){return 7}}).a})},function(t,e,n){var r=n(9).setDesc,i=n(76),s=n(1)("toStringTag");t.exports=function(t,e,n){t&&!i(t=n?t:t.prototype,s)&&r(t,s,{configurable:!0,value:e})}},function(t,e,n){"use strict";var r=n(16);t.exports=new r({explicit:[n(195),n(193),n(188)]})},function(t,e,n){t.exports={default:n(106),__esModule:!0}},function(t,e,n){"use strict";e.__esModule=!0,e.default=function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}},function(t,e,n){"use strict";e.__esModule=!0;var r=n(100),i=function(t){return t&&t.__esModule?t:{default:t}}(r);e.default=function(){function t(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),(0,i.default)(t,r.key,r)}}return function(e,n,r){return n&&t(e.prototype,n),r&&t(e,r),e}}()},function(t,e,n){"use strict";function r(t){return t&&t.__esModule?t:{default:t}}e.__esModule=!0;var i=n(101),s=r(i),o=n(99),a=r(o),u=n(61),c=r(u);e.default=function(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function, not "+(void 0===e?"undefined":(0,c.default)(e)));t.prototype=(0,a.default)(e&&e.prototype,{constructor:{value:t,enumerable:!1,writable:!0,configurable:!0}}),e&&(s.default?(0,s.default)(t,e):t.__proto__=e)}},function(t,e,n){"use strict";e.__esModule=!0;var r=n(61),i=function(t){return t&&t.__esModule?t:{default:t}}(r);e.default=function(t,e){if(!t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return!e||"object"!==(void 0===e?"undefined":(0,i.default)(e))&&"function"!=typeof e?t:e}},function(t,e,n){"use strict";function r(t){return t&&t.__esModule?t:{default:t}}e.__esModule=!0;var i=n(103),s=r(i),o=n(102),a=r(o),u="function"==typeof a.default&&"symbol"==typeof s.default?function(t){return typeof t}:function(t){return t&&"function"==typeof a.default&&t.constructor===a.default&&t!==a.default.prototype?"symbol":typeof t};e.default="function"==typeof a.default&&"symbol"===u(s.default)?function(t){return void 0===t?"undefined":u(t)}:function(t){return t&&"function"==typeof a.default&&t.constructor===a.default&&t!==a.default.prototype?"symbol":void 0===t?"undefined":u(t)}},function(t,e){var n={}.toString;t.exports=function(t){return n.call(t).slice(8,-1)}},function(t,e,n){var r=n(110);t.exports=function(t,e,n){if(r(t),void 0===e)return t;switch(n){case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,i){return t.call(e,n,r,i)}}return function(){return t.apply(e,arguments)}}},function(t,e,n){var r=n(20),i=n(4).document,s=r(i)&&r(i.createElement);t.exports=function(t){return s?i.createElement(t):{}}},function(t,e,n){t.exports=!n(5)&&!n(19)(function(){return 7!=Object.defineProperty(n(64)("div"),"a",{get:function(){return 7}}).a})},function(t,e,n){"use strict";var r=n(41),i=n(11),s=n(72),o=n(12),a=n(6),u=n(40),c=n(117),h=n(44),l=n(70),p=n(13)("iterator"),f=!([].keys&&"next"in[].keys()),d=function(){return this};t.exports=function(t,e,n,m,x,y,v){c(n,e,m);var D,g,E,A=function(t){if(!f&&t in b)return b[t];switch(t){case"keys":case"values":return function(){return new n(this,t)}}return function(){return new n(this,t)}},C=e+" Iterator",S="values"==x,w=!1,b=t.prototype,F=b[p]||b["@@iterator"]||x&&b[x],k=F||A(x),B=x?S?A("entries"):k:void 0,T="Array"==e?b.entries||F:F;if(T&&(E=l(T.call(new t)))!==Object.prototype&&(h(E,C,!0),r||a(E,p)||o(E,p,d)),S&&F&&"values"!==F.name&&(w=!0,k=function(){return F.call(this)}),r&&!v||!f&&!w&&b[p]||o(b,p,k),u[e]=k,u[C]=d,x)if(D={values:S?k:A("values"),keys:y?k:A("keys"),entries:B},v)for(g in D)g in b||s(b,g,D[g]);else i(i.P+i.F*(f||w),e,D);return D}},function(t,e,n){var r=n(43),i=n(28),s=n(8),o=n(48),a=n(6),u=n(65),c=Object.getOwnPropertyDescriptor;e.f=n(5)?c:function(t,e){if(t=s(t),e=o(e,!0),u)try{return c(t,e)}catch(t){}if(a(t,e))return i(!r.f.call(t,e),t[e])}},function(t,e,n){var r=n(71),i=n(39).concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,i)}},function(t,e){e.f=Object.getOwnPropertySymbols},function(t,e,n){var r=n(6),i=n(73),s=n(45)("IE_PROTO"),o=Object.prototype;t.exports=Object.getPrototypeOf||function(t){return t=i(t),r(t,s)?t[s]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?o:null}},function(t,e,n){var r=n(6),i=n(8),s=n(112)(!1),o=n(45)("IE_PROTO");t.exports=function(t,e){var n,a=i(t),u=0,c=[];for(n in a)n!=o&&r(a,n)&&c.push(n);for(;e.length>u;)r(a,n=e[u++])&&(~s(c,n)||c.push(n));return c}},function(t,e,n){t.exports=n(12)},function(t,e,n){var r=n(38);t.exports=function(t){return Object(r(t))}},function(t,e){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on  "+t);return t}},function(t,e,n){var r=n(2),i=n(22),s=n(14),o=n(32),a=n(23),u=function(t,e,n){var c,h,l,p,f=t&u.F,d=t&u.G,m=t&u.S,x=t&u.P,y=t&u.B,v=d?r:m?r[e]||(r[e]={}):(r[e]||{}).prototype,D=d?i:i[e]||(i[e]={}),g=D.prototype||(D.prototype={});d&&(n=e);for(c in n)h=!f&&v&&c in v,l=(h?v:n)[c],p=y&&h?a(l,r):x&&"function"==typeof l?a(Function.call,l):l,v&&!h&&o(v,c,l),D[c]!=l&&s(D,c,p),x&&g[c]!=l&&(g[c]=l)};r.core=i,u.F=1,u.G=2,u.S=4,u.P=8,u.B=16,u.W=32,t.exports=u},function(t,e){var n={}.hasOwnProperty;t.exports=function(t,e){return n.call(t,e)}},function(t,e,n){"use strict";var r=n(78),i=n(75),s=n(32),o=n(14),a=n(76),u=n(24),c=n(151),h=n(54),l=n(9).getProto,p=n(1)("iterator"),f=!([].keys&&"next"in[].keys()),d=function(){return this};t.exports=function(t,e,n,m,x,y,v){c(n,e,m);var D,g,E=function(t){if(!f&&t in w)return w[t];switch(t){case"keys":case"values":return function(){return new n(this,t)}}return function(){return new n(this,t)}},A=e+" Iterator",C="values"==x,S=!1,w=t.prototype,b=w[p]||w["@@iterator"]||x&&w[x],F=b||E(x);if(b){var k=l(F.call(new t));h(k,A,!0),!r&&a(w,"@@iterator")&&o(k,p,d),C&&"values"!==b.name&&(S=!0,F=function(){return b.call(this)})}if(r&&!v||!f&&!S&&w[p]||o(w,p,F),u[e]=F,u[A]=d,x)if(D={values:C?F:E("values"),keys:y?F:E("keys"),entries:C?E("entries"):F},v)for(g in D)g in w||s(w,g,D[g]);else i(i.P+i.F*(f||S),e,D);return D}},function(t,e){t.exports=!1},function(t,e){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},function(t,e){var n=Math.ceil,r=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?r:n)(t)}},function(t,e){var n=0,r=Math.random();t.exports=function(t){return"Symbol(".concat(void 0===t?"":t,")_",(++n+r).toString(36))}},function(t,e,n){"use strict";var r={};t.exports=r},function(t,e,n){"use strict";var r=n(16);t.exports=new r({include:[n(84)]})},function(t,e,n){"use strict";var r=n(16);t.exports=new r({include:[n(55)],implicit:[n(190),n(182),n(184),n(183)]})},function(t,e,n){t.exports=n(199)()},function(t,e,n){"use strict";t.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},function(t,e,n){"use strict";function r(t,e,n){this.props=t,this.context=e,this.refs=c,this.updater=n||u}function i(t,e,n){this.props=t,this.context=e,this.refs=c,this.updater=n||u}function s(){}var o=n(37),a=n(36),u=n(90),c=(n(91),n(82));n(10),n(211);r.prototype.isReactComponent={},r.prototype.setState=function(t,e){"object"!=typeof t&&"function"!=typeof t&&null!=t&&o("85"),this.updater.enqueueSetState(this,t),e&&this.updater.enqueueCallback(this,e,"setState")},r.prototype.forceUpdate=function(t){this.updater.enqueueForceUpdate(this),t&&this.updater.enqueueCallback(this,t,"forceUpdate")};s.prototype=r.prototype,i.prototype=new s,i.prototype.constructor=i,a(i.prototype,r.prototype),i.prototype.isPureReactComponent=!0,t.exports={Component:r,PureComponent:i}},function(t,e,n){"use strict";var r={current:null};t.exports=r},function(t,e,n){"use strict";var r="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103;t.exports=r},function(t,e,n){"use strict";var r=(n(34),{isMounted:function(t){return!1},enqueueCallback:function(t,e){},enqueueForceUpdate:function(t){},enqueueReplaceState:function(t,e){},enqueueSetState:function(t,e){}});t.exports=r},function(t,e,n){"use strict";var r=!1;t.exports=r},function(t,e,n){"use strict";t.exports=n(204)},function(t,e,n){"use strict";n(141)},function(t,e,n){"use strict";function r(t){return t&&t.__esModule?t:{default:t}}var i=n(98),s=r(i),o=n(96),a=r(o),u=n(95),c=r(u),h=[a.default,c.default,function(){return{components:{StandaloneLayout:s.default}}}];t.exports=h},function(t,e,n){"use strict";function r(t){return t&&t.__esModule?t:{default:t}}function i(t){var e=t.fn;return{statePlugins:{spec:{actions:{downloadConfig:function(t){return function(){return(0,e.fetch)(t)}},getConfigByUrl:function(t,e){return function(n){function r(n){n instanceof Error||n.status>=400?(i.updateLoadingStatus("failedConfig"),i.updateLoadingStatus("failedConfig"),i.updateUrl(""),console.error(n.statusText+" "+t),e(null)):e(c(n.text))}var i=n.specActions;if(t)return i.downloadConfig(t).then(r,r)}}},selectors:{getLocalConfig:function(){return c(u.default)}}}}}}Object.defineProperty(e,"__esModule",{value:!0}),e.default=i;var s=n(176),o=r(s),a=n(201),u=r(a),c=function(t,e){try{return o.default.safeLoad(t)}catch(t){return e&&e.errActions.newThrownErr(new Error(t)),{}}}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(){return{components:{Topbar:i.default}}};var r=n(97),i=function(t){return t&&t.__esModule?t:{default:t}}(r)},function(t,e,n){"use strict";function r(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});var i=n(56),s=r(i),o=n(57),a=r(o),u=n(58),c=r(u),h=n(60),l=r(h),p=n(59),f=r(p),d=n(92),m=r(d),x=n(85),y=r(x),v=n(214),D=r(v),g=function(t){function e(t,n){(0,a.default)(this,e);var r=(0,l.default)(this,(e.__proto__||(0,s.default)(e)).call(this,t,n));return r.onUrlChange=function(t){var e=t.target.value;r.setState({url:e})},r.loadSpec=function(t){r.props.specActions.updateUrl(t),r.props.specActions.download(t)},r.onUrlSelect=function(t){var e=t.target.value||t.target.href;r.loadSpec(e),r.setSelectedUrl(e),t.preventDefault()},r.downloadUrl=function(t){r.loadSpec(r.state.url),t.preventDefault()},r.setSelectedUrl=function(t){var e=r.props.getConfigs(),n=e.urls||[];n&&n.length&&t&&n.forEach(function(e,n){e.url===t&&r.setState({selectedIndex:n})})},r.onFilterChange=function(t){var e=t.target.value;r.props.layoutActions.updateFilter(e)},r.state={url:t.specSelectors.url(),selectedIndex:0},r}return(0,f.default)(e,t),(0,c.default)(e,[{key:"componentWillReceiveProps",value:function(t){this.setState({url:t.specSelectors.url()})}},{key:"componentWillMount",value:function(){var t=this,e=this.props.getConfigs(),n=e.urls||[];if(n&&n.length){var r=e["urls.primaryName"];r&&n.forEach(function(e,n){e.name===r&&t.setState({selectedIndex:n})})}}},{key:"componentDidMount",value:function(){var t=this.props.getConfigs().urls||[];t&&t.length&&this.loadSpec(t[this.state.selectedIndex].url)}},{key:"render",value:function(){var t=this.props,e=t.getComponent,n=t.specSelectors,r=t.getConfigs,i=e("Button"),s=e("Link"),o="loading"===n.loadingStatus(),a="failed"===n.loadingStatus(),u={};a&&(u.color="red"),o&&(u.color="#aaa");var c=r(),h=c.urls,l=[],p=null;if(h){var f=[];h.forEach(function(t,e){f.push(m.default.createElement("option",{key:e,value:t.url},t.name))}),l.push(m.default.createElement("label",{className:"select-label",htmlFor:"select"},m.default.createElement("span",null,"Select a spec"),m.default.createElement("select",{id:"select",disabled:o,onChange:this.onUrlSelect,value:h[this.state.selectedIndex].url},f)))}else p=this.downloadUrl,l.push(m.default.createElement("input",{className:"download-url-input",type:"text",onChange:this.onUrlChange,value:this.state.url,disabled:o,style:u})),l.push(m.default.createElement(i,{className:"download-url-button",onClick:this.downloadUrl},"Explore"));return m.default.createElement("div",{className:"topbar"},m.default.createElement("div",{className:"wrapper"},m.default.createElement("div",{className:"topbar-wrapper"},m.default.createElement(s,{href:"#",title:"Swagger UX"},m.default.createElement("img",{height:"30",width:"30",src:D.default,alt:"Swagger UI"}),m.default.createElement("span",null,"swagger")),m.default.createElement("form",{className:"download-url-wrapper",onSubmit:p},l))))}}]),e}(m.default.Component);g.propTypes={layoutActions:y.default.object.isRequired},e.default=g,g.propTypes={specSelectors:y.default.object.isRequired,specActions:y.default.object.isRequired,getComponent:y.default.func.isRequired,getConfigs:y.default.func.isRequired}},function(t,e,n){"use strict";function r(t){return t&&t.__esModule?t:{default:t}}Object.defineProperty(e,"__esModule",{value:!0});var i=n(56),s=r(i),o=n(57),a=r(o),u=n(58),c=r(u),h=n(60),l=r(h),p=n(59),f=r(p),d=n(92),m=r(d),x=n(85),y=r(x),v=function(t){function e(){return(0,a.default)(this,e),(0,l.default)(this,(e.__proto__||(0,s.default)(e)).apply(this,arguments))}return(0,f.default)(e,t),(0,c.default)(e,[{key:"render",value:function(){var t=this.props,e=t.getComponent,n=t.specSelectors,r=e("Container"),i=e("Row"),s=e("Col"),o=e("Topbar",!0),a=e("BaseLayout",!0),u=e("onlineValidatorBadge",!0),c=n.loadingStatus();return m.default.createElement(r,{className:"swagger-ui"},o?m.default.createElement(o,null):null,"loading"===c&&m.default.createElement("div",{className:"info"},m.default.createElement("h4",{className:"title"},"Loading...")),"failed"===c&&m.default.createElement("div",{className:"info"},m.default.createElement("h4",{className:"title"},"Failed to load spec.")),"failedConfig"===c&&m.default.createElement("div",{className:"info",style:{maxWidth:"880px",marginLeft:"auto",marginRight:"auto",textAlign:"center"}},m.default.createElement("h4",{className:"title"},"Failed to load config.")),!c||"success"===c&&m.default.createElement(a,null),m.default.createElement(i,null,m.default.createElement(s,null,m.default.createElement(u,null))))}}]),e}(m.default.Component);v.propTypes={errSelectors:y.default.object.isRequired,errActions:y.default.object.isRequired,specActions:y.default.object.isRequired,specSelectors:y.default.object.isRequired,layoutSelectors:y.default.object.isRequired,layoutActions:y.default.object.isRequired,getComponent:y.default.func.isRequired},e.default=v},function(t,e,n){t.exports={default:n(104),__esModule:!0}},function(t,e,n){t.exports={default:n(105),__esModule:!0}},function(t,e,n){t.exports={default:n(107),__esModule:!0}},function(t,e,n){t.exports={default:n(108),__esModule:!0}},function(t,e,n){t.exports={default:n(109),__esModule:!0}},function(t,e,n){n(129);var r=n(3).Object;t.exports=function(t,e){return r.create(t,e)}},function(t,e,n){n(130);var r=n(3).Object;t.exports=function(t,e,n){return r.defineProperty(t,e,n)}},function(t,e,n){n(131),t.exports=n(3).Object.getPrototypeOf},function(t,e,n){n(132),t.exports=n(3).Object.setPrototypeOf},function(t,e,n){n(135),n(133),n(136),n(137),t.exports=n(3).Symbol},function(t,e,n){n(134),n(138),t.exports=n(50).f("iterator")},function(t,e){t.exports=function(t){if("function"!=typeof t)throw TypeError(t+" is not a function!");return t}},function(t,e){t.exports=function(){}},function(t,e,n){var r=n(8),i=n(127),s=n(126);t.exports=function(t){return function(e,n,o){var a,u=r(e),c=i(u.length),h=s(o,c);if(t&&n!=n){for(;c>h;)if((a=u[h++])!=a)return!0}else for(;c>h;h++)if((t||h in u)&&u[h]===n)return t||h||0;return!t&&-1}}},function(t,e,n){var r=n(27),i=n(69),s=n(43);t.exports=function(t){var e=r(t),n=i.f;if(n)for(var o,a=n(t),u=s.f,c=0;a.length>c;)u.call(t,o=a[c++])&&e.push(o);return e}},function(t,e,n){t.exports=n(4).document&&document.documentElement},function(t,e,n){var r=n(62);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},function(t,e,n){var r=n(62);t.exports=Array.isArray||function(t){return"Array"==r(t)}},function(t,e,n){"use strict";var r=n(42),i=n(28),s=n(44),o={};n(12)(o,n(13)("iterator"),function(){return this}),t.exports=function(t,e,n){t.prototype=r(o,{next:i(1,n)}),s(t,e+" Iterator")}},function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},function(t,e,n){var r=n(27),i=n(8);t.exports=function(t,e){for(var n,s=i(t),o=r(s),a=o.length,u=0;a>u;)if(s[n=o[u++]]===e)return n}},function(t,e,n){var r=n(29)("meta"),i=n(20),s=n(6),o=n(7).f,a=0,u=Object.isExtensible||function(){return!0},c=!n(19)(function(){return u(Object.preventExtensions({}))}),h=function(t){o(t,r,{value:{i:"O"+ ++a,w:{}}})},l=function(t,e){if(!i(t))return"symbol"==typeof t?t:("string"==typeof t?"S":"P")+t;if(!s(t,r)){if(!u(t))return"F";if(!e)return"E";h(t)}return t[r].i},p=function(t,e){if(!s(t,r)){if(!u(t))return!0;if(!e)return!1;h(t)}return t[r].w},f=function(t){return c&&d.NEED&&u(t)&&!s(t,r)&&h(t),t},d=t.exports={KEY:r,NEED:!1,fastKey:l,getWeak:p,onFreeze:f}},function(t,e,n){var r=n(7),i=n(18),s=n(27);t.exports=n(5)?Object.defineProperties:function(t,e){i(t);for(var n,o=s(e),a=o.length,u=0;a>u;)r.f(t,n=o[u++],e[n]);return t}},function(t,e,n){var r=n(8),i=n(68).f,s={}.toString,o="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],a=function(t){try{return i(t)}catch(t){return o.slice()}};t.exports.f=function(t){return o&&"[object Window]"==s.call(t)?a(t):i(r(t))}},function(t,e,n){var r=n(11),i=n(3),s=n(19);t.exports=function(t,e){var n=(i.Object||{})[t]||Object[t],o={};o[t]=e(n),r(r.S+r.F*s(function(){n(1)}),"Object",o)}},function(t,e,n){var r=n(20),i=n(18),s=function(t,e){if(i(t),!r(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,r){try{r=n(63)(Function.call,n(67).f(Object.prototype,"__proto__").set,2),r(t,[]),e=!(t instanceof Array)}catch(t){e=!0}return function(t,n){return s(t,n),e?t.__proto__=n:r(t,n),t}}({},!1):void 0),check:s}},function(t,e,n){var r=n(47),i=n(38);t.exports=function(t){return function(e,n){var s,o,a=String(i(e)),u=r(n),c=a.length;return u<0||u>=c?t?"":void 0:(s=a.charCodeAt(u),s<55296||s>56319||u+1===c||(o=a.charCodeAt(u+1))<56320||o>57343?t?a.charAt(u):s:t?a.slice(u,u+2):o-56320+(s-55296<<10)+65536)}}},function(t,e,n){var r=n(47),i=Math.max,s=Math.min;t.exports=function(t,e){return t=r(t),t<0?i(t+e,0):s(t,e)}},function(t,e,n){var r=n(47),i=Math.min;t.exports=function(t){return t>0?i(r(t),9007199254740991):0}},function(t,e,n){"use strict";var r=n(111),i=n(118),s=n(40),o=n(8);t.exports=n(66)(Array,"Array",function(t,e){this._t=o(t),this._i=0,this._k=e},function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,i(1)):"keys"==e?i(0,n):"values"==e?i(0,t[n]):i(0,[n,t[n]])},"values"),s.Arguments=s.Array,r("keys"),r("values"),r("entries")},function(t,e,n){var r=n(11);r(r.S,"Object",{create:n(42)})},function(t,e,n){var r=n(11);r(r.S+r.F*!n(5),"Object",{defineProperty:n(7).f})},function(t,e,n){var r=n(73),i=n(70);n(123)("getPrototypeOf",function(){return function(t){return i(r(t))}})},function(t,e,n){var r=n(11);r(r.S,"Object",{setPrototypeOf:n(124).set})},function(t,e){},function(t,e,n){"use strict";var r=n(125)(!0);n(66)(String,"String",function(t){this._t=String(t),this._i=0},function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=r(e,n),this._i+=t.length,{value:t,done:!1})})},function(t,e,n){"use strict";var r=n(4),i=n(6),s=n(5),o=n(11),a=n(72),u=n(120).KEY,c=n(19),h=n(46),l=n(44),p=n(29),f=n(13),d=n(50),m=n(49),x=n(119),y=n(113),v=n(116),D=n(18),g=n(8),E=n(48),A=n(28),C=n(42),S=n(122),w=n(67),b=n(7),F=n(27),k=w.f,B=b.f,T=S.f,I=r.Symbol,P=r.JSON,N=P&&P.stringify,M=f("_hidden"),O=f("toPrimitive"),_={}.propertyIsEnumerable,R=h("symbol-registry"),U=h("symbols"),j=h("op-symbols"),L=Object.prototype,J="function"==typeof I,X=r.QObject,z=!X||!X.prototype||!X.prototype.findChild,Y=s&&c(function(){return 7!=C(B({},"a",{get:function(){return B(this,"a",{value:7}).a}})).a})?function(t,e,n){var r=k(L,e);r&&delete L[e],B(t,e,n),r&&t!==L&&B(L,e,r)}:B,K=function(t){var e=U[t]=C(I.prototype);return e._k=t,e},H=J&&"symbol"==typeof I.iterator?function(t){return"symbol"==typeof t}:function(t){return t instanceof I},W=function(t,e,n){return t===L&&W(j,e,n),D(t),e=E(e,!0),D(n),i(U,e)?(n.enumerable?(i(t,M)&&t[M][e]&&(t[M][e]=!1),n=C(n,{enumerable:A(0,!1)})):(i(t,M)||B(t,M,A(1,{})),t[M][e]=!0),Y(t,e,n)):B(t,e,n)},G=function(t,e){D(t);for(var n,r=y(e=g(e)),i=0,s=r.length;s>i;)W(t,n=r[i++],e[n]);return t},q=function(t,e){return void 0===e?C(t):G(C(t),e)},V=function(t){var e=_.call(this,t=E(t,!0));return!(this===L&&i(U,t)&&!i(j,t))&&(!(e||!i(this,t)||!i(U,t)||i(this,M)&&this[M][t])||e)},$=function(t,e){if(t=g(t),e=E(e,!0),t!==L||!i(U,e)||i(j,e)){var n=k(t,e);return!n||!i(U,e)||i(t,M)&&t[M][e]||(n.enumerable=!0),n}},Z=function(t){for(var e,n=T(g(t)),r=[],s=0;n.length>s;)i(U,e=n[s++])||e==M||e==u||r.push(e);return r},Q=function(t){for(var e,n=t===L,r=T(n?j:g(t)),s=[],o=0;r.length>o;)!i(U,e=r[o++])||n&&!i(L,e)||s.push(U[e]);return s};J||(I=function(){if(this instanceof I)throw TypeError("Symbol is not a constructor!");var t=p(arguments.length>0?arguments[0]:void 0),e=function(n){this===L&&e.call(j,n),i(this,M)&&i(this[M],t)&&(this[M][t]=!1),Y(this,t,A(1,n))};return s&&z&&Y(L,t,{configurable:!0,set:e}),K(t)},a(I.prototype,"toString",function(){return this._k}),w.f=$,b.f=W,n(68).f=S.f=Z,n(43).f=V,n(69).f=Q,s&&!n(41)&&a(L,"propertyIsEnumerable",V,!0),d.f=function(t){return K(f(t))}),o(o.G+o.W+o.F*!J,{Symbol:I});for(var tt="hasInstance,isConcatSpreadable,iterator,match,replace,search,species,split,toPrimitive,toStringTag,unscopables".split(","),et=0;tt.length>et;)f(tt[et++]);for(var tt=F(f.store),et=0;tt.length>et;)m(tt[et++]);o(o.S+o.F*!J,"Symbol",{for:function(t){return i(R,t+="")?R[t]:R[t]=I(t)},keyFor:function(t){if(H(t))return x(R,t);throw TypeError(t+" is not a symbol!")},useSetter:function(){z=!0},useSimple:function(){z=!1}}),o(o.S+o.F*!J,"Object",{create:q,defineProperty:W,defineProperties:G,getOwnPropertyDescriptor:$,getOwnPropertyNames:Z,getOwnPropertySymbols:Q}),P&&o(o.S+o.F*(!J||c(function(){var t=I();return"[null]"!=N([t])||"{}"!=N({a:t})||"{}"!=N(Object(t))})),"JSON",{stringify:function(t){if(void 0!==t&&!H(t)){for(var e,n,r=[t],i=1;arguments.length>i;)r.push(arguments[i++]);return e=r[1],"function"==typeof e&&(n=e),!n&&v(e)||(e=function(t,e){if(n&&(e=n.call(this,t,e)),!H(e))return e}),r[1]=e,N.apply(P,r)}}}),I.prototype[O]||n(12)(I.prototype,O,I.prototype.valueOf),l(I,"Symbol"),l(Math,"Math",!0),l(r.JSON,"JSON",!0)},function(t,e,n){n(49)("asyncIterator")},function(t,e,n){n(49)("observable")},function(t,e,n){n(128);for(var r=n(4),i=n(12),s=n(40),o=n(13)("toStringTag"),a=["NodeList","DOMTokenList","MediaList","StyleSheetList","CSSRuleList"],u=0;u<5;u++){var c=a[u],h=r[c],l=h&&h.prototype;l&&!l[o]&&i(l,o,c),s[c]=s.Array}},function(t,e,n){"use strict";function r(t){var e=t.length;if(e%4>0)throw new Error("Invalid string. Length must be a multiple of 4");return"="===t[e-2]?2:"="===t[e-1]?1:0}function i(t){return 3*t.length/4-r(t)}function s(t){var e,n,i,s,o,a=t.length;s=r(t),o=new l(3*a/4-s),n=s>0?a-4:a;var u=0;for(e=0;e<n;e+=4)i=h[t.charCodeAt(e)]<<18|h[t.charCodeAt(e+1)]<<12|h[t.charCodeAt(e+2)]<<6|h[t.charCodeAt(e+3)],o[u++]=i>>16&255,o[u++]=i>>8&255,o[u++]=255&i;return 2===s?(i=h[t.charCodeAt(e)]<<2|h[t.charCodeAt(e+1)]>>4,o[u++]=255&i):1===s&&(i=h[t.charCodeAt(e)]<<10|h[t.charCodeAt(e+1)]<<4|h[t.charCodeAt(e+2)]>>2,o[u++]=i>>8&255,o[u++]=255&i),o}function o(t){return c[t>>18&63]+c[t>>12&63]+c[t>>6&63]+c[63&t]}function a(t,e,n){for(var r,i=[],s=e;s<n;s+=3)r=(t[s]<<16)+(t[s+1]<<8)+t[s+2],i.push(o(r));return i.join("")}function u(t){for(var e,n=t.length,r=n%3,i="",s=[],o=0,u=n-r;o<u;o+=16383)s.push(a(t,o,o+16383>u?u:o+16383));return 1===r?(e=t[n-1],i+=c[e>>2],i+=c[e<<4&63],i+="=="):2===r&&(e=(t[n-2]<<8)+t[n-1],i+=c[e>>10],i+=c[e>>4&63],i+=c[e<<2&63],i+="="),s.push(i),s.join("")}e.byteLength=i,e.toByteArray=s,e.fromByteArray=u;for(var c=[],h=[],l="undefined"!=typeof Uint8Array?Uint8Array:Array,p="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/",f=0,d=p.length;f<d;++f)c[f]=p[f],h[p.charCodeAt(f)]=f;h["-".charCodeAt(0)]=62,h["_".charCodeAt(0)]=63},function(t,e,n){"use strict";(function(t){function r(){return s.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function i(t,e){if(r()<e)throw new RangeError("Invalid typed array length");return s.TYPED_ARRAY_SUPPORT?(t=new Uint8Array(e),t.__proto__=s.prototype):(null===t&&(t=new s(e)),t.length=e),t}function s(t,e,n){if(!(s.TYPED_ARRAY_SUPPORT||this instanceof s))return new s(t,e,n);if("number"==typeof t){if("string"==typeof e)throw new Error("If encoding is specified then the first argument must be a string");return c(this,t)}return o(this,t,e,n)}function o(t,e,n,r){if("number"==typeof e)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&e instanceof ArrayBuffer?p(t,e,n,r):"string"==typeof e?h(t,e,n):f(t,e)}function a(t){if("number"!=typeof t)throw new TypeError('"size" argument must be a number');if(t<0)throw new RangeError('"size" argument must not be negative')}function u(t,e,n,r){return a(e),e<=0?i(t,e):void 0!==n?"string"==typeof r?i(t,e).fill(n,r):i(t,e).fill(n):i(t,e)}function c(t,e){if(a(e),t=i(t,e<0?0:0|d(e)),!s.TYPED_ARRAY_SUPPORT)for(var n=0;n<e;++n)t[n]=0;return t}function h(t,e,n){if("string"==typeof n&&""!==n||(n="utf8"),!s.isEncoding(n))throw new TypeError('"encoding" must be a valid string encoding');var r=0|x(e,n);t=i(t,r);var o=t.write(e,n);return o!==r&&(t=t.slice(0,o)),t}function l(t,e){var n=e.length<0?0:0|d(e.length);t=i(t,n);for(var r=0;r<n;r+=1)t[r]=255&e[r];return t}function p(t,e,n,r){if(e.byteLength,n<0||e.byteLength<n)throw new RangeError("'offset' is out of bounds");if(e.byteLength<n+(r||0))throw new RangeError("'length' is out of bounds");return e=void 0===n&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,n):new Uint8Array(e,n,r),s.TYPED_ARRAY_SUPPORT?(t=e,t.__proto__=s.prototype):t=l(t,e),t}function f(t,e){if(s.isBuffer(e)){var n=0|d(e.length);return t=i(t,n),0===t.length?t:(e.copy(t,0,0,n),t)}if(e){if("undefined"!=typeof ArrayBuffer&&e.buffer instanceof ArrayBuffer||"length"in e)return"number"!=typeof e.length||q(e.length)?i(t,0):l(t,e);if("Buffer"===e.type&&Z(e.data))return l(t,e.data)}throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}function d(t){if(t>=r())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+r().toString(16)+" bytes");return 0|t}function m(t){return+t!=t&&(t=0),s.alloc(+t)}function x(t,e){if(s.isBuffer(t))return t.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(t)||t instanceof ArrayBuffer))return t.byteLength;"string"!=typeof t&&(t=""+t);var n=t.length;if(0===n)return 0;for(var r=!1;;)switch(e){case"ascii":case"latin1":case"binary":return n;case"utf8":case"utf-8":case void 0:return Y(t).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*n;case"hex":return n>>>1;case"base64":return W(t).length;default:if(r)return Y(t).length;e=(""+e).toLowerCase(),r=!0}}function y(t,e,n){var r=!1;if((void 0===e||e<0)&&(e=0),e>this.length)return"";if((void 0===n||n>this.length)&&(n=this.length),n<=0)return"";if(n>>>=0,e>>>=0,n<=e)return"";for(t||(t="utf8");;)switch(t){case"hex":return P(this,e,n);case"utf8":case"utf-8":return k(this,e,n);case"ascii":return T(this,e,n);case"latin1":case"binary":return I(this,e,n);case"base64":return F(this,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return N(this,e,n);default:if(r)throw new TypeError("Unknown encoding: "+t);t=(t+"").toLowerCase(),r=!0}}function v(t,e,n){var r=t[e];t[e]=t[n],t[n]=r}function D(t,e,n,r,i){if(0===t.length)return-1;if("string"==typeof n?(r=n,n=0):n>2147483647?n=2147483647:n<-2147483648&&(n=-2147483648),n=+n,isNaN(n)&&(n=i?0:t.length-1),n<0&&(n=t.length+n),n>=t.length){if(i)return-1;n=t.length-1}else if(n<0){if(!i)return-1;n=0}if("string"==typeof e&&(e=s.from(e,r)),s.isBuffer(e))return 0===e.length?-1:g(t,e,n,r,i);if("number"==typeof e)return e&=255,s.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(t,e,n):Uint8Array.prototype.lastIndexOf.call(t,e,n):g(t,[e],n,r,i);throw new TypeError("val must be string, number or Buffer")}function g(t,e,n,r,i){function s(t,e){return 1===o?t[e]:t.readUInt16BE(e*o)}var o=1,a=t.length,u=e.length;if(void 0!==r&&("ucs2"===(r=String(r).toLowerCase())||"ucs-2"===r||"utf16le"===r||"utf-16le"===r)){if(t.length<2||e.length<2)return-1;o=2,a/=2,u/=2,n/=2}var c;if(i){var h=-1;for(c=n;c<a;c++)if(s(t,c)===s(e,-1===h?0:c-h)){if(-1===h&&(h=c),c-h+1===u)return h*o}else-1!==h&&(c-=c-h),h=-1}else for(n+u>a&&(n=a-u),c=n;c>=0;c--){for(var l=!0,p=0;p<u;p++)if(s(t,c+p)!==s(e,p)){l=!1;break}if(l)return c}return-1}function E(t,e,n,r){n=Number(n)||0;var i=t.length-n;r?(r=Number(r))>i&&(r=i):r=i;var s=e.length;if(s%2!=0)throw new TypeError("Invalid hex string");r>s/2&&(r=s/2);for(var o=0;o<r;++o){var a=parseInt(e.substr(2*o,2),16);if(isNaN(a))return o;t[n+o]=a}return o}function A(t,e,n,r){return G(Y(e,t.length-n),t,n,r)}function C(t,e,n,r){return G(K(e),t,n,r)}function S(t,e,n,r){return C(t,e,n,r)}function w(t,e,n,r){return G(W(e),t,n,r)}function b(t,e,n,r){return G(H(e,t.length-n),t,n,r)}function F(t,e,n){return 0===e&&n===t.length?V.fromByteArray(t):V.fromByteArray(t.slice(e,n))}function k(t,e,n){n=Math.min(t.length,n);for(var r=[],i=e;i<n;){var s=t[i],o=null,a=s>239?4:s>223?3:s>191?2:1;if(i+a<=n){var u,c,h,l;switch(a){case 1:s<128&&(o=s);break;case 2:u=t[i+1],128==(192&u)&&(l=(31&s)<<6|63&u)>127&&(o=l);break;case 3:u=t[i+1],c=t[i+2],128==(192&u)&&128==(192&c)&&(l=(15&s)<<12|(63&u)<<6|63&c)>2047&&(l<55296||l>57343)&&(o=l);break;case 4:u=t[i+1],c=t[i+2],h=t[i+3],128==(192&u)&&128==(192&c)&&128==(192&h)&&(l=(15&s)<<18|(63&u)<<12|(63&c)<<6|63&h)>65535&&l<1114112&&(o=l)}}null===o?(o=65533,a=1):o>65535&&(o-=65536,r.push(o>>>10&1023|55296),o=56320|1023&o),r.push(o),i+=a}return B(r)}function B(t){var e=t.length;if(e<=Q)return String.fromCharCode.apply(String,t);for(var n="",r=0;r<e;)n+=String.fromCharCode.apply(String,t.slice(r,r+=Q));return n}function T(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(127&t[i]);return r}function I(t,e,n){var r="";n=Math.min(t.length,n);for(var i=e;i<n;++i)r+=String.fromCharCode(t[i]);return r}function P(t,e,n){var r=t.length;(!e||e<0)&&(e=0),(!n||n<0||n>r)&&(n=r);for(var i="",s=e;s<n;++s)i+=z(t[s]);return i}function N(t,e,n){for(var r=t.slice(e,n),i="",s=0;s<r.length;s+=2)i+=String.fromCharCode(r[s]+256*r[s+1]);return i}function M(t,e,n){if(t%1!=0||t<0)throw new RangeError("offset is not uint");if(t+e>n)throw new RangeError("Trying to access beyond buffer length")}function O(t,e,n,r,i,o){if(!s.isBuffer(t))throw new TypeError('"buffer" argument must be a Buffer instance');if(e>i||e<o)throw new RangeError('"value" argument is out of bounds');if(n+r>t.length)throw new RangeError("Index out of range")}function _(t,e,n,r){e<0&&(e=65535+e+1);for(var i=0,s=Math.min(t.length-n,2);i<s;++i)t[n+i]=(e&255<<8*(r?i:1-i))>>>8*(r?i:1-i)}function R(t,e,n,r){e<0&&(e=4294967295+e+1);for(var i=0,s=Math.min(t.length-n,4);i<s;++i)t[n+i]=e>>>8*(r?i:3-i)&255}function U(t,e,n,r,i,s){if(n+r>t.length)throw new RangeError("Index out of range");if(n<0)throw new RangeError("Index out of range")}function j(t,e,n,r,i){return i||U(t,e,n,4,3.4028234663852886e38,-3.4028234663852886e38),$.write(t,e,n,r,23,4),n+4}function L(t,e,n,r,i){return i||U(t,e,n,8,1.7976931348623157e308,-1.7976931348623157e308),$.write(t,e,n,r,52,8),n+8}function J(t){if(t=X(t).replace(tt,""),t.length<2)return"";for(;t.length%4!=0;)t+="=";return t}function X(t){return t.trim?t.trim():t.replace(/^\s+|\s+$/g,"")}function z(t){return t<16?"0"+t.toString(16):t.toString(16)}function Y(t,e){e=e||1/0;for(var n,r=t.length,i=null,s=[],o=0;o<r;++o){if((n=t.charCodeAt(o))>55295&&n<57344){if(!i){if(n>56319){(e-=3)>-1&&s.push(239,191,189);continue}if(o+1===r){(e-=3)>-1&&s.push(239,191,189);continue}i=n;continue}if(n<56320){(e-=3)>-1&&s.push(239,191,189),i=n;continue}n=65536+(i-55296<<10|n-56320)}else i&&(e-=3)>-1&&s.push(239,191,189);if(i=null,n<128){if((e-=1)<0)break;s.push(n)}else if(n<2048){if((e-=2)<0)break;s.push(n>>6|192,63&n|128)}else if(n<65536){if((e-=3)<0)break;s.push(n>>12|224,n>>6&63|128,63&n|128)}else{if(!(n<1114112))throw new Error("Invalid code point");if((e-=4)<0)break;s.push(n>>18|240,n>>12&63|128,n>>6&63|128,63&n|128)}}return s}function K(t){for(var e=[],n=0;n<t.length;++n)e.push(255&t.charCodeAt(n));return e}function H(t,e){for(var n,r,i,s=[],o=0;o<t.length&&!((e-=2)<0);++o)n=t.charCodeAt(o),r=n>>8,i=n%256,s.push(i),s.push(r);return s}function W(t){return V.toByteArray(J(t))}function G(t,e,n,r){for(var i=0;i<r&&!(i+n>=e.length||i>=t.length);++i)e[i+n]=t[i];return i}function q(t){return t!==t}/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
var V=n(139),$=n(174),Z=n(175);e.Buffer=s,e.SlowBuffer=m,e.INSPECT_MAX_BYTES=50,s.TYPED_ARRAY_SUPPORT=void 0!==t.TYPED_ARRAY_SUPPORT?t.TYPED_ARRAY_SUPPORT:function(){try{var t=new Uint8Array(1);return t.__proto__={__proto__:Uint8Array.prototype,foo:function(){return 42}},42===t.foo()&&"function"==typeof t.subarray&&0===t.subarray(1,1).byteLength}catch(t){return!1}}(),e.kMaxLength=r(),s.poolSize=8192,s._augment=function(t){return t.__proto__=s.prototype,t},s.from=function(t,e,n){return o(null,t,e,n)},s.TYPED_ARRAY_SUPPORT&&(s.prototype.__proto__=Uint8Array.prototype,s.__proto__=Uint8Array,"undefined"!=typeof Symbol&&Symbol.species&&s[Symbol.species]===s&&Object.defineProperty(s,Symbol.species,{value:null,configurable:!0})),s.alloc=function(t,e,n){return u(null,t,e,n)},s.allocUnsafe=function(t){return c(null,t)},s.allocUnsafeSlow=function(t){return c(null,t)},s.isBuffer=function(t){return!(null==t||!t._isBuffer)},s.compare=function(t,e){if(!s.isBuffer(t)||!s.isBuffer(e))throw new TypeError("Arguments must be Buffers");if(t===e)return 0;for(var n=t.length,r=e.length,i=0,o=Math.min(n,r);i<o;++i)if(t[i]!==e[i]){n=t[i],r=e[i];break}return n<r?-1:r<n?1:0},s.isEncoding=function(t){switch(String(t).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},s.concat=function(t,e){if(!Z(t))throw new TypeError('"list" argument must be an Array of Buffers');if(0===t.length)return s.alloc(0);var n;if(void 0===e)for(e=0,n=0;n<t.length;++n)e+=t[n].length;var r=s.allocUnsafe(e),i=0;for(n=0;n<t.length;++n){var o=t[n];if(!s.isBuffer(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(r,i),i+=o.length}return r},s.byteLength=x,s.prototype._isBuffer=!0,s.prototype.swap16=function(){var t=this.length;if(t%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var e=0;e<t;e+=2)v(this,e,e+1);return this},s.prototype.swap32=function(){var t=this.length;if(t%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var e=0;e<t;e+=4)v(this,e,e+3),v(this,e+1,e+2);return this},s.prototype.swap64=function(){var t=this.length;if(t%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var e=0;e<t;e+=8)v(this,e,e+7),v(this,e+1,e+6),v(this,e+2,e+5),v(this,e+3,e+4);return this},s.prototype.toString=function(){var t=0|this.length;return 0===t?"":0===arguments.length?k(this,0,t):y.apply(this,arguments)},s.prototype.equals=function(t){if(!s.isBuffer(t))throw new TypeError("Argument must be a Buffer");return this===t||0===s.compare(this,t)},s.prototype.inspect=function(){var t="",n=e.INSPECT_MAX_BYTES;return this.length>0&&(t=this.toString("hex",0,n).match(/.{2}/g).join(" "),this.length>n&&(t+=" ... ")),"<Buffer "+t+">"},s.prototype.compare=function(t,e,n,r,i){if(!s.isBuffer(t))throw new TypeError("Argument must be a Buffer");if(void 0===e&&(e=0),void 0===n&&(n=t?t.length:0),void 0===r&&(r=0),void 0===i&&(i=this.length),e<0||n>t.length||r<0||i>this.length)throw new RangeError("out of range index");if(r>=i&&e>=n)return 0;if(r>=i)return-1;if(e>=n)return 1;if(e>>>=0,n>>>=0,r>>>=0,i>>>=0,this===t)return 0;for(var o=i-r,a=n-e,u=Math.min(o,a),c=this.slice(r,i),h=t.slice(e,n),l=0;l<u;++l)if(c[l]!==h[l]){o=c[l],a=h[l];break}return o<a?-1:a<o?1:0},s.prototype.includes=function(t,e,n){return-1!==this.indexOf(t,e,n)},s.prototype.indexOf=function(t,e,n){return D(this,t,e,n,!0)},s.prototype.lastIndexOf=function(t,e,n){return D(this,t,e,n,!1)},s.prototype.write=function(t,e,n,r){if(void 0===e)r="utf8",n=this.length,e=0;else if(void 0===n&&"string"==typeof e)r=e,n=this.length,e=0;else{if(!isFinite(e))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");e|=0,isFinite(n)?(n|=0,void 0===r&&(r="utf8")):(r=n,n=void 0)}var i=this.length-e;if((void 0===n||n>i)&&(n=i),t.length>0&&(n<0||e<0)||e>this.length)throw new RangeError("Attempt to write outside buffer bounds");r||(r="utf8");for(var s=!1;;)switch(r){case"hex":return E(this,t,e,n);case"utf8":case"utf-8":return A(this,t,e,n);case"ascii":return C(this,t,e,n);case"latin1":case"binary":return S(this,t,e,n);case"base64":return w(this,t,e,n);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return b(this,t,e,n);default:if(s)throw new TypeError("Unknown encoding: "+r);r=(""+r).toLowerCase(),s=!0}},s.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var Q=4096;s.prototype.slice=function(t,e){var n=this.length;t=~~t,e=void 0===e?n:~~e,t<0?(t+=n)<0&&(t=0):t>n&&(t=n),e<0?(e+=n)<0&&(e=0):e>n&&(e=n),e<t&&(e=t);var r;if(s.TYPED_ARRAY_SUPPORT)r=this.subarray(t,e),r.__proto__=s.prototype;else{var i=e-t;r=new s(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+t]}return r},s.prototype.readUIntLE=function(t,e,n){t|=0,e|=0,n||M(t,e,this.length);for(var r=this[t],i=1,s=0;++s<e&&(i*=256);)r+=this[t+s]*i;return r},s.prototype.readUIntBE=function(t,e,n){t|=0,e|=0,n||M(t,e,this.length);for(var r=this[t+--e],i=1;e>0&&(i*=256);)r+=this[t+--e]*i;return r},s.prototype.readUInt8=function(t,e){return e||M(t,1,this.length),this[t]},s.prototype.readUInt16LE=function(t,e){return e||M(t,2,this.length),this[t]|this[t+1]<<8},s.prototype.readUInt16BE=function(t,e){return e||M(t,2,this.length),this[t]<<8|this[t+1]},s.prototype.readUInt32LE=function(t,e){return e||M(t,4,this.length),(this[t]|this[t+1]<<8|this[t+2]<<16)+16777216*this[t+3]},s.prototype.readUInt32BE=function(t,e){return e||M(t,4,this.length),16777216*this[t]+(this[t+1]<<16|this[t+2]<<8|this[t+3])},s.prototype.readIntLE=function(t,e,n){t|=0,e|=0,n||M(t,e,this.length);for(var r=this[t],i=1,s=0;++s<e&&(i*=256);)r+=this[t+s]*i;return i*=128,r>=i&&(r-=Math.pow(2,8*e)),r},s.prototype.readIntBE=function(t,e,n){t|=0,e|=0,n||M(t,e,this.length);for(var r=e,i=1,s=this[t+--r];r>0&&(i*=256);)s+=this[t+--r]*i;return i*=128,s>=i&&(s-=Math.pow(2,8*e)),s},s.prototype.readInt8=function(t,e){return e||M(t,1,this.length),128&this[t]?-1*(255-this[t]+1):this[t]},s.prototype.readInt16LE=function(t,e){e||M(t,2,this.length);var n=this[t]|this[t+1]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt16BE=function(t,e){e||M(t,2,this.length);var n=this[t+1]|this[t]<<8;return 32768&n?4294901760|n:n},s.prototype.readInt32LE=function(t,e){return e||M(t,4,this.length),this[t]|this[t+1]<<8|this[t+2]<<16|this[t+3]<<24},s.prototype.readInt32BE=function(t,e){return e||M(t,4,this.length),this[t]<<24|this[t+1]<<16|this[t+2]<<8|this[t+3]},s.prototype.readFloatLE=function(t,e){return e||M(t,4,this.length),$.read(this,t,!0,23,4)},s.prototype.readFloatBE=function(t,e){return e||M(t,4,this.length),$.read(this,t,!1,23,4)},s.prototype.readDoubleLE=function(t,e){return e||M(t,8,this.length),$.read(this,t,!0,52,8)},s.prototype.readDoubleBE=function(t,e){return e||M(t,8,this.length),$.read(this,t,!1,52,8)},s.prototype.writeUIntLE=function(t,e,n,r){if(t=+t,e|=0,n|=0,!r){O(this,t,e,n,Math.pow(2,8*n)-1,0)}var i=1,s=0;for(this[e]=255&t;++s<n&&(i*=256);)this[e+s]=t/i&255;return e+n},s.prototype.writeUIntBE=function(t,e,n,r){if(t=+t,e|=0,n|=0,!r){O(this,t,e,n,Math.pow(2,8*n)-1,0)}var i=n-1,s=1;for(this[e+i]=255&t;--i>=0&&(s*=256);)this[e+i]=t/s&255;return e+n},s.prototype.writeUInt8=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,1,255,0),s.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),this[e]=255&t,e+1},s.prototype.writeUInt16LE=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):_(this,t,e,!0),e+2},s.prototype.writeUInt16BE=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,2,65535,0),s.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):_(this,t,e,!1),e+2},s.prototype.writeUInt32LE=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[e+3]=t>>>24,this[e+2]=t>>>16,this[e+1]=t>>>8,this[e]=255&t):R(this,t,e,!0),e+4},s.prototype.writeUInt32BE=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,4,4294967295,0),s.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):R(this,t,e,!1),e+4},s.prototype.writeIntLE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);O(this,t,e,n,i-1,-i)}var s=0,o=1,a=0;for(this[e]=255&t;++s<n&&(o*=256);)t<0&&0===a&&0!==this[e+s-1]&&(a=1),this[e+s]=(t/o>>0)-a&255;return e+n},s.prototype.writeIntBE=function(t,e,n,r){if(t=+t,e|=0,!r){var i=Math.pow(2,8*n-1);O(this,t,e,n,i-1,-i)}var s=n-1,o=1,a=0;for(this[e+s]=255&t;--s>=0&&(o*=256);)t<0&&0===a&&0!==this[e+s+1]&&(a=1),this[e+s]=(t/o>>0)-a&255;return e+n},s.prototype.writeInt8=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,1,127,-128),s.TYPED_ARRAY_SUPPORT||(t=Math.floor(t)),t<0&&(t=255+t+1),this[e]=255&t,e+1},s.prototype.writeInt16LE=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8):_(this,t,e,!0),e+2},s.prototype.writeInt16BE=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,2,32767,-32768),s.TYPED_ARRAY_SUPPORT?(this[e]=t>>>8,this[e+1]=255&t):_(this,t,e,!1),e+2},s.prototype.writeInt32LE=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,4,2147483647,-2147483648),s.TYPED_ARRAY_SUPPORT?(this[e]=255&t,this[e+1]=t>>>8,this[e+2]=t>>>16,this[e+3]=t>>>24):R(this,t,e,!0),e+4},s.prototype.writeInt32BE=function(t,e,n){return t=+t,e|=0,n||O(this,t,e,4,2147483647,-2147483648),t<0&&(t=4294967295+t+1),s.TYPED_ARRAY_SUPPORT?(this[e]=t>>>24,this[e+1]=t>>>16,this[e+2]=t>>>8,this[e+3]=255&t):R(this,t,e,!1),e+4},s.prototype.writeFloatLE=function(t,e,n){return j(this,t,e,!0,n)},s.prototype.writeFloatBE=function(t,e,n){return j(this,t,e,!1,n)},s.prototype.writeDoubleLE=function(t,e,n){return L(this,t,e,!0,n)},s.prototype.writeDoubleBE=function(t,e,n){return L(this,t,e,!1,n)},s.prototype.copy=function(t,e,n,r){if(n||(n=0),r||0===r||(r=this.length),e>=t.length&&(e=t.length),e||(e=0),r>0&&r<n&&(r=n),r===n)return 0;if(0===t.length||0===this.length)return 0;if(e<0)throw new RangeError("targetStart out of bounds");if(n<0||n>=this.length)throw new RangeError("sourceStart out of bounds");if(r<0)throw new RangeError("sourceEnd out of bounds");r>this.length&&(r=this.length),t.length-e<r-n&&(r=t.length-e+n);var i,o=r-n;if(this===t&&n<e&&e<r)for(i=o-1;i>=0;--i)t[i+e]=this[i+n];else if(o<1e3||!s.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)t[i+e]=this[i+n];else Uint8Array.prototype.set.call(t,this.subarray(n,n+o),e);return o},s.prototype.fill=function(t,e,n,r){if("string"==typeof t){if("string"==typeof e?(r=e,e=0,n=this.length):"string"==typeof n&&(r=n,n=this.length),1===t.length){var i=t.charCodeAt(0);i<256&&(t=i)}if(void 0!==r&&"string"!=typeof r)throw new TypeError("encoding must be a string");if("string"==typeof r&&!s.isEncoding(r))throw new TypeError("Unknown encoding: "+r)}else"number"==typeof t&&(t&=255);if(e<0||this.length<e||this.length<n)throw new RangeError("Out of range index");if(n<=e)return this;e>>>=0,n=void 0===n?this.length:n>>>0,t||(t=0);var o;if("number"==typeof t)for(o=e;o<n;++o)this[o]=t;else{var a=s.isBuffer(t)?t:Y(new s(t,r).toString()),u=a.length;for(o=0;o<n-e;++o)this[o+e]=a[o%u]}return this};var tt=/[^+\/0-9A-Za-z-_]/g}).call(e,n(215))},function(t,e,n){n(168),n(170),n(171),n(169),t.exports=n(22).Promise},function(t,e,n){var r=n(1)("unscopables"),i=Array.prototype;void 0==i[r]&&n(14)(i,r,{}),t.exports=function(t){i[r][t]=!0}},function(t,e,n){var r=n(31),i=n(2).document,s=r(i)&&r(i.createElement);t.exports=function(t){return s?i.createElement(t):{}}},function(t,e){t.exports=function(t){try{return!!t()}catch(t){return!0}}},function(t,e,n){var r=n(23),i=n(150),s=n(149),o=n(21),a=n(165),u=n(166);t.exports=function(t,e,n,c){var h,l,p,f=u(t),d=r(n,c,e?2:1),m=0;if("function"!=typeof f)throw TypeError(t+" is not iterable!");if(s(f))for(h=a(t.length);h>m;m++)e?d(o(l=t[m])[0],l[1]):d(t[m]);else for(p=f.call(t);!(l=p.next()).done;)i(p,d,l.value,e)}},function(t,e,n){t.exports=n(2).document&&document.documentElement},function(t,e){t.exports=function(t,e,n){var r=void 0===n;switch(e.length){case 0:return r?t():t.call(n);case 1:return r?t(e[0]):t.call(n,e[0]);case 2:return r?t(e[0],e[1]):t.call(n,e[0],e[1]);case 3:return r?t(e[0],e[1],e[2]):t.call(n,e[0],e[1],e[2]);case 4:return r?t(e[0],e[1],e[2],e[3]):t.call(n,e[0],e[1],e[2],e[3])}return t.apply(n,e)}},function(t,e,n){var r=n(30);t.exports=Object("z").propertyIsEnumerable(0)?Object:function(t){return"String"==r(t)?t.split(""):Object(t)}},function(t,e,n){var r=n(24),i=n(1)("iterator"),s=Array.prototype;t.exports=function(t){return void 0!==t&&(r.Array===t||s[i]===t)}},function(t,e,n){var r=n(21);t.exports=function(t,e,n,i){try{return i?e(r(n)[0],n[1]):e(n)}catch(e){var s=t.return;throw void 0!==s&&r(s.call(t)),e}}},function(t,e,n){"use strict";var r=n(9),i=n(79),s=n(54),o={};n(14)(o,n(1)("iterator"),function(){return this}),t.exports=function(t,e,n){t.prototype=r.create(o,{next:i(1,n)}),s(t,e+" Iterator")}},function(t,e,n){var r=n(1)("iterator"),i=!1;try{var s=[7][r]();s.return=function(){i=!0},Array.from(s,function(){throw 2})}catch(t){}t.exports=function(t,e){if(!e&&!i)return!1;var n=!1;try{var s=[7],o=s[r]();o.next=function(){return{done:n=!0}},s[r]=function(){return o},t(s)}catch(t){}return n}},function(t,e){t.exports=function(t,e){return{value:e,done:!!t}}},function(t,e,n){var r,i,s,o=n(2),a=n(163).set,u=o.MutationObserver||o.WebKitMutationObserver,c=o.process,h=o.Promise,l="process"==n(30)(c),p=function(){var t,e,n;for(l&&(t=c.domain)&&(c.domain=null,t.exit());r;)e=r.domain,n=r.fn,e&&e.enter(),n(),e&&e.exit(),r=r.next;i=void 0,t&&t.enter()};if(l)s=function(){c.nextTick(p)};else if(u){var f=1,d=document.createTextNode("");new u(p).observe(d,{characterData:!0}),s=function(){d.data=f=-f}}else s=h&&h.resolve?function(){h.resolve().then(p)}:function(){a.call(o,p)};t.exports=function(t){var e={fn:t,next:void 0,domain:l&&c.domain};i&&(i.next=e),r||(r=e,s()),i=e}},function(t,e,n){var r=n(32);t.exports=function(t,e){for(var n in e)r(t,n,e[n]);return t}},function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t==1/e:t!=t&&e!=e}},function(t,e,n){var r=n(9).getDesc,i=n(31),s=n(21),o=function(t,e){if(s(t),!i(e)&&null!==e)throw TypeError(e+": can't set as prototype!")};t.exports={set:Object.setPrototypeOf||("__proto__"in{}?function(t,e,i){try{i=n(23)(Function.call,r(Object.prototype,"__proto__").set,2),i(t,[]),e=!(t instanceof Array)}catch(t){e=!0}return function(t,n){return o(t,n),e?t.__proto__=n:i(t,n),t}}({},!1):void 0),check:o}},function(t,e,n){"use strict";var r=n(2),i=n(9),s=n(53),o=n(1)("species");t.exports=function(t){var e=r[t];s&&e&&!e[o]&&i.setDesc(e,o,{configurable:!0,get:function(){return this}})}},function(t,e,n){var r=n(2),i=r["__core-js_shared__"]||(r["__core-js_shared__"]={});t.exports=function(t){return i[t]||(i[t]={})}},function(t,e,n){var r=n(21),i=n(51),s=n(1)("species");t.exports=function(t,e){var n,o=r(t).constructor;return void 0===o||void 0==(n=r(o)[s])?e:i(n)}},function(t,e){t.exports=function(t,e,n){if(!(t instanceof e))throw TypeError(n+": use the 'new' operator!");return t}},function(t,e,n){var r=n(80),i=n(74);t.exports=function(t){return function(e,n){var s,o,a=String(i(e)),u=r(n),c=a.length;return u<0||u>=c?t?"":void 0:(s=a.charCodeAt(u),s<55296||s>56319||u+1===c||(o=a.charCodeAt(u+1))<56320||o>57343?t?a.charAt(u):s:t?a.slice(u,u+2):o-56320+(s-55296<<10)+65536)}}},function(t,e,n){var r,i,s,o=n(23),a=n(147),u=n(146),c=n(143),h=n(2),l=h.process,p=h.setImmediate,f=h.clearImmediate,d=h.MessageChannel,m=0,x={},y=function(){var t=+this;if(x.hasOwnProperty(t)){var e=x[t];delete x[t],e()}},v=function(t){y.call(t.data)};p&&f||(p=function(t){for(var e=[],n=1;arguments.length>n;)e.push(arguments[n++]);return x[++m]=function(){a("function"==typeof t?t:Function(t),e)},r(m),m},f=function(t){delete x[t]},"process"==n(30)(l)?r=function(t){l.nextTick(o(y,t,1))}:d?(i=new d,s=i.port2,i.port1.onmessage=v,r=o(s.postMessage,s,1)):h.addEventListener&&"function"==typeof postMessage&&!h.importScripts?(r=function(t){h.postMessage(t+"","*")},h.addEventListener("message",v,!1)):r="onreadystatechange"in c("script")?function(t){u.appendChild(c("script")).onreadystatechange=function(){u.removeChild(this),y.call(t)}}:function(t){setTimeout(o(y,t,1),0)}),t.exports={set:p,clear:f}},function(t,e,n){var r=n(148),i=n(74);t.exports=function(t){return r(i(t))}},function(t,e,n){var r=n(80),i=Math.min;t.exports=function(t){return t>0?i(r(t),9007199254740991):0}},function(t,e,n){var r=n(52),i=n(1)("iterator"),s=n(24);t.exports=n(22).getIteratorMethod=function(t){if(void 0!=t)return t[i]||t["@@iterator"]||s[r(t)]}},function(t,e,n){"use strict";var r=n(142),i=n(153),s=n(24),o=n(164);t.exports=n(77)(Array,"Array",function(t,e){this._t=o(t),this._i=0,this._k=e},function(){var t=this._t,e=this._k,n=this._i++;return!t||n>=t.length?(this._t=void 0,i(1)):"keys"==e?i(0,n):"values"==e?i(0,t[n]):i(0,[n,t[n]])},"values"),s.Arguments=s.Array,r("keys"),r("values"),r("entries")},function(t,e,n){"use strict";var r=n(52),i={};i[n(1)("toStringTag")]="z",i+""!="[object z]"&&n(32)(Object.prototype,"toString",function(){return"[object "+r(this)+"]"},!0)},function(t,e,n){"use strict";var r,i=n(9),s=n(78),o=n(2),a=n(23),u=n(52),c=n(75),h=n(31),l=n(21),p=n(51),f=n(161),d=n(145),m=n(157).set,x=n(156),y=n(1)("species"),v=n(160),D=n(154),g=o.process,E="process"==u(g),A=o.Promise,C=function(){},S=function(t){var e,n=new A(C);return t&&(n.constructor=function(t){t(C,C)}),(e=A.resolve(n)).catch(C),e===n},w=function(){function t(e){var n=new A(e);return m(n,t.prototype),n}var e=!1;try{if(e=A&&A.resolve&&S(),m(t,A),t.prototype=i.create(A.prototype,{constructor:{value:t}}),t.resolve(5).then(function(){})instanceof t||(e=!1),e&&n(53)){var r=!1;A.resolve(i.setDesc({},"then",{get:function(){r=!0}})),e=r}}catch(t){e=!1}return e}(),b=function(t,e){return!(!s||t!==A||e!==r)||x(t,e)},F=function(t){var e=l(t)[y];return void 0!=e?e:t},k=function(t){var e;return!(!h(t)||"function"!=typeof(e=t.then))&&e},B=function(t){var e,n;this.promise=new t(function(t,r){if(void 0!==e||void 0!==n)throw TypeError("Bad Promise constructor");e=t,n=r}),this.resolve=p(e),this.reject=p(n)},T=function(t){try{t()}catch(t){return{error:t}}},I=function(t,e){if(!t.n){t.n=!0;var n=t.c;D(function(){for(var r=t.v,i=1==t.s,s=0;n.length>s;)!function(e){var n,s,o=i?e.ok:e.fail,a=e.resolve,u=e.reject;try{o?(i||(t.h=!0),n=!0===o?r:o(r),n===e.promise?u(TypeError("Promise-chain cycle")):(s=k(n))?s.call(n,a,u):a(n)):u(r)}catch(t){u(t)}}(n[s++]);n.length=0,t.n=!1,e&&setTimeout(function(){var e,n,i=t.p;P(i)&&(E?g.emit("unhandledRejection",r,i):(e=o.onunhandledrejection)?e({promise:i,reason:r}):(n=o.console)&&n.error&&n.error("Unhandled promise rejection",r)),t.a=void 0},1)})}},P=function(t){var e,n=t._d,r=n.a||n.c,i=0;if(n.h)return!1;for(;r.length>i;)if(e=r[i++],e.fail||!P(e.promise))return!1;return!0},N=function(t){var e=this;e.d||(e.d=!0,e=e.r||e,e.v=t,e.s=2,e.a=e.c.slice(),I(e,!0))},M=function(t){var e,n=this;if(!n.d){n.d=!0,n=n.r||n;try{if(n.p===t)throw TypeError("Promise can't be resolved itself");(e=k(t))?D(function(){var r={r:n,d:!1};try{e.call(t,a(M,r,1),a(N,r,1))}catch(t){N.call(r,t)}}):(n.v=t,n.s=1,I(n,!1))}catch(t){N.call({r:n,d:!1},t)}}};w||(A=function(t){p(t);var e=this._d={p:f(this,A,"Promise"),c:[],a:void 0,s:0,d:!1,v:void 0,h:!1,n:!1};try{t(a(M,e,1),a(N,e,1))}catch(t){N.call(e,t)}},n(155)(A.prototype,{then:function(t,e){var n=new B(v(this,A)),r=n.promise,i=this._d;return n.ok="function"!=typeof t||t,n.fail="function"==typeof e&&e,i.c.push(n),i.a&&i.a.push(n),i.s&&I(i,!1),r},catch:function(t){return this.then(void 0,t)}})),c(c.G+c.W+c.F*!w,{Promise:A}),n(54)(A,"Promise"),n(158)("Promise"),r=n(22).Promise,c(c.S+c.F*!w,"Promise",{reject:function(t){var e=new B(this);return(0,e.reject)(t),e.promise}}),c(c.S+c.F*(!w||S(!0)),"Promise",{resolve:function(t){if(t instanceof A&&b(t.constructor,this))return t;var e=new B(this);return(0,e.resolve)(t),e.promise}}),c(c.S+c.F*!(w&&n(152)(function(t){A.all(t).catch(function(){})})),"Promise",{all:function(t){var e=F(this),n=new B(e),r=n.resolve,s=n.reject,o=[],a=T(function(){d(t,!1,o.push,o);var n=o.length,a=Array(n);n?i.each.call(o,function(t,i){var o=!1;e.resolve(t).then(function(t){o||(o=!0,a[i]=t,--n||r(a))},s)}):r(a)});return a&&s(a.error),n.promise},race:function(t){var e=F(this),n=new B(e),r=n.reject,i=T(function(){d(t,!1,function(t){e.resolve(t).then(n.resolve,r)})});return i&&r(i.error),n.promise}})},function(t,e,n){"use strict";var r=n(162)(!0);n(77)(String,"String",function(t){this._t=String(t),this._i=0},function(){var t,e=this._t,n=this._i;return n>=e.length?{value:void 0,done:!0}:(t=r(e,n),this._i+=t.length,{value:t,done:!1})})},function(t,e,n){n(167);var r=n(2),i=n(14),s=n(24),o=n(1)("iterator"),a=r.NodeList,u=r.HTMLCollection,c=a&&a.prototype,h=u&&u.prototype,l=s.NodeList=s.HTMLCollection=s.Array;c&&!c[o]&&i(c,o,l),h&&!h[o]&&i(h,o,l)},function(t,e,n){"use strict";function r(t){return t}function i(t,e,n){function i(t,e){var n=v.hasOwnProperty(e)?v[e]:null;A.hasOwnProperty(e)&&a("OVERRIDE_BASE"===n,"ReactClassInterface: You are attempting to override `%s` from your class specification. Ensure that your method names do not overlap with React methods.",e),t&&a("DEFINE_MANY"===n||"DEFINE_MANY_MERGED"===n,"ReactClassInterface: You are attempting to define `%s` on your component more than once. This conflict may be due to a mixin.",e)}function c(t,n){if(n){a("function"!=typeof n,"ReactClass: You're attempting to use a component class or function as a mixin. Instead, just use a regular object."),a(!e(n),"ReactClass: You're attempting to use a component as a mixin. Instead, just use a regular object.");var r=t.prototype,s=r.__reactAutoBindPairs;n.hasOwnProperty(u)&&D.mixins(t,n.mixins);for(var o in n)if(n.hasOwnProperty(o)&&o!==u){var c=n[o],h=r.hasOwnProperty(o);if(i(h,o),D.hasOwnProperty(o))D[o](t,c);else{var l=v.hasOwnProperty(o),d="function"==typeof c,m=d&&!l&&!h&&!1!==n.autobind;if(m)s.push(o,c),r[o]=c;else if(h){var x=v[o];a(l&&("DEFINE_MANY_MERGED"===x||"DEFINE_MANY"===x),"ReactClass: Unexpected spec policy %s for key %s when mixing in component specs.",x,o),"DEFINE_MANY_MERGED"===x?r[o]=p(r[o],c):"DEFINE_MANY"===x&&(r[o]=f(r[o],c))}else r[o]=c}}}else;}function h(t,e){if(e)for(var n in e){var r=e[n];if(e.hasOwnProperty(n)){var i=n in D;a(!i,'ReactClass: You are attempting to define a reserved property, `%s`, that shouldn\'t be on the "statics" key. Define it as an instance property instead; it will still be accessible on the constructor.',n);var s=n in t;a(!s,"ReactClass: You are attempting to define `%s` on your component more than once. This conflict may be due to a mixin.",n),t[n]=r}}}function l(t,e){a(t&&e&&"object"==typeof t&&"object"==typeof e,"mergeIntoWithNoDuplicateKeys(): Cannot merge non-objects.");for(var n in e)e.hasOwnProperty(n)&&(a(void 0===t[n],"mergeIntoWithNoDuplicateKeys(): Tried to merge two objects with the same key: `%s`. This conflict may be due to a mixin; in particular, this may be caused by two getInitialState() or getDefaultProps() methods returning objects with clashing keys.",n),t[n]=e[n]);return t}function p(t,e){return function(){var n=t.apply(this,arguments),r=e.apply(this,arguments);if(null==n)return r;if(null==r)return n;var i={};return l(i,n),l(i,r),i}}function f(t,e){return function(){t.apply(this,arguments),e.apply(this,arguments)}}function d(t,e){var n=e.bind(t);return n}function m(t){for(var e=t.__reactAutoBindPairs,n=0;n<e.length;n+=2){var r=e[n],i=e[n+1];t[r]=d(t,i)}}function x(t){var e=r(function(t,r,i){this.__reactAutoBindPairs.length&&m(this),this.props=t,this.context=r,this.refs=o,this.updater=i||n,this.state=null;var s=this.getInitialState?this.getInitialState():null;a("object"==typeof s&&!Array.isArray(s),"%s.getInitialState(): must return an object or null",e.displayName||"ReactCompositeComponent"),this.state=s});e.prototype=new C,e.prototype.constructor=e,e.prototype.__reactAutoBindPairs=[],y.forEach(c.bind(null,e)),c(e,g),c(e,t),c(e,E),e.getDefaultProps&&(e.defaultProps=e.getDefaultProps()),a(e.prototype.render,"createClass(...): Class specification must implement a `render` method.");for(var i in v)e.prototype[i]||(e.prototype[i]=null);return e}var y=[],v={mixins:"DEFINE_MANY",statics:"DEFINE_MANY",propTypes:"DEFINE_MANY",contextTypes:"DEFINE_MANY",childContextTypes:"DEFINE_MANY",getDefaultProps:"DEFINE_MANY_MERGED",getInitialState:"DEFINE_MANY_MERGED",getChildContext:"DEFINE_MANY_MERGED",render:"DEFINE_ONCE",componentWillMount:"DEFINE_MANY",componentDidMount:"DEFINE_MANY",componentWillReceiveProps:"DEFINE_MANY",shouldComponentUpdate:"DEFINE_ONCE",componentWillUpdate:"DEFINE_MANY",componentDidUpdate:"DEFINE_MANY",componentWillUnmount:"DEFINE_MANY",updateComponent:"OVERRIDE_BASE"},D={displayName:function(t,e){t.displayName=e},mixins:function(t,e){if(e)for(var n=0;n<e.length;n++)c(t,e[n])},childContextTypes:function(t,e){t.childContextTypes=s({},t.childContextTypes,e)},contextTypes:function(t,e){t.contextTypes=s({},t.contextTypes,e)},getDefaultProps:function(t,e){t.getDefaultProps?t.getDefaultProps=p(t.getDefaultProps,e):t.getDefaultProps=e},propTypes:function(t,e){t.propTypes=s({},t.propTypes,e)},statics:function(t,e){h(t,e)},autobind:function(){}},g={componentDidMount:function(){this.__isMounted=!0}},E={componentWillUnmount:function(){this.__isMounted=!1}},A={replaceState:function(t,e){this.updater.enqueueReplaceState(this,t,e)},isMounted:function(){return!!this.__isMounted}},C=function(){};return s(C.prototype,t.prototype,A),x}var s=n(36),o=n(82),a=n(10),u="mixins";t.exports=i},function(t,e,n){!function(e,n){t.exports=n()}(0,function(){return function(t){function e(r){if(n[r])return n[r].exports;var i=n[r]={exports:{},id:r,loaded:!1};return t[r].call(i.exports,i,i.exports,e),i.loaded=!0,i.exports}var n={};return e.m=t,e.c=n,e.p="",e(0)}([function(t,e,n){"use strict";function r(t,e,n){var r=null,i=function(t,e){n&&n(t,e),r&&r.visit(t,e)},s="function"==typeof n?i:null,o=!1;if(e){o="boolean"==typeof e.comment&&e.comment;var h="boolean"==typeof e.attachComment&&e.attachComment;(o||h)&&(r=new a.CommentHandler,r.attach=h,e.comment=!0,s=i)}var l=!1;e&&"string"==typeof e.sourceType&&(l="module"===e.sourceType);var p;p=e&&"boolean"==typeof e.jsx&&e.jsx?new u.JSXParser(t,e,s):new c.Parser(t,e,s);var f=l?p.parseModule():p.parseScript(),d=f;return o&&r&&(d.comments=r.comments),p.config.tokens&&(d.tokens=p.tokens),p.config.tolerant&&(d.errors=p.errorHandler.errors),d}function i(t,e,n){var i=e||{};return i.sourceType="module",r(t,i,n)}function s(t,e,n){var i=e||{};return i.sourceType="script",r(t,i,n)}function o(t,e,n){var r,i=new h.Tokenizer(t,e);r=[];try{for(;;){var s=i.getNextToken();if(!s)break;n&&(s=n(s)),r.push(s)}}catch(t){i.errorHandler.tolerate(t)}return i.errorHandler.tolerant&&(r.errors=i.errors()),r}Object.defineProperty(e,"__esModule",{value:!0});var a=n(1),u=n(3),c=n(8),h=n(15);e.parse=r,e.parseModule=i,e.parseScript=s,e.tokenize=o;var l=n(2);e.Syntax=l.Syntax,e.version="4.0.0"},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(2),i=function(){function t(){this.attach=!1,this.comments=[],this.stack=[],this.leading=[],this.trailing=[]}return t.prototype.insertInnerComments=function(t,e){if(t.type===r.Syntax.BlockStatement&&0===t.body.length){for(var n=[],i=this.leading.length-1;i>=0;--i){var s=this.leading[i];e.end.offset>=s.start&&(n.unshift(s.comment),this.leading.splice(i,1),this.trailing.splice(i,1))}n.length&&(t.innerComments=n)}},t.prototype.findTrailingComments=function(t){var e=[];if(this.trailing.length>0){for(var n=this.trailing.length-1;n>=0;--n){var r=this.trailing[n];r.start>=t.end.offset&&e.unshift(r.comment)}return this.trailing.length=0,e}var i=this.stack[this.stack.length-1];if(i&&i.node.trailingComments){var s=i.node.trailingComments[0];s&&s.range[0]>=t.end.offset&&(e=i.node.trailingComments,delete i.node.trailingComments)}return e},t.prototype.findLeadingComments=function(t){for(var e,n=[];this.stack.length>0;){var r=this.stack[this.stack.length-1];if(!(r&&r.start>=t.start.offset))break;e=r.node,this.stack.pop()}if(e){for(var i=e.leadingComments?e.leadingComments.length:0,s=i-1;s>=0;--s){var o=e.leadingComments[s];o.range[1]<=t.start.offset&&(n.unshift(o),e.leadingComments.splice(s,1))}return e.leadingComments&&0===e.leadingComments.length&&delete e.leadingComments,n}for(var s=this.leading.length-1;s>=0;--s){var r=this.leading[s];r.start<=t.start.offset&&(n.unshift(r.comment),this.leading.splice(s,1))}return n},t.prototype.visitNode=function(t,e){if(!(t.type===r.Syntax.Program&&t.body.length>0)){this.insertInnerComments(t,e);var n=this.findTrailingComments(e),i=this.findLeadingComments(e);i.length>0&&(t.leadingComments=i),n.length>0&&(t.trailingComments=n),this.stack.push({node:t,start:e.start.offset})}},t.prototype.visitComment=function(t,e){var n="L"===t.type[0]?"Line":"Block",r={type:n,value:t.value};if(t.range&&(r.range=t.range),t.loc&&(r.loc=t.loc),this.comments.push(r),this.attach){var i={comment:{type:n,value:t.value,range:[e.start.offset,e.end.offset]},start:e.start.offset};t.loc&&(i.comment.loc=t.loc),t.type=n,this.leading.push(i),this.trailing.push(i)}},t.prototype.visit=function(t,e){"LineComment"===t.type?this.visitComment(t,e):"BlockComment"===t.type?this.visitComment(t,e):this.attach&&this.visitNode(t,e)},t}();e.CommentHandler=i},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Syntax={AssignmentExpression:"AssignmentExpression",AssignmentPattern:"AssignmentPattern",ArrayExpression:"ArrayExpression",ArrayPattern:"ArrayPattern",ArrowFunctionExpression:"ArrowFunctionExpression",AwaitExpression:"AwaitExpression",BlockStatement:"BlockStatement",BinaryExpression:"BinaryExpression",BreakStatement:"BreakStatement",CallExpression:"CallExpression",CatchClause:"CatchClause",ClassBody:"ClassBody",ClassDeclaration:"ClassDeclaration",ClassExpression:"ClassExpression",ConditionalExpression:"ConditionalExpression",ContinueStatement:"ContinueStatement",DoWhileStatement:"DoWhileStatement",DebuggerStatement:"DebuggerStatement",EmptyStatement:"EmptyStatement",ExportAllDeclaration:"ExportAllDeclaration",ExportDefaultDeclaration:"ExportDefaultDeclaration",ExportNamedDeclaration:"ExportNamedDeclaration",ExportSpecifier:"ExportSpecifier",ExpressionStatement:"ExpressionStatement",ForStatement:"ForStatement",ForOfStatement:"ForOfStatement",ForInStatement:"ForInStatement",FunctionDeclaration:"FunctionDeclaration",FunctionExpression:"FunctionExpression",Identifier:"Identifier",IfStatement:"IfStatement",ImportDeclaration:"ImportDeclaration",ImportDefaultSpecifier:"ImportDefaultSpecifier",ImportNamespaceSpecifier:"ImportNamespaceSpecifier",ImportSpecifier:"ImportSpecifier",Literal:"Literal",LabeledStatement:"LabeledStatement",LogicalExpression:"LogicalExpression",MemberExpression:"MemberExpression",MetaProperty:"MetaProperty",MethodDefinition:"MethodDefinition",NewExpression:"NewExpression",ObjectExpression:"ObjectExpression",ObjectPattern:"ObjectPattern",Program:"Program",Property:"Property",RestElement:"RestElement",ReturnStatement:"ReturnStatement",SequenceExpression:"SequenceExpression",SpreadElement:"SpreadElement",Super:"Super",SwitchCase:"SwitchCase",SwitchStatement:"SwitchStatement",TaggedTemplateExpression:"TaggedTemplateExpression",TemplateElement:"TemplateElement",TemplateLiteral:"TemplateLiteral",ThisExpression:"ThisExpression",ThrowStatement:"ThrowStatement",TryStatement:"TryStatement",UnaryExpression:"UnaryExpression",UpdateExpression:"UpdateExpression",VariableDeclaration:"VariableDeclaration",VariableDeclarator:"VariableDeclarator",WhileStatement:"WhileStatement",WithStatement:"WithStatement",YieldExpression:"YieldExpression"}},function(t,e,n){"use strict";function r(t){var e;switch(t.type){case a.JSXSyntax.JSXIdentifier:e=t.name;break;case a.JSXSyntax.JSXNamespacedName:var n=t;e=r(n.namespace)+":"+r(n.name);break;case a.JSXSyntax.JSXMemberExpression:var i=t;e=r(i.object)+"."+r(i.property)}return e}var i=this&&this.__extends||function(){var t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)e.hasOwnProperty(n)&&(t[n]=e[n])};return function(e,n){function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}}();Object.defineProperty(e,"__esModule",{value:!0});var s=n(4),o=n(5),a=n(6),u=n(7),c=n(8),h=n(13),l=n(14);h.TokenName[100]="JSXIdentifier",h.TokenName[101]="JSXText";var p=function(t){function e(e,n,r){return t.call(this,e,n,r)||this}return i(e,t),e.prototype.parsePrimaryExpression=function(){return this.match("<")?this.parseJSXRoot():t.prototype.parsePrimaryExpression.call(this)},e.prototype.startJSX=function(){this.scanner.index=this.startMarker.index,this.scanner.lineNumber=this.startMarker.line,this.scanner.lineStart=this.startMarker.index-this.startMarker.column},e.prototype.finishJSX=function(){this.nextToken()},e.prototype.reenterJSX=function(){this.startJSX(),this.expectJSX("}"),this.config.tokens&&this.tokens.pop()},e.prototype.createJSXNode=function(){return this.collectComments(),{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},e.prototype.createJSXChildNode=function(){return{index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}},e.prototype.scanXHTMLEntity=function(t){for(var e="&",n=!0,r=!1,i=!1,o=!1;!this.scanner.eof()&&n&&!r;){var a=this.scanner.source[this.scanner.index];if(a===t)break;if(r=";"===a,e+=a,++this.scanner.index,!r)switch(e.length){case 2:i="#"===a;break;case 3:i&&(o="x"===a,n=o||s.Character.isDecimalDigit(a.charCodeAt(0)),i=i&&!o);break;default:n=n&&!(i&&!s.Character.isDecimalDigit(a.charCodeAt(0))),n=n&&!(o&&!s.Character.isHexDigit(a.charCodeAt(0)))}}if(n&&r&&e.length>2){var u=e.substr(1,e.length-2);i&&u.length>1?e=String.fromCharCode(parseInt(u.substr(1),10)):o&&u.length>2?e=String.fromCharCode(parseInt("0"+u.substr(1),16)):i||o||!l.XHTMLEntities[u]||(e=l.XHTMLEntities[u])}return e},e.prototype.lexJSX=function(){var t=this.scanner.source.charCodeAt(this.scanner.index);if(60===t||62===t||47===t||58===t||61===t||123===t||125===t){var e=this.scanner.source[this.scanner.index++];return{type:7,value:e,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index-1,end:this.scanner.index}}if(34===t||39===t){for(var n=this.scanner.index,r=this.scanner.source[this.scanner.index++],i="";!this.scanner.eof();){var o=this.scanner.source[this.scanner.index++];if(o===r)break;i+="&"===o?this.scanXHTMLEntity(r):o}return{type:8,value:i,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:n,end:this.scanner.index}}if(46===t){var a=this.scanner.source.charCodeAt(this.scanner.index+1),u=this.scanner.source.charCodeAt(this.scanner.index+2),e=46===a&&46===u?"...":".",n=this.scanner.index;return this.scanner.index+=e.length,{type:7,value:e,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:n,end:this.scanner.index}}if(96===t)return{type:10,value:"",lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:this.scanner.index,end:this.scanner.index};if(s.Character.isIdentifierStart(t)&&92!==t){var n=this.scanner.index;for(++this.scanner.index;!this.scanner.eof();){var o=this.scanner.source.charCodeAt(this.scanner.index);if(s.Character.isIdentifierPart(o)&&92!==o)++this.scanner.index;else{if(45!==o)break;++this.scanner.index}}return{type:100,value:this.scanner.source.slice(n,this.scanner.index),lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:n,end:this.scanner.index}}return this.scanner.lex()},e.prototype.nextJSXToken=function(){this.collectComments(),this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart;var t=this.lexJSX();return this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.config.tokens&&this.tokens.push(this.convertToken(t)),t},e.prototype.nextJSXText=function(){this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart;for(var t=this.scanner.index,e="";!this.scanner.eof();){var n=this.scanner.source[this.scanner.index];if("{"===n||"<"===n)break;++this.scanner.index,e+=n,s.Character.isLineTerminator(n.charCodeAt(0))&&(++this.scanner.lineNumber,"\r"===n&&"\n"===this.scanner.source[this.scanner.index]&&++this.scanner.index,this.scanner.lineStart=this.scanner.index)}this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart;var r={type:101,value:e,lineNumber:this.scanner.lineNumber,lineStart:this.scanner.lineStart,start:t,end:this.scanner.index};return e.length>0&&this.config.tokens&&this.tokens.push(this.convertToken(r)),r},e.prototype.peekJSXToken=function(){var t=this.scanner.saveState();this.scanner.scanComments();var e=this.lexJSX();return this.scanner.restoreState(t),e},e.prototype.expectJSX=function(t){var e=this.nextJSXToken();7===e.type&&e.value===t||this.throwUnexpectedToken(e)},e.prototype.matchJSX=function(t){var e=this.peekJSXToken();return 7===e.type&&e.value===t},e.prototype.parseJSXIdentifier=function(){var t=this.createJSXNode(),e=this.nextJSXToken();return 100!==e.type&&this.throwUnexpectedToken(e),this.finalize(t,new o.JSXIdentifier(e.value))},e.prototype.parseJSXElementName=function(){var t=this.createJSXNode(),e=this.parseJSXIdentifier();if(this.matchJSX(":")){var n=e;this.expectJSX(":");var r=this.parseJSXIdentifier();e=this.finalize(t,new o.JSXNamespacedName(n,r))}else if(this.matchJSX("."))for(;this.matchJSX(".");){var i=e;this.expectJSX(".");var s=this.parseJSXIdentifier();e=this.finalize(t,new o.JSXMemberExpression(i,s))}return e},e.prototype.parseJSXAttributeName=function(){var t,e=this.createJSXNode(),n=this.parseJSXIdentifier();if(this.matchJSX(":")){var r=n;this.expectJSX(":");var i=this.parseJSXIdentifier();t=this.finalize(e,new o.JSXNamespacedName(r,i))}else t=n;return t},e.prototype.parseJSXStringLiteralAttribute=function(){var t=this.createJSXNode(),e=this.nextJSXToken();8!==e.type&&this.throwUnexpectedToken(e);var n=this.getTokenRaw(e);return this.finalize(t,new u.Literal(e.value,n))},e.prototype.parseJSXExpressionAttribute=function(){var t=this.createJSXNode();this.expectJSX("{"),this.finishJSX(),this.match("}")&&this.tolerateError("JSX attributes must only be assigned a non-empty expression");var e=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(t,new o.JSXExpressionContainer(e))},e.prototype.parseJSXAttributeValue=function(){return this.matchJSX("{")?this.parseJSXExpressionAttribute():this.matchJSX("<")?this.parseJSXElement():this.parseJSXStringLiteralAttribute()},e.prototype.parseJSXNameValueAttribute=function(){var t=this.createJSXNode(),e=this.parseJSXAttributeName(),n=null;return this.matchJSX("=")&&(this.expectJSX("="),n=this.parseJSXAttributeValue()),this.finalize(t,new o.JSXAttribute(e,n))},e.prototype.parseJSXSpreadAttribute=function(){var t=this.createJSXNode();this.expectJSX("{"),this.expectJSX("..."),this.finishJSX();var e=this.parseAssignmentExpression();return this.reenterJSX(),this.finalize(t,new o.JSXSpreadAttribute(e))},e.prototype.parseJSXAttributes=function(){for(var t=[];!this.matchJSX("/")&&!this.matchJSX(">");){var e=this.matchJSX("{")?this.parseJSXSpreadAttribute():this.parseJSXNameValueAttribute();t.push(e)}return t},e.prototype.parseJSXOpeningElement=function(){var t=this.createJSXNode();this.expectJSX("<");var e=this.parseJSXElementName(),n=this.parseJSXAttributes(),r=this.matchJSX("/");return r&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(t,new o.JSXOpeningElement(e,r,n))},e.prototype.parseJSXBoundaryElement=function(){var t=this.createJSXNode();if(this.expectJSX("<"),this.matchJSX("/")){this.expectJSX("/");var e=this.parseJSXElementName();return this.expectJSX(">"),this.finalize(t,new o.JSXClosingElement(e))}var n=this.parseJSXElementName(),r=this.parseJSXAttributes(),i=this.matchJSX("/");return i&&this.expectJSX("/"),this.expectJSX(">"),this.finalize(t,new o.JSXOpeningElement(n,i,r))},e.prototype.parseJSXEmptyExpression=function(){var t=this.createJSXChildNode();return this.collectComments(),this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.finalize(t,new o.JSXEmptyExpression)},e.prototype.parseJSXExpressionContainer=function(){var t=this.createJSXNode();this.expectJSX("{");var e;return this.matchJSX("}")?(e=this.parseJSXEmptyExpression(),this.expectJSX("}")):(this.finishJSX(),e=this.parseAssignmentExpression(),this.reenterJSX()),this.finalize(t,new o.JSXExpressionContainer(e))},e.prototype.parseJSXChildren=function(){for(var t=[];!this.scanner.eof();){var e=this.createJSXChildNode(),n=this.nextJSXText();if(n.start<n.end){var r=this.getTokenRaw(n),i=this.finalize(e,new o.JSXText(n.value,r));t.push(i)}if("{"!==this.scanner.source[this.scanner.index])break;var s=this.parseJSXExpressionContainer();t.push(s)}return t},e.prototype.parseComplexJSXElement=function(t){for(var e=[];!this.scanner.eof();){t.children=t.children.concat(this.parseJSXChildren());var n=this.createJSXChildNode(),i=this.parseJSXBoundaryElement();if(i.type===a.JSXSyntax.JSXOpeningElement){var s=i;if(s.selfClosing){var u=this.finalize(n,new o.JSXElement(s,[],null));t.children.push(u)}else e.push(t),t={node:n,opening:s,closing:null,children:[]}}if(i.type===a.JSXSyntax.JSXClosingElement){t.closing=i;var c=r(t.opening.name);if(c!==r(t.closing.name)&&this.tolerateError("Expected corresponding JSX closing tag for %0",c),!(e.length>0))break;var u=this.finalize(t.node,new o.JSXElement(t.opening,t.children,t.closing));t=e[e.length-1],t.children.push(u),e.pop()}}return t},e.prototype.parseJSXElement=function(){var t=this.createJSXNode(),e=this.parseJSXOpeningElement(),n=[],r=null;if(!e.selfClosing){var i=this.parseComplexJSXElement({node:t,opening:e,closing:r,children:n});n=i.children,r=i.closing}return this.finalize(t,new o.JSXElement(e,n,r))},e.prototype.parseJSXRoot=function(){this.config.tokens&&this.tokens.pop(),this.startJSX();var t=this.parseJSXElement();return this.finishJSX(),t},e.prototype.isStartOfExpression=function(){return t.prototype.isStartOfExpression.call(this)||this.match("<")},e}(c.Parser);e.JSXParser=p},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n={NonAsciiIdentifierStart:/[\xAA\xB5\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0370-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u05D0-\u05EA\u05F0-\u05F2\u0620-\u064A\u066E\u066F\u0671-\u06D3\u06D5\u06E5\u06E6\u06EE\u06EF\u06FA-\u06FC\u06FF\u0710\u0712-\u072F\u074D-\u07A5\u07B1\u07CA-\u07EA\u07F4\u07F5\u07FA\u0800-\u0815\u081A\u0824\u0828\u0840-\u0858\u08A0-\u08B4\u0904-\u0939\u093D\u0950\u0958-\u0961\u0971-\u0980\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BD\u09CE\u09DC\u09DD\u09DF-\u09E1\u09F0\u09F1\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A59-\u0A5C\u0A5E\u0A72-\u0A74\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABD\u0AD0\u0AE0\u0AE1\u0AF9\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3D\u0B5C\u0B5D\u0B5F-\u0B61\u0B71\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BD0\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D\u0C58-\u0C5A\u0C60\u0C61\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBD\u0CDE\u0CE0\u0CE1\u0CF1\u0CF2\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D\u0D4E\u0D5F-\u0D61\u0D7A-\u0D7F\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0E01-\u0E30\u0E32\u0E33\u0E40-\u0E46\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB0\u0EB2\u0EB3\u0EBD\u0EC0-\u0EC4\u0EC6\u0EDC-\u0EDF\u0F00\u0F40-\u0F47\u0F49-\u0F6C\u0F88-\u0F8C\u1000-\u102A\u103F\u1050-\u1055\u105A-\u105D\u1061\u1065\u1066\u106E-\u1070\u1075-\u1081\u108E\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1711\u1720-\u1731\u1740-\u1751\u1760-\u176C\u176E-\u1770\u1780-\u17B3\u17D7\u17DC\u1820-\u1877\u1880-\u18A8\u18AA\u18B0-\u18F5\u1900-\u191E\u1950-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u1A00-\u1A16\u1A20-\u1A54\u1AA7\u1B05-\u1B33\u1B45-\u1B4B\u1B83-\u1BA0\u1BAE\u1BAF\u1BBA-\u1BE5\u1C00-\u1C23\u1C4D-\u1C4F\u1C5A-\u1C7D\u1CE9-\u1CEC\u1CEE-\u1CF1\u1CF5\u1CF6\u1D00-\u1DBF\u1E00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u2071\u207F\u2090-\u209C\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CEE\u2CF2\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u3005-\u3007\u3021-\u3029\u3031-\u3035\u3038-\u303C\u3041-\u3096\u309B-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA61F\uA62A\uA62B\uA640-\uA66E\uA67F-\uA69D\uA6A0-\uA6EF\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA801\uA803-\uA805\uA807-\uA80A\uA80C-\uA822\uA840-\uA873\uA882-\uA8B3\uA8F2-\uA8F7\uA8FB\uA8FD\uA90A-\uA925\uA930-\uA946\uA960-\uA97C\uA984-\uA9B2\uA9CF\uA9E0-\uA9E4\uA9E6-\uA9EF\uA9FA-\uA9FE\uAA00-\uAA28\uAA40-\uAA42\uAA44-\uAA4B\uAA60-\uAA76\uAA7A\uAA7E-\uAAAF\uAAB1\uAAB5\uAAB6\uAAB9-\uAABD\uAAC0\uAAC2\uAADB-\uAADD\uAAE0-\uAAEA\uAAF2-\uAAF4\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABE2\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D\uFB1F-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE70-\uFE74\uFE76-\uFEFC\uFF21-\uFF3A\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDE80-\uDE9C\uDEA0-\uDED0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF75\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00\uDE10-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE4\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC03-\uDC37\uDC83-\uDCAF\uDCD0-\uDCE8\uDD03-\uDD26\uDD50-\uDD72\uDD76\uDD83-\uDDB2\uDDC1-\uDDC4\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE2B\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEDE\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3D\uDF50\uDF5D-\uDF61]|\uD805[\uDC80-\uDCAF\uDCC4\uDCC5\uDCC7\uDD80-\uDDAE\uDDD8-\uDDDB\uDE00-\uDE2F\uDE44\uDE80-\uDEAA\uDF00-\uDF19]|\uD806[\uDCA0-\uDCDF\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDED0-\uDEED\uDF00-\uDF2F\uDF40-\uDF43\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50\uDF93-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB]|\uD83A[\uDC00-\uDCC4]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]/,NonAsciiIdentifierPart:/[\xAA\xB5\xB7\xBA\xC0-\xD6\xD8-\xF6\xF8-\u02C1\u02C6-\u02D1\u02E0-\u02E4\u02EC\u02EE\u0300-\u0374\u0376\u0377\u037A-\u037D\u037F\u0386-\u038A\u038C\u038E-\u03A1\u03A3-\u03F5\u03F7-\u0481\u0483-\u0487\u048A-\u052F\u0531-\u0556\u0559\u0561-\u0587\u0591-\u05BD\u05BF\u05C1\u05C2\u05C4\u05C5\u05C7\u05D0-\u05EA\u05F0-\u05F2\u0610-\u061A\u0620-\u0669\u066E-\u06D3\u06D5-\u06DC\u06DF-\u06E8\u06EA-\u06FC\u06FF\u0710-\u074A\u074D-\u07B1\u07C0-\u07F5\u07FA\u0800-\u082D\u0840-\u085B\u08A0-\u08B4\u08E3-\u0963\u0966-\u096F\u0971-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09F1\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A75\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AEF\u0AF9\u0B01-\u0B03\u0B05-\u0B0C\u0B0F\u0B10\u0B13-\u0B28\u0B2A-\u0B30\u0B32\u0B33\u0B35-\u0B39\u0B3C-\u0B44\u0B47\u0B48\u0B4B-\u0B4D\u0B56\u0B57\u0B5C\u0B5D\u0B5F-\u0B63\u0B66-\u0B6F\u0B71\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BEF\u0C00-\u0C03\u0C05-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3D-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C60-\u0C63\u0C66-\u0C6F\u0C81-\u0C83\u0C85-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1\u0CF2\u0D01-\u0D03\u0D05-\u0D0C\u0D0E-\u0D10\u0D12-\u0D3A\u0D3D-\u0D44\u0D46-\u0D48\u0D4A-\u0D4E\u0D57\u0D5F-\u0D63\u0D66-\u0D6F\u0D7A-\u0D7F\u0D82\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2\u0DF3\u0E01-\u0E3A\u0E40-\u0E4E\u0E50-\u0E59\u0E81\u0E82\u0E84\u0E87\u0E88\u0E8A\u0E8D\u0E94-\u0E97\u0E99-\u0E9F\u0EA1-\u0EA3\u0EA5\u0EA7\u0EAA\u0EAB\u0EAD-\u0EB9\u0EBB-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECD\u0ED0-\u0ED9\u0EDC-\u0EDF\u0F00\u0F18\u0F19\u0F20-\u0F29\u0F35\u0F37\u0F39\u0F3E-\u0F47\u0F49-\u0F6C\u0F71-\u0F84\u0F86-\u0F97\u0F99-\u0FBC\u0FC6\u1000-\u1049\u1050-\u109D\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u135F\u1369-\u1371\u1380-\u138F\u13A0-\u13F5\u13F8-\u13FD\u1401-\u166C\u166F-\u167F\u1681-\u169A\u16A0-\u16EA\u16EE-\u16F8\u1700-\u170C\u170E-\u1714\u1720-\u1734\u1740-\u1753\u1760-\u176C\u176E-\u1770\u1772\u1773\u1780-\u17D3\u17D7\u17DC\u17DD\u17E0-\u17E9\u180B-\u180D\u1810-\u1819\u1820-\u1877\u1880-\u18AA\u18B0-\u18F5\u1900-\u191E\u1920-\u192B\u1930-\u193B\u1946-\u196D\u1970-\u1974\u1980-\u19AB\u19B0-\u19C9\u19D0-\u19DA\u1A00-\u1A1B\u1A20-\u1A5E\u1A60-\u1A7C\u1A7F-\u1A89\u1A90-\u1A99\u1AA7\u1AB0-\u1ABD\u1B00-\u1B4B\u1B50-\u1B59\u1B6B-\u1B73\u1B80-\u1BF3\u1C00-\u1C37\u1C40-\u1C49\u1C4D-\u1C7D\u1CD0-\u1CD2\u1CD4-\u1CF6\u1CF8\u1CF9\u1D00-\u1DF5\u1DFC-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FBC\u1FBE\u1FC2-\u1FC4\u1FC6-\u1FCC\u1FD0-\u1FD3\u1FD6-\u1FDB\u1FE0-\u1FEC\u1FF2-\u1FF4\u1FF6-\u1FFC\u200C\u200D\u203F\u2040\u2054\u2071\u207F\u2090-\u209C\u20D0-\u20DC\u20E1\u20E5-\u20F0\u2102\u2107\u210A-\u2113\u2115\u2118-\u211D\u2124\u2126\u2128\u212A-\u2139\u213C-\u213F\u2145-\u2149\u214E\u2160-\u2188\u2C00-\u2C2E\u2C30-\u2C5E\u2C60-\u2CE4\u2CEB-\u2CF3\u2D00-\u2D25\u2D27\u2D2D\u2D30-\u2D67\u2D6F\u2D7F-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\u2DE0-\u2DFF\u3005-\u3007\u3021-\u302F\u3031-\u3035\u3038-\u303C\u3041-\u3096\u3099-\u309F\u30A1-\u30FA\u30FC-\u30FF\u3105-\u312D\u3131-\u318E\u31A0-\u31BA\u31F0-\u31FF\u3400-\u4DB5\u4E00-\u9FD5\uA000-\uA48C\uA4D0-\uA4FD\uA500-\uA60C\uA610-\uA62B\uA640-\uA66F\uA674-\uA67D\uA67F-\uA6F1\uA717-\uA71F\uA722-\uA788\uA78B-\uA7AD\uA7B0-\uA7B7\uA7F7-\uA827\uA840-\uA873\uA880-\uA8C4\uA8D0-\uA8D9\uA8E0-\uA8F7\uA8FB\uA8FD\uA900-\uA92D\uA930-\uA953\uA960-\uA97C\uA980-\uA9C0\uA9CF-\uA9D9\uA9E0-\uA9FE\uAA00-\uAA36\uAA40-\uAA4D\uAA50-\uAA59\uAA60-\uAA76\uAA7A-\uAAC2\uAADB-\uAADD\uAAE0-\uAAEF\uAAF2-\uAAF6\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E\uAB30-\uAB5A\uAB5C-\uAB65\uAB70-\uABEA\uABEC\uABED\uABF0-\uABF9\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uF900-\uFA6D\uFA70-\uFAD9\uFB00-\uFB06\uFB13-\uFB17\uFB1D-\uFB28\uFB2A-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFBB1\uFBD3-\uFD3D\uFD50-\uFD8F\uFD92-\uFDC7\uFDF0-\uFDFB\uFE00-\uFE0F\uFE20-\uFE2F\uFE33\uFE34\uFE4D-\uFE4F\uFE70-\uFE74\uFE76-\uFEFC\uFF10-\uFF19\uFF21-\uFF3A\uFF3F\uFF41-\uFF5A\uFF66-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]|\uD800[\uDC00-\uDC0B\uDC0D-\uDC26\uDC28-\uDC3A\uDC3C\uDC3D\uDC3F-\uDC4D\uDC50-\uDC5D\uDC80-\uDCFA\uDD40-\uDD74\uDDFD\uDE80-\uDE9C\uDEA0-\uDED0\uDEE0\uDF00-\uDF1F\uDF30-\uDF4A\uDF50-\uDF7A\uDF80-\uDF9D\uDFA0-\uDFC3\uDFC8-\uDFCF\uDFD1-\uDFD5]|\uD801[\uDC00-\uDC9D\uDCA0-\uDCA9\uDD00-\uDD27\uDD30-\uDD63\uDE00-\uDF36\uDF40-\uDF55\uDF60-\uDF67]|\uD802[\uDC00-\uDC05\uDC08\uDC0A-\uDC35\uDC37\uDC38\uDC3C\uDC3F-\uDC55\uDC60-\uDC76\uDC80-\uDC9E\uDCE0-\uDCF2\uDCF4\uDCF5\uDD00-\uDD15\uDD20-\uDD39\uDD80-\uDDB7\uDDBE\uDDBF\uDE00-\uDE03\uDE05\uDE06\uDE0C-\uDE13\uDE15-\uDE17\uDE19-\uDE33\uDE38-\uDE3A\uDE3F\uDE60-\uDE7C\uDE80-\uDE9C\uDEC0-\uDEC7\uDEC9-\uDEE6\uDF00-\uDF35\uDF40-\uDF55\uDF60-\uDF72\uDF80-\uDF91]|\uD803[\uDC00-\uDC48\uDC80-\uDCB2\uDCC0-\uDCF2]|\uD804[\uDC00-\uDC46\uDC66-\uDC6F\uDC7F-\uDCBA\uDCD0-\uDCE8\uDCF0-\uDCF9\uDD00-\uDD34\uDD36-\uDD3F\uDD50-\uDD73\uDD76\uDD80-\uDDC4\uDDCA-\uDDCC\uDDD0-\uDDDA\uDDDC\uDE00-\uDE11\uDE13-\uDE37\uDE80-\uDE86\uDE88\uDE8A-\uDE8D\uDE8F-\uDE9D\uDE9F-\uDEA8\uDEB0-\uDEEA\uDEF0-\uDEF9\uDF00-\uDF03\uDF05-\uDF0C\uDF0F\uDF10\uDF13-\uDF28\uDF2A-\uDF30\uDF32\uDF33\uDF35-\uDF39\uDF3C-\uDF44\uDF47\uDF48\uDF4B-\uDF4D\uDF50\uDF57\uDF5D-\uDF63\uDF66-\uDF6C\uDF70-\uDF74]|\uD805[\uDC80-\uDCC5\uDCC7\uDCD0-\uDCD9\uDD80-\uDDB5\uDDB8-\uDDC0\uDDD8-\uDDDD\uDE00-\uDE40\uDE44\uDE50-\uDE59\uDE80-\uDEB7\uDEC0-\uDEC9\uDF00-\uDF19\uDF1D-\uDF2B\uDF30-\uDF39]|\uD806[\uDCA0-\uDCE9\uDCFF\uDEC0-\uDEF8]|\uD808[\uDC00-\uDF99]|\uD809[\uDC00-\uDC6E\uDC80-\uDD43]|[\uD80C\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872][\uDC00-\uDFFF]|\uD80D[\uDC00-\uDC2E]|\uD811[\uDC00-\uDE46]|\uD81A[\uDC00-\uDE38\uDE40-\uDE5E\uDE60-\uDE69\uDED0-\uDEED\uDEF0-\uDEF4\uDF00-\uDF36\uDF40-\uDF43\uDF50-\uDF59\uDF63-\uDF77\uDF7D-\uDF8F]|\uD81B[\uDF00-\uDF44\uDF50-\uDF7E\uDF8F-\uDF9F]|\uD82C[\uDC00\uDC01]|\uD82F[\uDC00-\uDC6A\uDC70-\uDC7C\uDC80-\uDC88\uDC90-\uDC99\uDC9D\uDC9E]|\uD834[\uDD65-\uDD69\uDD6D-\uDD72\uDD7B-\uDD82\uDD85-\uDD8B\uDDAA-\uDDAD\uDE42-\uDE44]|\uD835[\uDC00-\uDC54\uDC56-\uDC9C\uDC9E\uDC9F\uDCA2\uDCA5\uDCA6\uDCA9-\uDCAC\uDCAE-\uDCB9\uDCBB\uDCBD-\uDCC3\uDCC5-\uDD05\uDD07-\uDD0A\uDD0D-\uDD14\uDD16-\uDD1C\uDD1E-\uDD39\uDD3B-\uDD3E\uDD40-\uDD44\uDD46\uDD4A-\uDD50\uDD52-\uDEA5\uDEA8-\uDEC0\uDEC2-\uDEDA\uDEDC-\uDEFA\uDEFC-\uDF14\uDF16-\uDF34\uDF36-\uDF4E\uDF50-\uDF6E\uDF70-\uDF88\uDF8A-\uDFA8\uDFAA-\uDFC2\uDFC4-\uDFCB\uDFCE-\uDFFF]|\uD836[\uDE00-\uDE36\uDE3B-\uDE6C\uDE75\uDE84\uDE9B-\uDE9F\uDEA1-\uDEAF]|\uD83A[\uDC00-\uDCC4\uDCD0-\uDCD6]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB]|\uD869[\uDC00-\uDED6\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF34\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1]|\uD87E[\uDC00-\uDE1D]|\uDB40[\uDD00-\uDDEF]/};e.Character={fromCodePoint:function(t){return t<65536?String.fromCharCode(t):String.fromCharCode(55296+(t-65536>>10))+String.fromCharCode(56320+(t-65536&1023))},isWhiteSpace:function(t){return 32===t||9===t||11===t||12===t||160===t||t>=5760&&[5760,8192,8193,8194,8195,8196,8197,8198,8199,8200,8201,8202,8239,8287,12288,65279].indexOf(t)>=0},isLineTerminator:function(t){return 10===t||13===t||8232===t||8233===t},isIdentifierStart:function(t){return 36===t||95===t||t>=65&&t<=90||t>=97&&t<=122||92===t||t>=128&&n.NonAsciiIdentifierStart.test(e.Character.fromCodePoint(t))},isIdentifierPart:function(t){return 36===t||95===t||t>=65&&t<=90||t>=97&&t<=122||t>=48&&t<=57||92===t||t>=128&&n.NonAsciiIdentifierPart.test(e.Character.fromCodePoint(t))},isDecimalDigit:function(t){return t>=48&&t<=57},isHexDigit:function(t){return t>=48&&t<=57||t>=65&&t<=70||t>=97&&t<=102},isOctalDigit:function(t){return t>=48&&t<=55}}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(6),i=function(){function t(t){this.type=r.JSXSyntax.JSXClosingElement,this.name=t}return t}();e.JSXClosingElement=i;var s=function(){function t(t,e,n){this.type=r.JSXSyntax.JSXElement,this.openingElement=t,this.children=e,this.closingElement=n}return t}();e.JSXElement=s;var o=function(){function t(){this.type=r.JSXSyntax.JSXEmptyExpression}return t}();e.JSXEmptyExpression=o;var a=function(){function t(t){this.type=r.JSXSyntax.JSXExpressionContainer,this.expression=t}return t}();e.JSXExpressionContainer=a;var u=function(){function t(t){this.type=r.JSXSyntax.JSXIdentifier,this.name=t}return t}();e.JSXIdentifier=u;var c=function(){function t(t,e){this.type=r.JSXSyntax.JSXMemberExpression,this.object=t,this.property=e}return t}();e.JSXMemberExpression=c;var h=function(){function t(t,e){this.type=r.JSXSyntax.JSXAttribute,this.name=t,this.value=e}return t}();e.JSXAttribute=h;var l=function(){function t(t,e){this.type=r.JSXSyntax.JSXNamespacedName,this.namespace=t,this.name=e}return t}();e.JSXNamespacedName=l;var p=function(){function t(t,e,n){this.type=r.JSXSyntax.JSXOpeningElement,this.name=t,this.selfClosing=e,this.attributes=n}return t}();e.JSXOpeningElement=p;var f=function(){function t(t){this.type=r.JSXSyntax.JSXSpreadAttribute,this.argument=t}return t}();e.JSXSpreadAttribute=f;var d=function(){function t(t,e){this.type=r.JSXSyntax.JSXText,this.value=t,this.raw=e}return t}();e.JSXText=d},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.JSXSyntax={JSXAttribute:"JSXAttribute",JSXClosingElement:"JSXClosingElement",JSXElement:"JSXElement",JSXEmptyExpression:"JSXEmptyExpression",JSXExpressionContainer:"JSXExpressionContainer",JSXIdentifier:"JSXIdentifier",JSXMemberExpression:"JSXMemberExpression",JSXNamespacedName:"JSXNamespacedName",JSXOpeningElement:"JSXOpeningElement",JSXSpreadAttribute:"JSXSpreadAttribute",JSXText:"JSXText"}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(2),i=function(){function t(t){this.type=r.Syntax.ArrayExpression,this.elements=t}return t}();e.ArrayExpression=i;var s=function(){function t(t){this.type=r.Syntax.ArrayPattern,this.elements=t}return t}();e.ArrayPattern=s;var o=function(){function t(t,e,n){this.type=r.Syntax.ArrowFunctionExpression,this.id=null,this.params=t,this.body=e,this.generator=!1,this.expression=n,this.async=!1}return t}();e.ArrowFunctionExpression=o;var a=function(){function t(t,e,n){this.type=r.Syntax.AssignmentExpression,this.operator=t,this.left=e,this.right=n}return t}();e.AssignmentExpression=a;var u=function(){function t(t,e){this.type=r.Syntax.AssignmentPattern,this.left=t,this.right=e}return t}();e.AssignmentPattern=u;var c=function(){function t(t,e,n){this.type=r.Syntax.ArrowFunctionExpression,this.id=null,this.params=t,this.body=e,this.generator=!1,this.expression=n,this.async=!0}return t}();e.AsyncArrowFunctionExpression=c;var h=function(){function t(t,e,n){this.type=r.Syntax.FunctionDeclaration,this.id=t,this.params=e,this.body=n,this.generator=!1,this.expression=!1,this.async=!0}return t}();e.AsyncFunctionDeclaration=h;var l=function(){function t(t,e,n){this.type=r.Syntax.FunctionExpression,this.id=t,this.params=e,this.body=n,this.generator=!1,this.expression=!1,this.async=!0}return t}();e.AsyncFunctionExpression=l;var p=function(){function t(t){this.type=r.Syntax.AwaitExpression,this.argument=t}return t}();e.AwaitExpression=p;var f=function(){function t(t,e,n){var i="||"===t||"&&"===t;this.type=i?r.Syntax.LogicalExpression:r.Syntax.BinaryExpression,this.operator=t,this.left=e,this.right=n}return t}();e.BinaryExpression=f;var d=function(){function t(t){this.type=r.Syntax.BlockStatement,this.body=t}return t}();e.BlockStatement=d;var m=function(){function t(t){this.type=r.Syntax.BreakStatement,this.label=t}return t}();e.BreakStatement=m;var x=function(){function t(t,e){this.type=r.Syntax.CallExpression,this.callee=t,this.arguments=e}return t}();e.CallExpression=x;var y=function(){function t(t,e){this.type=r.Syntax.CatchClause,this.param=t,this.body=e}return t}();e.CatchClause=y;var v=function(){function t(t){this.type=r.Syntax.ClassBody,this.body=t}return t}();e.ClassBody=v;var D=function(){function t(t,e,n){this.type=r.Syntax.ClassDeclaration,this.id=t,this.superClass=e,this.body=n}return t}();e.ClassDeclaration=D;var g=function(){function t(t,e,n){this.type=r.Syntax.ClassExpression,this.id=t,this.superClass=e,this.body=n}return t}();e.ClassExpression=g;var E=function(){function t(t,e){this.type=r.Syntax.MemberExpression,this.computed=!0,this.object=t,this.property=e}return t}();e.ComputedMemberExpression=E;var A=function(){function t(t,e,n){this.type=r.Syntax.ConditionalExpression,this.test=t,this.consequent=e,this.alternate=n}return t}();e.ConditionalExpression=A;var C=function(){function t(t){this.type=r.Syntax.ContinueStatement,this.label=t}return t}();e.ContinueStatement=C;var S=function(){function t(){this.type=r.Syntax.DebuggerStatement}return t}();e.DebuggerStatement=S;var w=function(){function t(t,e){this.type=r.Syntax.ExpressionStatement,this.expression=t,this.directive=e}return t}();e.Directive=w;var b=function(){function t(t,e){this.type=r.Syntax.DoWhileStatement,this.body=t,this.test=e}return t}();e.DoWhileStatement=b;var F=function(){function t(){this.type=r.Syntax.EmptyStatement}return t}();e.EmptyStatement=F;var k=function(){function t(t){this.type=r.Syntax.ExportAllDeclaration,this.source=t}return t}();e.ExportAllDeclaration=k;var B=function(){function t(t){this.type=r.Syntax.ExportDefaultDeclaration,this.declaration=t}return t}();e.ExportDefaultDeclaration=B;var T=function(){function t(t,e,n){this.type=r.Syntax.ExportNamedDeclaration,this.declaration=t,this.specifiers=e,this.source=n}return t}();e.ExportNamedDeclaration=T;var I=function(){function t(t,e){this.type=r.Syntax.ExportSpecifier,this.exported=e,this.local=t}return t}();e.ExportSpecifier=I;var P=function(){function t(t){this.type=r.Syntax.ExpressionStatement,this.expression=t}return t}();e.ExpressionStatement=P;var N=function(){function t(t,e,n){this.type=r.Syntax.ForInStatement,this.left=t,this.right=e,this.body=n,this.each=!1}return t}();e.ForInStatement=N;var M=function(){function t(t,e,n){this.type=r.Syntax.ForOfStatement,this.left=t,this.right=e,this.body=n}return t}();e.ForOfStatement=M;var O=function(){function t(t,e,n,i){this.type=r.Syntax.ForStatement,this.init=t,this.test=e,this.update=n,this.body=i}return t}();e.ForStatement=O;var _=function(){function t(t,e,n,i){this.type=r.Syntax.FunctionDeclaration,this.id=t,this.params=e,this.body=n,this.generator=i,this.expression=!1,this.async=!1}return t}();e.FunctionDeclaration=_;var R=function(){function t(t,e,n,i){this.type=r.Syntax.FunctionExpression,this.id=t,this.params=e,this.body=n,this.generator=i,this.expression=!1,this.async=!1}return t}();e.FunctionExpression=R;var U=function(){function t(t){this.type=r.Syntax.Identifier,this.name=t}return t}();e.Identifier=U;var j=function(){function t(t,e,n){this.type=r.Syntax.IfStatement,this.test=t,this.consequent=e,this.alternate=n}return t}();e.IfStatement=j;var L=function(){function t(t,e){this.type=r.Syntax.ImportDeclaration,this.specifiers=t,this.source=e}return t}();e.ImportDeclaration=L;var J=function(){function t(t){this.type=r.Syntax.ImportDefaultSpecifier,this.local=t}return t}();e.ImportDefaultSpecifier=J;var X=function(){function t(t){this.type=r.Syntax.ImportNamespaceSpecifier,this.local=t}return t}();e.ImportNamespaceSpecifier=X;var z=function(){function t(t,e){this.type=r.Syntax.ImportSpecifier,this.local=t,this.imported=e}return t}();e.ImportSpecifier=z;var Y=function(){function t(t,e){this.type=r.Syntax.LabeledStatement,this.label=t,this.body=e}return t}();e.LabeledStatement=Y;var K=function(){function t(t,e){this.type=r.Syntax.Literal,this.value=t,this.raw=e}return t}();e.Literal=K;var H=function(){function t(t,e){this.type=r.Syntax.MetaProperty,this.meta=t,this.property=e}return t}();e.MetaProperty=H;var W=function(){function t(t,e,n,i,s){this.type=r.Syntax.MethodDefinition,this.key=t,this.computed=e,this.value=n,this.kind=i,this.static=s}return t}();e.MethodDefinition=W;var G=function(){function t(t){this.type=r.Syntax.Program,this.body=t,this.sourceType="module"}return t}();e.Module=G;var q=function(){function t(t,e){this.type=r.Syntax.NewExpression,this.callee=t,this.arguments=e}return t}();e.NewExpression=q;var V=function(){function t(t){this.type=r.Syntax.ObjectExpression,this.properties=t}return t}();e.ObjectExpression=V;var $=function(){function t(t){this.type=r.Syntax.ObjectPattern,this.properties=t}return t}();e.ObjectPattern=$;var Z=function(){function t(t,e,n,i,s,o){this.type=r.Syntax.Property,this.key=e,this.computed=n,this.value=i,this.kind=t,this.method=s,this.shorthand=o}return t}();e.Property=Z;var Q=function(){function t(t,e,n,i){this.type=r.Syntax.Literal,this.value=t,this.raw=e,this.regex={pattern:n,flags:i}}return t}();e.RegexLiteral=Q;var tt=function(){function t(t){this.type=r.Syntax.RestElement,this.argument=t}return t}();e.RestElement=tt;var et=function(){function t(t){this.type=r.Syntax.ReturnStatement,this.argument=t}return t}();e.ReturnStatement=et;var nt=function(){function t(t){this.type=r.Syntax.Program,this.body=t,this.sourceType="script"}return t}();e.Script=nt;var rt=function(){function t(t){this.type=r.Syntax.SequenceExpression,this.expressions=t}return t}();e.SequenceExpression=rt;var it=function(){function t(t){this.type=r.Syntax.SpreadElement,this.argument=t}return t}();e.SpreadElement=it;var st=function(){function t(t,e){this.type=r.Syntax.MemberExpression,this.computed=!1,this.object=t,this.property=e}return t}();e.StaticMemberExpression=st;var ot=function(){function t(){this.type=r.Syntax.Super}return t}();e.Super=ot;var at=function(){function t(t,e){this.type=r.Syntax.SwitchCase,this.test=t,this.consequent=e}return t}();e.SwitchCase=at;var ut=function(){function t(t,e){this.type=r.Syntax.SwitchStatement,this.discriminant=t,this.cases=e}return t}();e.SwitchStatement=ut;var ct=function(){function t(t,e){this.type=r.Syntax.TaggedTemplateExpression,this.tag=t,this.quasi=e}return t}();e.TaggedTemplateExpression=ct;var ht=function(){function t(t,e){this.type=r.Syntax.TemplateElement,this.value=t,this.tail=e}return t}();e.TemplateElement=ht;var lt=function(){function t(t,e){this.type=r.Syntax.TemplateLiteral,this.quasis=t,this.expressions=e}return t}();e.TemplateLiteral=lt;var pt=function(){function t(){this.type=r.Syntax.ThisExpression}return t}();e.ThisExpression=pt;var ft=function(){function t(t){this.type=r.Syntax.ThrowStatement,this.argument=t}return t}();e.ThrowStatement=ft;var dt=function(){function t(t,e,n){this.type=r.Syntax.TryStatement,this.block=t,this.handler=e,this.finalizer=n}return t}();e.TryStatement=dt;var mt=function(){function t(t,e){this.type=r.Syntax.UnaryExpression,this.operator=t,this.argument=e,this.prefix=!0}return t}();e.UnaryExpression=mt;var xt=function(){function t(t,e,n){this.type=r.Syntax.UpdateExpression,this.operator=t,this.argument=e,this.prefix=n}return t}();e.UpdateExpression=xt;var yt=function(){function t(t,e){this.type=r.Syntax.VariableDeclaration,this.declarations=t,this.kind=e}return t}();e.VariableDeclaration=yt;var vt=function(){function t(t,e){this.type=r.Syntax.VariableDeclarator,this.id=t,this.init=e}return t}();e.VariableDeclarator=vt;var Dt=function(){function t(t,e){this.type=r.Syntax.WhileStatement,this.test=t,this.body=e}return t}();e.WhileStatement=Dt;var gt=function(){function t(t,e){this.type=r.Syntax.WithStatement,this.object=t,this.body=e}return t}();e.WithStatement=gt;var Et=function(){function t(t,e){this.type=r.Syntax.YieldExpression,this.argument=t,this.delegate=e}return t}();e.YieldExpression=Et},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(9),i=n(10),s=n(11),o=n(7),a=n(12),u=n(2),c=n(13),h=function(){function t(t,e,n){void 0===e&&(e={}),this.config={range:"boolean"==typeof e.range&&e.range,loc:"boolean"==typeof e.loc&&e.loc,source:null,tokens:"boolean"==typeof e.tokens&&e.tokens,comment:"boolean"==typeof e.comment&&e.comment,tolerant:"boolean"==typeof e.tolerant&&e.tolerant},this.config.loc&&e.source&&null!==e.source&&(this.config.source=String(e.source)),this.delegate=n,this.errorHandler=new i.ErrorHandler,this.errorHandler.tolerant=this.config.tolerant,this.scanner=new a.Scanner(t,this.errorHandler),this.scanner.trackComment=this.config.comment,this.operatorPrecedence={")":0,";":0,",":0,"=":0,"]":0,"||":1,"&&":2,"|":3,"^":4,"&":5,"==":6,"!=":6,"===":6,"!==":6,"<":7,">":7,"<=":7,">=":7,"<<":8,">>":8,">>>":8,"+":9,"-":9,"*":11,"/":11,"%":11},this.lookahead={type:2,value:"",lineNumber:this.scanner.lineNumber,lineStart:0,start:0,end:0},this.hasLineTerminator=!1,this.context={isModule:!1,await:!1,allowIn:!0,allowStrictDirective:!0,allowYield:!0,firstCoverInitializedNameError:null,isAssignmentTarget:!1,isBindingElement:!1,inFunctionBody:!1,inIteration:!1,inSwitch:!1,labelSet:{},strict:!1},this.tokens=[],this.startMarker={index:0,line:this.scanner.lineNumber,column:0},this.lastMarker={index:0,line:this.scanner.lineNumber,column:0},this.nextToken(),this.lastMarker={index:this.scanner.index,line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}return t.prototype.throwError=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var i=Array.prototype.slice.call(arguments,1),s=t.replace(/%(\d)/g,function(t,e){return r.assert(e<i.length,"Message reference must be in range"),i[e]}),o=this.lastMarker.index,a=this.lastMarker.line,u=this.lastMarker.column+1;throw this.errorHandler.createError(o,a,u,s)},t.prototype.tolerateError=function(t){for(var e=[],n=1;n<arguments.length;n++)e[n-1]=arguments[n];var i=Array.prototype.slice.call(arguments,1),s=t.replace(/%(\d)/g,function(t,e){return r.assert(e<i.length,"Message reference must be in range"),i[e]}),o=this.lastMarker.index,a=this.scanner.lineNumber,u=this.lastMarker.column+1;this.errorHandler.tolerateError(o,a,u,s)},t.prototype.unexpectedTokenError=function(t,e){var n,r=e||s.Messages.UnexpectedToken;if(t?(e||(r=2===t.type?s.Messages.UnexpectedEOS:3===t.type?s.Messages.UnexpectedIdentifier:6===t.type?s.Messages.UnexpectedNumber:8===t.type?s.Messages.UnexpectedString:10===t.type?s.Messages.UnexpectedTemplate:s.Messages.UnexpectedToken,4===t.type&&(this.scanner.isFutureReservedWord(t.value)?r=s.Messages.UnexpectedReserved:this.context.strict&&this.scanner.isStrictModeReservedWord(t.value)&&(r=s.Messages.StrictReservedWord))),n=t.value):n="ILLEGAL",r=r.replace("%0",n),t&&"number"==typeof t.lineNumber){var i=t.start,o=t.lineNumber,a=this.lastMarker.index-this.lastMarker.column,u=t.start-a+1;return this.errorHandler.createError(i,o,u,r)}var i=this.lastMarker.index,o=this.lastMarker.line,u=this.lastMarker.column+1;return this.errorHandler.createError(i,o,u,r)},t.prototype.throwUnexpectedToken=function(t,e){throw this.unexpectedTokenError(t,e)},t.prototype.tolerateUnexpectedToken=function(t,e){this.errorHandler.tolerate(this.unexpectedTokenError(t,e))},t.prototype.collectComments=function(){if(this.config.comment){var t=this.scanner.scanComments();if(t.length>0&&this.delegate)for(var e=0;e<t.length;++e){var n=t[e],r=void 0;r={type:n.multiLine?"BlockComment":"LineComment",value:this.scanner.source.slice(n.slice[0],n.slice[1])},this.config.range&&(r.range=n.range),this.config.loc&&(r.loc=n.loc);var i={start:{line:n.loc.start.line,column:n.loc.start.column,offset:n.range[0]},end:{line:n.loc.end.line,column:n.loc.end.column,offset:n.range[1]}};this.delegate(r,i)}}else this.scanner.scanComments()},t.prototype.getTokenRaw=function(t){return this.scanner.source.slice(t.start,t.end)},t.prototype.convertToken=function(t){var e={type:c.TokenName[t.type],value:this.getTokenRaw(t)};if(this.config.range&&(e.range=[t.start,t.end]),this.config.loc&&(e.loc={start:{line:this.startMarker.line,column:this.startMarker.column},end:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart}}),9===t.type){var n=t.pattern,r=t.flags;e.regex={pattern:n,flags:r}}return e},t.prototype.nextToken=function(){var t=this.lookahead;this.lastMarker.index=this.scanner.index,this.lastMarker.line=this.scanner.lineNumber,this.lastMarker.column=this.scanner.index-this.scanner.lineStart,this.collectComments(),this.scanner.index!==this.startMarker.index&&(this.startMarker.index=this.scanner.index,this.startMarker.line=this.scanner.lineNumber,this.startMarker.column=this.scanner.index-this.scanner.lineStart);var e=this.scanner.lex();return this.hasLineTerminator=t.lineNumber!==e.lineNumber,e&&this.context.strict&&3===e.type&&this.scanner.isStrictModeReservedWord(e.value)&&(e.type=4),this.lookahead=e,this.config.tokens&&2!==e.type&&this.tokens.push(this.convertToken(e)),t},t.prototype.nextRegexToken=function(){this.collectComments();var t=this.scanner.scanRegExp();return this.config.tokens&&(this.tokens.pop(),this.tokens.push(this.convertToken(t))),this.lookahead=t,this.nextToken(),t},t.prototype.createNode=function(){return{index:this.startMarker.index,line:this.startMarker.line,column:this.startMarker.column}},t.prototype.startNode=function(t){return{index:t.start,line:t.lineNumber,column:t.start-t.lineStart}},t.prototype.finalize=function(t,e){if(this.config.range&&(e.range=[t.index,this.lastMarker.index]),this.config.loc&&(e.loc={start:{line:t.line,column:t.column},end:{line:this.lastMarker.line,column:this.lastMarker.column}},this.config.source&&(e.loc.source=this.config.source)),this.delegate){var n={start:{line:t.line,column:t.column,offset:t.index},end:{line:this.lastMarker.line,column:this.lastMarker.column,offset:this.lastMarker.index}};this.delegate(e,n)}return e},t.prototype.expect=function(t){var e=this.nextToken();7===e.type&&e.value===t||this.throwUnexpectedToken(e)},t.prototype.expectCommaSeparator=function(){if(this.config.tolerant){var t=this.lookahead;7===t.type&&","===t.value?this.nextToken():7===t.type&&";"===t.value?(this.nextToken(),this.tolerateUnexpectedToken(t)):this.tolerateUnexpectedToken(t,s.Messages.UnexpectedToken)}else this.expect(",")},t.prototype.expectKeyword=function(t){var e=this.nextToken();4===e.type&&e.value===t||this.throwUnexpectedToken(e)},t.prototype.match=function(t){return 7===this.lookahead.type&&this.lookahead.value===t},t.prototype.matchKeyword=function(t){return 4===this.lookahead.type&&this.lookahead.value===t},t.prototype.matchContextualKeyword=function(t){return 3===this.lookahead.type&&this.lookahead.value===t},t.prototype.matchAssign=function(){if(7!==this.lookahead.type)return!1;var t=this.lookahead.value;return"="===t||"*="===t||"**="===t||"/="===t||"%="===t||"+="===t||"-="===t||"<<="===t||">>="===t||">>>="===t||"&="===t||"^="===t||"|="===t},t.prototype.isolateCoverGrammar=function(t){var e=this.context.isBindingElement,n=this.context.isAssignmentTarget,r=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var i=t.call(this);return null!==this.context.firstCoverInitializedNameError&&this.throwUnexpectedToken(this.context.firstCoverInitializedNameError),this.context.isBindingElement=e,this.context.isAssignmentTarget=n,this.context.firstCoverInitializedNameError=r,i},t.prototype.inheritCoverGrammar=function(t){var e=this.context.isBindingElement,n=this.context.isAssignmentTarget,r=this.context.firstCoverInitializedNameError;this.context.isBindingElement=!0,this.context.isAssignmentTarget=!0,this.context.firstCoverInitializedNameError=null;var i=t.call(this);return this.context.isBindingElement=this.context.isBindingElement&&e,this.context.isAssignmentTarget=this.context.isAssignmentTarget&&n,this.context.firstCoverInitializedNameError=r||this.context.firstCoverInitializedNameError,i},t.prototype.consumeSemicolon=function(){this.match(";")?this.nextToken():this.hasLineTerminator||(2===this.lookahead.type||this.match("}")||this.throwUnexpectedToken(this.lookahead),this.lastMarker.index=this.startMarker.index,this.lastMarker.line=this.startMarker.line,this.lastMarker.column=this.startMarker.column)},t.prototype.parsePrimaryExpression=function(){var t,e,n,r=this.createNode();switch(this.lookahead.type){case 3:(this.context.isModule||this.context.await)&&"await"===this.lookahead.value&&this.tolerateUnexpectedToken(this.lookahead),t=this.matchAsyncFunction()?this.parseFunctionExpression():this.finalize(r,new o.Identifier(this.nextToken().value));break;case 6:case 8:this.context.strict&&this.lookahead.octal&&this.tolerateUnexpectedToken(this.lookahead,s.Messages.StrictOctalLiteral),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,e=this.nextToken(),n=this.getTokenRaw(e),t=this.finalize(r,new o.Literal(e.value,n));break;case 1:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,e=this.nextToken(),n=this.getTokenRaw(e),t=this.finalize(r,new o.Literal("true"===e.value,n));break;case 5:this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,e=this.nextToken(),n=this.getTokenRaw(e),t=this.finalize(r,new o.Literal(null,n));break;case 10:t=this.parseTemplateLiteral();break;case 7:switch(this.lookahead.value){case"(":this.context.isBindingElement=!1,t=this.inheritCoverGrammar(this.parseGroupExpression);break;case"[":t=this.inheritCoverGrammar(this.parseArrayInitializer);break;case"{":t=this.inheritCoverGrammar(this.parseObjectInitializer);break;case"/":case"/=":this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.scanner.index=this.startMarker.index,e=this.nextRegexToken(),n=this.getTokenRaw(e),t=this.finalize(r,new o.RegexLiteral(e.regex,n,e.pattern,e.flags));break;default:t=this.throwUnexpectedToken(this.nextToken())}break;case 4:!this.context.strict&&this.context.allowYield&&this.matchKeyword("yield")?t=this.parseIdentifierName():!this.context.strict&&this.matchKeyword("let")?t=this.finalize(r,new o.Identifier(this.nextToken().value)):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.matchKeyword("function")?t=this.parseFunctionExpression():this.matchKeyword("this")?(this.nextToken(),t=this.finalize(r,new o.ThisExpression)):t=this.matchKeyword("class")?this.parseClassExpression():this.throwUnexpectedToken(this.nextToken()));break;default:t=this.throwUnexpectedToken(this.nextToken())}return t},t.prototype.parseSpreadElement=function(){var t=this.createNode();this.expect("...");var e=this.inheritCoverGrammar(this.parseAssignmentExpression);return this.finalize(t,new o.SpreadElement(e))},t.prototype.parseArrayInitializer=function(){var t=this.createNode(),e=[];for(this.expect("[");!this.match("]");)if(this.match(","))this.nextToken(),e.push(null);else if(this.match("...")){var n=this.parseSpreadElement();this.match("]")||(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1,this.expect(",")),e.push(n)}else e.push(this.inheritCoverGrammar(this.parseAssignmentExpression)),this.match("]")||this.expect(",");return this.expect("]"),this.finalize(t,new o.ArrayExpression(e))},t.prototype.parsePropertyMethod=function(t){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var e=this.context.strict,n=this.context.allowStrictDirective;this.context.allowStrictDirective=t.simple;var r=this.isolateCoverGrammar(this.parseFunctionSourceElements);return this.context.strict&&t.firstRestricted&&this.tolerateUnexpectedToken(t.firstRestricted,t.message),this.context.strict&&t.stricted&&this.tolerateUnexpectedToken(t.stricted,t.message),this.context.strict=e,this.context.allowStrictDirective=n,r},t.prototype.parsePropertyMethodFunction=function(){var t=this.createNode(),e=this.context.allowYield;this.context.allowYield=!1;var n=this.parseFormalParameters(),r=this.parsePropertyMethod(n);return this.context.allowYield=e,this.finalize(t,new o.FunctionExpression(null,n.params,r,!1))},t.prototype.parsePropertyMethodAsyncFunction=function(){var t=this.createNode(),e=this.context.allowYield,n=this.context.await;this.context.allowYield=!1,this.context.await=!0;var r=this.parseFormalParameters(),i=this.parsePropertyMethod(r);return this.context.allowYield=e,this.context.await=n,this.finalize(t,new o.AsyncFunctionExpression(null,r.params,i))},t.prototype.parseObjectPropertyKey=function(){var t,e=this.createNode(),n=this.nextToken();switch(n.type){case 8:case 6:this.context.strict&&n.octal&&this.tolerateUnexpectedToken(n,s.Messages.StrictOctalLiteral);var r=this.getTokenRaw(n);t=this.finalize(e,new o.Literal(n.value,r));break;case 3:case 1:case 5:case 4:t=this.finalize(e,new o.Identifier(n.value));break;case 7:"["===n.value?(t=this.isolateCoverGrammar(this.parseAssignmentExpression),this.expect("]")):t=this.throwUnexpectedToken(n);break;default:t=this.throwUnexpectedToken(n)}return t},t.prototype.isPropertyKey=function(t,e){return t.type===u.Syntax.Identifier&&t.name===e||t.type===u.Syntax.Literal&&t.value===e},t.prototype.parseObjectProperty=function(t){var e,n=this.createNode(),r=this.lookahead,i=null,a=null,u=!1,c=!1,h=!1,l=!1;if(3===r.type){var p=r.value;this.nextToken(),u=this.match("["),l=!(this.hasLineTerminator||"async"!==p||this.match(":")||this.match("(")||this.match("*")),i=l?this.parseObjectPropertyKey():this.finalize(n,new o.Identifier(p))}else this.match("*")?this.nextToken():(u=this.match("["),i=this.parseObjectPropertyKey());var f=this.qualifiedPropertyName(this.lookahead);if(3===r.type&&!l&&"get"===r.value&&f)e="get",u=this.match("["),i=this.parseObjectPropertyKey(),this.context.allowYield=!1,a=this.parseGetterMethod();else if(3===r.type&&!l&&"set"===r.value&&f)e="set",u=this.match("["),i=this.parseObjectPropertyKey(),a=this.parseSetterMethod();else if(7===r.type&&"*"===r.value&&f)e="init",u=this.match("["),i=this.parseObjectPropertyKey(),a=this.parseGeneratorMethod(),c=!0;else if(i||this.throwUnexpectedToken(this.lookahead),e="init",this.match(":")&&!l)!u&&this.isPropertyKey(i,"__proto__")&&(t.value&&this.tolerateError(s.Messages.DuplicateProtoProperty),t.value=!0),this.nextToken(),a=this.inheritCoverGrammar(this.parseAssignmentExpression);else if(this.match("("))a=l?this.parsePropertyMethodAsyncFunction():this.parsePropertyMethodFunction(),c=!0;else if(3===r.type){var p=this.finalize(n,new o.Identifier(r.value));if(this.match("=")){this.context.firstCoverInitializedNameError=this.lookahead,this.nextToken(),h=!0;var d=this.isolateCoverGrammar(this.parseAssignmentExpression);a=this.finalize(n,new o.AssignmentPattern(p,d))}else h=!0,a=p}else this.throwUnexpectedToken(this.nextToken());return this.finalize(n,new o.Property(e,i,u,a,c,h))},t.prototype.parseObjectInitializer=function(){var t=this.createNode();this.expect("{");for(var e=[],n={value:!1};!this.match("}");)e.push(this.parseObjectProperty(n)),this.match("}")||this.expectCommaSeparator();return this.expect("}"),this.finalize(t,new o.ObjectExpression(e))},t.prototype.parseTemplateHead=function(){r.assert(this.lookahead.head,"Template literal must start with a template head");var t=this.createNode(),e=this.nextToken(),n=e.value,i=e.cooked;return this.finalize(t,new o.TemplateElement({raw:n,cooked:i},e.tail))},t.prototype.parseTemplateElement=function(){10!==this.lookahead.type&&this.throwUnexpectedToken();var t=this.createNode(),e=this.nextToken(),n=e.value,r=e.cooked;return this.finalize(t,new o.TemplateElement({raw:n,cooked:r},e.tail))},t.prototype.parseTemplateLiteral=function(){var t=this.createNode(),e=[],n=[],r=this.parseTemplateHead();for(n.push(r);!r.tail;)e.push(this.parseExpression()),r=this.parseTemplateElement(),n.push(r);return this.finalize(t,new o.TemplateLiteral(n,e))},t.prototype.reinterpretExpressionAsPattern=function(t){switch(t.type){case u.Syntax.Identifier:case u.Syntax.MemberExpression:case u.Syntax.RestElement:case u.Syntax.AssignmentPattern:break;case u.Syntax.SpreadElement:t.type=u.Syntax.RestElement,this.reinterpretExpressionAsPattern(t.argument);break;case u.Syntax.ArrayExpression:t.type=u.Syntax.ArrayPattern;for(var e=0;e<t.elements.length;e++)null!==t.elements[e]&&this.reinterpretExpressionAsPattern(t.elements[e]);break;case u.Syntax.ObjectExpression:t.type=u.Syntax.ObjectPattern;for(var e=0;e<t.properties.length;e++)this.reinterpretExpressionAsPattern(t.properties[e].value);break;case u.Syntax.AssignmentExpression:t.type=u.Syntax.AssignmentPattern,delete t.operator,this.reinterpretExpressionAsPattern(t.left)}},t.prototype.parseGroupExpression=function(){var t;if(this.expect("("),this.match(")"))this.nextToken(),this.match("=>")||this.expect("=>"),t={type:"ArrowParameterPlaceHolder",params:[],async:!1};else{var e=this.lookahead,n=[];if(this.match("..."))t=this.parseRestElement(n),this.expect(")"),this.match("=>")||this.expect("=>"),t={type:"ArrowParameterPlaceHolder",params:[t],async:!1};else{var r=!1;if(this.context.isBindingElement=!0,t=this.inheritCoverGrammar(this.parseAssignmentExpression),this.match(",")){var i=[];for(this.context.isAssignmentTarget=!1,i.push(t);2!==this.lookahead.type&&this.match(",");){if(this.nextToken(),this.match(")")){this.nextToken();for(var s=0;s<i.length;s++)this.reinterpretExpressionAsPattern(i[s]);r=!0,t={type:"ArrowParameterPlaceHolder",params:i,async:!1}}else if(this.match("...")){this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),i.push(this.parseRestElement(n)),this.expect(")"),this.match("=>")||this.expect("=>"),this.context.isBindingElement=!1;for(var s=0;s<i.length;s++)this.reinterpretExpressionAsPattern(i[s]);r=!0,t={type:"ArrowParameterPlaceHolder",params:i,async:!1}}else i.push(this.inheritCoverGrammar(this.parseAssignmentExpression));if(r)break}r||(t=this.finalize(this.startNode(e),new o.SequenceExpression(i)))}if(!r){if(this.expect(")"),this.match("=>")&&(t.type===u.Syntax.Identifier&&"yield"===t.name&&(r=!0,t={type:"ArrowParameterPlaceHolder",params:[t],async:!1}),!r)){if(this.context.isBindingElement||this.throwUnexpectedToken(this.lookahead),t.type===u.Syntax.SequenceExpression)for(var s=0;s<t.expressions.length;s++)this.reinterpretExpressionAsPattern(t.expressions[s]);else this.reinterpretExpressionAsPattern(t);t={type:"ArrowParameterPlaceHolder",params:t.type===u.Syntax.SequenceExpression?t.expressions:[t],async:!1}}this.context.isBindingElement=!1}}}return t},t.prototype.parseArguments=function(){this.expect("(");var t=[];if(!this.match(")"))for(;;){var e=this.match("...")?this.parseSpreadElement():this.isolateCoverGrammar(this.parseAssignmentExpression);if(t.push(e),this.match(")"))break;if(this.expectCommaSeparator(),this.match(")"))break}return this.expect(")"),t},t.prototype.isIdentifierName=function(t){return 3===t.type||4===t.type||1===t.type||5===t.type},t.prototype.parseIdentifierName=function(){var t=this.createNode(),e=this.nextToken();return this.isIdentifierName(e)||this.throwUnexpectedToken(e),this.finalize(t,new o.Identifier(e.value))},t.prototype.parseNewExpression=function(){var t=this.createNode(),e=this.parseIdentifierName();r.assert("new"===e.name,"New expression must start with `new`");var n;if(this.match("."))if(this.nextToken(),3===this.lookahead.type&&this.context.inFunctionBody&&"target"===this.lookahead.value){var i=this.parseIdentifierName();n=new o.MetaProperty(e,i)}else this.throwUnexpectedToken(this.lookahead);else{var s=this.isolateCoverGrammar(this.parseLeftHandSideExpression),a=this.match("(")?this.parseArguments():[];n=new o.NewExpression(s,a),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return this.finalize(t,n)},t.prototype.parseAsyncArgument=function(){var t=this.parseAssignmentExpression();return this.context.firstCoverInitializedNameError=null,t},t.prototype.parseAsyncArguments=function(){this.expect("(");var t=[];if(!this.match(")"))for(;;){var e=this.match("...")?this.parseSpreadElement():this.isolateCoverGrammar(this.parseAsyncArgument);if(t.push(e),this.match(")"))break;if(this.expectCommaSeparator(),this.match(")"))break}return this.expect(")"),t},t.prototype.parseLeftHandSideExpressionAllowCall=function(){var t=this.lookahead,e=this.matchContextualKeyword("async"),n=this.context.allowIn;this.context.allowIn=!0;var r;for(this.matchKeyword("super")&&this.context.inFunctionBody?(r=this.createNode(),this.nextToken(),r=this.finalize(r,new o.Super),this.match("(")||this.match(".")||this.match("[")||this.throwUnexpectedToken(this.lookahead)):r=this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match(".")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect(".");var i=this.parseIdentifierName();r=this.finalize(this.startNode(t),new o.StaticMemberExpression(r,i))}else if(this.match("(")){var s=e&&t.lineNumber===this.lookahead.lineNumber;this.context.isBindingElement=!1,this.context.isAssignmentTarget=!1;var a=s?this.parseAsyncArguments():this.parseArguments();if(r=this.finalize(this.startNode(t),new o.CallExpression(r,a)),s&&this.match("=>")){for(var u=0;u<a.length;++u)this.reinterpretExpressionAsPattern(a[u]);r={type:"ArrowParameterPlaceHolder",params:a,async:!0}}}else if(this.match("[")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("[");var i=this.isolateCoverGrammar(this.parseExpression);this.expect("]"),r=this.finalize(this.startNode(t),new o.ComputedMemberExpression(r,i))}else{if(10!==this.lookahead.type||!this.lookahead.head)break;var c=this.parseTemplateLiteral();r=this.finalize(this.startNode(t),new o.TaggedTemplateExpression(r,c))}return this.context.allowIn=n,r},t.prototype.parseSuper=function(){var t=this.createNode();return this.expectKeyword("super"),this.match("[")||this.match(".")||this.throwUnexpectedToken(this.lookahead),this.finalize(t,new o.Super)},t.prototype.parseLeftHandSideExpression=function(){r.assert(this.context.allowIn,"callee of new expression always allow in keyword.");for(var t=this.startNode(this.lookahead),e=this.matchKeyword("super")&&this.context.inFunctionBody?this.parseSuper():this.inheritCoverGrammar(this.matchKeyword("new")?this.parseNewExpression:this.parsePrimaryExpression);;)if(this.match("[")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect("[");var n=this.isolateCoverGrammar(this.parseExpression);this.expect("]"),e=this.finalize(t,new o.ComputedMemberExpression(e,n))}else if(this.match(".")){this.context.isBindingElement=!1,this.context.isAssignmentTarget=!0,this.expect(".");var n=this.parseIdentifierName();e=this.finalize(t,new o.StaticMemberExpression(e,n))}else{if(10!==this.lookahead.type||!this.lookahead.head)break;var i=this.parseTemplateLiteral();e=this.finalize(t,new o.TaggedTemplateExpression(e,i))}return e},t.prototype.parseUpdateExpression=function(){var t,e=this.lookahead;if(this.match("++")||this.match("--")){var n=this.startNode(e),r=this.nextToken();t=this.inheritCoverGrammar(this.parseUnaryExpression),this.context.strict&&t.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(t.name)&&this.tolerateError(s.Messages.StrictLHSPrefix),this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment);var i=!0;t=this.finalize(n,new o.UpdateExpression(r.value,t,i)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else if(t=this.inheritCoverGrammar(this.parseLeftHandSideExpressionAllowCall),!this.hasLineTerminator&&7===this.lookahead.type&&(this.match("++")||this.match("--"))){this.context.strict&&t.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(t.name)&&this.tolerateError(s.Messages.StrictLHSPostfix),this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var a=this.nextToken().value,i=!1;t=this.finalize(this.startNode(e),new o.UpdateExpression(a,t,i))}return t},t.prototype.parseAwaitExpression=function(){var t=this.createNode();this.nextToken();var e=this.parseUnaryExpression();return this.finalize(t,new o.AwaitExpression(e))},t.prototype.parseUnaryExpression=function(){var t;if(this.match("+")||this.match("-")||this.match("~")||this.match("!")||this.matchKeyword("delete")||this.matchKeyword("void")||this.matchKeyword("typeof")){var e=this.startNode(this.lookahead),n=this.nextToken();t=this.inheritCoverGrammar(this.parseUnaryExpression),t=this.finalize(e,new o.UnaryExpression(n.value,t)),this.context.strict&&"delete"===t.operator&&t.argument.type===u.Syntax.Identifier&&this.tolerateError(s.Messages.StrictDelete),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}else t=this.context.await&&this.matchContextualKeyword("await")?this.parseAwaitExpression():this.parseUpdateExpression();return t},t.prototype.parseExponentiationExpression=function(){var t=this.lookahead,e=this.inheritCoverGrammar(this.parseUnaryExpression);if(e.type!==u.Syntax.UnaryExpression&&this.match("**")){this.nextToken(),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var n=e,r=this.isolateCoverGrammar(this.parseExponentiationExpression);e=this.finalize(this.startNode(t),new o.BinaryExpression("**",n,r))}return e},t.prototype.binaryPrecedence=function(t){var e=t.value;return 7===t.type?this.operatorPrecedence[e]||0:4===t.type&&("instanceof"===e||this.context.allowIn&&"in"===e)?7:0},t.prototype.parseBinaryExpression=function(){var t=this.lookahead,e=this.inheritCoverGrammar(this.parseExponentiationExpression),n=this.lookahead,r=this.binaryPrecedence(n);if(r>0){this.nextToken(),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;for(var i=[t,this.lookahead],s=e,a=this.isolateCoverGrammar(this.parseExponentiationExpression),u=[s,n.value,a],c=[r];;){if((r=this.binaryPrecedence(this.lookahead))<=0)break;for(;u.length>2&&r<=c[c.length-1];){a=u.pop();var h=u.pop();c.pop(),s=u.pop(),i.pop();var l=this.startNode(i[i.length-1]);u.push(this.finalize(l,new o.BinaryExpression(h,s,a)))}u.push(this.nextToken().value),c.push(r),i.push(this.lookahead),u.push(this.isolateCoverGrammar(this.parseExponentiationExpression))}var p=u.length-1;for(e=u[p],i.pop();p>1;){var l=this.startNode(i.pop()),h=u[p-1];e=this.finalize(l,new o.BinaryExpression(h,u[p-2],e)),p-=2}}return e},t.prototype.parseConditionalExpression=function(){var t=this.lookahead,e=this.inheritCoverGrammar(this.parseBinaryExpression);if(this.match("?")){this.nextToken();var n=this.context.allowIn;this.context.allowIn=!0;var r=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowIn=n,this.expect(":");var i=this.isolateCoverGrammar(this.parseAssignmentExpression);e=this.finalize(this.startNode(t),new o.ConditionalExpression(e,r,i)),this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1}return e},t.prototype.checkPatternParam=function(t,e){switch(e.type){case u.Syntax.Identifier:this.validateParam(t,e,e.name);break;case u.Syntax.RestElement:this.checkPatternParam(t,e.argument);break;case u.Syntax.AssignmentPattern:this.checkPatternParam(t,e.left);break;case u.Syntax.ArrayPattern:for(var n=0;n<e.elements.length;n++)null!==e.elements[n]&&this.checkPatternParam(t,e.elements[n]);break;case u.Syntax.ObjectPattern:for(var n=0;n<e.properties.length;n++)this.checkPatternParam(t,e.properties[n].value)}t.simple=t.simple&&e instanceof o.Identifier},t.prototype.reinterpretAsCoverFormalsList=function(t){var e,n=[t],r=!1;switch(t.type){case u.Syntax.Identifier:break;case"ArrowParameterPlaceHolder":n=t.params,r=t.async;break;default:return null}e={simple:!0,paramSet:{}};for(var i=0;i<n.length;++i){var o=n[i];o.type===u.Syntax.AssignmentPattern?o.right.type===u.Syntax.YieldExpression&&(o.right.argument&&this.throwUnexpectedToken(this.lookahead),o.right.type=u.Syntax.Identifier,o.right.name="yield",delete o.right.argument,delete o.right.delegate):r&&o.type===u.Syntax.Identifier&&"await"===o.name&&this.throwUnexpectedToken(this.lookahead),this.checkPatternParam(e,o),n[i]=o}if(this.context.strict||!this.context.allowYield)for(var i=0;i<n.length;++i){var o=n[i];o.type===u.Syntax.YieldExpression&&this.throwUnexpectedToken(this.lookahead)}if(e.message===s.Messages.StrictParamDupe){var a=this.context.strict?e.stricted:e.firstRestricted;this.throwUnexpectedToken(a,e.message)}return{simple:e.simple,params:n,stricted:e.stricted,firstRestricted:e.firstRestricted,message:e.message}},t.prototype.parseAssignmentExpression=function(){var t;if(!this.context.allowYield&&this.matchKeyword("yield"))t=this.parseYieldExpression();else{var e=this.lookahead,n=e;if(t=this.parseConditionalExpression(),3===n.type&&n.lineNumber===this.lookahead.lineNumber&&"async"===n.value&&(3===this.lookahead.type||this.matchKeyword("yield"))){var r=this.parsePrimaryExpression();this.reinterpretExpressionAsPattern(r),t={type:"ArrowParameterPlaceHolder",params:[r],async:!0}}if("ArrowParameterPlaceHolder"===t.type||this.match("=>")){this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1;var i=t.async,a=this.reinterpretAsCoverFormalsList(t);if(a){this.hasLineTerminator&&this.tolerateUnexpectedToken(this.lookahead),this.context.firstCoverInitializedNameError=null;var c=this.context.strict,h=this.context.allowStrictDirective;this.context.allowStrictDirective=a.simple;var l=this.context.allowYield,p=this.context.await;this.context.allowYield=!0,this.context.await=i;var f=this.startNode(e);this.expect("=>");var d=void 0;if(this.match("{")){var m=this.context.allowIn;this.context.allowIn=!0,d=this.parseFunctionSourceElements(),this.context.allowIn=m}else d=this.isolateCoverGrammar(this.parseAssignmentExpression);var x=d.type!==u.Syntax.BlockStatement;this.context.strict&&a.firstRestricted&&this.throwUnexpectedToken(a.firstRestricted,a.message),this.context.strict&&a.stricted&&this.tolerateUnexpectedToken(a.stricted,a.message),t=i?this.finalize(f,new o.AsyncArrowFunctionExpression(a.params,d,x)):this.finalize(f,new o.ArrowFunctionExpression(a.params,d,x)),this.context.strict=c,this.context.allowStrictDirective=h,this.context.allowYield=l,this.context.await=p}}else if(this.matchAssign()){if(this.context.isAssignmentTarget||this.tolerateError(s.Messages.InvalidLHSInAssignment),this.context.strict&&t.type===u.Syntax.Identifier){var y=t;this.scanner.isRestrictedWord(y.name)&&this.tolerateUnexpectedToken(n,s.Messages.StrictLHSAssignment),this.scanner.isStrictModeReservedWord(y.name)&&this.tolerateUnexpectedToken(n,s.Messages.StrictReservedWord)}this.match("=")?this.reinterpretExpressionAsPattern(t):(this.context.isAssignmentTarget=!1,this.context.isBindingElement=!1),n=this.nextToken();var v=n.value,D=this.isolateCoverGrammar(this.parseAssignmentExpression);t=this.finalize(this.startNode(e),new o.AssignmentExpression(v,t,D)),this.context.firstCoverInitializedNameError=null}}return t},t.prototype.parseExpression=function(){var t=this.lookahead,e=this.isolateCoverGrammar(this.parseAssignmentExpression);if(this.match(",")){var n=[];for(n.push(e);2!==this.lookahead.type&&this.match(",");)this.nextToken(),n.push(this.isolateCoverGrammar(this.parseAssignmentExpression));e=this.finalize(this.startNode(t),new o.SequenceExpression(n))}return e},t.prototype.parseStatementListItem=function(){var t;if(this.context.isAssignmentTarget=!0,this.context.isBindingElement=!0,4===this.lookahead.type)switch(this.lookahead.value){case"export":this.context.isModule||this.tolerateUnexpectedToken(this.lookahead,s.Messages.IllegalExportDeclaration),t=this.parseExportDeclaration();break;case"import":this.context.isModule||this.tolerateUnexpectedToken(this.lookahead,s.Messages.IllegalImportDeclaration),t=this.parseImportDeclaration();break;case"const":t=this.parseLexicalDeclaration({inFor:!1});break;case"function":t=this.parseFunctionDeclaration();break;case"class":t=this.parseClassDeclaration();break;case"let":t=this.isLexicalDeclaration()?this.parseLexicalDeclaration({inFor:!1}):this.parseStatement();break;default:t=this.parseStatement()}else t=this.parseStatement();return t},t.prototype.parseBlock=function(){var t=this.createNode();this.expect("{");for(var e=[];;){if(this.match("}"))break;e.push(this.parseStatementListItem())}return this.expect("}"),this.finalize(t,new o.BlockStatement(e))},t.prototype.parseLexicalBinding=function(t,e){var n=this.createNode(),r=[],i=this.parsePattern(r,t);this.context.strict&&i.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(i.name)&&this.tolerateError(s.Messages.StrictVarName);var a=null;return"const"===t?this.matchKeyword("in")||this.matchContextualKeyword("of")||(this.match("=")?(this.nextToken(),a=this.isolateCoverGrammar(this.parseAssignmentExpression)):this.throwError(s.Messages.DeclarationMissingInitializer,"const")):(!e.inFor&&i.type!==u.Syntax.Identifier||this.match("="))&&(this.expect("="),a=this.isolateCoverGrammar(this.parseAssignmentExpression)),this.finalize(n,new o.VariableDeclarator(i,a))},t.prototype.parseBindingList=function(t,e){for(var n=[this.parseLexicalBinding(t,e)];this.match(",");)this.nextToken(),n.push(this.parseLexicalBinding(t,e));return n},t.prototype.isLexicalDeclaration=function(){var t=this.scanner.saveState();this.scanner.scanComments();var e=this.scanner.lex();return this.scanner.restoreState(t),3===e.type||7===e.type&&"["===e.value||7===e.type&&"{"===e.value||4===e.type&&"let"===e.value||4===e.type&&"yield"===e.value},t.prototype.parseLexicalDeclaration=function(t){var e=this.createNode(),n=this.nextToken().value;r.assert("let"===n||"const"===n,"Lexical declaration must be either let or const");var i=this.parseBindingList(n,t);return this.consumeSemicolon(),this.finalize(e,new o.VariableDeclaration(i,n))},t.prototype.parseBindingRestElement=function(t,e){var n=this.createNode();this.expect("...");var r=this.parsePattern(t,e);return this.finalize(n,new o.RestElement(r))},t.prototype.parseArrayPattern=function(t,e){var n=this.createNode();this.expect("[");for(var r=[];!this.match("]");)if(this.match(","))this.nextToken(),r.push(null);else{if(this.match("...")){r.push(this.parseBindingRestElement(t,e));break}r.push(this.parsePatternWithDefault(t,e)),this.match("]")||this.expect(",")}return this.expect("]"),this.finalize(n,new o.ArrayPattern(r))},t.prototype.parsePropertyPattern=function(t,e){var n,r,i=this.createNode(),s=!1,a=!1;if(3===this.lookahead.type){var u=this.lookahead;n=this.parseVariableIdentifier();var c=this.finalize(i,new o.Identifier(u.value));if(this.match("=")){t.push(u),a=!0,this.nextToken();var h=this.parseAssignmentExpression();r=this.finalize(this.startNode(u),new o.AssignmentPattern(c,h))}else this.match(":")?(this.expect(":"),r=this.parsePatternWithDefault(t,e)):(t.push(u),a=!0,r=c)}else s=this.match("["),n=this.parseObjectPropertyKey(),this.expect(":"),r=this.parsePatternWithDefault(t,e);return this.finalize(i,new o.Property("init",n,s,r,!1,a))},t.prototype.parseObjectPattern=function(t,e){var n=this.createNode(),r=[];for(this.expect("{");!this.match("}");)r.push(this.parsePropertyPattern(t,e)),this.match("}")||this.expect(",");return this.expect("}"),this.finalize(n,new o.ObjectPattern(r))},t.prototype.parsePattern=function(t,e){var n;return this.match("[")?n=this.parseArrayPattern(t,e):this.match("{")?n=this.parseObjectPattern(t,e):(!this.matchKeyword("let")||"const"!==e&&"let"!==e||this.tolerateUnexpectedToken(this.lookahead,s.Messages.LetInLexicalBinding),t.push(this.lookahead),n=this.parseVariableIdentifier(e)),n},t.prototype.parsePatternWithDefault=function(t,e){var n=this.lookahead,r=this.parsePattern(t,e);if(this.match("=")){this.nextToken();var i=this.context.allowYield;this.context.allowYield=!0;var s=this.isolateCoverGrammar(this.parseAssignmentExpression);this.context.allowYield=i,r=this.finalize(this.startNode(n),new o.AssignmentPattern(r,s))}return r},t.prototype.parseVariableIdentifier=function(t){var e=this.createNode(),n=this.nextToken();return 4===n.type&&"yield"===n.value?this.context.strict?this.tolerateUnexpectedToken(n,s.Messages.StrictReservedWord):this.context.allowYield||this.throwUnexpectedToken(n):3!==n.type?this.context.strict&&4===n.type&&this.scanner.isStrictModeReservedWord(n.value)?this.tolerateUnexpectedToken(n,s.Messages.StrictReservedWord):(this.context.strict||"let"!==n.value||"var"!==t)&&this.throwUnexpectedToken(n):(this.context.isModule||this.context.await)&&3===n.type&&"await"===n.value&&this.tolerateUnexpectedToken(n),this.finalize(e,new o.Identifier(n.value))},t.prototype.parseVariableDeclaration=function(t){var e=this.createNode(),n=[],r=this.parsePattern(n,"var");this.context.strict&&r.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(r.name)&&this.tolerateError(s.Messages.StrictVarName);var i=null;return this.match("=")?(this.nextToken(),i=this.isolateCoverGrammar(this.parseAssignmentExpression)):r.type===u.Syntax.Identifier||t.inFor||this.expect("="),this.finalize(e,new o.VariableDeclarator(r,i))},t.prototype.parseVariableDeclarationList=function(t){var e={inFor:t.inFor},n=[];for(n.push(this.parseVariableDeclaration(e));this.match(",");)this.nextToken(),n.push(this.parseVariableDeclaration(e));return n},t.prototype.parseVariableStatement=function(){var t=this.createNode();this.expectKeyword("var");var e=this.parseVariableDeclarationList({inFor:!1});return this.consumeSemicolon(),this.finalize(t,new o.VariableDeclaration(e,"var"))},t.prototype.parseEmptyStatement=function(){var t=this.createNode();return this.expect(";"),this.finalize(t,new o.EmptyStatement)},t.prototype.parseExpressionStatement=function(){var t=this.createNode(),e=this.parseExpression();return this.consumeSemicolon(),this.finalize(t,new o.ExpressionStatement(e))},t.prototype.parseIfClause=function(){return this.context.strict&&this.matchKeyword("function")&&this.tolerateError(s.Messages.StrictFunction),this.parseStatement()},t.prototype.parseIfStatement=function(){var t,e=this.createNode(),n=null;this.expectKeyword("if"),this.expect("(");var r=this.parseExpression();return!this.match(")")&&this.config.tolerant?(this.tolerateUnexpectedToken(this.nextToken()),t=this.finalize(this.createNode(),new o.EmptyStatement)):(this.expect(")"),t=this.parseIfClause(),this.matchKeyword("else")&&(this.nextToken(),n=this.parseIfClause())),this.finalize(e,new o.IfStatement(r,t,n))},t.prototype.parseDoWhileStatement=function(){var t=this.createNode();this.expectKeyword("do");var e=this.context.inIteration;this.context.inIteration=!0;var n=this.parseStatement();this.context.inIteration=e,this.expectKeyword("while"),this.expect("(");var r=this.parseExpression();return!this.match(")")&&this.config.tolerant?this.tolerateUnexpectedToken(this.nextToken()):(this.expect(")"),this.match(";")&&this.nextToken()),this.finalize(t,new o.DoWhileStatement(n,r))},t.prototype.parseWhileStatement=function(){var t,e=this.createNode();this.expectKeyword("while"),this.expect("(");var n=this.parseExpression();if(!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),t=this.finalize(this.createNode(),new o.EmptyStatement);else{this.expect(")");var r=this.context.inIteration;this.context.inIteration=!0,t=this.parseStatement(),this.context.inIteration=r}return this.finalize(e,new o.WhileStatement(n,t))},t.prototype.parseForStatement=function(){var t,e,n=null,r=null,i=null,a=!0,c=this.createNode();if(this.expectKeyword("for"),this.expect("("),this.match(";"))this.nextToken();else if(this.matchKeyword("var")){n=this.createNode(),this.nextToken();var h=this.context.allowIn;this.context.allowIn=!1;var l=this.parseVariableDeclarationList({inFor:!0});if(this.context.allowIn=h,1===l.length&&this.matchKeyword("in")){var p=l[0];p.init&&(p.id.type===u.Syntax.ArrayPattern||p.id.type===u.Syntax.ObjectPattern||this.context.strict)&&this.tolerateError(s.Messages.ForInOfLoopInitializer,"for-in"),n=this.finalize(n,new o.VariableDeclaration(l,"var")),this.nextToken(),t=n,e=this.parseExpression(),n=null}else 1===l.length&&null===l[0].init&&this.matchContextualKeyword("of")?(n=this.finalize(n,new o.VariableDeclaration(l,"var")),this.nextToken(),t=n,e=this.parseAssignmentExpression(),n=null,a=!1):(n=this.finalize(n,new o.VariableDeclaration(l,"var")),this.expect(";"))}else if(this.matchKeyword("const")||this.matchKeyword("let")){n=this.createNode();var f=this.nextToken().value;if(this.context.strict||"in"!==this.lookahead.value){var h=this.context.allowIn;this.context.allowIn=!1;var l=this.parseBindingList(f,{inFor:!0});this.context.allowIn=h,1===l.length&&null===l[0].init&&this.matchKeyword("in")?(n=this.finalize(n,new o.VariableDeclaration(l,f)),this.nextToken(),t=n,e=this.parseExpression(),n=null):1===l.length&&null===l[0].init&&this.matchContextualKeyword("of")?(n=this.finalize(n,new o.VariableDeclaration(l,f)),this.nextToken(),t=n,e=this.parseAssignmentExpression(),n=null,a=!1):(this.consumeSemicolon(),n=this.finalize(n,new o.VariableDeclaration(l,f)))}else n=this.finalize(n,new o.Identifier(f)),this.nextToken(),t=n,e=this.parseExpression(),n=null}else{var d=this.lookahead,h=this.context.allowIn;if(this.context.allowIn=!1,n=this.inheritCoverGrammar(this.parseAssignmentExpression),this.context.allowIn=h,this.matchKeyword("in"))this.context.isAssignmentTarget&&n.type!==u.Syntax.AssignmentExpression||this.tolerateError(s.Messages.InvalidLHSInForIn),this.nextToken(),this.reinterpretExpressionAsPattern(n),t=n,e=this.parseExpression(),n=null;else if(this.matchContextualKeyword("of"))this.context.isAssignmentTarget&&n.type!==u.Syntax.AssignmentExpression||this.tolerateError(s.Messages.InvalidLHSInForLoop),this.nextToken(),this.reinterpretExpressionAsPattern(n),t=n,e=this.parseAssignmentExpression(),n=null,a=!1;else{if(this.match(",")){for(var m=[n];this.match(",");)this.nextToken(),m.push(this.isolateCoverGrammar(this.parseAssignmentExpression));n=this.finalize(this.startNode(d),new o.SequenceExpression(m))}this.expect(";")}}void 0===t&&(this.match(";")||(r=this.parseExpression()),this.expect(";"),this.match(")")||(i=this.parseExpression()));var x;if(!this.match(")")&&this.config.tolerant)this.tolerateUnexpectedToken(this.nextToken()),x=this.finalize(this.createNode(),new o.EmptyStatement);else{this.expect(")");var y=this.context.inIteration;this.context.inIteration=!0,x=this.isolateCoverGrammar(this.parseStatement),this.context.inIteration=y}return void 0===t?this.finalize(c,new o.ForStatement(n,r,i,x)):a?this.finalize(c,new o.ForInStatement(t,e,x)):this.finalize(c,new o.ForOfStatement(t,e,x))},t.prototype.parseContinueStatement=function(){var t=this.createNode();this.expectKeyword("continue");var e=null;if(3===this.lookahead.type&&!this.hasLineTerminator){var n=this.parseVariableIdentifier();e=n;var r="$"+n.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,r)||this.throwError(s.Messages.UnknownLabel,n.name)}return this.consumeSemicolon(),null!==e||this.context.inIteration||this.throwError(s.Messages.IllegalContinue),this.finalize(t,new o.ContinueStatement(e))},t.prototype.parseBreakStatement=function(){var t=this.createNode();this.expectKeyword("break");var e=null;if(3===this.lookahead.type&&!this.hasLineTerminator){var n=this.parseVariableIdentifier(),r="$"+n.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,r)||this.throwError(s.Messages.UnknownLabel,n.name),e=n}return this.consumeSemicolon(),null!==e||this.context.inIteration||this.context.inSwitch||this.throwError(s.Messages.IllegalBreak),this.finalize(t,new o.BreakStatement(e))},t.prototype.parseReturnStatement=function(){this.context.inFunctionBody||this.tolerateError(s.Messages.IllegalReturn);var t=this.createNode();this.expectKeyword("return");var e=!this.match(";")&&!this.match("}")&&!this.hasLineTerminator&&2!==this.lookahead.type,n=e?this.parseExpression():null;return this.consumeSemicolon(),this.finalize(t,new o.ReturnStatement(n))},t.prototype.parseWithStatement=function(){this.context.strict&&this.tolerateError(s.Messages.StrictModeWith);var t,e=this.createNode();this.expectKeyword("with"),this.expect("(");var n=this.parseExpression();return!this.match(")")&&this.config.tolerant?(this.tolerateUnexpectedToken(this.nextToken()),t=this.finalize(this.createNode(),new o.EmptyStatement)):(this.expect(")"),t=this.parseStatement()),this.finalize(e,new o.WithStatement(n,t))},t.prototype.parseSwitchCase=function(){var t,e=this.createNode();this.matchKeyword("default")?(this.nextToken(),t=null):(this.expectKeyword("case"),t=this.parseExpression()),this.expect(":");for(var n=[];;){if(this.match("}")||this.matchKeyword("default")||this.matchKeyword("case"))break;n.push(this.parseStatementListItem())}return this.finalize(e,new o.SwitchCase(t,n))},t.prototype.parseSwitchStatement=function(){var t=this.createNode();this.expectKeyword("switch"),this.expect("(");var e=this.parseExpression();this.expect(")");var n=this.context.inSwitch;this.context.inSwitch=!0;var r=[],i=!1;for(this.expect("{");;){if(this.match("}"))break;var a=this.parseSwitchCase();null===a.test&&(i&&this.throwError(s.Messages.MultipleDefaultsInSwitch),i=!0),r.push(a)}return this.expect("}"),this.context.inSwitch=n,this.finalize(t,new o.SwitchStatement(e,r))},t.prototype.parseLabelledStatement=function(){var t,e=this.createNode(),n=this.parseExpression();if(n.type===u.Syntax.Identifier&&this.match(":")){this.nextToken();var r=n,i="$"+r.name;Object.prototype.hasOwnProperty.call(this.context.labelSet,i)&&this.throwError(s.Messages.Redeclaration,"Label",r.name),this.context.labelSet[i]=!0;var a=void 0;if(this.matchKeyword("class"))this.tolerateUnexpectedToken(this.lookahead),a=this.parseClassDeclaration();else if(this.matchKeyword("function")){var c=this.lookahead,h=this.parseFunctionDeclaration();this.context.strict?this.tolerateUnexpectedToken(c,s.Messages.StrictFunction):h.generator&&this.tolerateUnexpectedToken(c,s.Messages.GeneratorInLegacyContext),a=h}else a=this.parseStatement();delete this.context.labelSet[i],t=new o.LabeledStatement(r,a)}else this.consumeSemicolon(),t=new o.ExpressionStatement(n);return this.finalize(e,t)},t.prototype.parseThrowStatement=function(){var t=this.createNode();this.expectKeyword("throw"),this.hasLineTerminator&&this.throwError(s.Messages.NewlineAfterThrow);var e=this.parseExpression();return this.consumeSemicolon(),this.finalize(t,new o.ThrowStatement(e))},t.prototype.parseCatchClause=function(){var t=this.createNode();this.expectKeyword("catch"),this.expect("("),this.match(")")&&this.throwUnexpectedToken(this.lookahead);for(var e=[],n=this.parsePattern(e),r={},i=0;i<e.length;i++){var a="$"+e[i].value;Object.prototype.hasOwnProperty.call(r,a)&&this.tolerateError(s.Messages.DuplicateBinding,e[i].value),r[a]=!0}this.context.strict&&n.type===u.Syntax.Identifier&&this.scanner.isRestrictedWord(n.name)&&this.tolerateError(s.Messages.StrictCatchVariable),this.expect(")");var c=this.parseBlock();return this.finalize(t,new o.CatchClause(n,c))},t.prototype.parseFinallyClause=function(){return this.expectKeyword("finally"),this.parseBlock()},t.prototype.parseTryStatement=function(){var t=this.createNode();this.expectKeyword("try");var e=this.parseBlock(),n=this.matchKeyword("catch")?this.parseCatchClause():null,r=this.matchKeyword("finally")?this.parseFinallyClause():null;return n||r||this.throwError(s.Messages.NoCatchOrFinally),this.finalize(t,new o.TryStatement(e,n,r))},t.prototype.parseDebuggerStatement=function(){var t=this.createNode();return this.expectKeyword("debugger"),this.consumeSemicolon(),this.finalize(t,new o.DebuggerStatement)},t.prototype.parseStatement=function(){var t;switch(this.lookahead.type){case 1:case 5:case 6:case 8:case 10:case 9:t=this.parseExpressionStatement();break;case 7:var e=this.lookahead.value;t="{"===e?this.parseBlock():"("===e?this.parseExpressionStatement():";"===e?this.parseEmptyStatement():this.parseExpressionStatement();break;case 3:t=this.matchAsyncFunction()?this.parseFunctionDeclaration():this.parseLabelledStatement();break;case 4:switch(this.lookahead.value){case"break":t=this.parseBreakStatement();break;case"continue":t=this.parseContinueStatement();break;case"debugger":t=this.parseDebuggerStatement();break;case"do":t=this.parseDoWhileStatement();break;case"for":t=this.parseForStatement();break;case"function":t=this.parseFunctionDeclaration();break;case"if":t=this.parseIfStatement();break;case"return":t=this.parseReturnStatement();break;case"switch":t=this.parseSwitchStatement();break;case"throw":t=this.parseThrowStatement();break;case"try":t=this.parseTryStatement();break;case"var":t=this.parseVariableStatement();break;case"while":t=this.parseWhileStatement();break;case"with":t=this.parseWithStatement();break;default:t=this.parseExpressionStatement()}break;default:t=this.throwUnexpectedToken(this.lookahead)}return t},t.prototype.parseFunctionSourceElements=function(){var t=this.createNode();this.expect("{");var e=this.parseDirectivePrologues(),n=this.context.labelSet,r=this.context.inIteration,i=this.context.inSwitch,s=this.context.inFunctionBody;for(this.context.labelSet={},this.context.inIteration=!1,this.context.inSwitch=!1,this.context.inFunctionBody=!0;2!==this.lookahead.type&&!this.match("}");)e.push(this.parseStatementListItem());return this.expect("}"),this.context.labelSet=n,this.context.inIteration=r,this.context.inSwitch=i,this.context.inFunctionBody=s,this.finalize(t,new o.BlockStatement(e))},t.prototype.validateParam=function(t,e,n){var r="$"+n;this.context.strict?(this.scanner.isRestrictedWord(n)&&(t.stricted=e,t.message=s.Messages.StrictParamName),Object.prototype.hasOwnProperty.call(t.paramSet,r)&&(t.stricted=e,t.message=s.Messages.StrictParamDupe)):t.firstRestricted||(this.scanner.isRestrictedWord(n)?(t.firstRestricted=e,t.message=s.Messages.StrictParamName):this.scanner.isStrictModeReservedWord(n)?(t.firstRestricted=e,t.message=s.Messages.StrictReservedWord):Object.prototype.hasOwnProperty.call(t.paramSet,r)&&(t.stricted=e,t.message=s.Messages.StrictParamDupe)),"function"==typeof Object.defineProperty?Object.defineProperty(t.paramSet,r,{value:!0,enumerable:!0,writable:!0,configurable:!0}):t.paramSet[r]=!0},t.prototype.parseRestElement=function(t){var e=this.createNode();this.expect("...");var n=this.parsePattern(t);return this.match("=")&&this.throwError(s.Messages.DefaultRestParameter),this.match(")")||this.throwError(s.Messages.ParameterAfterRestParameter),this.finalize(e,new o.RestElement(n))},t.prototype.parseFormalParameter=function(t){for(var e=[],n=this.match("...")?this.parseRestElement(e):this.parsePatternWithDefault(e),r=0;r<e.length;r++)this.validateParam(t,e[r],e[r].value);t.simple=t.simple&&n instanceof o.Identifier,t.params.push(n)},t.prototype.parseFormalParameters=function(t){var e;if(e={simple:!0,params:[],firstRestricted:t},this.expect("("),!this.match(")"))for(e.paramSet={};2!==this.lookahead.type&&(this.parseFormalParameter(e),!this.match(")"))&&(this.expect(","),!this.match(")")););return this.expect(")"),{simple:e.simple,params:e.params,stricted:e.stricted,firstRestricted:e.firstRestricted,message:e.message}},t.prototype.matchAsyncFunction=function(){var t=this.matchContextualKeyword("async");if(t){var e=this.scanner.saveState();this.scanner.scanComments();var n=this.scanner.lex();this.scanner.restoreState(e),t=e.lineNumber===n.lineNumber&&4===n.type&&"function"===n.value}return t},t.prototype.parseFunctionDeclaration=function(t){var e=this.createNode(),n=this.matchContextualKeyword("async");n&&this.nextToken(),this.expectKeyword("function");var r=!n&&this.match("*");r&&this.nextToken();var i,a=null,u=null;if(!t||!this.match("(")){var c=this.lookahead;a=this.parseVariableIdentifier(),this.context.strict?this.scanner.isRestrictedWord(c.value)&&this.tolerateUnexpectedToken(c,s.Messages.StrictFunctionName):this.scanner.isRestrictedWord(c.value)?(u=c,i=s.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(c.value)&&(u=c,i=s.Messages.StrictReservedWord)}var h=this.context.await,l=this.context.allowYield;this.context.await=n,this.context.allowYield=!r;var p=this.parseFormalParameters(u),f=p.params,d=p.stricted;u=p.firstRestricted,p.message&&(i=p.message);var m=this.context.strict,x=this.context.allowStrictDirective;this.context.allowStrictDirective=p.simple;var y=this.parseFunctionSourceElements();return this.context.strict&&u&&this.throwUnexpectedToken(u,i),this.context.strict&&d&&this.tolerateUnexpectedToken(d,i),this.context.strict=m,this.context.allowStrictDirective=x,this.context.await=h,this.context.allowYield=l,n?this.finalize(e,new o.AsyncFunctionDeclaration(a,f,y)):this.finalize(e,new o.FunctionDeclaration(a,f,y,r))},t.prototype.parseFunctionExpression=function(){var t=this.createNode(),e=this.matchContextualKeyword("async");e&&this.nextToken(),this.expectKeyword("function");var n=!e&&this.match("*");n&&this.nextToken();var r,i,a=null,u=this.context.await,c=this.context.allowYield;if(this.context.await=e,this.context.allowYield=!n,!this.match("(")){var h=this.lookahead;a=this.context.strict||n||!this.matchKeyword("yield")?this.parseVariableIdentifier():this.parseIdentifierName(),this.context.strict?this.scanner.isRestrictedWord(h.value)&&this.tolerateUnexpectedToken(h,s.Messages.StrictFunctionName):this.scanner.isRestrictedWord(h.value)?(i=h,r=s.Messages.StrictFunctionName):this.scanner.isStrictModeReservedWord(h.value)&&(i=h,r=s.Messages.StrictReservedWord)}var l=this.parseFormalParameters(i),p=l.params,f=l.stricted;i=l.firstRestricted,l.message&&(r=l.message);var d=this.context.strict,m=this.context.allowStrictDirective;this.context.allowStrictDirective=l.simple;var x=this.parseFunctionSourceElements();return this.context.strict&&i&&this.throwUnexpectedToken(i,r),this.context.strict&&f&&this.tolerateUnexpectedToken(f,r),this.context.strict=d,this.context.allowStrictDirective=m,this.context.await=u,this.context.allowYield=c,e?this.finalize(t,new o.AsyncFunctionExpression(a,p,x)):this.finalize(t,new o.FunctionExpression(a,p,x,n))},t.prototype.parseDirective=function(){var t=this.lookahead,e=this.createNode(),n=this.parseExpression(),r=n.type===u.Syntax.Literal?this.getTokenRaw(t).slice(1,-1):null;return this.consumeSemicolon(),this.finalize(e,r?new o.Directive(n,r):new o.ExpressionStatement(n))},t.prototype.parseDirectivePrologues=function(){for(var t=null,e=[];;){var n=this.lookahead;if(8!==n.type)break;var r=this.parseDirective();e.push(r);var i=r.directive;if("string"!=typeof i)break;"use strict"===i?(this.context.strict=!0,t&&this.tolerateUnexpectedToken(t,s.Messages.StrictOctalLiteral),this.context.allowStrictDirective||this.tolerateUnexpectedToken(n,s.Messages.IllegalLanguageModeDirective)):!t&&n.octal&&(t=n)}return e},t.prototype.qualifiedPropertyName=function(t){switch(t.type){case 3:case 8:case 1:case 5:case 6:case 4:return!0;case 7:return"["===t.value}return!1},t.prototype.parseGetterMethod=function(){var t=this.createNode(),e=this.context.allowYield;this.context.allowYield=!1;var n=this.parseFormalParameters();n.params.length>0&&this.tolerateError(s.Messages.BadGetterArity);var r=this.parsePropertyMethod(n);return this.context.allowYield=e,this.finalize(t,new o.FunctionExpression(null,n.params,r,!1))},t.prototype.parseSetterMethod=function(){var t=this.createNode(),e=this.context.allowYield;this.context.allowYield=!1;var n=this.parseFormalParameters();1!==n.params.length?this.tolerateError(s.Messages.BadSetterArity):n.params[0]instanceof o.RestElement&&this.tolerateError(s.Messages.BadSetterRestParameter);var r=this.parsePropertyMethod(n);return this.context.allowYield=e,this.finalize(t,new o.FunctionExpression(null,n.params,r,!1))},t.prototype.parseGeneratorMethod=function(){var t=this.createNode(),e=this.context.allowYield;this.context.allowYield=!0;var n=this.parseFormalParameters();this.context.allowYield=!1;var r=this.parsePropertyMethod(n);return this.context.allowYield=e,this.finalize(t,new o.FunctionExpression(null,n.params,r,!0))},t.prototype.isStartOfExpression=function(){var t=!0,e=this.lookahead.value;switch(this.lookahead.type){case 7:t="["===e||"("===e||"{"===e||"+"===e||"-"===e||"!"===e||"~"===e||"++"===e||"--"===e||"/"===e||"/="===e;break;case 4:t="class"===e||"delete"===e||"function"===e||"let"===e||"new"===e||"super"===e||"this"===e||"typeof"===e||"void"===e||"yield"===e}return t},t.prototype.parseYieldExpression=function(){var t=this.createNode();this.expectKeyword("yield");var e=null,n=!1;if(!this.hasLineTerminator){var r=this.context.allowYield;this.context.allowYield=!1,n=this.match("*"),n?(this.nextToken(),e=this.parseAssignmentExpression()):this.isStartOfExpression()&&(e=this.parseAssignmentExpression()),this.context.allowYield=r}return this.finalize(t,new o.YieldExpression(e,n))},t.prototype.parseClassElement=function(t){var e=this.lookahead,n=this.createNode(),r="",i=null,a=null,u=!1,c=!1,h=!1,l=!1;if(this.match("*"))this.nextToken();else{u=this.match("["),i=this.parseObjectPropertyKey();if("static"===i.name&&(this.qualifiedPropertyName(this.lookahead)||this.match("*"))&&(e=this.lookahead,h=!0,u=this.match("["),this.match("*")?this.nextToken():i=this.parseObjectPropertyKey()),3===e.type&&!this.hasLineTerminator&&"async"===e.value){var p=this.lookahead.value;":"!==p&&"("!==p&&"*"!==p&&(l=!0,e=this.lookahead,i=this.parseObjectPropertyKey(),3===e.type&&("get"===e.value||"set"===e.value?this.tolerateUnexpectedToken(e):"constructor"===e.value&&this.tolerateUnexpectedToken(e,s.Messages.ConstructorIsAsync)))}}var f=this.qualifiedPropertyName(this.lookahead);return 3===e.type?"get"===e.value&&f?(r="get",u=this.match("["),i=this.parseObjectPropertyKey(),this.context.allowYield=!1,a=this.parseGetterMethod()):"set"===e.value&&f&&(r="set",u=this.match("["),i=this.parseObjectPropertyKey(),a=this.parseSetterMethod()):7===e.type&&"*"===e.value&&f&&(r="init",u=this.match("["),i=this.parseObjectPropertyKey(),a=this.parseGeneratorMethod(),c=!0),!r&&i&&this.match("(")&&(r="init",a=l?this.parsePropertyMethodAsyncFunction():this.parsePropertyMethodFunction(),c=!0),r||this.throwUnexpectedToken(this.lookahead),"init"===r&&(r="method"),u||(h&&this.isPropertyKey(i,"prototype")&&this.throwUnexpectedToken(e,s.Messages.StaticPrototype),!h&&this.isPropertyKey(i,"constructor")&&(("method"!==r||!c||a&&a.generator)&&this.throwUnexpectedToken(e,s.Messages.ConstructorSpecialMethod),t.value?this.throwUnexpectedToken(e,s.Messages.DuplicateConstructor):t.value=!0,r="constructor")),this.finalize(n,new o.MethodDefinition(i,u,a,r,h))},t.prototype.parseClassElementList=function(){var t=[],e={value:!1};for(this.expect("{");!this.match("}");)this.match(";")?this.nextToken():t.push(this.parseClassElement(e));return this.expect("}"),t},t.prototype.parseClassBody=function(){var t=this.createNode(),e=this.parseClassElementList();return this.finalize(t,new o.ClassBody(e))},t.prototype.parseClassDeclaration=function(t){var e=this.createNode(),n=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var r=t&&3!==this.lookahead.type?null:this.parseVariableIdentifier(),i=null;this.matchKeyword("extends")&&(this.nextToken(),i=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var s=this.parseClassBody();return this.context.strict=n,this.finalize(e,new o.ClassDeclaration(r,i,s))},t.prototype.parseClassExpression=function(){var t=this.createNode(),e=this.context.strict;this.context.strict=!0,this.expectKeyword("class");var n=3===this.lookahead.type?this.parseVariableIdentifier():null,r=null;this.matchKeyword("extends")&&(this.nextToken(),r=this.isolateCoverGrammar(this.parseLeftHandSideExpressionAllowCall));var i=this.parseClassBody();return this.context.strict=e,this.finalize(t,new o.ClassExpression(n,r,i))},t.prototype.parseModule=function(){this.context.strict=!0,this.context.isModule=!0;for(var t=this.createNode(),e=this.parseDirectivePrologues();2!==this.lookahead.type;)e.push(this.parseStatementListItem());return this.finalize(t,new o.Module(e))},t.prototype.parseScript=function(){for(var t=this.createNode(),e=this.parseDirectivePrologues();2!==this.lookahead.type;)e.push(this.parseStatementListItem());return this.finalize(t,new o.Script(e))},t.prototype.parseModuleSpecifier=function(){var t=this.createNode();8!==this.lookahead.type&&this.throwError(s.Messages.InvalidModuleSpecifier);var e=this.nextToken(),n=this.getTokenRaw(e);return this.finalize(t,new o.Literal(e.value,n))},t.prototype.parseImportSpecifier=function(){var t,e,n=this.createNode();return 3===this.lookahead.type?(t=this.parseVariableIdentifier(),e=t,this.matchContextualKeyword("as")&&(this.nextToken(),e=this.parseVariableIdentifier())):(t=this.parseIdentifierName(),e=t,this.matchContextualKeyword("as")?(this.nextToken(),e=this.parseVariableIdentifier()):this.throwUnexpectedToken(this.nextToken())),this.finalize(n,new o.ImportSpecifier(e,t))},t.prototype.parseNamedImports=function(){this.expect("{");for(var t=[];!this.match("}");)t.push(this.parseImportSpecifier()),this.match("}")||this.expect(",");return this.expect("}"),t},t.prototype.parseImportDefaultSpecifier=function(){var t=this.createNode(),e=this.parseIdentifierName();return this.finalize(t,new o.ImportDefaultSpecifier(e))},t.prototype.parseImportNamespaceSpecifier=function(){var t=this.createNode();this.expect("*"),this.matchContextualKeyword("as")||this.throwError(s.Messages.NoAsAfterImportNamespace),this.nextToken();var e=this.parseIdentifierName();return this.finalize(t,new o.ImportNamespaceSpecifier(e))},t.prototype.parseImportDeclaration=function(){this.context.inFunctionBody&&this.throwError(s.Messages.IllegalImportDeclaration);var t=this.createNode();this.expectKeyword("import");var e,n=[];if(8===this.lookahead.type)e=this.parseModuleSpecifier();else{if(this.match("{")?n=n.concat(this.parseNamedImports()):this.match("*")?n.push(this.parseImportNamespaceSpecifier()):this.isIdentifierName(this.lookahead)&&!this.matchKeyword("default")?(n.push(this.parseImportDefaultSpecifier()),this.match(",")&&(this.nextToken(),this.match("*")?n.push(this.parseImportNamespaceSpecifier()):this.match("{")?n=n.concat(this.parseNamedImports()):this.throwUnexpectedToken(this.lookahead))):this.throwUnexpectedToken(this.nextToken()),!this.matchContextualKeyword("from")){var r=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(r,this.lookahead.value)}this.nextToken(),e=this.parseModuleSpecifier()}return this.consumeSemicolon(),this.finalize(t,new o.ImportDeclaration(n,e))},t.prototype.parseExportSpecifier=function(){var t=this.createNode(),e=this.parseIdentifierName(),n=e;return this.matchContextualKeyword("as")&&(this.nextToken(),n=this.parseIdentifierName()),this.finalize(t,new o.ExportSpecifier(e,n))},t.prototype.parseExportDeclaration=function(){this.context.inFunctionBody&&this.throwError(s.Messages.IllegalExportDeclaration);var t=this.createNode();this.expectKeyword("export");var e;if(this.matchKeyword("default"))if(this.nextToken(),this.matchKeyword("function")){var n=this.parseFunctionDeclaration(!0);e=this.finalize(t,new o.ExportDefaultDeclaration(n))}else if(this.matchKeyword("class")){var n=this.parseClassDeclaration(!0);e=this.finalize(t,new o.ExportDefaultDeclaration(n))}else if(this.matchContextualKeyword("async")){var n=this.matchAsyncFunction()?this.parseFunctionDeclaration(!0):this.parseAssignmentExpression();e=this.finalize(t,new o.ExportDefaultDeclaration(n))}else{this.matchContextualKeyword("from")&&this.throwError(s.Messages.UnexpectedToken,this.lookahead.value);var n=this.match("{")?this.parseObjectInitializer():this.match("[")?this.parseArrayInitializer():this.parseAssignmentExpression();this.consumeSemicolon(),e=this.finalize(t,new o.ExportDefaultDeclaration(n))}else if(this.match("*")){if(this.nextToken(),!this.matchContextualKeyword("from")){var r=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(r,this.lookahead.value)}this.nextToken();var i=this.parseModuleSpecifier();this.consumeSemicolon(),e=this.finalize(t,new o.ExportAllDeclaration(i))}else if(4===this.lookahead.type){var n=void 0;switch(this.lookahead.value){case"let":case"const":n=this.parseLexicalDeclaration({inFor:!1});break;case"var":case"class":case"function":n=this.parseStatementListItem();break;default:this.throwUnexpectedToken(this.lookahead)}e=this.finalize(t,new o.ExportNamedDeclaration(n,[],null))}else if(this.matchAsyncFunction()){var n=this.parseFunctionDeclaration();e=this.finalize(t,new o.ExportNamedDeclaration(n,[],null))}else{var a=[],u=null,c=!1;for(this.expect("{");!this.match("}");)c=c||this.matchKeyword("default"),a.push(this.parseExportSpecifier()),this.match("}")||this.expect(",");if(this.expect("}"),this.matchContextualKeyword("from"))this.nextToken(),u=this.parseModuleSpecifier(),this.consumeSemicolon();else if(c){var r=this.lookahead.value?s.Messages.UnexpectedToken:s.Messages.MissingFromClause;this.throwError(r,this.lookahead.value)}else this.consumeSemicolon();e=this.finalize(t,new o.ExportNamedDeclaration(null,a,u))}return e},t}();e.Parser=h},function(t,e){"use strict";function n(t,e){if(!t)throw new Error("ASSERT: "+e)}Object.defineProperty(e,"__esModule",{value:!0}),e.assert=n},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=function(){function t(){this.errors=[],this.tolerant=!1}return t.prototype.recordError=function(t){this.errors.push(t)},t.prototype.tolerate=function(t){if(!this.tolerant)throw t;this.recordError(t)},t.prototype.constructError=function(t,e){var n=new Error(t);try{throw n}catch(t){Object.create&&Object.defineProperty&&(n=Object.create(t),Object.defineProperty(n,"column",{value:e}))}return n},t.prototype.createError=function(t,e,n,r){var i="Line "+e+": "+r,s=this.constructError(i,n);return s.index=t,s.lineNumber=e,s.description=r,s},t.prototype.throwError=function(t,e,n,r){throw this.createError(t,e,n,r)},t.prototype.tolerateError=function(t,e,n,r){var i=this.createError(t,e,n,r);if(!this.tolerant)throw i;this.recordError(i)},t}();e.ErrorHandler=n},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.Messages={BadGetterArity:"Getter must not have any formal parameters",BadSetterArity:"Setter must have exactly one formal parameter",BadSetterRestParameter:"Setter function argument must not be a rest parameter",ConstructorIsAsync:"Class constructor may not be an async method",ConstructorSpecialMethod:"Class constructor may not be an accessor",DeclarationMissingInitializer:"Missing initializer in %0 declaration",DefaultRestParameter:"Unexpected token =",DuplicateBinding:"Duplicate binding %0",DuplicateConstructor:"A class may only have one constructor",DuplicateProtoProperty:"Duplicate __proto__ fields are not allowed in object literals",ForInOfLoopInitializer:"%0 loop variable declaration may not have an initializer",GeneratorInLegacyContext:"Generator declarations are not allowed in legacy contexts",IllegalBreak:"Illegal break statement",IllegalContinue:"Illegal continue statement",IllegalExportDeclaration:"Unexpected token",IllegalImportDeclaration:"Unexpected token",IllegalLanguageModeDirective:"Illegal 'use strict' directive in function with non-simple parameter list",IllegalReturn:"Illegal return statement",InvalidEscapedReservedWord:"Keyword must not contain escaped characters",InvalidHexEscapeSequence:"Invalid hexadecimal escape sequence",InvalidLHSInAssignment:"Invalid left-hand side in assignment",InvalidLHSInForIn:"Invalid left-hand side in for-in",InvalidLHSInForLoop:"Invalid left-hand side in for-loop",InvalidModuleSpecifier:"Unexpected token",InvalidRegExp:"Invalid regular expression",LetInLexicalBinding:"let is disallowed as a lexically bound name",MissingFromClause:"Unexpected token",MultipleDefaultsInSwitch:"More than one default clause in switch statement",NewlineAfterThrow:"Illegal newline after throw",NoAsAfterImportNamespace:"Unexpected token",NoCatchOrFinally:"Missing catch or finally after try",ParameterAfterRestParameter:"Rest parameter must be last formal parameter",Redeclaration:"%0 '%1' has already been declared",StaticPrototype:"Classes may not have static property named prototype",StrictCatchVariable:"Catch variable may not be eval or arguments in strict mode",StrictDelete:"Delete of an unqualified identifier in strict mode.",StrictFunction:"In strict mode code, functions can only be declared at top level or inside a block",StrictFunctionName:"Function name may not be eval or arguments in strict mode",StrictLHSAssignment:"Assignment to eval or arguments is not allowed in strict mode",StrictLHSPostfix:"Postfix increment/decrement may not have eval or arguments operand in strict mode",StrictLHSPrefix:"Prefix increment/decrement may not have eval or arguments operand in strict mode",StrictModeWith:"Strict mode code may not include a with statement",StrictOctalLiteral:"Octal literals are not allowed in strict mode.",StrictParamDupe:"Strict mode function may not have duplicate parameter names",StrictParamName:"Parameter name eval or arguments is not allowed in strict mode",StrictReservedWord:"Use of future reserved word in strict mode",StrictVarName:"Variable name may not be eval or arguments in strict mode",TemplateOctalLiteral:"Octal literals are not allowed in template strings.",UnexpectedEOS:"Unexpected end of input",UnexpectedIdentifier:"Unexpected identifier",UnexpectedNumber:"Unexpected number",UnexpectedReserved:"Unexpected reserved word",UnexpectedString:"Unexpected string",UnexpectedTemplate:"Unexpected quasi %0",UnexpectedToken:"Unexpected token %0",UnexpectedTokenIllegal:"Unexpected token ILLEGAL",UnknownLabel:"Undefined label '%0'",UnterminatedRegExp:"Invalid regular expression: missing /"}},function(t,e,n){"use strict";function r(t){return"**********abcdef".indexOf(t.toLowerCase())}function i(t){return"01234567".indexOf(t)}Object.defineProperty(e,"__esModule",{value:!0});var s=n(9),o=n(4),a=n(11),u=function(){function t(t,e){this.source=t,this.errorHandler=e,this.trackComment=!1,this.length=t.length,this.index=0,this.lineNumber=t.length>0?1:0,this.lineStart=0,this.curlyStack=[]}return t.prototype.saveState=function(){return{index:this.index,lineNumber:this.lineNumber,lineStart:this.lineStart}},t.prototype.restoreState=function(t){this.index=t.index,this.lineNumber=t.lineNumber,this.lineStart=t.lineStart},t.prototype.eof=function(){return this.index>=this.length},t.prototype.throwUnexpectedToken=function(t){return void 0===t&&(t=a.Messages.UnexpectedTokenIllegal),this.errorHandler.throwError(this.index,this.lineNumber,this.index-this.lineStart+1,t)},t.prototype.tolerateUnexpectedToken=function(t){void 0===t&&(t=a.Messages.UnexpectedTokenIllegal),this.errorHandler.tolerateError(this.index,this.lineNumber,this.index-this.lineStart+1,t)},t.prototype.skipSingleLineComment=function(t){var e,n,r=[];for(this.trackComment&&(r=[],e=this.index-t,n={start:{line:this.lineNumber,column:this.index-this.lineStart-t},end:{}});!this.eof();){var i=this.source.charCodeAt(this.index);if(++this.index,o.Character.isLineTerminator(i)){if(this.trackComment){n.end={line:this.lineNumber,column:this.index-this.lineStart-1};var s={multiLine:!1,slice:[e+t,this.index-1],range:[e,this.index-1],loc:n};r.push(s)}return 13===i&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,r}}if(this.trackComment){n.end={line:this.lineNumber,column:this.index-this.lineStart};var s={multiLine:!1,slice:[e+t,this.index],range:[e,this.index],loc:n};r.push(s)}return r},t.prototype.skipMultiLineComment=function(){var t,e,n=[];for(this.trackComment&&(n=[],t=this.index-2,e={start:{line:this.lineNumber,column:this.index-this.lineStart-2},end:{}});!this.eof();){var r=this.source.charCodeAt(this.index);if(o.Character.isLineTerminator(r))13===r&&10===this.source.charCodeAt(this.index+1)&&++this.index,++this.lineNumber,++this.index,this.lineStart=this.index;else if(42===r){if(47===this.source.charCodeAt(this.index+1)){if(this.index+=2,this.trackComment){e.end={line:this.lineNumber,column:this.index-this.lineStart};var i={multiLine:!0,slice:[t+2,this.index-2],range:[t,this.index],loc:e};n.push(i)}return n}++this.index}else++this.index}if(this.trackComment){e.end={line:this.lineNumber,column:this.index-this.lineStart};var i={multiLine:!0,slice:[t+2,this.index],range:[t,this.index],loc:e};n.push(i)}return this.tolerateUnexpectedToken(),n},t.prototype.scanComments=function(){var t;this.trackComment&&(t=[]);for(var e=0===this.index;!this.eof();){var n=this.source.charCodeAt(this.index);if(o.Character.isWhiteSpace(n))++this.index;else if(o.Character.isLineTerminator(n))++this.index,13===n&&10===this.source.charCodeAt(this.index)&&++this.index,++this.lineNumber,this.lineStart=this.index,e=!0;else if(47===n)if(47===(n=this.source.charCodeAt(this.index+1))){this.index+=2;var r=this.skipSingleLineComment(2);this.trackComment&&(t=t.concat(r)),e=!0}else{if(42!==n)break;this.index+=2;var r=this.skipMultiLineComment();this.trackComment&&(t=t.concat(r))}else if(e&&45===n){if(45!==this.source.charCodeAt(this.index+1)||62!==this.source.charCodeAt(this.index+2))break;this.index+=3;var r=this.skipSingleLineComment(3);this.trackComment&&(t=t.concat(r))}else{if(60!==n)break;if("!--"!==this.source.slice(this.index+1,this.index+4))break;this.index+=4;var r=this.skipSingleLineComment(4);this.trackComment&&(t=t.concat(r))}}return t},t.prototype.isFutureReservedWord=function(t){switch(t){case"enum":case"export":case"import":case"super":return!0;default:return!1}},t.prototype.isStrictModeReservedWord=function(t){switch(t){case"implements":case"interface":case"package":case"private":case"protected":case"public":case"static":case"yield":case"let":return!0;default:return!1}},t.prototype.isRestrictedWord=function(t){return"eval"===t||"arguments"===t},t.prototype.isKeyword=function(t){switch(t.length){case 2:return"if"===t||"in"===t||"do"===t;case 3:return"var"===t||"for"===t||"new"===t||"try"===t||"let"===t;case 4:return"this"===t||"else"===t||"case"===t||"void"===t||"with"===t||"enum"===t;case 5:return"while"===t||"break"===t||"catch"===t||"throw"===t||"const"===t||"yield"===t||"class"===t||"super"===t;case 6:return"return"===t||"typeof"===t||"delete"===t||"switch"===t||"export"===t||"import"===t;case 7:return"default"===t||"finally"===t||"extends"===t;case 8:return"function"===t||"continue"===t||"debugger"===t;case 10:return"instanceof"===t;default:return!1}},t.prototype.codePointAt=function(t){var e=this.source.charCodeAt(t);if(e>=55296&&e<=56319){var n=this.source.charCodeAt(t+1);if(n>=56320&&n<=57343){e=1024*(e-55296)+n-56320+65536}}return e},t.prototype.scanHexEscape=function(t){for(var e="u"===t?4:2,n=0,i=0;i<e;++i){if(this.eof()||!o.Character.isHexDigit(this.source.charCodeAt(this.index)))return null;n=16*n+r(this.source[this.index++])}return String.fromCharCode(n)},t.prototype.scanUnicodeCodePointEscape=function(){var t=this.source[this.index],e=0;for("}"===t&&this.throwUnexpectedToken();!this.eof()&&(t=this.source[this.index++],o.Character.isHexDigit(t.charCodeAt(0)));)e=16*e+r(t);return(e>1114111||"}"!==t)&&this.throwUnexpectedToken(),o.Character.fromCodePoint(e)},t.prototype.getIdentifier=function(){for(var t=this.index++;!this.eof();){var e=this.source.charCodeAt(this.index);if(92===e)return this.index=t,this.getComplexIdentifier();if(e>=55296&&e<57343)return this.index=t,this.getComplexIdentifier();if(!o.Character.isIdentifierPart(e))break;++this.index}return this.source.slice(t,this.index)},t.prototype.getComplexIdentifier=function(){var t=this.codePointAt(this.index),e=o.Character.fromCodePoint(t);this.index+=e.length;var n;for(92===t&&(117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,n=this.scanUnicodeCodePointEscape()):null!==(n=this.scanHexEscape("u"))&&"\\"!==n&&o.Character.isIdentifierStart(n.charCodeAt(0))||this.throwUnexpectedToken(),e=n);!this.eof()&&(t=this.codePointAt(this.index),o.Character.isIdentifierPart(t));)n=o.Character.fromCodePoint(t),e+=n,this.index+=n.length,92===t&&(e=e.substr(0,e.length-1),117!==this.source.charCodeAt(this.index)&&this.throwUnexpectedToken(),++this.index,"{"===this.source[this.index]?(++this.index,n=this.scanUnicodeCodePointEscape()):null!==(n=this.scanHexEscape("u"))&&"\\"!==n&&o.Character.isIdentifierPart(n.charCodeAt(0))||this.throwUnexpectedToken(),e+=n);return e},t.prototype.octalToDecimal=function(t){var e="0"!==t,n=i(t);return!this.eof()&&o.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(e=!0,n=8*n+i(this.source[this.index++]),"0123".indexOf(t)>=0&&!this.eof()&&o.Character.isOctalDigit(this.source.charCodeAt(this.index))&&(n=8*n+i(this.source[this.index++]))),{code:n,octal:e}},t.prototype.scanIdentifier=function(){var t,e=this.index,n=92===this.source.charCodeAt(e)?this.getComplexIdentifier():this.getIdentifier();if(3!==(t=1===n.length?3:this.isKeyword(n)?4:"null"===n?5:"true"===n||"false"===n?1:3)&&e+n.length!==this.index){var r=this.index;this.index=e,this.tolerateUnexpectedToken(a.Messages.InvalidEscapedReservedWord),this.index=r}return{type:t,value:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},t.prototype.scanPunctuator=function(){var t=this.index,e=this.source[this.index];switch(e){case"(":case"{":"{"===e&&this.curlyStack.push("{"),++this.index;break;case".":++this.index,"."===this.source[this.index]&&"."===this.source[this.index+1]&&(this.index+=2,e="...");break;case"}":++this.index,this.curlyStack.pop();break;case")":case";":case",":case"[":case"]":case":":case"?":case"~":++this.index;break;default:e=this.source.substr(this.index,4),">>>="===e?this.index+=4:(e=e.substr(0,3),"==="===e||"!=="===e||">>>"===e||"<<="===e||">>="===e||"**="===e?this.index+=3:(e=e.substr(0,2),"&&"===e||"||"===e||"=="===e||"!="===e||"+="===e||"-="===e||"*="===e||"/="===e||"++"===e||"--"===e||"<<"===e||">>"===e||"&="===e||"|="===e||"^="===e||"%="===e||"<="===e||">="===e||"=>"===e||"**"===e?this.index+=2:(e=this.source[this.index],"<>=!+-*%&|^/".indexOf(e)>=0&&++this.index)))}return this.index===t&&this.throwUnexpectedToken(),{type:7,value:e,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanHexLiteral=function(t){for(var e="";!this.eof()&&o.Character.isHexDigit(this.source.charCodeAt(this.index));)e+=this.source[this.index++];return 0===e.length&&this.throwUnexpectedToken(),o.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:6,value:parseInt("0x"+e,16),lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanBinaryLiteral=function(t){for(var e,n="";!this.eof()&&("0"===(e=this.source[this.index])||"1"===e);)n+=this.source[this.index++];return 0===n.length&&this.throwUnexpectedToken(),this.eof()||(e=this.source.charCodeAt(this.index),(o.Character.isIdentifierStart(e)||o.Character.isDecimalDigit(e))&&this.throwUnexpectedToken()),{type:6,value:parseInt(n,2),lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanOctalLiteral=function(t,e){var n="",r=!1;for(o.Character.isOctalDigit(t.charCodeAt(0))?(r=!0,n="0"+this.source[this.index++]):++this.index;!this.eof()&&o.Character.isOctalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];return r||0!==n.length||this.throwUnexpectedToken(),(o.Character.isIdentifierStart(this.source.charCodeAt(this.index))||o.Character.isDecimalDigit(this.source.charCodeAt(this.index)))&&this.throwUnexpectedToken(),{type:6,value:parseInt(n,8),octal:r,lineNumber:this.lineNumber,lineStart:this.lineStart,start:e,end:this.index}},t.prototype.isImplicitOctalLiteral=function(){for(var t=this.index+1;t<this.length;++t){var e=this.source[t];if("8"===e||"9"===e)return!1;if(!o.Character.isOctalDigit(e.charCodeAt(0)))return!0}return!0},t.prototype.scanNumericLiteral=function(){var t=this.index,e=this.source[t];s.assert(o.Character.isDecimalDigit(e.charCodeAt(0))||"."===e,"Numeric literal must start with a decimal digit or a decimal point");var n="";if("."!==e){if(n=this.source[this.index++],e=this.source[this.index],"0"===n){if("x"===e||"X"===e)return++this.index,this.scanHexLiteral(t);if("b"===e||"B"===e)return++this.index,this.scanBinaryLiteral(t);if("o"===e||"O"===e)return this.scanOctalLiteral(e,t);if(e&&o.Character.isOctalDigit(e.charCodeAt(0))&&this.isImplicitOctalLiteral())return this.scanOctalLiteral(e,t)}for(;o.Character.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];e=this.source[this.index]}if("."===e){for(n+=this.source[this.index++];o.Character.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];e=this.source[this.index]}if("e"===e||"E"===e)if(n+=this.source[this.index++],e=this.source[this.index],"+"!==e&&"-"!==e||(n+=this.source[this.index++]),o.Character.isDecimalDigit(this.source.charCodeAt(this.index)))for(;o.Character.isDecimalDigit(this.source.charCodeAt(this.index));)n+=this.source[this.index++];else this.throwUnexpectedToken();return o.Character.isIdentifierStart(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(),{type:6,value:parseFloat(n),lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanStringLiteral=function(){var t=this.index,e=this.source[t];s.assert("'"===e||'"'===e,"String literal must starts with a quote"),++this.index;for(var n=!1,r="";!this.eof();){var i=this.source[this.index++];if(i===e){e="";break}if("\\"===i)if((i=this.source[this.index++])&&o.Character.isLineTerminator(i.charCodeAt(0)))++this.lineNumber,"\r"===i&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(i){case"u":if("{"===this.source[this.index])++this.index,r+=this.scanUnicodeCodePointEscape();else{var u=this.scanHexEscape(i);null===u&&this.throwUnexpectedToken(),r+=u}break;case"x":var c=this.scanHexEscape(i);null===c&&this.throwUnexpectedToken(a.Messages.InvalidHexEscapeSequence),r+=c;break;case"n":r+="\n";break;case"r":r+="\r";break;case"t":r+="\t";break;case"b":r+="\b";break;case"f":r+="\f";break;case"v":r+="\v";break;case"8":case"9":r+=i,this.tolerateUnexpectedToken();break;default:if(i&&o.Character.isOctalDigit(i.charCodeAt(0))){var h=this.octalToDecimal(i);n=h.octal||n,r+=String.fromCharCode(h.code)}else r+=i}else{if(o.Character.isLineTerminator(i.charCodeAt(0)))break;r+=i}}return""!==e&&(this.index=t,this.throwUnexpectedToken()),{type:8,value:r,octal:n,lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.scanTemplate=function(){var t="",e=!1,n=this.index,r="`"===this.source[n],i=!1,s=2;for(++this.index;!this.eof();){var u=this.source[this.index++];if("`"===u){s=1,i=!0,e=!0;break}if("$"===u){if("{"===this.source[this.index]){this.curlyStack.push("${"),++this.index,e=!0;break}t+=u}else if("\\"===u)if(u=this.source[this.index++],o.Character.isLineTerminator(u.charCodeAt(0)))++this.lineNumber,"\r"===u&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index;else switch(u){case"n":t+="\n";break;case"r":t+="\r";break;case"t":t+="\t";break;case"u":if("{"===this.source[this.index])++this.index,t+=this.scanUnicodeCodePointEscape();else{var c=this.index,h=this.scanHexEscape(u);null!==h?t+=h:(this.index=c,t+=u)}break;case"x":var l=this.scanHexEscape(u);null===l&&this.throwUnexpectedToken(a.Messages.InvalidHexEscapeSequence),t+=l;break;case"b":t+="\b";break;case"f":t+="\f";break;case"v":t+="\v";break;default:"0"===u?(o.Character.isDecimalDigit(this.source.charCodeAt(this.index))&&this.throwUnexpectedToken(a.Messages.TemplateOctalLiteral),t+="\0"):o.Character.isOctalDigit(u.charCodeAt(0))?this.throwUnexpectedToken(a.Messages.TemplateOctalLiteral):t+=u}else o.Character.isLineTerminator(u.charCodeAt(0))?(++this.lineNumber,"\r"===u&&"\n"===this.source[this.index]&&++this.index,this.lineStart=this.index,t+="\n"):t+=u}return e||this.throwUnexpectedToken(),r||this.curlyStack.pop(),{type:10,value:this.source.slice(n+1,this.index-s),cooked:t,head:r,tail:i,lineNumber:this.lineNumber,lineStart:this.lineStart,start:n,end:this.index}},t.prototype.testRegExp=function(t,e){var n=t,r=this;e.indexOf("u")>=0&&(n=n.replace(/\\u\{([0-9a-fA-F]+)\}|\\u([a-fA-F0-9]{4})/g,function(t,e,n){var i=parseInt(e||n,16);return i>1114111&&r.throwUnexpectedToken(a.Messages.InvalidRegExp),i<=65535?String.fromCharCode(i):"￿"}).replace(/[\uD800-\uDBFF][\uDC00-\uDFFF]/g,"￿"));try{RegExp(n)}catch(t){this.throwUnexpectedToken(a.Messages.InvalidRegExp)}try{return new RegExp(t,e)}catch(t){return null}},t.prototype.scanRegExpBody=function(){var t=this.source[this.index];s.assert("/"===t,"Regular expression literal must start with a slash");for(var e=this.source[this.index++],n=!1,r=!1;!this.eof();)if(t=this.source[this.index++],e+=t,"\\"===t)t=this.source[this.index++],o.Character.isLineTerminator(t.charCodeAt(0))&&this.throwUnexpectedToken(a.Messages.UnterminatedRegExp),e+=t;else if(o.Character.isLineTerminator(t.charCodeAt(0)))this.throwUnexpectedToken(a.Messages.UnterminatedRegExp);else if(n)"]"===t&&(n=!1);else{if("/"===t){r=!0;break}"["===t&&(n=!0)}return r||this.throwUnexpectedToken(a.Messages.UnterminatedRegExp),e.substr(1,e.length-2)},t.prototype.scanRegExpFlags=function(){for(var t="",e="";!this.eof();){var n=this.source[this.index];if(!o.Character.isIdentifierPart(n.charCodeAt(0)))break;if(++this.index,"\\"!==n||this.eof())e+=n,t+=n;else if("u"===(n=this.source[this.index])){++this.index;var r=this.index,i=this.scanHexEscape("u");if(null!==i)for(e+=i,t+="\\u";r<this.index;++r)t+=this.source[r];else this.index=r,e+="u",t+="\\u";this.tolerateUnexpectedToken()}else t+="\\",this.tolerateUnexpectedToken()}return e},t.prototype.scanRegExp=function(){var t=this.index,e=this.scanRegExpBody(),n=this.scanRegExpFlags();return{type:9,value:"",pattern:e,flags:n,regex:this.testRegExp(e,n),lineNumber:this.lineNumber,lineStart:this.lineStart,start:t,end:this.index}},t.prototype.lex=function(){if(this.eof())return{type:2,value:"",lineNumber:this.lineNumber,lineStart:this.lineStart,start:this.index,end:this.index};var t=this.source.charCodeAt(this.index);return o.Character.isIdentifierStart(t)?this.scanIdentifier():40===t||41===t||59===t?this.scanPunctuator():39===t||34===t?this.scanStringLiteral():46===t?o.Character.isDecimalDigit(this.source.charCodeAt(this.index+1))?this.scanNumericLiteral():this.scanPunctuator():o.Character.isDecimalDigit(t)?this.scanNumericLiteral():96===t||125===t&&"${"===this.curlyStack[this.curlyStack.length-1]?this.scanTemplate():t>=55296&&t<57343&&o.Character.isIdentifierStart(this.codePointAt(this.index))?this.scanIdentifier():this.scanPunctuator()},t}();e.Scanner=u},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.TokenName={},e.TokenName[1]="Boolean",e.TokenName[2]="<end>",e.TokenName[3]="Identifier",e.TokenName[4]="Keyword",e.TokenName[5]="Null",e.TokenName[6]="Numeric",e.TokenName[7]="Punctuator",e.TokenName[8]="String",e.TokenName[9]="RegularExpression",e.TokenName[10]="Template"},function(t,e){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.XHTMLEntities={quot:'"',amp:"&",apos:"'",gt:">",nbsp:" ",iexcl:"¡",cent:"¢",pound:"£",curren:"¤",yen:"¥",brvbar:"¦",sect:"§",uml:"¨",copy:"©",ordf:"ª",laquo:"«",not:"¬",shy:"­",reg:"®",macr:"¯",deg:"°",plusmn:"±",sup2:"²",sup3:"³",acute:"´",micro:"µ",para:"¶",middot:"·",cedil:"¸",sup1:"¹",ordm:"º",raquo:"»",frac14:"¼",frac12:"½",frac34:"¾",iquest:"¿",Agrave:"À",Aacute:"Á",Acirc:"Â",Atilde:"Ã",Auml:"Ä",Aring:"Å",AElig:"Æ",Ccedil:"Ç",Egrave:"È",Eacute:"É",Ecirc:"Ê",Euml:"Ë",Igrave:"Ì",Iacute:"Í",Icirc:"Î",Iuml:"Ï",ETH:"Ð",Ntilde:"Ñ",Ograve:"Ò",Oacute:"Ó",Ocirc:"Ô",Otilde:"Õ",Ouml:"Ö",times:"×",Oslash:"Ø",Ugrave:"Ù",Uacute:"Ú",Ucirc:"Û",Uuml:"Ü",Yacute:"Ý",THORN:"Þ",szlig:"ß",agrave:"à",aacute:"á",acirc:"â",atilde:"ã",auml:"ä",aring:"å",aelig:"æ",ccedil:"ç",egrave:"è",eacute:"é",ecirc:"ê",euml:"ë",igrave:"ì",iacute:"í",icirc:"î",iuml:"ï",eth:"ð",ntilde:"ñ",ograve:"ò",oacute:"ó",ocirc:"ô",otilde:"õ",ouml:"ö",divide:"÷",oslash:"ø",ugrave:"ù",uacute:"ú",ucirc:"û",uuml:"ü",yacute:"ý",thorn:"þ",yuml:"ÿ",OElig:"Œ",oelig:"œ",Scaron:"Š",scaron:"š",Yuml:"Ÿ",fnof:"ƒ",circ:"ˆ",tilde:"˜",Alpha:"Α",Beta:"Β",Gamma:"Γ",Delta:"Δ",Epsilon:"Ε",Zeta:"Ζ",Eta:"Η",Theta:"Θ",Iota:"Ι",Kappa:"Κ",Lambda:"Λ",Mu:"Μ",Nu:"Ν",Xi:"Ξ",Omicron:"Ο",Pi:"Π",Rho:"Ρ",Sigma:"Σ",Tau:"Τ",Upsilon:"Υ",Phi:"Φ",Chi:"Χ",Psi:"Ψ",Omega:"Ω",alpha:"α",beta:"β",gamma:"γ",delta:"δ",epsilon:"ε",zeta:"ζ",eta:"η",theta:"θ",iota:"ι",kappa:"κ",lambda:"λ",mu:"μ",nu:"ν",xi:"ξ",omicron:"ο",pi:"π",rho:"ρ",sigmaf:"ς",sigma:"σ",tau:"τ",upsilon:"υ",phi:"φ",chi:"χ",psi:"ψ",omega:"ω",thetasym:"ϑ",upsih:"ϒ",piv:"ϖ",ensp:" ",emsp:" ",thinsp:" ",zwnj:"‌",zwj:"‍",lrm:"‎",rlm:"‏",ndash:"–",mdash:"—",lsquo:"‘",rsquo:"’",sbquo:"‚",ldquo:"“",rdquo:"”",bdquo:"„",dagger:"†",Dagger:"‡",bull:"•",hellip:"…",permil:"‰",prime:"′",Prime:"″",lsaquo:"‹",rsaquo:"›",oline:"‾",frasl:"⁄",euro:"€",image:"ℑ",weierp:"℘",real:"ℜ",trade:"™",alefsym:"ℵ",larr:"←",uarr:"↑",rarr:"→",darr:"↓",harr:"↔",crarr:"↵",lArr:"⇐",uArr:"⇑",rArr:"⇒",dArr:"⇓",hArr:"⇔",forall:"∀",part:"∂",exist:"∃",empty:"∅",nabla:"∇",isin:"∈",notin:"∉",ni:"∋",prod:"∏",sum:"∑",minus:"−",lowast:"∗",radic:"√",prop:"∝",infin:"∞",ang:"∠",and:"∧",or:"∨",cap:"∩",cup:"∪",int:"∫",there4:"∴",sim:"∼",cong:"≅",asymp:"≈",ne:"≠",equiv:"≡",le:"≤",ge:"≥",sub:"⊂",sup:"⊃",nsub:"⊄",sube:"⊆",supe:"⊇",oplus:"⊕",otimes:"⊗",perp:"⊥",sdot:"⋅",lceil:"⌈",rceil:"⌉",lfloor:"⌊",rfloor:"⌋",loz:"◊",spades:"♠",clubs:"♣",hearts:"♥",diams:"♦",lang:"⟨",rang:"⟩"}},function(t,e,n){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var r=n(10),i=n(12),s=n(13),o=function(){function t(){this.values=[],this.curly=this.paren=-1}return t.prototype.beforeFunctionExpression=function(t){return["(","{","[","in","typeof","instanceof","new","return","case","delete","throw","void","=","+=","-=","*=","**=","/=","%=","<<=",">>=",">>>=","&=","|=","^=",",","+","-","*","**","/","%","++","--","<<",">>",">>>","&","|","^","!","~","&&","||","?",":","===","==",">=","<=","<",">","!=","!=="].indexOf(t)>=0},t.prototype.isRegexStart=function(){var t=this.values[this.values.length-1],e=null!==t;switch(t){case"this":case"]":e=!1;break;case")":var n=this.values[this.paren-1];e="if"===n||"while"===n||"for"===n||"with"===n;break;case"}":if(e=!1,"function"===this.values[this.curly-3]){var r=this.values[this.curly-4];e=!!r&&!this.beforeFunctionExpression(r)}else if("function"===this.values[this.curly-4]){var r=this.values[this.curly-5];e=!r||!this.beforeFunctionExpression(r)}}return e},t.prototype.push=function(t){7===t.type||4===t.type?("{"===t.value?this.curly=this.values.length:"("===t.value&&(this.paren=this.values.length),this.values.push(t.value)):this.values.push(null)},t}(),a=function(){function t(t,e){this.errorHandler=new r.ErrorHandler,this.errorHandler.tolerant=!!e&&("boolean"==typeof e.tolerant&&e.tolerant),this.scanner=new i.Scanner(t,this.errorHandler),this.scanner.trackComment=!!e&&("boolean"==typeof e.comment&&e.comment),this.trackRange=!!e&&("boolean"==typeof e.range&&e.range),this.trackLoc=!!e&&("boolean"==typeof e.loc&&e.loc),this.buffer=[],this.reader=new o}return t.prototype.errors=function(){return this.errorHandler.errors},t.prototype.getNextToken=function(){if(0===this.buffer.length){var t=this.scanner.scanComments();if(this.scanner.trackComment)for(var e=0;e<t.length;++e){var n=t[e],r=this.scanner.source.slice(n.slice[0],n.slice[1]),i={type:n.multiLine?"BlockComment":"LineComment",value:r};this.trackRange&&(i.range=n.range),this.trackLoc&&(i.loc=n.loc),this.buffer.push(i)}if(!this.scanner.eof()){var o=void 0;this.trackLoc&&(o={start:{line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},end:{}});var a="/"===this.scanner.source[this.scanner.index]&&this.reader.isRegexStart(),u=a?this.scanner.scanRegExp():this.scanner.lex();this.reader.push(u);var c={type:s.TokenName[u.type],value:this.scanner.source.slice(u.start,u.end)};if(this.trackRange&&(c.range=[u.start,u.end]),this.trackLoc&&(o.end={line:this.scanner.lineNumber,column:this.scanner.index-this.scanner.lineStart},c.loc=o),9===u.type){var h=u.pattern,l=u.flags;c.regex={pattern:h,flags:l}}this.buffer.push(c)}}return this.buffer.shift()},t}();e.Tokenizer=a}])})},function(t,e){e.read=function(t,e,n,r,i){var s,o,a=8*i-r-1,u=(1<<a)-1,c=u>>1,h=-7,l=n?i-1:0,p=n?-1:1,f=t[e+l];for(l+=p,s=f&(1<<-h)-1,f>>=-h,h+=a;h>0;s=256*s+t[e+l],l+=p,h-=8);for(o=s&(1<<-h)-1,s>>=-h,h+=r;h>0;o=256*o+t[e+l],l+=p,h-=8);if(0===s)s=1-c;else{if(s===u)return o?NaN:1/0*(f?-1:1);o+=Math.pow(2,r),s-=c}return(f?-1:1)*o*Math.pow(2,s-r)},e.write=function(t,e,n,r,i,s){var o,a,u,c=8*s-i-1,h=(1<<c)-1,l=h>>1,p=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,f=r?0:s-1,d=r?1:-1,m=e<0||0===e&&1/e<0?1:0;for(e=Math.abs(e),isNaN(e)||e===1/0?(a=isNaN(e)?1:0,o=h):(o=Math.floor(Math.log(e)/Math.LN2),e*(u=Math.pow(2,-o))<1&&(o--,u*=2),e+=o+l>=1?p/u:p*Math.pow(2,1-l),e*u>=2&&(o++,u/=2),o+l>=h?(a=0,o=h):o+l>=1?(a=(e*u-1)*Math.pow(2,i),o+=l):(a=e*Math.pow(2,l-1)*Math.pow(2,i),o=0));i>=8;t[n+f]=255&a,f+=d,a/=256,i-=8);for(o=o<<i|a,c+=i;c>0;t[n+f]=255&o,f+=d,o/=256,c-=8);t[n+f-d]|=128*m}},function(t,e){var n={}.toString;t.exports=Array.isArray||function(t){return"[object Array]"==n.call(t)}},function(t,e,n){"use strict";var r=n(177);t.exports=r},function(t,e,n){"use strict";function r(t){return function(){throw new Error("Function "+t+" is deprecated and cannot be used.")}}var i=n(179),s=n(178);t.exports.Type=n(0),t.exports.Schema=n(16),t.exports.FAILSAFE_SCHEMA=n(55),t.exports.JSON_SCHEMA=n(84),t.exports.CORE_SCHEMA=n(83),t.exports.DEFAULT_SAFE_SCHEMA=n(26),t.exports.DEFAULT_FULL_SCHEMA=n(35),t.exports.load=i.load,t.exports.loadAll=i.loadAll,t.exports.safeLoad=i.safeLoad,t.exports.safeLoadAll=i.safeLoadAll,t.exports.dump=s.dump,t.exports.safeDump=s.safeDump,t.exports.YAMLException=n(25),t.exports.MINIMAL_SCHEMA=n(55),t.exports.SAFE_SCHEMA=n(26),t.exports.DEFAULT_SCHEMA=n(35),t.exports.scan=r("scan"),t.exports.parse=r("parse"),t.exports.compose=r("compose"),t.exports.addConstructor=r("addConstructor")},function(t,e,n){"use strict";function r(t,e){var n,r,i,s,o,a,u;if(null===e)return{};for(n={},r=Object.keys(e),i=0,s=r.length;i<s;i+=1)o=r[i],a=String(e[o]),"!!"===o.slice(0,2)&&(o="tag:yaml.org,2002:"+o.slice(2)),u=t.compiledTypeMap.fallback[o],u&&O.call(u.styleAliases,a)&&(a=u.styleAliases[a]),n[o]=a;return n}function i(t){var e,n,r;if(e=t.toString(16).toUpperCase(),t<=255)n="x",r=2;else if(t<=65535)n="u",r=4;else{if(!(t<=4294967295))throw new I("code point within a string may not be greater than 0xFFFFFFFF");n="U",r=8}return"\\"+n+T.repeat("0",r-e.length)+e}function s(t){this.schema=t.schema||P,this.indent=Math.max(1,t.indent||2),this.skipInvalid=t.skipInvalid||!1,this.flowLevel=T.isNothing(t.flowLevel)?-1:t.flowLevel,this.styleMap=r(this.schema,t.styles||null),this.sortKeys=t.sortKeys||!1,this.lineWidth=t.lineWidth||80,this.noRefs=t.noRefs||!1,this.noCompatMode=t.noCompatMode||!1,this.condenseFlow=t.condenseFlow||!1,this.implicitTypes=this.schema.compiledImplicit,this.explicitTypes=this.schema.compiledExplicit,this.tag=null,this.result="",this.duplicates=[],this.usedDuplicates=null}function o(t,e){for(var n,r=T.repeat(" ",e),i=0,s=-1,o="",a=t.length;i<a;)s=t.indexOf("\n",i),-1===s?(n=t.slice(i),i=a):(n=t.slice(i,s+1),i=s+1),n.length&&"\n"!==n&&(o+=r),o+=n;return o}function a(t,e){return"\n"+T.repeat(" ",t.indent*e)}function u(t,e){var n,r,i;for(n=0,r=t.implicitTypes.length;n<r;n+=1)if(i=t.implicitTypes[n],i.resolve(e))return!0;return!1}function c(t){return t===U||t===_}function h(t){return 32<=t&&t<=126||161<=t&&t<=55295&&8232!==t&&8233!==t||57344<=t&&t<=65533&&65279!==t||65536<=t&&t<=1114111}function l(t){return h(t)&&65279!==t&&t!==H&&t!==Z&&t!==Q&&t!==et&&t!==rt&&t!==G&&t!==J}function p(t){return h(t)&&65279!==t&&!c(t)&&t!==W&&t!==V&&t!==G&&t!==H&&t!==Z&&t!==Q&&t!==et&&t!==rt&&t!==J&&t!==z&&t!==K&&t!==j&&t!==nt&&t!==q&&t!==Y&&t!==L&&t!==X&&t!==$&&t!==tt}function f(t,e,n,r,i){var s,o,a=!1,u=!1,f=-1!==r,d=-1,m=p(t.charCodeAt(0))&&!c(t.charCodeAt(t.length-1));if(e)for(s=0;s<t.length;s++){if(o=t.charCodeAt(s),!h(o))return ht;m=m&&l(o)}else{for(s=0;s<t.length;s++){if((o=t.charCodeAt(s))===R)a=!0,f&&(u=u||s-d-1>r&&" "!==t[d+1],d=s);else if(!h(o))return ht;m=m&&l(o)}u=u||f&&s-d-1>r&&" "!==t[d+1]}return a||u?" "===t[0]&&n>9?ht:u?ct:ut:m&&!i(t)?ot:at}function d(t,e,n,r){t.dump=function(){function i(e){return u(t,e)}if(0===e.length)return"''";if(!t.noCompatMode&&-1!==st.indexOf(e))return"'"+e+"'";var s=t.indent*Math.max(1,n),a=-1===t.lineWidth?-1:Math.max(Math.min(t.lineWidth,40),t.lineWidth-s),c=r||t.flowLevel>-1&&n>=t.flowLevel;switch(f(e,c,t.indent,a,i)){case ot:return e;case at:return"'"+e.replace(/'/g,"''")+"'";case ut:return"|"+m(e,t.indent)+x(o(e,s));case ct:return">"+m(e,t.indent)+x(o(y(e,a),s));case ht:return'"'+D(e)+'"';default:throw new I("impossible error: invalid scalar style")}}()}function m(t,e){var n=" "===t[0]?String(e):"",r="\n"===t[t.length-1];return n+(!r||"\n"!==t[t.length-2]&&"\n"!==t?r?"":"-":"+")+"\n"}function x(t){return"\n"===t[t.length-1]?t.slice(0,-1):t}function y(t,e){for(var n,r,i=/(\n+)([^\n]*)/g,s=function(){var n=t.indexOf("\n");return n=-1!==n?n:t.length,i.lastIndex=n,v(t.slice(0,n),e)}(),o="\n"===t[0]||" "===t[0];r=i.exec(t);){var a=r[1],u=r[2];n=" "===u[0],s+=a+(o||n||""===u?"":"\n")+v(u,e),o=n}return s}function v(t,e){if(""===t||" "===t[0])return t;for(var n,r,i=/ [^ ]/g,s=0,o=0,a=0,u="";n=i.exec(t);)a=n.index,a-s>e&&(r=o>s?o:a,u+="\n"+t.slice(s,r),s=r+1),o=a;return u+="\n",t.length-s>e&&o>s?u+=t.slice(s,o)+"\n"+t.slice(o+1):u+=t.slice(s),u.slice(1)}function D(t){for(var e,n,r="",s=0;s<t.length;s++)e=t.charCodeAt(s),n=it[e],r+=!n&&h(e)?t[s]:n||i(e);return r}function g(t,e,n){var r,i,s="",o=t.tag;for(r=0,i=n.length;r<i;r+=1)w(t,e,n[r],!1,!1)&&(0!==r&&(s+=","+(t.condenseFlow?"":" ")),s+=t.dump);t.tag=o,t.dump="["+s+"]"}function E(t,e,n,r){var i,s,o="",u=t.tag;for(i=0,s=n.length;i<s;i+=1)w(t,e+1,n[i],!0,!0)&&(r&&0===i||(o+=a(t,e)),t.dump&&R===t.dump.charCodeAt(0)?o+="-":o+="- ",o+=t.dump);t.tag=u,t.dump=o||"[]"}function A(t,e,n){var r,i,s,o,a,u="",c=t.tag,h=Object.keys(n);for(r=0,i=h.length;r<i;r+=1)a="",0!==r&&(a+=", "),s=h[r],o=n[s],w(t,e,s,!1,!1)&&(t.dump.length>1024&&(a+="? "),a+=t.dump+":"+(t.condenseFlow?"":" "),w(t,e,o,!1,!1)&&(a+=t.dump,u+=a));t.tag=c,t.dump="{"+u+"}"}function C(t,e,n,r){var i,s,o,u,c,h,l="",p=t.tag,f=Object.keys(n);if(!0===t.sortKeys)f.sort();else if("function"==typeof t.sortKeys)f.sort(t.sortKeys);else if(t.sortKeys)throw new I("sortKeys must be a boolean or a function");for(i=0,s=f.length;i<s;i+=1)h="",r&&0===i||(h+=a(t,e)),o=f[i],u=n[o],w(t,e+1,o,!0,!0,!0)&&(c=null!==t.tag&&"?"!==t.tag||t.dump&&t.dump.length>1024,c&&(t.dump&&R===t.dump.charCodeAt(0)?h+="?":h+="? "),h+=t.dump,c&&(h+=a(t,e)),w(t,e+1,u,!0,c)&&(t.dump&&R===t.dump.charCodeAt(0)?h+=":":h+=": ",h+=t.dump,l+=h));t.tag=p,t.dump=l||"{}"}function S(t,e,n){var r,i,s,o,a,u;for(i=n?t.explicitTypes:t.implicitTypes,s=0,o=i.length;s<o;s+=1)if(a=i[s],(a.instanceOf||a.predicate)&&(!a.instanceOf||"object"==typeof e&&e instanceof a.instanceOf)&&(!a.predicate||a.predicate(e))){if(t.tag=n?a.tag:"?",a.represent){if(u=t.styleMap[a.tag]||a.defaultStyle,"[object Function]"===M.call(a.represent))r=a.represent(e,u);else{if(!O.call(a.represent,u))throw new I("!<"+a.tag+'> tag resolver accepts not "'+u+'" style');r=a.represent[u](e,u)}t.dump=r}return!0}return!1}function w(t,e,n,r,i,s){t.tag=null,t.dump=n,S(t,n,!1)||S(t,n,!0);var o=M.call(t.dump);r&&(r=t.flowLevel<0||t.flowLevel>e);var a,u,c="[object Object]"===o||"[object Array]"===o;if(c&&(a=t.duplicates.indexOf(n),u=-1!==a),(null!==t.tag&&"?"!==t.tag||u||2!==t.indent&&e>0)&&(i=!1),u&&t.usedDuplicates[a])t.dump="*ref_"+a;else{if(c&&u&&!t.usedDuplicates[a]&&(t.usedDuplicates[a]=!0),"[object Object]"===o)r&&0!==Object.keys(t.dump).length?(C(t,e,t.dump,i),u&&(t.dump="&ref_"+a+t.dump)):(A(t,e,t.dump),u&&(t.dump="&ref_"+a+" "+t.dump));else if("[object Array]"===o)r&&0!==t.dump.length?(E(t,e,t.dump,i),u&&(t.dump="&ref_"+a+t.dump)):(g(t,e,t.dump),u&&(t.dump="&ref_"+a+" "+t.dump));else{if("[object String]"!==o){if(t.skipInvalid)return!1;throw new I("unacceptable kind of an object to dump "+o)}"?"!==t.tag&&d(t,t.dump,e,s)}null!==t.tag&&"?"!==t.tag&&(t.dump="!<"+t.tag+"> "+t.dump)}return!0}function b(t,e){var n,r,i=[],s=[];for(F(t,i,s),n=0,r=s.length;n<r;n+=1)e.duplicates.push(i[s[n]]);e.usedDuplicates=new Array(r)}function F(t,e,n){var r,i,s;if(null!==t&&"object"==typeof t)if(-1!==(i=e.indexOf(t)))-1===n.indexOf(i)&&n.push(i);else if(e.push(t),Array.isArray(t))for(i=0,s=t.length;i<s;i+=1)F(t[i],e,n);else for(r=Object.keys(t),i=0,s=r.length;i<s;i+=1)F(t[r[i]],e,n)}function k(t,e){e=e||{};var n=new s(e);return n.noRefs||b(t,n),w(n,0,t,!0,!0)?n.dump+"\n":""}function B(t,e){return k(t,T.extend({schema:N},e))}var T=n(15),I=n(25),P=n(35),N=n(26),M=Object.prototype.toString,O=Object.prototype.hasOwnProperty,_=9,R=10,U=32,j=33,L=34,J=35,X=37,z=38,Y=39,K=42,H=44,W=45,G=58,q=62,V=63,$=64,Z=91,Q=93,tt=96,et=123,nt=124,rt=125,it={};it[0]="\\0",it[7]="\\a",it[8]="\\b",it[9]="\\t",it[10]="\\n",it[11]="\\v",it[12]="\\f",it[13]="\\r",it[27]="\\e",it[34]='\\"',it[92]="\\\\",it[133]="\\N",it[160]="\\_",it[8232]="\\L",it[8233]="\\P";var st=["y","Y","yes","Yes","YES","on","On","ON","n","N","no","No","NO","off","Off","OFF"],ot=1,at=2,ut=3,ct=4,ht=5;t.exports.dump=k,t.exports.safeDump=B},function(t,e,n){"use strict";function r(t){return 10===t||13===t}function i(t){return 9===t||32===t}function s(t){return 9===t||32===t||10===t||13===t}function o(t){return 44===t||91===t||93===t||123===t||125===t}function a(t){var e;return 48<=t&&t<=57?t-48:(e=32|t,97<=e&&e<=102?e-97+10:-1)}function u(t){return 120===t?2:117===t?4:85===t?8:0}function c(t){return 48<=t&&t<=57?t-48:-1}function h(t){return 48===t?"\0":97===t?"":98===t?"\b":116===t?"\t":9===t?"\t":110===t?"\n":118===t?"\v":102===t?"\f":114===t?"\r":101===t?"":32===t?" ":34===t?'"':47===t?"/":92===t?"\\":78===t?"":95===t?" ":76===t?"\u2028":80===t?"\u2029":""}function l(t){return t<=65535?String.fromCharCode(t):String.fromCharCode(55296+(t-65536>>10),56320+(t-65536&1023))}function p(t,e){this.input=t,this.filename=e.filename||null,this.schema=e.schema||Y,this.onWarning=e.onWarning||null,this.legacy=e.legacy||!1,this.json=e.json||!1,this.listener=e.listener||null,this.implicitTypes=this.schema.compiledImplicit,this.typeMap=this.schema.compiledTypeMap,this.length=t.length,this.position=0,this.line=0,this.lineStart=0,this.lineIndent=0,this.documents=[]}function f(t,e){return new J(e,new X(t.filename,t.input,t.position,t.line,t.position-t.lineStart))}function d(t,e){throw f(t,e)}function m(t,e){t.onWarning&&t.onWarning.call(null,f(t,e))}function x(t,e,n,r){var i,s,o,a;if(e<n){if(a=t.input.slice(e,n),r)for(i=0,s=a.length;i<s;i+=1)9===(o=a.charCodeAt(i))||32<=o&&o<=1114111||d(t,"expected valid JSON character");else Q.test(a)&&d(t,"the stream contains non-printable characters");t.result+=a}}function y(t,e,n,r){var i,s,o,a;for(L.isObject(n)||d(t,"cannot merge mappings; the provided source object is unacceptable"),i=Object.keys(n),o=0,a=i.length;o<a;o+=1)s=i[o],K.call(e,s)||(e[s]=n[s],r[s]=!0)}function v(t,e,n,r,i,s,o,a){var u,c;if(i=String(i),null===e&&(e={}),"tag:yaml.org,2002:merge"===r)if(Array.isArray(s))for(u=0,c=s.length;u<c;u+=1)y(t,e,s[u],n);else y(t,e,s,n);else t.json||K.call(n,i)||!K.call(e,i)||(t.line=o||t.line,t.position=a||t.position,d(t,"duplicated mapping key")),e[i]=s,delete n[i];return e}function D(t){var e;e=t.input.charCodeAt(t.position),10===e?t.position++:13===e?(t.position++,10===t.input.charCodeAt(t.position)&&t.position++):d(t,"a line break is expected"),t.line+=1,t.lineStart=t.position}function g(t,e,n){for(var s=0,o=t.input.charCodeAt(t.position);0!==o;){for(;i(o);)o=t.input.charCodeAt(++t.position);if(e&&35===o)do{o=t.input.charCodeAt(++t.position)}while(10!==o&&13!==o&&0!==o);if(!r(o))break;for(D(t),o=t.input.charCodeAt(t.position),s++,t.lineIndent=0;32===o;)t.lineIndent++,o=t.input.charCodeAt(++t.position)}return-1!==n&&0!==s&&t.lineIndent<n&&m(t,"deficient indentation"),s}function E(t){var e,n=t.position;return!(45!==(e=t.input.charCodeAt(n))&&46!==e||e!==t.input.charCodeAt(n+1)||e!==t.input.charCodeAt(n+2)||(n+=3,0!==(e=t.input.charCodeAt(n))&&!s(e)))}function A(t,e){1===e?t.result+=" ":e>1&&(t.result+=L.repeat("\n",e-1))}function C(t,e,n){var a,u,c,h,l,p,f,d,m,y=t.kind,v=t.result;if(m=t.input.charCodeAt(t.position),s(m)||o(m)||35===m||38===m||42===m||33===m||124===m||62===m||39===m||34===m||37===m||64===m||96===m)return!1;if((63===m||45===m)&&(u=t.input.charCodeAt(t.position+1),s(u)||n&&o(u)))return!1;for(t.kind="scalar",t.result="",c=h=t.position,l=!1;0!==m;){if(58===m){if(u=t.input.charCodeAt(t.position+1),s(u)||n&&o(u))break}else if(35===m){if(a=t.input.charCodeAt(t.position-1),s(a))break}else{if(t.position===t.lineStart&&E(t)||n&&o(m))break;if(r(m)){if(p=t.line,f=t.lineStart,d=t.lineIndent,g(t,!1,-1),t.lineIndent>=e){l=!0,m=t.input.charCodeAt(t.position);continue}t.position=h,t.line=p,t.lineStart=f,t.lineIndent=d;break}}l&&(x(t,c,h,!1),A(t,t.line-p),c=h=t.position,l=!1),i(m)||(h=t.position+1),m=t.input.charCodeAt(++t.position)}return x(t,c,h,!1),!!t.result||(t.kind=y,t.result=v,!1)}function S(t,e){var n,i,s;if(39!==(n=t.input.charCodeAt(t.position)))return!1;for(t.kind="scalar",t.result="",t.position++,i=s=t.position;0!==(n=t.input.charCodeAt(t.position));)if(39===n){if(x(t,i,t.position,!0),39!==(n=t.input.charCodeAt(++t.position)))return!0;i=t.position,t.position++,s=t.position}else r(n)?(x(t,i,s,!0),A(t,g(t,!1,e)),i=s=t.position):t.position===t.lineStart&&E(t)?d(t,"unexpected end of the document within a single quoted scalar"):(t.position++,s=t.position);d(t,"unexpected end of the stream within a single quoted scalar")}function w(t,e){var n,i,s,o,c,h;if(34!==(h=t.input.charCodeAt(t.position)))return!1;for(t.kind="scalar",t.result="",t.position++,n=i=t.position;0!==(h=t.input.charCodeAt(t.position));){if(34===h)return x(t,n,t.position,!0),t.position++,!0;if(92===h){if(x(t,n,t.position,!0),h=t.input.charCodeAt(++t.position),r(h))g(t,!1,e);else if(h<256&&it[h])t.result+=st[h],t.position++;else if((c=u(h))>0){for(s=c,o=0;s>0;s--)h=t.input.charCodeAt(++t.position),(c=a(h))>=0?o=(o<<4)+c:d(t,"expected hexadecimal character");t.result+=l(o),t.position++}else d(t,"unknown escape sequence");n=i=t.position}else r(h)?(x(t,n,i,!0),A(t,g(t,!1,e)),n=i=t.position):t.position===t.lineStart&&E(t)?d(t,"unexpected end of the document within a double quoted scalar"):(t.position++,i=t.position)}d(t,"unexpected end of the stream within a double quoted scalar")}function b(t,e){var n,r,i,o,a,u,c,h,l,p,f,m=!0,x=t.tag,y=t.anchor,D={};if(91===(f=t.input.charCodeAt(t.position)))o=93,c=!1,r=[];else{if(123!==f)return!1;o=125,c=!0,r={}}for(null!==t.anchor&&(t.anchorMap[t.anchor]=r),f=t.input.charCodeAt(++t.position);0!==f;){if(g(t,!0,e),(f=t.input.charCodeAt(t.position))===o)return t.position++,t.tag=x,t.anchor=y,t.kind=c?"mapping":"sequence",t.result=r,!0;m||d(t,"missed comma between flow collection entries"),l=h=p=null,a=u=!1,63===f&&(i=t.input.charCodeAt(t.position+1),s(i)&&(a=u=!0,t.position++,g(t,!0,e))),n=t.line,N(t,e,H,!1,!0),l=t.tag,h=t.result,g(t,!0,e),f=t.input.charCodeAt(t.position),!u&&t.line!==n||58!==f||(a=!0,f=t.input.charCodeAt(++t.position),g(t,!0,e),N(t,e,H,!1,!0),p=t.result),c?v(t,r,D,l,h,p):a?r.push(v(t,null,D,l,h,p)):r.push(h),g(t,!0,e),f=t.input.charCodeAt(t.position),44===f?(m=!0,f=t.input.charCodeAt(++t.position)):m=!1}d(t,"unexpected end of the stream within a flow collection")}function F(t,e){var n,s,o,a,u=V,h=!1,l=!1,p=e,f=0,m=!1;if(124===(a=t.input.charCodeAt(t.position)))s=!1;else{if(62!==a)return!1;s=!0}for(t.kind="scalar",t.result="";0!==a;)if(43===(a=t.input.charCodeAt(++t.position))||45===a)V===u?u=43===a?Z:$:d(t,"repeat of a chomping mode identifier");else{if(!((o=c(a))>=0))break;0===o?d(t,"bad explicit indentation width of a block scalar; it cannot be less than one"):l?d(t,"repeat of an indentation width identifier"):(p=e+o-1,l=!0)}if(i(a)){do{a=t.input.charCodeAt(++t.position)}while(i(a));if(35===a)do{a=t.input.charCodeAt(++t.position)}while(!r(a)&&0!==a)}for(;0!==a;){for(D(t),t.lineIndent=0,a=t.input.charCodeAt(t.position);(!l||t.lineIndent<p)&&32===a;)t.lineIndent++,a=t.input.charCodeAt(++t.position);if(!l&&t.lineIndent>p&&(p=t.lineIndent),r(a))f++;else{if(t.lineIndent<p){u===Z?t.result+=L.repeat("\n",h?1+f:f):u===V&&h&&(t.result+="\n");break}for(s?i(a)?(m=!0,t.result+=L.repeat("\n",h?1+f:f)):m?(m=!1,t.result+=L.repeat("\n",f+1)):0===f?h&&(t.result+=" "):t.result+=L.repeat("\n",f):t.result+=L.repeat("\n",h?1+f:f),h=!0,l=!0,f=0,n=t.position;!r(a)&&0!==a;)a=t.input.charCodeAt(++t.position);x(t,n,t.position,!1)}}return!0}function k(t,e){var n,r,i,o=t.tag,a=t.anchor,u=[],c=!1;for(null!==t.anchor&&(t.anchorMap[t.anchor]=u),i=t.input.charCodeAt(t.position);0!==i&&45===i&&(r=t.input.charCodeAt(t.position+1),s(r));)if(c=!0,t.position++,g(t,!0,-1)&&t.lineIndent<=e)u.push(null),i=t.input.charCodeAt(t.position);else if(n=t.line,N(t,e,G,!1,!0),u.push(t.result),g(t,!0,-1),i=t.input.charCodeAt(t.position),(t.line===n||t.lineIndent>e)&&0!==i)d(t,"bad indentation of a sequence entry");else if(t.lineIndent<e)break;return!!c&&(t.tag=o,t.anchor=a,t.kind="sequence",t.result=u,!0)}function B(t,e,n){var r,o,a,u,c,h=t.tag,l=t.anchor,p={},f={},m=null,x=null,y=null,D=!1,E=!1;for(null!==t.anchor&&(t.anchorMap[t.anchor]=p),c=t.input.charCodeAt(t.position);0!==c;){if(r=t.input.charCodeAt(t.position+1),a=t.line,u=t.position,63!==c&&58!==c||!s(r)){if(!N(t,n,W,!1,!0))break;if(t.line===a){for(c=t.input.charCodeAt(t.position);i(c);)c=t.input.charCodeAt(++t.position);if(58===c)c=t.input.charCodeAt(++t.position),s(c)||d(t,"a whitespace character is expected after the key-value separator within a block mapping"),D&&(v(t,p,f,m,x,null),m=x=y=null),E=!0,D=!1,o=!1,m=t.tag,x=t.result;else{if(!E)return t.tag=h,t.anchor=l,!0;d(t,"can not read an implicit mapping pair; a colon is missed")}}else{if(!E)return t.tag=h,t.anchor=l,!0;d(t,"can not read a block mapping entry; a multiline key may not be an implicit key")}}else 63===c?(D&&(v(t,p,f,m,x,null),m=x=y=null),E=!0,D=!0,o=!0):D?(D=!1,o=!0):d(t,"incomplete explicit mapping pair; a key node is missed"),t.position+=1,c=r;if((t.line===a||t.lineIndent>e)&&(N(t,e,q,!0,o)&&(D?x=t.result:y=t.result),D||(v(t,p,f,m,x,y,a,u),m=x=y=null),g(t,!0,-1),c=t.input.charCodeAt(t.position)),t.lineIndent>e&&0!==c)d(t,"bad indentation of a mapping entry");else if(t.lineIndent<e)break}return D&&v(t,p,f,m,x,null),E&&(t.tag=h,t.anchor=l,t.kind="mapping",t.result=p),E}function T(t){var e,n,r,i,o=!1,a=!1;if(33!==(i=t.input.charCodeAt(t.position)))return!1;if(null!==t.tag&&d(t,"duplication of a tag property"),i=t.input.charCodeAt(++t.position),60===i?(o=!0,i=t.input.charCodeAt(++t.position)):33===i?(a=!0,n="!!",i=t.input.charCodeAt(++t.position)):n="!",e=t.position,o){do{i=t.input.charCodeAt(++t.position)}while(0!==i&&62!==i);t.position<t.length?(r=t.input.slice(e,t.position),i=t.input.charCodeAt(++t.position)):d(t,"unexpected end of the stream within a verbatim tag")}else{for(;0!==i&&!s(i);)33===i&&(a?d(t,"tag suffix cannot contain exclamation marks"):(n=t.input.slice(e-1,t.position+1),nt.test(n)||d(t,"named tag handle cannot contain such characters"),a=!0,e=t.position+1)),i=t.input.charCodeAt(++t.position);r=t.input.slice(e,t.position),et.test(r)&&d(t,"tag suffix cannot contain flow indicator characters")}return r&&!rt.test(r)&&d(t,"tag name cannot contain such characters: "+r),o?t.tag=r:K.call(t.tagMap,n)?t.tag=t.tagMap[n]+r:"!"===n?t.tag="!"+r:"!!"===n?t.tag="tag:yaml.org,2002:"+r:d(t,'undeclared tag handle "'+n+'"'),!0}function I(t){var e,n;if(38!==(n=t.input.charCodeAt(t.position)))return!1;for(null!==t.anchor&&d(t,"duplication of an anchor property"),n=t.input.charCodeAt(++t.position),e=t.position;0!==n&&!s(n)&&!o(n);)n=t.input.charCodeAt(++t.position);return t.position===e&&d(t,"name of an anchor node must contain at least one character"),t.anchor=t.input.slice(e,t.position),!0}function P(t){var e,n,r;if(42!==(r=t.input.charCodeAt(t.position)))return!1;for(r=t.input.charCodeAt(++t.position),e=t.position;0!==r&&!s(r)&&!o(r);)r=t.input.charCodeAt(++t.position);return t.position===e&&d(t,"name of an alias node must contain at least one character"),n=t.input.slice(e,t.position),t.anchorMap.hasOwnProperty(n)||d(t,'unidentified alias "'+n+'"'),t.result=t.anchorMap[n],g(t,!0,-1),!0}function N(t,e,n,r,i){var s,o,a,u,c,h,l,p,f=1,m=!1,x=!1;if(null!==t.listener&&t.listener("open",t),t.tag=null,t.anchor=null,t.kind=null,t.result=null,s=o=a=q===n||G===n,r&&g(t,!0,-1)&&(m=!0,t.lineIndent>e?f=1:t.lineIndent===e?f=0:t.lineIndent<e&&(f=-1)),1===f)for(;T(t)||I(t);)g(t,!0,-1)?(m=!0,a=s,t.lineIndent>e?f=1:t.lineIndent===e?f=0:t.lineIndent<e&&(f=-1)):a=!1;if(a&&(a=m||i),1!==f&&q!==n||(l=H===n||W===n?e:e+1,p=t.position-t.lineStart,1===f?a&&(k(t,p)||B(t,p,l))||b(t,l)?x=!0:(o&&F(t,l)||S(t,l)||w(t,l)?x=!0:P(t)?(x=!0,null===t.tag&&null===t.anchor||d(t,"alias node should not have any properties")):C(t,l,H===n)&&(x=!0,null===t.tag&&(t.tag="?")),null!==t.anchor&&(t.anchorMap[t.anchor]=t.result)):0===f&&(x=a&&k(t,p))),null!==t.tag&&"!"!==t.tag)if("?"===t.tag){for(u=0,c=t.implicitTypes.length;u<c;u+=1)if(h=t.implicitTypes[u],h.resolve(t.result)){t.result=h.construct(t.result),t.tag=h.tag,null!==t.anchor&&(t.anchorMap[t.anchor]=t.result);break}}else K.call(t.typeMap[t.kind||"fallback"],t.tag)?(h=t.typeMap[t.kind||"fallback"][t.tag],null!==t.result&&h.kind!==t.kind&&d(t,"unacceptable node kind for !<"+t.tag+'> tag; it should be "'+h.kind+'", not "'+t.kind+'"'),h.resolve(t.result)?(t.result=h.construct(t.result),null!==t.anchor&&(t.anchorMap[t.anchor]=t.result)):d(t,"cannot resolve a node with !<"+t.tag+"> explicit tag")):d(t,"unknown tag !<"+t.tag+">");return null!==t.listener&&t.listener("close",t),null!==t.tag||null!==t.anchor||x}function M(t){var e,n,o,a,u=t.position,c=!1;for(t.version=null,t.checkLineBreaks=t.legacy,t.tagMap={},t.anchorMap={};0!==(a=t.input.charCodeAt(t.position))&&(g(t,!0,-1),a=t.input.charCodeAt(t.position),!(t.lineIndent>0||37!==a));){for(c=!0,a=t.input.charCodeAt(++t.position),e=t.position;0!==a&&!s(a);)a=t.input.charCodeAt(++t.position);for(n=t.input.slice(e,t.position),o=[],n.length<1&&d(t,"directive name must not be less than one character in length");0!==a;){for(;i(a);)a=t.input.charCodeAt(++t.position);if(35===a){do{a=t.input.charCodeAt(++t.position)}while(0!==a&&!r(a));break}if(r(a))break;for(e=t.position;0!==a&&!s(a);)a=t.input.charCodeAt(++t.position);o.push(t.input.slice(e,t.position))}0!==a&&D(t),K.call(at,n)?at[n](t,n,o):m(t,'unknown document directive "'+n+'"')}if(g(t,!0,-1),0===t.lineIndent&&45===t.input.charCodeAt(t.position)&&45===t.input.charCodeAt(t.position+1)&&45===t.input.charCodeAt(t.position+2)?(t.position+=3,g(t,!0,-1)):c&&d(t,"directives end mark is expected"),N(t,t.lineIndent-1,q,!1,!0),g(t,!0,-1),t.checkLineBreaks&&tt.test(t.input.slice(u,t.position))&&m(t,"non-ASCII line breaks are interpreted as content"),t.documents.push(t.result),t.position===t.lineStart&&E(t))return void(46===t.input.charCodeAt(t.position)&&(t.position+=3,g(t,!0,-1)));t.position<t.length-1&&d(t,"end of the stream or a document separator is expected")}function O(t,e){t=String(t),e=e||{},0!==t.length&&(10!==t.charCodeAt(t.length-1)&&13!==t.charCodeAt(t.length-1)&&(t+="\n"),65279===t.charCodeAt(0)&&(t=t.slice(1)));var n=new p(t,e);for(n.input+="\0";32===n.input.charCodeAt(n.position);)n.lineIndent+=1,n.position+=1;for(;n.position<n.length-1;)M(n);return n.documents}function _(t,e,n){var r,i,s=O(t,n);if("function"!=typeof e)return s;for(r=0,i=s.length;r<i;r+=1)e(s[r])}function R(t,e){var n=O(t,e);if(0!==n.length){if(1===n.length)return n[0];throw new J("expected a single document in the stream, but found more")}}function U(t,e,n){if("function"!=typeof e)return _(t,L.extend({schema:z},n));_(t,e,L.extend({schema:z},n))}function j(t,e){return R(t,L.extend({schema:z},e))}for(var L=n(15),J=n(25),X=n(180),z=n(26),Y=n(35),K=Object.prototype.hasOwnProperty,H=1,W=2,G=3,q=4,V=1,$=2,Z=3,Q=/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F-\x84\x86-\x9F\uFFFE\uFFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])|(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]/,tt=/[\x85\u2028\u2029]/,et=/[,\[\]\{\}]/,nt=/^(?:!|!!|![a-z\-]+!)$/i,rt=/^(?:!|[^,\[\]\{\}])(?:%[0-9a-f]{2}|[0-9a-z\-#;\/\?:@&=\+\$,_\.!~\*'\(\)\[\]])*$/i,it=new Array(256),st=new Array(256),ot=0;ot<256;ot++)it[ot]=h(ot)?1:0,st[ot]=h(ot);var at={YAML:function(t,e,n){var r,i,s;null!==t.version&&d(t,"duplication of %YAML directive"),1!==n.length&&d(t,"YAML directive accepts exactly one argument"),r=/^([0-9]+)\.([0-9]+)$/.exec(n[0]),null===r&&d(t,"ill-formed argument of the YAML directive"),i=parseInt(r[1],10),s=parseInt(r[2],10),1!==i&&d(t,"unacceptable YAML version of the document"),t.version=n[0],t.checkLineBreaks=s<2,1!==s&&2!==s&&m(t,"unsupported YAML version of the document")},TAG:function(t,e,n){var r,i;2!==n.length&&d(t,"TAG directive accepts exactly two arguments"),r=n[0],i=n[1],nt.test(r)||d(t,"ill-formed tag handle (first argument) of the TAG directive"),K.call(t.tagMap,r)&&d(t,'there is a previously declared suffix for "'+r+'" tag handle'),rt.test(i)||d(t,"ill-formed tag prefix (second argument) of the TAG directive"),t.tagMap[r]=i}};t.exports.loadAll=_,t.exports.load=R,t.exports.safeLoadAll=U,t.exports.safeLoad=j},function(t,e,n){"use strict";function r(t,e,n,r,i){this.name=t,this.buffer=e,this.position=n,this.line=r,this.column=i}var i=n(15);r.prototype.getSnippet=function(t,e){var n,r,s,o,a;if(!this.buffer)return null;for(t=t||4,e=e||75,n="",r=this.position;r>0&&-1==="\0\r\n\u2028\u2029".indexOf(this.buffer.charAt(r-1));)if(r-=1,this.position-r>e/2-1){n=" ... ",r+=5;break}for(s="",o=this.position;o<this.buffer.length&&-1==="\0\r\n\u2028\u2029".indexOf(this.buffer.charAt(o));)if((o+=1)-this.position>e/2-1){s=" ... ",o-=5;break}return a=this.buffer.slice(r,o),i.repeat(" ",t)+n+a+s+"\n"+i.repeat(" ",t+this.position-r+n.length)+"^"},r.prototype.toString=function(t){var e,n="";return this.name&&(n+='in "'+this.name+'" '),n+="at line "+(this.line+1)+", column "+(this.column+1),t||(e=this.getSnippet())&&(n+=":\n"+e),n},t.exports=r},function(t,e,n){"use strict";function r(t){if(null===t)return!1;var e,n,r=0,i=t.length,s=c;for(n=0;n<i;n++)if(!((e=s.indexOf(t.charAt(n)))>64)){if(e<0)return!1;r+=6}return r%8==0}function i(t){var e,n,r=t.replace(/[\r\n=]/g,""),i=r.length,s=c,o=0,u=[];for(e=0;e<i;e++)e%4==0&&e&&(u.push(o>>16&255),u.push(o>>8&255),u.push(255&o)),o=o<<6|s.indexOf(r.charAt(e));return n=i%4*6,0===n?(u.push(o>>16&255),u.push(o>>8&255),u.push(255&o)):18===n?(u.push(o>>10&255),u.push(o>>2&255)):12===n&&u.push(o>>4&255),a?a.from?a.from(u):new a(u):u}function s(t){var e,n,r="",i=0,s=t.length,o=c;for(e=0;e<s;e++)e%3==0&&e&&(r+=o[i>>18&63],r+=o[i>>12&63],r+=o[i>>6&63],r+=o[63&i]),i=(i<<8)+t[e];return n=s%3,0===n?(r+=o[i>>18&63],r+=o[i>>12&63],r+=o[i>>6&63],r+=o[63&i]):2===n?(r+=o[i>>10&63],r+=o[i>>4&63],r+=o[i<<2&63],r+=o[64]):1===n&&(r+=o[i>>2&63],r+=o[i<<4&63],r+=o[64],r+=o[64]),r}function o(t){return a&&a.isBuffer(t)}var a;try{a=n(140).Buffer}catch(t){}var u=n(0),c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz**********+/=\n\r";t.exports=new u("tag:yaml.org,2002:binary",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:s})},function(t,e,n){"use strict";function r(t){if(null===t)return!1;var e=t.length;return 4===e&&("true"===t||"True"===t||"TRUE"===t)||5===e&&("false"===t||"False"===t||"FALSE"===t)}function i(t){return"true"===t||"True"===t||"TRUE"===t}function s(t){return"[object Boolean]"===Object.prototype.toString.call(t)}var o=n(0);t.exports=new o("tag:yaml.org,2002:bool",{kind:"scalar",resolve:r,construct:i,predicate:s,represent:{lowercase:function(t){return t?"true":"false"},uppercase:function(t){return t?"TRUE":"FALSE"},camelcase:function(t){return t?"True":"False"}},defaultStyle:"lowercase"})},function(t,e,n){"use strict";function r(t){return null!==t&&!(!c.test(t)||"_"===t[t.length-1])}function i(t){var e,n,r,i;return e=t.replace(/_/g,"").toLowerCase(),n="-"===e[0]?-1:1,i=[],"+-".indexOf(e[0])>=0&&(e=e.slice(1)),".inf"===e?1===n?Number.POSITIVE_INFINITY:Number.NEGATIVE_INFINITY:".nan"===e?NaN:e.indexOf(":")>=0?(e.split(":").forEach(function(t){i.unshift(parseFloat(t,10))}),e=0,r=1,i.forEach(function(t){e+=t*r,r*=60}),n*e):n*parseFloat(e,10)}function s(t,e){var n;if(isNaN(t))switch(e){case"lowercase":return".nan";case"uppercase":return".NAN";case"camelcase":return".NaN"}else if(Number.POSITIVE_INFINITY===t)switch(e){case"lowercase":return".inf";case"uppercase":return".INF";case"camelcase":return".Inf"}else if(Number.NEGATIVE_INFINITY===t)switch(e){case"lowercase":return"-.inf";case"uppercase":return"-.INF";case"camelcase":return"-.Inf"}else if(a.isNegativeZero(t))return"-0.0";return n=t.toString(10),h.test(n)?n.replace("e",".e"):n}function o(t){return"[object Number]"===Object.prototype.toString.call(t)&&(t%1!=0||a.isNegativeZero(t))}var a=n(15),u=n(0),c=new RegExp("^(?:[-+]?(?:0|[1-9][0-9_]*)(?:\\.[0-9_]*)?(?:[eE][-+]?[0-9]+)?|\\.[0-9_]+(?:[eE][-+]?[0-9]+)?|[-+]?[0-9][0-9_]*(?::[0-5]?[0-9])+\\.[0-9_]*|[-+]?\\.(?:inf|Inf|INF)|\\.(?:nan|NaN|NAN))$"),h=/^[-+]?[0-9]+e/;t.exports=new u("tag:yaml.org,2002:float",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:s,defaultStyle:"lowercase"})},function(t,e,n){"use strict";function r(t){return 48<=t&&t<=57||65<=t&&t<=70||97<=t&&t<=102}function i(t){return 48<=t&&t<=55}function s(t){return 48<=t&&t<=57}function o(t){if(null===t)return!1;var e,n=t.length,o=0,a=!1;if(!n)return!1;if(e=t[o],"-"!==e&&"+"!==e||(e=t[++o]),"0"===e){if(o+1===n)return!0;if("b"===(e=t[++o])){for(o++;o<n;o++)if("_"!==(e=t[o])){if("0"!==e&&"1"!==e)return!1;a=!0}return a&&"_"!==e}if("x"===e){for(o++;o<n;o++)if("_"!==(e=t[o])){if(!r(t.charCodeAt(o)))return!1;a=!0}return a&&"_"!==e}for(;o<n;o++)if("_"!==(e=t[o])){if(!i(t.charCodeAt(o)))return!1;a=!0}return a&&"_"!==e}if("_"===e)return!1;for(;o<n;o++)if("_"!==(e=t[o])){if(":"===e)break;if(!s(t.charCodeAt(o)))return!1;a=!0}return!(!a||"_"===e)&&(":"!==e||/^(:[0-5]?[0-9])+$/.test(t.slice(o)))}function a(t){var e,n,r=t,i=1,s=[];return-1!==r.indexOf("_")&&(r=r.replace(/_/g,"")),e=r[0],"-"!==e&&"+"!==e||("-"===e&&(i=-1),r=r.slice(1),e=r[0]),"0"===r?0:"0"===e?"b"===r[1]?i*parseInt(r.slice(2),2):"x"===r[1]?i*parseInt(r,16):i*parseInt(r,8):-1!==r.indexOf(":")?(r.split(":").forEach(function(t){s.unshift(parseInt(t,10))}),r=0,n=1,s.forEach(function(t){r+=t*n,n*=60}),i*r):i*parseInt(r,10)}function u(t){return"[object Number]"===Object.prototype.toString.call(t)&&t%1==0&&!c.isNegativeZero(t)}var c=n(15),h=n(0);t.exports=new h("tag:yaml.org,2002:int",{kind:"scalar",resolve:o,construct:a,predicate:u,represent:{binary:function(t){return"0b"+t.toString(2)},octal:function(t){return"0"+t.toString(8)},decimal:function(t){return t.toString(10)},hexadecimal:function(t){return"0x"+t.toString(16).toUpperCase()}},defaultStyle:"decimal",styleAliases:{binary:[2,"bin"],octal:[8,"oct"],decimal:[10,"dec"],hexadecimal:[16,"hex"]}})},function(t,e,n){"use strict";function r(t){if(null===t)return!1;try{var e="("+t+")",n=a.parse(e,{range:!0});return"Program"===n.type&&1===n.body.length&&"ExpressionStatement"===n.body[0].type&&"FunctionExpression"===n.body[0].expression.type}catch(t){return!1}}function i(t){var e,n="("+t+")",r=a.parse(n,{range:!0}),i=[];if("Program"!==r.type||1!==r.body.length||"ExpressionStatement"!==r.body[0].type||"FunctionExpression"!==r.body[0].expression.type)throw new Error("Failed to resolve function");return r.body[0].expression.params.forEach(function(t){i.push(t.name)}),e=r.body[0].expression.body.range,new Function(i,n.slice(e[0]+1,e[1]-1))}function s(t){return t.toString()}function o(t){return"[object Function]"===Object.prototype.toString.call(t)}var a;try{a=n(173)}catch(t){"undefined"!=typeof window&&(a=window.esprima)}var u=n(0);t.exports=new u("tag:yaml.org,2002:js/function",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:s})},function(t,e,n){"use strict";function r(t){if(null===t)return!1;if(0===t.length)return!1;var e=t,n=/\/([gim]*)$/.exec(t),r="";if("/"===e[0]){if(n&&(r=n[1]),r.length>3)return!1;if("/"!==e[e.length-r.length-1])return!1}return!0}function i(t){var e=t,n=/\/([gim]*)$/.exec(t),r="";return"/"===e[0]&&(n&&(r=n[1]),e=e.slice(1,e.length-r.length-1)),new RegExp(e,r)}function s(t){var e="/"+t.source+"/";return t.global&&(e+="g"),t.multiline&&(e+="m"),t.ignoreCase&&(e+="i"),e}function o(t){return"[object RegExp]"===Object.prototype.toString.call(t)}var a=n(0);t.exports=new a("tag:yaml.org,2002:js/regexp",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:s})},function(t,e,n){"use strict";function r(){return!0}function i(){}function s(){return""}function o(t){return void 0===t}var a=n(0);t.exports=new a("tag:yaml.org,2002:js/undefined",{kind:"scalar",resolve:r,construct:i,predicate:o,represent:s})},function(t,e,n){"use strict";var r=n(0);t.exports=new r("tag:yaml.org,2002:map",{kind:"mapping",construct:function(t){return null!==t?t:{}}})},function(t,e,n){"use strict";function r(t){return"<<"===t||null===t}var i=n(0);t.exports=new i("tag:yaml.org,2002:merge",{kind:"scalar",resolve:r})},function(t,e,n){"use strict";function r(t){if(null===t)return!0;var e=t.length;return 1===e&&"~"===t||4===e&&("null"===t||"Null"===t||"NULL"===t)}function i(){return null}function s(t){return null===t}var o=n(0);t.exports=new o("tag:yaml.org,2002:null",{kind:"scalar",resolve:r,construct:i,predicate:s,represent:{canonical:function(){return"~"},lowercase:function(){return"null"},uppercase:function(){return"NULL"},camelcase:function(){return"Null"}},defaultStyle:"lowercase"})},function(t,e,n){"use strict";function r(t){if(null===t)return!0;var e,n,r,i,s,u=[],c=t;for(e=0,n=c.length;e<n;e+=1){if(r=c[e],s=!1,"[object Object]"!==a.call(r))return!1;for(i in r)if(o.call(r,i)){if(s)return!1;s=!0}if(!s)return!1;if(-1!==u.indexOf(i))return!1;u.push(i)}return!0}function i(t){return null!==t?t:[]}var s=n(0),o=Object.prototype.hasOwnProperty,a=Object.prototype.toString;t.exports=new s("tag:yaml.org,2002:omap",{kind:"sequence",resolve:r,construct:i})},function(t,e,n){"use strict";function r(t){if(null===t)return!0;var e,n,r,i,s,a=t;for(s=new Array(a.length),e=0,n=a.length;e<n;e+=1){if(r=a[e],"[object Object]"!==o.call(r))return!1;if(i=Object.keys(r),1!==i.length)return!1;s[e]=[i[0],r[i[0]]]}return!0}function i(t){if(null===t)return[];var e,n,r,i,s,o=t;for(s=new Array(o.length),e=0,n=o.length;e<n;e+=1)r=o[e],i=Object.keys(r),s[e]=[i[0],r[i[0]]];return s}var s=n(0),o=Object.prototype.toString;t.exports=new s("tag:yaml.org,2002:pairs",{kind:"sequence",resolve:r,construct:i})},function(t,e,n){"use strict";var r=n(0);t.exports=new r("tag:yaml.org,2002:seq",{kind:"sequence",construct:function(t){return null!==t?t:[]}})},function(t,e,n){"use strict";function r(t){if(null===t)return!0;var e,n=t;for(e in n)if(o.call(n,e)&&null!==n[e])return!1;return!0}function i(t){return null!==t?t:{}}var s=n(0),o=Object.prototype.hasOwnProperty;t.exports=new s("tag:yaml.org,2002:set",{kind:"mapping",resolve:r,construct:i})},function(t,e,n){"use strict";var r=n(0);t.exports=new r("tag:yaml.org,2002:str",{kind:"scalar",construct:function(t){return null!==t?t:""}})},function(t,e,n){"use strict";function r(t){return null!==t&&(null!==a.exec(t)||null!==u.exec(t))}function i(t){var e,n,r,i,s,o,c,h,l,p,f=0,d=null;if(e=a.exec(t),null===e&&(e=u.exec(t)),null===e)throw new Error("Date resolve error");if(n=+e[1],r=+e[2]-1,i=+e[3],!e[4])return new Date(Date.UTC(n,r,i));if(s=+e[4],o=+e[5],c=+e[6],e[7]){for(f=e[7].slice(0,3);f.length<3;)f+="0";f=+f}return e[9]&&(h=+e[10],l=+(e[11]||0),d=6e4*(60*h+l),"-"===e[9]&&(d=-d)),p=new Date(Date.UTC(n,r,i,s,o,c,f)),d&&p.setTime(p.getTime()-d),p}function s(t){return t.toISOString()}var o=n(0),a=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9])-([0-9][0-9])$"),u=new RegExp("^([0-9][0-9][0-9][0-9])-([0-9][0-9]?)-([0-9][0-9]?)(?:[Tt]|[ \\t]+)([0-9][0-9]?):([0-9][0-9]):([0-9][0-9])(?:\\.([0-9]*))?(?:[ \\t]*(Z|([-+])([0-9][0-9]?)(?::([0-9][0-9]))?))?$");t.exports=new o("tag:yaml.org,2002:timestamp",{kind:"scalar",resolve:r,construct:i,instanceOf:Date,represent:s})},function(t,e,n){"use strict";function r(t,e,n,r,i){}t.exports=r},function(t,e,n){"use strict";var r=n(200);t.exports=function(t){return r(t,!1)}},function(t,e,n){"use strict";var r=n(33),i=n(10),s=n(86);t.exports=function(){function t(t,e,n,r,o,a){a!==s&&i(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types")}function e(){return t}t.isRequired=t;var n={array:t,bool:t,func:t,number:t,object:t,string:t,symbol:t,any:t,arrayOf:e,element:t,instanceOf:e,node:t,objectOf:e,oneOf:e,oneOfType:e,shape:e};return n.checkPropTypes=r,n.PropTypes=n,n}},function(t,e,n){"use strict";var r=n(33),i=n(10),s=n(34),o=n(86),a=n(197);t.exports=function(t,e){function n(t){var e=t&&(S&&t[S]||t[w]);if("function"==typeof e)return e}function u(t,e){return t===e?0!==t||1/t==1/e:t!==t&&e!==e}function c(t){this.message=t,this.stack=""}function h(t){function n(n,r,s,a,u,h,l){if(a=a||b,h=h||s,l!==o)if(e)i(!1,"Calling PropTypes validators directly is not supported by the `prop-types` package. Use `PropTypes.checkPropTypes()` to call them. Read more at http://fb.me/use-check-prop-types");else;return null==r[s]?n?new c(null===r[s]?"The "+u+" `"+h+"` is marked as required in `"+a+"`, but its value is `null`.":"The "+u+" `"+h+"` is marked as required in `"+a+"`, but its value is `undefined`."):null:t(r,s,a,u,h)}var r=n.bind(null,!1);return r.isRequired=n.bind(null,!0),r}function l(t){function e(e,n,r,i,s,o){var a=e[n];if(g(a)!==t)return new c("Invalid "+i+" `"+s+"` of type `"+E(a)+"` supplied to `"+r+"`, expected `"+t+"`.");return null}return h(e)}function p(t){function e(e,n,r,i,s){if("function"!=typeof t)return new c("Property `"+s+"` of component `"+r+"` has invalid PropType notation inside arrayOf.");var a=e[n];if(!Array.isArray(a)){return new c("Invalid "+i+" `"+s+"` of type `"+g(a)+"` supplied to `"+r+"`, expected an array.")}for(var u=0;u<a.length;u++){var h=t(a,u,r,i,s+"["+u+"]",o);if(h instanceof Error)return h}return null}return h(e)}function f(t){function e(e,n,r,i,s){if(!(e[n]instanceof t)){var o=t.name||b;return new c("Invalid "+i+" `"+s+"` of type `"+C(e[n])+"` supplied to `"+r+"`, expected instance of `"+o+"`.")}return null}return h(e)}function d(t){function e(e,n,r,i,s){for(var o=e[n],a=0;a<t.length;a++)if(u(o,t[a]))return null;return new c("Invalid "+i+" `"+s+"` of value `"+o+"` supplied to `"+r+"`, expected one of "+JSON.stringify(t)+".")}return Array.isArray(t)?h(e):r.thatReturnsNull}function m(t){function e(e,n,r,i,s){if("function"!=typeof t)return new c("Property `"+s+"` of component `"+r+"` has invalid PropType notation inside objectOf.");var a=e[n],u=g(a);if("object"!==u)return new c("Invalid "+i+" `"+s+"` of type `"+u+"` supplied to `"+r+"`, expected an object.");for(var h in a)if(a.hasOwnProperty(h)){var l=t(a,h,r,i,s+"."+h,o);if(l instanceof Error)return l}return null}return h(e)}function x(t){function e(e,n,r,i,s){for(var a=0;a<t.length;a++){if(null==(0,t[a])(e,n,r,i,s,o))return null}return new c("Invalid "+i+" `"+s+"` supplied to `"+r+"`.")}if(!Array.isArray(t))return r.thatReturnsNull;for(var n=0;n<t.length;n++){var i=t[n];if("function"!=typeof i)return s(!1,"Invalid argument supplid to oneOfType. Expected an array of check functions, but received %s at index %s.",A(i),n),r.thatReturnsNull}return h(e)}function y(t){function e(e,n,r,i,s){var a=e[n],u=g(a);if("object"!==u)return new c("Invalid "+i+" `"+s+"` of type `"+u+"` supplied to `"+r+"`, expected `object`.");for(var h in t){var l=t[h];if(l){var p=l(a,h,r,i,s+"."+h,o);if(p)return p}}return null}return h(e)}function v(e){switch(typeof e){case"number":case"string":case"undefined":return!0;case"boolean":return!e;case"object":if(Array.isArray(e))return e.every(v);if(null===e||t(e))return!0;var r=n(e);if(!r)return!1;var i,s=r.call(e);if(r!==e.entries){for(;!(i=s.next()).done;)if(!v(i.value))return!1}else for(;!(i=s.next()).done;){var o=i.value;if(o&&!v(o[1]))return!1}return!0;default:return!1}}function D(t,e){return"symbol"===t||("Symbol"===e["@@toStringTag"]||"function"==typeof Symbol&&e instanceof Symbol)}function g(t){var e=typeof t;return Array.isArray(t)?"array":t instanceof RegExp?"object":D(e,t)?"symbol":e}function E(t){if(void 0===t||null===t)return""+t;var e=g(t);if("object"===e){if(t instanceof Date)return"date";if(t instanceof RegExp)return"regexp"}return e}function A(t){var e=E(t);switch(e){case"array":case"object":return"an "+e;case"boolean":case"date":case"regexp":return"a "+e;default:return e}}function C(t){return t.constructor&&t.constructor.name?t.constructor.name:b}var S="function"==typeof Symbol&&Symbol.iterator,w="@@iterator",b="<<anonymous>>",F={array:l("array"),bool:l("boolean"),func:l("function"),number:l("number"),object:l("object"),string:l("string"),symbol:l("symbol"),any:function(){return h(r.thatReturnsNull)}(),arrayOf:p,element:function(){function e(e,n,r,i,s){var o=e[n];if(!t(o)){return new c("Invalid "+i+" `"+s+"` of type `"+g(o)+"` supplied to `"+r+"`, expected a single ReactElement.")}return null}return h(e)}(),instanceOf:f,node:function(){function t(t,e,n,r,i){return v(t[e])?null:new c("Invalid "+r+" `"+i+"` supplied to `"+n+"`, expected a ReactNode.")}return h(t)}(),objectOf:m,oneOf:d,oneOfType:x,shape:y};return c.prototype=Error.prototype,F.checkPropTypes=a,F.PropTypes=F,F}},function(t,e){t.exports='---\nurl: "http://petstore.swagger.io/v2/swagger.json"\ndom_id: "#swagger-ui"\nvalidatorUrl: "https://online.swagger.io/validator"\noauth2RedirectUrl: "http://localhost:3200/oauth2-redirect.html"\n'},function(t,e,n){"use strict";function r(t){var e={"=":"=0",":":"=2"};return"$"+(""+t).replace(/[=:]/g,function(t){return e[t]})}function i(t){var e=/(=0|=2)/g,n={"=0":"=","=2":":"};return(""+("."===t[0]&&"$"===t[1]?t.substring(2):t.substring(1))).replace(e,function(t){return n[t]})}var s={escape:r,unescape:i};t.exports=s},function(t,e,n){"use strict";var r=n(37),i=(n(10),function(t){var e=this;if(e.instancePool.length){var n=e.instancePool.pop();return e.call(n,t),n}return new e(t)}),s=function(t,e){var n=this;if(n.instancePool.length){var r=n.instancePool.pop();return n.call(r,t,e),r}return new n(t,e)},o=function(t,e,n){var r=this;if(r.instancePool.length){var i=r.instancePool.pop();return r.call(i,t,e,n),i}return new r(t,e,n)},a=function(t,e,n,r){var i=this;if(i.instancePool.length){var s=i.instancePool.pop();return i.call(s,t,e,n,r),s}return new i(t,e,n,r)},u=function(t){var e=this;t instanceof e||r("25"),t.destructor(),e.instancePool.length<e.poolSize&&e.instancePool.push(t)},c=i,h=function(t,e){var n=t;return n.instancePool=[],n.getPooled=e||c,n.poolSize||(n.poolSize=10),n.release=u,n},l={addPoolingTo:h,oneArgumentPooler:i,twoArgumentPooler:s,threeArgumentPooler:o,fourArgumentPooler:a};t.exports=l},function(t,e,n){"use strict";var r=n(36),i=n(87),s=n(205),o=n(206),a=n(17),u=n(207),c=n(208),h=n(209),l=n(212),p=a.createElement,f=a.createFactory,d=a.cloneElement,m=r,x=function(t){return t},y={Children:{map:s.map,forEach:s.forEach,count:s.count,toArray:s.toArray,only:l},Component:i.Component,PureComponent:i.PureComponent,createElement:p,cloneElement:d,isValidElement:a.isValidElement,PropTypes:u,createClass:h,createFactory:f,createMixin:x,DOM:o,version:c,__spread:m};t.exports=y},function(t,e,n){"use strict";function r(t){return(""+t).replace(g,"$&/")}function i(t,e){this.func=t,this.context=e,this.count=0}function s(t,e,n){var r=t.func,i=t.context;r.call(i,e,t.count++)}function o(t,e,n){if(null==t)return t;var r=i.getPooled(e,n);y(t,s,r),i.release(r)}function a(t,e,n,r){this.result=t,this.keyPrefix=e,this.func=n,this.context=r,this.count=0}function u(t,e,n){var i=t.result,s=t.keyPrefix,o=t.func,a=t.context,u=o.call(a,e,t.count++);Array.isArray(u)?c(u,i,n,x.thatReturnsArgument):null!=u&&(m.isValidElement(u)&&(u=m.cloneAndReplaceKey(u,s+(!u.key||e&&e.key===u.key?"":r(u.key)+"/")+n)),i.push(u))}function c(t,e,n,i,s){var o="";null!=n&&(o=r(n)+"/");var c=a.getPooled(e,o,i,s);y(t,u,c),a.release(c)}function h(t,e,n){if(null==t)return t;var r=[];return c(t,r,null,e,n),r}function l(t,e,n){return null}function p(t,e){return y(t,l,null)}function f(t){var e=[];return c(t,e,null,x.thatReturnsArgument),e}var d=n(203),m=n(17),x=n(33),y=n(213),v=d.twoArgumentPooler,D=d.fourArgumentPooler,g=/\/+/g;i.prototype.destructor=function(){this.func=null,this.context=null,this.count=0},d.addPoolingTo(i,v),a.prototype.destructor=function(){this.result=null,this.keyPrefix=null,this.func=null,this.context=null,this.count=0},d.addPoolingTo(a,D);var E={forEach:o,map:h,mapIntoWithKeyPrefixInternal:c,count:p,toArray:f};t.exports=E},function(t,e,n){"use strict";var r=n(17),i=r.createFactory,s={a:i("a"),abbr:i("abbr"),address:i("address"),area:i("area"),article:i("article"),aside:i("aside"),audio:i("audio"),b:i("b"),base:i("base"),bdi:i("bdi"),bdo:i("bdo"),big:i("big"),blockquote:i("blockquote"),body:i("body"),br:i("br"),button:i("button"),canvas:i("canvas"),caption:i("caption"),cite:i("cite"),code:i("code"),col:i("col"),colgroup:i("colgroup"),data:i("data"),datalist:i("datalist"),dd:i("dd"),del:i("del"),details:i("details"),dfn:i("dfn"),dialog:i("dialog"),div:i("div"),dl:i("dl"),dt:i("dt"),em:i("em"),embed:i("embed"),fieldset:i("fieldset"),figcaption:i("figcaption"),figure:i("figure"),footer:i("footer"),form:i("form"),h1:i("h1"),h2:i("h2"),h3:i("h3"),h4:i("h4"),h5:i("h5"),h6:i("h6"),head:i("head"),header:i("header"),hgroup:i("hgroup"),hr:i("hr"),html:i("html"),i:i("i"),iframe:i("iframe"),img:i("img"),input:i("input"),ins:i("ins"),kbd:i("kbd"),keygen:i("keygen"),label:i("label"),legend:i("legend"),li:i("li"),link:i("link"),main:i("main"),map:i("map"),mark:i("mark"),menu:i("menu"),menuitem:i("menuitem"),meta:i("meta"),meter:i("meter"),nav:i("nav"),noscript:i("noscript"),object:i("object"),ol:i("ol"),optgroup:i("optgroup"),option:i("option"),output:i("output"),p:i("p"),param:i("param"),picture:i("picture"),pre:i("pre"),progress:i("progress"),q:i("q"),rp:i("rp"),rt:i("rt"),ruby:i("ruby"),s:i("s"),samp:i("samp"),script:i("script"),section:i("section"),select:i("select"),small:i("small"),source:i("source"),span:i("span"),strong:i("strong"),style:i("style"),sub:i("sub"),summary:i("summary"),sup:i("sup"),table:i("table"),tbody:i("tbody"),td:i("td"),textarea:i("textarea"),tfoot:i("tfoot"),th:i("th"),thead:i("thead"),time:i("time"),title:i("title"),tr:i("tr"),track:i("track"),u:i("u"),ul:i("ul"),var:i("var"),video:i("video"),wbr:i("wbr"),circle:i("circle"),clipPath:i("clipPath"),defs:i("defs"),ellipse:i("ellipse"),g:i("g"),image:i("image"),line:i("line"),linearGradient:i("linearGradient"),mask:i("mask"),path:i("path"),pattern:i("pattern"),polygon:i("polygon"),polyline:i("polyline"),radialGradient:i("radialGradient"),rect:i("rect"),stop:i("stop"),svg:i("svg"),text:i("text"),tspan:i("tspan")};t.exports=s},function(t,e,n){"use strict";var r=n(17),i=r.isValidElement,s=n(198);t.exports=s(i)},function(t,e,n){"use strict";t.exports="15.6.1"},function(t,e,n){"use strict";var r=n(87),i=r.Component,s=n(17),o=s.isValidElement,a=n(90),u=n(172);t.exports=u(i,o,a)},function(t,e,n){"use strict";function r(t){var e=t&&(i&&t[i]||t[s]);if("function"==typeof e)return e}var i="function"==typeof Symbol&&Symbol.iterator,s="@@iterator";t.exports=r},function(t,e,n){"use strict";var r=function(){};t.exports=r},function(t,e,n){"use strict";function r(t){return s.isValidElement(t)||i("143"),t}var i=n(37),s=n(17);n(10);t.exports=r},function(t,e,n){"use strict";function r(t,e){return t&&"object"==typeof t&&null!=t.key?c.escape(t.key):e.toString(36)}function i(t,e,n,s){var p=typeof t;if("undefined"!==p&&"boolean"!==p||(t=null),null===t||"string"===p||"number"===p||"object"===p&&t.$$typeof===a)return n(s,t,""===e?h+r(t,0):e),1;var f,d,m=0,x=""===e?h:e+l;if(Array.isArray(t))for(var y=0;y<t.length;y++)f=t[y],d=x+r(f,y),m+=i(f,d,n,s);else{var v=u(t);if(v){var D,g=v.call(t);if(v!==t.entries)for(var E=0;!(D=g.next()).done;)f=D.value,d=x+r(f,E++),m+=i(f,d,n,s);else for(;!(D=g.next()).done;){var A=D.value;A&&(f=A[1],d=x+c.escape(A[0])+l+r(f,0),m+=i(f,d,n,s))}}else if("object"===p){var C="",S=String(t);o("31","[object Object]"===S?"object with keys {"+Object.keys(t).join(", ")+"}":S,C)}}return m}function s(t,e,n){return null==t?0:i(t,"",e,n)}var o=n(37),a=(n(88),n(89)),u=n(210),c=(n(10),n(202)),h=(n(34),"."),l=":";t.exports=s},function(t,e){t.exports="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAB4AAAAeCAYAAAA7MK6iAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJbWFnZVJlYWR5ccllPAAAAqRJREFUeNrEVz1s00AUfnGXii5maMXoEUEHVwIpEkPNgkBdMnQoU5ytiKHJwpp2Q2JIO8DCUDOxIJFIVOoWZyJSh3pp1Q2PVVlcCVBH3ufeVZZ9Zye1Ay86nXV+ue/9fO/lheg/Se02X1rvksmbnTiKvuxQMBNgBnN4a/LCbmnUAP6JV58NCUsBC8CuAJxGPF47OgNqBaA93tolUhnx6jC4NxGwyOEwlccyAs+3kwdzKq0HDn2vEBTi8J2XpyMaywNDE157BhXUE3zJhlq8GKq+Zd2zaWHepPA8oN9XkfLmRdOiJV4XUUg/IyWncLjCYY/SHndV2u7zHr3bPKZtdxgboJOnthvrfGj/oMf3G0r7JVmNlLfKklmrt2MvvcNO7LFOhoFHfuAJI5o6ta10jpt5CQLgwXhXG2YIwvu+34qf78ybOjWTnWwkgR36d7JqJOrW0hHmNrKg9xhiS4+1jFmrxymh03B0w+6kURIAu3yHtOD5oaUNojMnGgbcctNvwdAnyxvxRR+/vaJnjzbpzcZX+nN1SdGv85i9eH8w3qPO+mdm/y4dnQ1iI8Fq6Nf4cxL6GWSjiFDSs0VRnxC5g0xSB2cgHpaseTxfqOv5uoHkNQ6Ha/N1Yz9mNMppEkEkYKj79q6uCq4bCHcSX3fJ0Vk/k9siASjCm1N6gZH6Ec9IXt2WkFES2K/ixoIyktJPAu/ptOA1SgO5zqtr6KASJPF0nMV8dgMsRhRPOcMwqQAOoi0VAIMLAEWJ6YYC1c8ibj1GP51RqwzYwZVMHQuvOzMCBUtb2tGHx5NAdLKqp5AX7Ng4d+Zi8AGDI9z1ijx9yaCH04y3GCP2S+QcvaGl+pcxyUBvinFlawoDQjHSelX8hQEoIrAq8p/mgC88HOS1YCl/BRgAmiD/1gn6Nu8AAAAASUVORK5CYII="},function(t,e){var n;n=function(){return this}();try{n=n||Function("return this")()||(0,eval)("this")}catch(t){"object"==typeof window&&(n=window)}t.exports=n},function(t,e,n){n(93),t.exports=n(94)}])});
//# sourceMappingURL=swagger-ui-standalone-preset.js.map