package cn.ucmed.transfer;

import cn.ucmed.utils.HttpClientUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ucmed.bean.NUCResponse;
import com.ucmed.bean.UCResponse;
import com.ucmed.bean.retbean.UserBindInfoVO;
import com.ucmed.thirdparty.ThirdPartyService;
import org.springframework.stereotype.Service;

import java.util.List;

import static cn.ucmed.constant.UserCenterConstant.USER_CENTER_URL;
@Service
public class ThirdPartyServiceImpl implements ThirdPartyService {
    @Override
    public UCResponse bind(String phone, String openId, int appCode, String roleName, String thirdPartyType) {
        String url = USER_CENTER_URL + "/thirdParty/bindAndRegistration?openId=" + openId
                + "&appCode=" + appCode + "&phone=" + phone + "&roleName=" + roleName + "&thirdPartyType=" + thirdPartyType;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse login(String openId, int appCode, String roleName) {
        String url = USER_CENTER_URL + "/thirdParty/login?openId=" + openId
                + "&appCode=" + appCode + "&roleName=" + roleName;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse bind(String id, int appCode, String roleName, String thirdPartyType) {
        String url = USER_CENTER_URL + "/thirdParty/bind?openId=" + id
                + "&appCode=" + appCode + "&roleName=" + roleName + "&thirdPartyType=" + thirdPartyType;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse bind(String phone, String openId, int appCode, String roleName, String thirdPartyType, String msgCode) {
        String url = USER_CENTER_URL + "/thirdParty/bindAndRegistrationByMsgCode?openId=" + openId
                + "&appCode=" + appCode + "&roleName=" + roleName + "&thirdPartyType=" + thirdPartyType
                + "&phone=" + phone + "&msgCode=" + msgCode;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse changePhone(String phone, String openId, int appCode, String roleName, String thirdPartyType) {
        String url = USER_CENTER_URL + "/thirdParty/changeBindPhone?openId=" + openId
                + "&appCode=" + appCode + "&roleName=" + roleName + "&thirdPartyType=" + thirdPartyType
                + "&phone=" + phone;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse unBind(String openId, String phone, String thirdPartyType) {
        String url = USER_CENTER_URL + "/thirdParty/unBind?openId=" + openId
                + "&thirdPartyType=" + thirdPartyType
                + "&phone=" + phone;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse bindIgnorePhone(String phone, String openId, int appCode, String roleName, String thirdPartyType) {
        String url = USER_CENTER_URL + "/thirdParty/ocBindAndRegistration?openId=" + openId
                + "&thirdPartyType=" + thirdPartyType + "&roleName=" + roleName + "&appCode=" + appCode
                + "&phone=" + phone;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public NUCResponse<List<UserBindInfoVO>> getBindInfo(String userIdOrPhone, String appCodes) {
        String url = USER_CENTER_URL + "/thirdParty/getUserBindInfo?appCodes=" + appCodes
                + "&userIdOrPhone=" + userIdOrPhone;
        return JSONObject.parseObject(JSON.toJSONString(HttpClientUtils.doGet(url, new JSONObject(), null, NUCResponse.class)), new TypeReference<NUCResponse<List<UserBindInfoVO>>>() {
        });
    }
}
