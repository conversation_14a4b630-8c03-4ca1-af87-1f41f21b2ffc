package com.ucmed.message.model;

import javax.persistence.*;
import java.util.Date;

@Table(name = "sms_gateway")
public class PSmsGateway {
    /**
     * 主键
     */
    @Id
    @Column(name = "sms_gateway_id")
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private String smsGatewayId;

    /**
     * 项目号
     */
    @Column(name = "app_code")
    private String appCode;


    /**
     * 协议类型 默认http
     */
    @Column(name = "protocol_type")
    private String protocolType;

    /**
     * 短信平台 token
     */
    @Column(name = "platsms_token")
    private String platsmsToken;


    /**
     * 短信网关地址
     */
    private String url;

    /**
     * 短信厂商名称
     */
    @Column(name = "sms_name")
    private String smsName;

    /**
     * 短信拥有单位
     */
    private String owner;

    /**
     * 创建者
     */
    private String createdby;

    /**
     * 创建时间
     */
    private Date createdon;

    /**
     * 修改者
     */
    private String modifiedby;

    /**
     * 最后修改时间
     */
    private Date modifiedon;

    /**
     * 删除状态,0未删除，1已删除
     */
    @Column(name = "deletion_state")
    private String deletionState;

    /**
     * 排序，越大越排前
     */
    private Integer seq;

    /**
     * 备注
     */
    private String description;

    /**
     * 获取主键
     *
     * @return sms_gateway_id - 主键
     */
    public String getSmsGatewayId() {
        return smsGatewayId;
    }

    /**
     * 设置主键
     *
     * @param smsGatewayId 主键
     */
    public void setSmsGatewayId(String smsGatewayId) {
        this.smsGatewayId = smsGatewayId;
    }

    /**
     * 获取项目号
     *
     * @return project_id - 项目号
     */
    public String getAppCode() {
        return appCode;
    }

    /**
     * 设置项目号
     *
     * @param appCode 项目号
     */
    public void setAppCode(String appCode) {
        this.appCode = appCode;
    }

    public String getProtocolType() {
        return protocolType;
    }

    public void setProtocolType(String protocolType) {
        this.protocolType = protocolType;
    }

    public String getPlatsmsToken() {
        return platsmsToken;
    }

    public void setPlatsmsToken(String platsmsToken) {
        this.platsmsToken = platsmsToken;
    }

    /**
     * 获取短信网关地址
     *
     * @return url - 短信网关地址
     */
    public String getUrl() {
        return url;
    }

    /**
     * 设置短信网关地址
     *
     * @param url 短信网关地址
     */
    public void setUrl(String url) {
        this.url = url;
    }

    /**
     * 获取短信厂商名称
     *
     * @return sms_name - 短信厂商名称
     */
    public String getSmsName() {
        return smsName;
    }

    /**
     * 设置短信厂商名称
     *
     * @param smsName 短信厂商名称
     */
    public void setSmsName(String smsName) {
        this.smsName = smsName;
    }

    /**
     * 获取短信拥有单位
     *
     * @return owner - 短信拥有单位
     */
    public String getOwner() {
        return owner;
    }

    /**
     * 设置短信拥有单位
     *
     * @param owner 短信拥有单位
     */
    public void setOwner(String owner) {
        this.owner = owner;
    }

    /**
     * 获取创建者
     *
     * @return createdby - 创建者
     */
    public String getCreatedby() {
        return createdby;
    }

    /**
     * 设置创建者
     *
     * @param createdby 创建者
     */
    public void setCreatedby(String createdby) {
        this.createdby = createdby;
    }

    /**
     * 获取创建时间
     *
     * @return createdon - 创建时间
     */
    public Date getCreatedon() {
        return createdon;
    }

    /**
     * 设置创建时间
     *
     * @param createdon 创建时间
     */
    public void setCreatedon(Date createdon) {
        this.createdon = createdon;
    }

    /**
     * 获取修改者
     *
     * @return modifiedby - 修改者
     */
    public String getModifiedby() {
        return modifiedby;
    }

    /**
     * 设置修改者
     *
     * @param modifiedby 修改者
     */
    public void setModifiedby(String modifiedby) {
        this.modifiedby = modifiedby;
    }

    /**
     * 获取最后修改时间
     *
     * @return modifiedon - 最后修改时间
     */
    public Date getModifiedon() {
        return modifiedon;
    }

    /**
     * 设置最后修改时间
     *
     * @param modifiedon 最后修改时间
     */
    public void setModifiedon(Date modifiedon) {
        this.modifiedon = modifiedon;
    }

    /**
     * 获取删除状态,0未删除，1已删除
     *
     * @return deletion_state - 删除状态,0未删除，1已删除
     */
    public String getDeletionState() {
        return deletionState;
    }

    /**
     * 设置删除状态,0未删除，1已删除
     *
     * @param deletionState 删除状态,0未删除，1已删除
     */
    public void setDeletionState(String deletionState) {
        this.deletionState = deletionState;
    }

    /**
     * 获取排序，越大越排前
     *
     * @return seq - 排序，越大越排前
     */
    public Integer getSeq() {
        return seq;
    }

    /**
     * 设置排序，越大越排前
     *
     * @param seq 排序，越大越排前
     */
    public void setSeq(Integer seq) {
        this.seq = seq;
    }

    /**
     * 获取备注
     *
     * @return description - 备注
     */
    public String getDescription() {
        return description;
    }

    /**
     * 设置备注
     *
     * @param description 备注
     */
    public void setDescription(String description) {
        this.description = description;
    }
}