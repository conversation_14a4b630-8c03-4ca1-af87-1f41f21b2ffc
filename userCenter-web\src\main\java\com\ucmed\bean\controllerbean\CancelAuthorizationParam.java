package com.ucmed.bean.controllerbean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/01/02 14:16
 */
@Data
public class CancelAuthorizationParam {
    @ApiModelProperty(value="项目id", name="project_id", required = true, example="0")
    private Integer project_id;
    @ApiModelProperty(value="手机号", name="phone", required = true, example="string")
    private String phone;
    @ApiModelProperty(value="应用名称", name="app_name", required = true, example="string")
    private String app_name;
    @ApiModelProperty(value="来源", name="source", required = true, example="string")
    private String source;
    @ApiModelProperty(value="时间戳", name="timestamp", required = true, example="0")
    private Long timestamp;
    @ApiModelProperty(value="签名喔", name="sign", required = true, example="string")
    private String sign;
}
