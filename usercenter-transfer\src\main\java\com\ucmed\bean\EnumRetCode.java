package com.ucmed.bean;

import cn.ucmed.common.constants.GlobalConstants;
import org.apache.commons.lang.StringUtils;

public enum EnumRetCode {
    /**
     *
     */
    SUCCESS(0, "操作成功"),
    PASSWORDERROR(1, "密码错误"),
    USERLOCKED(2, "用户被锁定，请联系管理员建议使用大小写字母、数字和特殊字符"),
    SIMPLEPASSWORD(3, "密码太简单,建议使用大小写字母、数字和特殊字符"),
    INVALIDUSERID(4, "用户名不合法"),
    INVALIDPHONE(5, "手机号不合法"),
    USERNOTINWHITELIST(6, "此用户不在白名单内，无法注册"),
    USERINBLACKLIST(7, "此用户在黑名单内，无法注册"),
    VALIDATIONMSGCODEERROR(40009, "短信验证码错误或已过期，验证短信验证码失败"),
    VALIDATIONPICCODEERROR(40010, "图形验证码错误或已过期，验证图形验证码失败"),
    USERINFOERROR(40011, "用户信息错误"),
    PHONEERROR(-1, "手机号错误"),
    OLDPASSWORDERROR(40013, "修改密码失败，旧密码错误"),
    TYPEERROR(40014,"type类型错误"),
    THIRD_PARTY_TYPE_ERROR(40015, "thirdPartyType类型错误"),
    INVALIDEMAIL(8, "邮箱格式不合法"),
    NOTSAMEBINDTYPE(40016, "绑定的第三方type与该第三方type不一致"),
    NOTSAMEBINDPHONE(40016, "第三方用户绑定的账号与该手机号不一致"),
    NULLUSER(3, "用户未注册"),
    USERAPPNULLPERMISSION(41002, "用户未授权该应用，是否继续？"),
    USERROLENULLPERMISSION(41003, "用户未授权该角色，是否继续？"),
    USERNULLBIND(41004, "该第三方账号未绑定任何账号"),
    USERREGISTERED(1, "用户已注册，请使用原账号密码登录"),
    PHONEEGISTERED(42002, "手机号已注册"),
    USERHASBIND(42003,"该第三方账号已绑定账号"),
    BINDSAMEUSERERROR(42004, "请勿重复绑定同一个账号"),
    SERVICEERROR(50000, "服务器错误"),
    NULLAPP(-1, "应用未注册"),
    NULLROLE(-3, "角色不存在"),
    INVALIDTOKEN(401, "会话已失效"),
    BINDERROR(50004,"绑定失败"),
    NULLOPENID(401, "openId不存在"),
    GETMSGERROR(50006, "获取短信验证码失败"),
    PICCODEOUTTIME(50007, "图形验证码过期，请刷新后重试"),
    USER_PWD_ERROR_TIMES_CODE(40001, "您的账号密码输入错误次数过多，已被锁定，请在1小时后再试"),
    ;


    private int key;
    private String value;
    EnumRetCode(int key, String value) {
            this.key = key;
            this.value = value;
    }

    public int getKey() {
        return key;
    }

    public String getValue() {
        return value;
    }

    public static String getValue(int key) {
        for (EnumRetCode type : values()) {
            if (type.key == key) {
                return type.value;
            }
        }
        return StringUtils.EMPTY;
    }
}
