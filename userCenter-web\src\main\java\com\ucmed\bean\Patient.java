package com.ucmed.bean;

public class Patient {
	private int patient_id;
	private String user_id;
	private String patient_name;
	private String patient_sex;
	private String patient_birthday;
	private String patient_card_type;
	private String patient_card_id;
	private String patient_medicare;
	private String patient_medicare_id;
	private String patient_city_id;
	private String patient_address;
	private String patient_w_chat;
	private String patient_mobile;
	private String patient_e_mail;
	private String create_time;
	private String update_time;
	
	public int getPatient_id() {
		return patient_id;
	}
	public void setPatient_id(int patient_id) {
		this.patient_id = patient_id;
	}
	public String getUser_id() {
		return user_id;
	}
	public void setUser_id(String user_id) {
		this.user_id = user_id;
	}
	public String getPatient_name() {
		return patient_name;
	}
	public void setPatient_name(String patient_name) {
		this.patient_name = patient_name;
	}
	public String getPatient_sex() {
		return patient_sex;
	}
	public void setPatient_sex(String patient_sex) {
		this.patient_sex = patient_sex;
	}
	public String getPatient_birthday() {
		return patient_birthday;
	}
	public void setPatient_birthday(String patient_birthday) {
		this.patient_birthday = patient_birthday;
	}
	public String getPatient_card_type() {
		return patient_card_type;
	}
	public void setPatient_card_type(String patient_card_type) {
		this.patient_card_type = patient_card_type;
	}
	public String getPatient_card_id() {
		return patient_card_id;
	}
	public void setPatient_card_id(String patient_card_id) {
		this.patient_card_id = patient_card_id;
	}
	public String getPatient_medicare() {
		return patient_medicare;
	}
	public void setPatient_medicare(String patient_medicare) {
		this.patient_medicare = patient_medicare;
	}
	public String getPatient_medicare_id() {
		return patient_medicare_id;
	}
	public void setPatient_medicare_id(String patient_medicare_id) {
		this.patient_medicare_id = patient_medicare_id;
	}
	public String getPatient_address() {
		return patient_address;
	}
	public void setPatient_address(String patient_address) {
		this.patient_address = patient_address;
	}
	public String getPatient_w_chat() {
		return patient_w_chat;
	}
	public void setPatient_w_chat(String patient_w_chat) {
		this.patient_w_chat = patient_w_chat;
	}
	public String getPatient_mobile() {
		return patient_mobile;
	}
	public void setPatient_mobile(String patient_mobile) {
		this.patient_mobile = patient_mobile;
	}
	public String getPatient_e_mail() {
		return patient_e_mail;
	}
	public void setPatient_e_mail(String patient_e_mail) {
		this.patient_e_mail = patient_e_mail;
	}
	public String getCreate_time() {
		return create_time;
	}
	public void setCreate_time(String create_time) {
		this.create_time = create_time;
	}
	public String getUpdate_time() {
		return update_time;
	}
	public void setUpdate_time(String update_time) {
		this.update_time = update_time;
	}
	public String getPatient_city_id() {
		return patient_city_id;
	}
	public void setPatient_city_id(String patient_city_id) {
		this.patient_city_id = patient_city_id;
	}
}
