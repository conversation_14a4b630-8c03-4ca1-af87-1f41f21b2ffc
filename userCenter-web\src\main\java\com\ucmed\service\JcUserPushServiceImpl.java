package com.ucmed.service;

import com.ucmed.bean.JcUserPush;
import com.ucmed.mapper.JcUserPushMapper;
import com.ucmed.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.UUID;

@Service
public class JcUserPushServiceImpl implements JcUserPushService {

    @Autowired
    private JcUserPushMapper jcUserPushMapper;

    @Override
    public int insert(String openId, String userId, int appCode, String pushId) {
        JcUserPush userPush = new JcUserPush();
        userPush.setUserPushId(UUID.randomUUID().toString());
        userPush.setOpenId(openId);
        userPush.setPushId(pushId);
        userPush.setAppCode(appCode);
        userPush.setCreatedby(userId);
        userPush.setModifiedby(userId);
        userPush.setDeletionState("0");
        userPush.setCreatedon(TimeUtil.getCurrentTime());
        userPush.setModifiedon(TimeUtil.getCurrentTime());
        return jcUserPushMapper.insert(userPush);
    }

    @Override
    public void update(JcUserPush jcUserPush) {
        jcUserPushMapper.updateByPrimaryKey(jcUserPush);
    }

    @Override
    public JcUserPush selectByOpenId(String OpenId) {
        return jcUserPushMapper.selectByOpenId(OpenId);
    }

    @Override
    public void updateByOpenId(String pushId, String modifiedon, String openId) {
        jcUserPushMapper.updateByOpenId(pushId, modifiedon, openId);
    }
}
