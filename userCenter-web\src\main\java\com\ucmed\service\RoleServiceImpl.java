package com.ucmed.service;

import com.ucmed.bean.Role;
import com.ucmed.bean.UC_UserInfo;
import com.ucmed.common.service.CommonService;
import com.ucmed.dao.SecurityRoleDao;
import com.ucmed.util.JsonFormat;
import com.ucmed.util.TimeUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.ucmed.common.constant.CommonConstant.*;

/**
 * Created by HUANGYIXIN on 2016/11/15.
 */
@Service
public class RoleServiceImpl extends CommonService implements RoleService {

    @Autowired
    private SecurityRoleDao securityRoleDao;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 添加角色
     *
     * @param rcv
     */
    @Override
    public String addRole(JSONObject rcv) {
        Role role = new Role();
        role.setAppCode(rcv.getInt(APPCODE));
        role.setRoleName(rcv.getString(ROLENAME));
        role.setRoleDesc(rcv.getString(ROLEDESC));
        role.setValid("1");
        role.setOperUser(rcv.getString(APPCODE));
        role.setOperDate(TimeUtil.getCurrentTime());
        int ret = securityRoleDao.addRole(role);
        if (ret == 1) {
            return JsonFormat.retFormat(0, "添加角色成功");
        } else {
            return JsonFormat.retFormat(1, "添加角色失败");
        }
    }

    /**
     * 更新角色信息
     *
     * @param rcv
     */
    @Override
    public String updateRole(JSONObject rcv) {
        Role role = new Role();
        role.setRoleId(rcv.getInt(ROLEID));
        role.setAppCode(rcv.getInt(APPCODE));
        role.setRoleName(rcv.getString(ROLENAME));
        role.setRoleDesc(rcv.getString(ROLEDESC));
        role.setValid("1");
        role.setOperUser(rcv.getString(APPCODE));
        role.setOperDate(TimeUtil.getCurrentTime());
        int ret = securityRoleDao.updateRole(role);
        if (ret == 1) {
            return JsonFormat.retFormat(0, "更新角色成功");
        } else {
            return JsonFormat.retFormat(1, "更新角色失败");
        }

    }

    /**
     * 删除角色
     *
     * @param rcv
     */
    @Override
    public String deleteRole(JSONObject rcv) {
        int ret = securityRoleDao.deleteRoleByRoleId(rcv.getInt(ROLEID));
        if (ret == 1) {
            return JsonFormat.retFormat(0, "删除角色成功");
        } else {
            return JsonFormat.retFormat(1, "删除角色失败");
        }
    }

    /**
     * 根据appcode查询所有角色信息
     *
     * @param rcv
     * @return
     */
    @Override
    public String queryRole(JSONObject rcv) {
        List<Role> listRole = securityRoleDao.queryRoleByAppCode(rcv.getInt(APPCODE));
        if (listRole.size() == 0)
            return JsonFormat.retFormat(1, "该应用下没有角色");
        else {
            return JsonFormat.retFormat(0, "查询成功", JSONArray.fromObject(listRole));
        }
    }

    @Override
    public List<Role> queryUserRole(String userId, int appCode) {
        List<Role> listRole = securityRoleDao.queryUserRole(userId, appCode);
        if (listRole.size() == 0)
            return null;
        else {
            return listRole;
        }
    }

    @Override
    public List<Role> listByAppCode(int appCode) {
        List<Role> roles = (List<Role>) redisTemplate.opsForValue().get(ROLES_CACHE + appCode);
        if (roles == null) {
            roles = securityRoleDao.queryRoleByAppCode(appCode);
            redisTemplate.opsForValue().set(ROLES_CACHE + appCode, roles, 1, TimeUnit.DAYS);
        }
        return roles;
    }

    @Override
    public boolean isExists(int appCode, String roleName) {
        List<Role> roles = listByAppCode(appCode);
        for (Role role : roles) {
            if (roleName.equals(role.getRoleName())) {
                return true;
            }
        }
        return false;
    }

    @Override
    public Role getRoleById(int roleId) {
        return securityRoleDao.getRoleByRoleId(roleId);
    }

    @Override
    public Role getRoleByAppCodeAndName(int appCode, String roleName) {
        List<Role> roles = listByAppCode(appCode);
        for (Role role:roles) {
            if (roleName.equals(role.getRoleName())) {
                return role;
            }
        }
        return null;
    }
}
