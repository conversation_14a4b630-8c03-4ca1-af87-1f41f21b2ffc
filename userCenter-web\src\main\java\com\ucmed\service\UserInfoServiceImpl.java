package com.ucmed.service;

import com.ucmed.bean.*;
import com.ucmed.common.constant.UserInfoConstant;
import com.ucmed.common.service.CommonService;
import com.ucmed.dao.UserDao;
import com.ucmed.dto.UserDTO;
import com.ucmed.dto.UserInfoExcelDTO;
import com.ucmed.dto.UserProjectDTO;
import com.ucmed.exception.BusinessException;
import com.ucmed.mapper.JcUserInfoMapper;
import com.ucmed.transfer.UserServiceImpl;
import com.ucmed.util.*;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.ucmed.common.constant.CommonConstant.*;

/**
 * 用户信息服务实现类
 * Created by QIUJIAHAO on 2016/9/18.
 */
@Service
public class UserInfoServiceImpl extends CommonService implements UserInfoService {
    private static Logger log4j = Logger.getLogger(UserInfoServiceImpl.class.getName());

    @Autowired
    private YLXZService ylxzService;
    @Autowired
    private UserDao userDao;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private JcUserInfoMapper jcUserInfoMapper;
    @Autowired
    private RocketMQUtil rocketMQUtil;

    /**
     * 根据user_id返回用户信息
     */
    @Override
    @Transactional
    public String getUserInfo(JSONObject rcv) {

        // 移动远程医生用户
        if (isRemoteMedical(rcv)) {
            return transRetStr(JSONObject.fromObject(ylxzService.getUserInfo(rcv)));
        }

        String user_id = rcv.getString("user_id");
        UC_UserInfo user = getUser(user_id);
        //判断用户是否存在
        if (user == null) {
            return JsonFormat.retFormat(1, "账号未注册");
        }
        user_id = user.getUser_id();
        UserInfo userInfo = getJcUserInfoDao().getUserInfoByUserId(user_id);
        if (userInfo == null) {
            return JsonFormat.retFormat(2, "无个人信息");
        }
        /*解密*/
        if (!"".equals(userInfo.getCard_id()) && userInfo.getCard_id() != null) {
            userInfo.setCard_id(ThreeDESUtil.get3DESDecrypt(userInfo.getCard_id(), "zjkjxxb"));
        }
        if (!"".equals(userInfo.getMobile()) && userInfo.getMobile() != null) {
            userInfo.setMobile(ThreeDESUtil.get3DESDecrypt(userInfo.getMobile(), "zjkjxxb"));
        }
        if (!"".equals(userInfo.getMedicare_id()) && userInfo.getMedicare_id() != null) {
            userInfo.setMedicare_id(ThreeDESUtil.get3DESDecrypt(userInfo.getMedicare_id(), "zjkjxxb"));
        }
        int uid = user.getUid();//获取UID
        userInfo.setId(uid);
        return JsonFormat.retFormat(0, "查询成功", JSONObject.fromObject(userInfo));
    }

    @Override
    public UserInfo getUserInfo(String token) throws BusinessException {
        String userId = verifyToken(token);
        if (userId == null) {
            throw new BusinessException(401, "会话已失效");
        }
        UserInfo userInfo = (UserInfo) redisTemplate.opsForValue().get(USERINFO_CACHE + userId);
        if (userInfo == null) {
            userInfo = getJcUserInfoDao().getUserInfoByUserId(userId);
            redisTemplate.opsForValue().set(USERINFO_CACHE + userId, userInfo, 1, TimeUnit.DAYS);
        }
        if (!"".equals(userInfo.getMobile()) && userInfo.getMobile() != null) {
            String phone = ThreeDESDecrypt(userInfo.getMobile());
            // 区分华侨手机号，将华侨手机号显示初始化
            phone = (phone != null && phone.startsWith(OC_PHONE_MARK)) ?
                    phone.substring(OC_PHONE_MARK.length(), phone.length()) : phone;
            userInfo.setMobile(phone);
        }
        if (!"".equals(userInfo.getCard_id()) && userInfo.getCard_id() != null) {
            userInfo.setCard_id(ThreeDESDecrypt(userInfo.getCard_id()));
        }
        if (!"".equals(userInfo.getMedicare_id()) && userInfo.getMedicare_id() != null) {
            userInfo.setMedicare_id(ThreeDESDecrypt(userInfo.getMedicare_id()));
        }
        return userInfo;
    }

    @Override
    public UserInfo getUserInfoByUserId(String userId) throws BusinessException {
        UserInfo userInfo = (UserInfo) redisTemplate.opsForValue().get(USERINFO_CACHE + userId);
        if (userInfo == null) {
            userInfo = getJcUserInfoDao().getUserInfoByUserId(userId);
            if (userInfo == null) {
                throw new BusinessException(1, "用户未注册");
            } else {
                redisTemplate.opsForValue().set(USERINFO_CACHE + userId, userInfo, 1, TimeUnit.DAYS);
            }
        }
        if (!"".equals(userInfo.getMobile()) && userInfo.getMobile() != null) {
            userInfo.setMobile(ThreeDESDecrypt(userInfo.getMobile()));
        }
        if (!"".equals(userInfo.getCard_id()) && userInfo.getCard_id() != null) {
            userInfo.setCard_id(ThreeDESDecrypt(userInfo.getCard_id()));
        }
        if (!"".equals(userInfo.getMedicare_id()) && userInfo.getMedicare_id() != null) {
            userInfo.setMedicare_id(ThreeDESDecrypt(userInfo.getMedicare_id()));
        }
        return userInfo;
    }

    /**
     * 更新用户信息
     */
    @Override
    @Transactional
    public String updateUserInfo(JSONObject rcv) {
        String token = rcv.getString(TOKEN);
        String user_id = rcv.getString(USERID);
        String currentTime = TimeUtil.getCurrentTime();

        UC_UserInfo user = getUser(user_id);
        //账号是否存在
        if (user == null) {
            return JsonFormat.retFormat(1, "该账号未注册");
        }
        user_id = user.getUser_id();
        //token验证
        String userId = verifyToken(token);
        if (userId == null) {
            return JsonFormat.retFormat(104, "token验证失败");
        }
        activeToken(token);
//        if (!token.equals(user.getToken())) {
//            return JsonFormat.retFormat(104, "token验证失败");
//        }
        UserInfo userInfo = getJcUserInfoDao().getUserInfoByUserId(user_id);
        if (userInfo == null) {
            return JsonFormat.retFormat(2, "无用户信息");
        }
        userInfo = setUserInfo(userInfo, rcv);
        userInfo.setUpdate_time(currentTime);
        getJcUserInfoDao().updateUserInfo(userInfo);
        return JsonFormat.retFormat(0, "修改用户信息成功");
    }

    @Override
    public UserDTO getUserDTO(String userId) {
        List<UserDO> userDO = userDao.getUser(userId);
        if (userDO.size() == 0) {
            return null;
        }
        UserDTO userDTO = new UserDTO();
        List<UserProjectDTO> userProjects = new ArrayList<>();
        for (UserDO user : userDO) {
            userDTO.setUserId(user.getUserId());
            userDTO.setPhone(user.getPhone());
            userDTO.setPassword(user.getPassword());
            userDTO.setLock(user.getLock());
            userDTO.setUid(user.getUid());
            userDTO.setSecurityKey(user.getSecurityKey());
            userDTO.setLoginTimes(user.getLoginTimes());
            userDTO.setToken(user.getToken());
            UserProjectDTO userProject = new UserProjectDTO();
            userProject.setAppCode(user.getAppCode());
            userProject.setOpenId(user.getOpenId());
            userProject.setProjCode(user.getProjCode());
            userProject.setRoleId(user.getRoleId());
            userProject.setRoleName(user.getRoleName());
            userProject.setPushId(user.getPushId());
            userProject.setDl_pwd(user.getDl_pwd());
            userProject.setSecurityKey(user.getDl_securityKey());
            userProjects.add(userProject);
        }
        userDTO.setUserProjects(userProjects);
        return userDTO;
    }

    @Override
//    @Transactional(rollbackFor = IllegalArgumentException.class)
    public void updateUserInfo(UserInfo userInfo) throws IllegalArgumentException {
        log4j.info("userinfoserviceimpl:更新用户信息");
        try {
            checkUserInfo(userInfo);
        } catch (IllegalArgumentException e) {
            throw e;
        }
        userInfo = encryptUserInfo(userInfo);
        userInfo.setUpdate_time(TimeUtil.getCurrentTime());
        jcUserInfoMapper.updateByPrimaryKeySelective(userInfo);
        rocketMQUtil.updateUserInfoMessage(decryptUserInfo(userInfo));
        redisTemplate.delete(USERINFO_CACHE + userInfo.getUser_id());
    }

    @Override
    @Transactional(rollbackFor = IllegalArgumentException.class)
    public void saveUserInfo(UserInfo userInfo) throws IllegalArgumentException {
        log4j.info("userinfoserviceimpl:保存用户信息");
        try {
            checkUserInfo(userInfo);
        } catch (IllegalArgumentException e) {
            throw e;
        }
        userInfo = encryptUserInfo(userInfo);
        userInfo.setCreate_time(TimeUtil.getCurrentTime());
        userInfo.setUpdate_time(TimeUtil.getCurrentTime());
        jcUserInfoMapper.insertSelective(userInfo);
        redisTemplate.opsForValue().set(USERINFO_CACHE + userInfo.getUser_id(), userInfo, 1, TimeUnit.DAYS);
    }

    /**
     * 身份证号码校验
     *
     * @param idCard
     * @throws BusinessException 6：身份证号码已存在； 412：身份证规则错误
     */
    public void checkIdCard(String idCard, String userId) throws BusinessException {
        if (idCard != null) {
            JcUserInfoExample example = new JcUserInfoExample();
            JcUserInfoExample.Criteria criteria = example.createCriteria();
            criteria.andCardIdEqualTo(ThreeDESEncrypt(idCard));
            criteria.andUserIdNotEqualTo(userId);
            List<UserInfo> userInfos = jcUserInfoMapper.selectByExample(example);
            if (userInfos != null && userInfos.size() > 0) {
                throw new BusinessException(412, "身份证号码已存在");
            }
            // 校验身份证规则
            String idCardValid = IdcardValidator.validatorIdcard(idCard);
            if (!"".equals(idCardValid)) {
                throw new BusinessException(412, idCardValid);
            }
        }
    }

    /**
     * 加密userInfo中的敏感信息
     *
     * @param userInfo 用户信息
     * @return
     */
    private UserInfo encryptUserInfo(UserInfo userInfo) {
        if (userInfo.getMedicare_id() != null) {
            userInfo.setMedicare_id(ThreeDESEncrypt(userInfo.getMedicare_id()));
        }
        if (userInfo.getCard_id() != null) {
            userInfo.setCard_id(ThreeDESEncrypt(userInfo.getCard_id()));
        }
        if (userInfo.getMobile() != null) {
            userInfo.setMobile(ThreeDESEncrypt(userInfo.getMobile()));
        }
        if (userInfo.getFamilyPhone() != null) {
            userInfo.setFamilyPhone(ThreeDESEncrypt(userInfo.getFamilyPhone()));
        }
        return userInfo;
    }

    /**
     * 解密userInfo中的敏感信息
     *
     * @param userInfo 用户信息
     * @return
     */
    @Override
    public UserInfo decryptUserInfo(UserInfo userInfo) {
        if (userInfo.getFamilyPhone() != null) {
            userInfo.setFamilyPhone(ThreeDESUtil.get3DESDecrypt(userInfo.getFamilyPhone(), SPKEY));
        }
        if (userInfo.getMobile() != null) {
            userInfo.setMobile(ThreeDESUtil.get3DESDecrypt(userInfo.getMobile(), SPKEY));
        }
        if (userInfo.getCard_id() != null) {
            userInfo.setCard_id(ThreeDESUtil.get3DESDecrypt(userInfo.getCard_id(), SPKEY));
        }
        return userInfo;
    }

    /**
     * 读取要修改的字段和值
     *
     * @param rcv
     * @return
     */
    private UserInfo setUserInfo(UserInfo userInfo, JSONObject rcv) {
        if (rcv.containsKey("the_name")) {
            userInfo.setThe_name(rcv.getString("the_name"));
        }
        if (rcv.containsKey("sex")) {
            userInfo.setSex(rcv.getString("sex"));
        }
        if (rcv.containsKey("birthday")) {
            userInfo.setBirthday(rcv.getString("birthday"));
        }
        if (rcv.containsKey("card_type")) {
            userInfo.setCard_type(rcv.getString("card_type"));
        }
        if (rcv.containsKey("card_id")) {
            userInfo.setCard_id(rcv.getString("card_id"));
        }
        if (rcv.containsKey("medicare")) {
            userInfo.setMedicare(rcv.getString("medicare"));
        }
        if (rcv.containsKey("medicare_id")) {
            userInfo.setMedicare_id(rcv.getString("medicare_id"));
        }
        if (rcv.containsKey("city_id")) {
            userInfo.setCity_id(rcv.getString("city_id"));
        }
        if (rcv.containsKey("address")) {
            userInfo.setAddress(rcv.getString("address"));
        }
        if (rcv.containsKey("w_chat")) {
            userInfo.setW_chat(rcv.getString("w_chat"));
        }
        if (rcv.containsKey("mobile")) {
            String mobile = rcv.getString("mobile");
            mobile = ThreeDESEncrypt(mobile);
            userInfo.setMobile(mobile);
        }
        if (rcv.containsKey("e_mail")) {
            userInfo.setE_mail(rcv.getString("e_mail"));
        }
        if (rcv.containsKey("source_ids")) {
            userInfo.setSource_ids(rcv.getString("source_ids"));
        }
        return userInfo;
    }

    public void checkUserInfo(UserInfo userInfo) throws IllegalArgumentException {
        if (userInfo.getSex() != null && !checkSex(userInfo.getSex())) {
            throw new IllegalArgumentException("sex（性别）格式错误");
        }
        if (userInfo.getMedicareExpenseTypes() != null && !checkMedicareExpenseTypes(userInfo.getMedicareExpenseTypes())) {
            throw new IllegalArgumentException("medicareExpenseTypes（医保报销类型）格式错误");
        }
        if (userInfo.getCard_type() != null || UserInfoConstant.CARD_TYPE_ID_CARD.equals(userInfo.getCard_type())) {
            try {
                // 四种其中一种就不判断了
                if (!(UserInfoConstant.CARD_TYPE_TEMPORARY_ID_CARD.equals(userInfo.getCard_type())
                    || UserInfoConstant.CARD_TYPE_RESIDENCE_BOOKLET.equals(userInfo.getCard_type())
                    || UserInfoConstant.CARD_TYPE_OFFICERS.equals(userInfo.getCard_type())
                    || UserInfoConstant.CARD_TYPE_OTHERS.equals(userInfo.getCard_type()))) {
                    checkIdCard(userInfo.getCard_id(), userInfo.getUser_id());
                }
            } catch (BusinessException e) {
                throw new IllegalArgumentException(e.getMessage());
            }
        }
        if (userInfo.getMedicare() != null && !checkMedicareType(userInfo.getMedicare())) {
            throw new IllegalArgumentException("medicare（医保卡）类型格式错误");
        }
        if (userInfo.getEmergencyContactType() != null && !checkEmerContactType(userInfo.getEmergencyContactType())) {
            throw new IllegalArgumentException("emergencyContactType（紧急联系人类型）格式错误");
        }
        if (userInfo.getMobile() != null && isNotPhonePattern(userInfo.getMobile())) {
            throw new IllegalArgumentException("mobile（手机号）格式错误");
        }
    }

    /**
     * 性别
     *
     * @param sex
     * @return
     */
    public boolean checkSex(String sex) {
        return UserInfoConstant.SEX_MALE.equals(sex) ||
                UserInfoConstant.SEX_FEMALE.equals(sex) ||
                UserInfoConstant.SEX_FEMALE_TO_MALE.equals(sex) ||
                UserInfoConstant.SEX_MALE_TO_FEMALE.equals(sex) ||
                UserInfoConstant.SEX_UNKNOWN.equals(sex) ||
                UserInfoConstant.SEX_UNSPECIFIED.equals(sex);
    }

    /**
     * 医保类型
     *
     * @param medicareType
     * @return
     */
    public boolean checkMedicareType(String medicareType) {
        return UserInfoConstant.MEDICARE_TYPE_CITY.equals(medicareType) ||
                UserInfoConstant.MEDICARE_TYPE_PROVINCE.equals(medicareType) ||
                UserInfoConstant.MEDICARE_TYPE_HEALTHY.equals(medicareType) ||
                UserInfoConstant.MEDICARE_TYPE_CITY_COLLEGE.equals(medicareType) ||
                UserInfoConstant.MEDICARE_TYPE_PROVINCE_COLLEGE.equals(medicareType) ||
                UserInfoConstant.MEDICARE_TYPE_CITY_CHILD.equals(medicareType) ||
                UserInfoConstant.MEDICARE_TYPE_OTHERS.equals(medicareType);
    }

    /**
     * 医保报销类型
     *
     * @param medicareExpenseType
     * @return
     */
    public boolean checkMedicareExpenseTypes(String medicareExpenseType) {
        return UserInfoConstant.MEDICARE_EXPENSE_TYPE_BASE.equals(medicareExpenseType) ||
                UserInfoConstant.MEDICARE_EXPENSE_TYPE_COMMERCIAL.equals(medicareExpenseType) ||
                UserInfoConstant.MEDICARE_EXPENSE_TYPE_SERIOUS_DISEASE.equals(medicareExpenseType) ||
                UserInfoConstant.MEDICARE_EXPENSE_TYPE_COOPERATIVE.equals(medicareExpenseType) ||
                UserInfoConstant.MEDICARE_EXPENSE_TYPE_CITY_BASE.equals(medicareExpenseType) ||
                UserInfoConstant.MEDICARE_EXPENSE_TYPE_FREE_MEDICAL.equals(medicareExpenseType) ||
                UserInfoConstant.MEDICARE_EXPENSE_TYPE_OTHERS.equals(medicareExpenseType);
    }

    /**
     * 紧急联系人类型
     * @param emerContactType
     * @return
     */
    public boolean checkEmerContactType(String emerContactType) {
        return UserInfoConstant.EMER_CONTACT_TYPE_PAIR.equals(emerContactType) ||
                UserInfoConstant.EMER_CONTACT_TYPE_GUARDIAN.equals(emerContactType) ||
                UserInfoConstant.EMER_CONTACT_TYPE_HOME.equals(emerContactType) ||
                UserInfoConstant.EMER_CONTACT_TYPE_OFFICE.equals(emerContactType) ||
                UserInfoConstant.EMER_CONTACT_TYPE_NEIGHBORHOOD.equals(emerContactType) ||
                UserInfoConstant.EMER_CONTACT_TYPE_OTHERS.equals(emerContactType) ||
                UserInfoConstant.MEDICARE_EXPENSE_TYPE_OTHERS.equals(emerContactType);
    }

    @Override
    public UCResponse getUserInfoByOpenId(String openId, String appCode) {
        JcUserInfo result = jcUserInfoMapper.getUserInfoByOpenId(openId, appCode);
        if(result != null){
            result.setMobile(ThreeDESDecrypt(result.getMobile()));
        }else{
            return new UCResponse(1, "此用户未注册");
        }
        return new UCResponse(0,"查询成功", result);
    }

    @Override
    public List<UserInfoExcelDTO> listUserInfoWithinTimeByProjCode(String startTime, String endTime, Integer projCode) {
        List<UserInfoExcelDTO> userInfoExcelDTOS = jcUserInfoMapper.listUserInfoWithinTimeByProjCode(startTime, endTime, projCode);
        if (userInfoExcelDTOS == null || userInfoExcelDTOS.size() == 0) {
            return null;
        }
        return userInfoExcelDTOS;
    }


    @Override
    public List<UserInfoExcelDTO> listUserInfoWithinTimeByProjCodeLimitPage(String startTime, String endTime, Integer projCode, Integer pageNum, Integer pageSize) {
        List<UserInfoExcelDTO> userInfoExcelDTOS = jcUserInfoMapper.listUserInfoWithinTimeByProjCodeLimitPage(startTime, endTime, projCode, pageNum, pageSize);
        if (userInfoExcelDTOS == null || userInfoExcelDTOS.size() == 0) {
            return null;
        }
        return userInfoExcelDTOS;
    }

    @Override
    public Integer countRegisteredPhone(String startTime, String endTime, Integer projCode) {
        return jcUserInfoMapper.countRegisteredPhone(startTime, endTime, projCode);
    }
}
