//package com.ucmed.filter;
//
//import cn.ucmed.cat.microservices.common.CatContext;
//import com.ucmed.util.PropertyUtil;
//import lombok.extern.slf4j.Slf4j;
//import org.apache.commons.lang.StringUtils;
//import org.springframework.beans.factory.annotation.Value;
//import com.dianping.cat.Cat;
//import com.dianping.cat.message.Transaction;
//import org.springframework.stereotype.Component;
//import tk.mybatis.mapper.util.StringUtil;
//
//import javax.servlet.*;
//import javax.servlet.http.HttpServletRequest;
//import java.io.IOException;
//@Component
//@Slf4j
//public class CommonCatFilter implements Filter {
//
//    @Value("${spring.application.name}")
//    private String appName;
////    @Value("${spring.cloud.consul.discovery.health-check-url:/actuator/health}")
//    private String heartBeatURI = "";
//
//    @Override
//    public void init(FilterConfig filterConfig) throws ServletException {
//        if (StringUtils.isEmpty(appName)) {
//            appName = PropertyUtil.getProperty("spring.application.name");
//        }
//    }
//
//    @Override
//    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
//        HttpServletRequest httpServletRequest = (HttpServletRequest)servletRequest;
//        if (StringUtil.isEmpty(appName)) {
//            log.trace("common cat filter can't called");
//            filterChain.doFilter(servletRequest, servletResponse);
//            return;
//        }
//        if (this.heartBeatURI != null && !"".equals(this.heartBeatURI) && this.heartBeatURI.equals(httpServletRequest.getRequestURI())) {
//            log.trace("common cat filter: heart beat!");
//            Cat.logEvent("HeartBeat", this.appName);
//        } else {
//            Transaction t = Cat.getProducer().newTransaction(this.appName + ".RESTFUL", httpServletRequest.getRequestURI());
//            log.trace("common cat filter called: doFilter()");
//
//            try {
//                if (httpServletRequest.getHeader("_catRootMessageId") != null && !"".equals(httpServletRequest.getHeader("_catRootMessageId"))) {
//                    Cat.Context ctx = new CatContext();
//                    ctx.addProperty("_catRootMessageId", httpServletRequest.getHeader("_catRootMessageId"));
//                    ctx.addProperty("_catParentMessageId", httpServletRequest.getHeader("_catParentMessageId"));
//                    ctx.addProperty("_catChildMessageId", httpServletRequest.getHeader("_catChildMessageId"));
//                    Cat.logRemoteCallServer(ctx);
//                    log.trace("cat reqeust interceptor(): rootId is " + ctx.getProperty("_catRootMessageId") + " parentId is " + ctx.getProperty("_catParentMessageId") + " childId is " + ctx.getProperty("_catChildMessageId"));
//                }
//
//                filterChain.doFilter(servletRequest, servletResponse);
//                t.setStatus("0");
//            } catch (Exception var10) {
//                Cat.logError(var10);
//                t.setStatus(var10);
//                throw var10;
//            } finally {
//                t.complete();
//            }
//
//        }
//    }
//
//    @Override
//    public void destroy() {
//
//    }
//}
