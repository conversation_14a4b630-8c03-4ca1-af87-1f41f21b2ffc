package com.ucmed.util;

import com.ucmed.bean.UserInfo;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.DefaultMQProducer;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import static com.ucmed.common.constant.CommonConstant.PROJCODE;
import static com.ucmed.common.constant.CommonConstant.USERID;


/**
 * <AUTHOR>
 * @date 2017/12/15 10:43
 */
@Component
public class RocketMQUtil {

    private static Logger log4j = Logger.getLogger(RocketMQUtil.class.getName());

    @Autowired
    private DefaultMQProducer defaultMQProducer;

    @Value("${rocketmq.topic}")
    private String topicName;

    @Value("${rocketmq.statistic.topic}")
    private String statisticTopicName;

    private static final String REGISTER_TAG = "register_success";
    private static final String UPDATE_USERINFO_TAG = "update_userinfo_success";
    private static final String ACTIVITY_TAG = "activity_success";

    /**
     * 注册成功消息
     * @param messageJson
     */
    public void registerMessage(JSONObject messageJson) {
        Message message = new Message(topicName, REGISTER_TAG, messageJson.optString(USERID), messageJson.toString().getBytes());
        sendOneWay(message);
    }

    /**
     * 更新用户信息消息
     * @param userInfo
     */
    public void updateUserInfoMessage(UserInfo userInfo) {
        Message message = new Message(topicName, UPDATE_USERINFO_TAG, userInfo.getUser_id(), JSONObject.fromObject(userInfo).toString().getBytes());
        sendOneWay(message);
    }

    /**
     * 活跃成功消息
     * @param msg
     */
    public void activityMessage(JSONObject messageJson) {
        if (StringUtils.isEmpty(statisticTopicName)){
            //如果配置文件未配置topic，则不发送用户活跃消息
            return;
        }
        Message message = new Message(statisticTopicName, ACTIVITY_TAG, messageJson.toString(), messageJson.toString().getBytes());
        sendOneWay(message);
    }

    private void sendOneWay(Message message) {
        try {
            defaultMQProducer.sendOneway(message);
            log4j.info("MQ通过sendOneway发送消息：" + message.toString());
        } catch (MQClientException e) {
            e.printStackTrace();
            log4j.error("rocketMQ send message error: ", e);
        } catch (RemotingException e) {
            e.printStackTrace();
            log4j.error("rocketMQ send message error: ", e);
        } catch (InterruptedException e) {
            e.printStackTrace();
            log4j.error("rocketMQ send message error: ", e);
        }
    }
}
