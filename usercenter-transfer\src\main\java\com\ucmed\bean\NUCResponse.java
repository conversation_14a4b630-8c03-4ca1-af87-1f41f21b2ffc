package com.ucmed.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

@ApiModel(value="用户中心接口返回值",description="用户中心接口返回值")
public class NUCResponse<T> implements Serializable {
    private static final long serialVersionUID = -4345612602298647175L;
    @ApiModelProperty(value="响应码", name="retCode", example="0")
    int retCode;
    @ApiModelProperty(value="返回信息", name="retInfo", example="操作成功")
    String retInfo;
    @ApiModelProperty(value="返回数据", name="param", example="someMessage")
    T param;

    public NUCResponse() {
    }

    public NUCResponse(int retCode, String retInfo, T param) {
        this.retCode = retCode;
        this.retInfo = retInfo;
        this.param = param;
    }

    public NUCResponse(int retCode, String retInfo) {
        this.retCode = retCode;
        this.retInfo = retInfo;
    }

    public static <T> NUCResponse<T> createBySuccess() {
        return new NUCResponse<T>(0, "返回成功");
    }

    public static <T> NUCResponse<T> createBySuccess(T data) {
        return new NUCResponse<T>(0, "返回成功", data);
    }

    public static <T> NUCResponse<T> createByError(int retCode, String retInfo) {
        return new NUCResponse<T>(retCode, retInfo);
    }

    public static <T> NUCResponse<T> createByError(int retCode, String retInfo, T data) {
        return new NUCResponse<T>(retCode, retInfo, data);
    }

    @Override
    public String toString() {
        return "NUCResponse{" +
                "retCode=" + retCode +
                ", retInfo='" + retInfo + '\'' +
                ", param=" + param +
                '}';
    }

    public int getRetCode() {
        return retCode;
    }

    public void setRetCode(int retCode) {
        this.retCode = retCode;
    }

    public String getRetInfo() {
        return retInfo;
    }

    public void setRetInfo(String retInfo) {
        this.retInfo = retInfo;
    }

    public T getParam() {
        return param;
    }

    public void setParam(T param) {
        this.param = param;
    }
}
