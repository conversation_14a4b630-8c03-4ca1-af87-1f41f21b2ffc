package com.ucmed.util;

import lombok.extern.java.Log;
import lombok.extern.log4j.Log4j2;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.Properties;

@Log
public class PropertyUtil {

    private static Properties props;

    static {
        loadProps();
    }

    synchronized static private void loadProps() {
        log.info("start to load properties.......");
        props = new Properties();
        InputStream in = null;
        try {
            in = PropertyUtil.class.getClassLoader().
                    getResourceAsStream("configure.properties");
            props.load(in);
            log.info("load success");
        } catch (FileNotFoundException e) {
            log.fine("properties not found!");
        } catch (IOException e) {
            log.fine("IOException");
        } finally {
            try {
                if (null != in) {
                    in.close();
                }
            } catch (IOException e) {
                log.fine("properties close Exception!");
            }
        }
        log.info("load properties over...........");
    }

    public static String getProperty(String key) {
        if (null == props) {
            loadProps();
        }
        return props.getProperty(key);
    }
}
