package com.ucmed.bean.redisbean;

import com.ucmed.util.TimeUtil;

import java.io.Serializable;

public class UserPwdErrorTimes {

    private String userId;

    private String phone;

    private int failedTimes;

    private String projCode;


    private String loginTime;

    public UserPwdErrorTimes() {
    }

    public UserPwdErrorTimes(String userId, String phone, String projCode) {
        this.userId = userId;
        this.phone = phone;
        this.projCode = projCode;
        this.loginTime = TimeUtil.getCurrentTime();
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getFailedTimes() {
        return failedTimes;
    }

    public void setFailedTimes(int failedTimes) {
        this.failedTimes = failedTimes;
    }

    public String getProjCode() {
        return projCode;
    }

    public void setProjCode(String projCode) {
        this.projCode = projCode;
    }

    public String getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(String loginTime) {
        this.loginTime = loginTime;
    }

    @Override
    public String toString() {
        return "UserPwdErrorTimes{" +
                "userId='" + userId + '\'' +
                ", phone='" + phone + '\'' +
                ", failedTimes=" + failedTimes +
                ", projCode=" + projCode +
                ", loginTime='" + loginTime + '\'' +
                '}';
    }

    public String errorLog() {
        return "用户：" + userId + "于" + getLoginTime() + "在" + getProjCode()
                + "输入密码错误， 错误次数：" + getFailedTimes();
    }
}
