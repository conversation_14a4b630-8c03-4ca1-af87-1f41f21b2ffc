package com.ucmed.dao;

import com.ucmed.bean.Module;
import com.ucmed.common.dao.BaseDaoImpl;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.ucmed.common.constant.TableName.SECURITYMODULE;
import static com.ucmed.common.constant.TableName.SECURITYROLEMODULE;


/**
 * Created by QIUJIAHAO on 2016/9/12.
 */
@Repository
public class ModuleDaoImpl extends BaseDaoImpl implements ModuleDao {

    /**
     * 查询有效功能点
     */
    public List<Module> findModuleByRoleId(int roleId) {
        String sql = "SELECT " +
                "a.role_id,a.module_id,b.module_name,b.module_url " +
                "FROM " + SECURITYROLEMODULE + " a," + SECURITYMODULE + " b " +
                "WHERE a.role_id=" + roleId + " AND a.valid=1 AND a.module_id = b.module_id";
        return getJdbcTemplate().query(sql,new BeanPropertyRowMapper(Module.class));
    }

    @Override
    public List<Module> listUserModules(int appCode, String userId) {
        String sql = "SELECT " +
                "m.module_id, m.module_name, m.module_url, m.module_desc " +
                "FROM " +
                "security_user_role ur " +
                "INNER JOIN security_role_module rm ON rm.role_id = ur.role_id " +
                "INNER JOIN security_module m ON m.module_id = rm.module_id AND m.app_code = ?" +
                "WHERE " +
                "(ur.user_id = ?) GROUP BY m.module_id";
        return getJdbcTemplate().query(sql, new Object[]{appCode, userId}, new BeanPropertyRowMapper(Module.class));
    }
}
