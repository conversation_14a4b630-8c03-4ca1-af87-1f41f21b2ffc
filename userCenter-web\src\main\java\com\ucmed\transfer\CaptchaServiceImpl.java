package com.ucmed.transfer;

import cn.hutool.http.HttpUtil;
import cn.ucmed.common.constants.GlobalConstants;
import com.alibaba.fastjson.JSON;
import com.ucmed.api.SendSmsApi;
import com.ucmed.bean.*;
import com.ucmed.common.constant.AppMemoryInfo;
import com.ucmed.common.constant.CommonConstant;
import com.ucmed.common.constants.controller.URLConstants;
import com.ucmed.common.exception.IllegalPatternException;
import com.ucmed.common.exception.RepeatedAccountException;
import com.ucmed.common.service.CommonService;
import com.ucmed.dto.MessageBody;
import com.ucmed.dto.MessageResult;
import com.ucmed.exception.BusinessException;
import com.ucmed.exception.UnknownAccountException;
import com.ucmed.mapper.SecurityProjectMapper;
import com.ucmed.message.client.SmsClient;
import com.ucmed.message.dao.SmsHistoryMapper;
import com.ucmed.message.model.MsgCache;
import com.ucmed.message.model.SMSHistory;
import com.ucmed.service.CaptchaService;
import com.ucmed.service.GetAppInfoService;
import com.ucmed.service.SecurityUserProjectService;
import net.sf.json.JSONObject;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.UUID;
import java.util.concurrent.TimeUnit;


/**
 * Created by XXB-QJH-1303.
 * Date: 2017/9/4 9:04
 */
@Service
public class CaptchaServiceImpl extends CommonService implements CaptchaService {

    private static Logger log = Logger.getLogger(CaptchaServiceImpl.class.getName());

    @Value("${host}")
    private String hostAddress;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    SecurityProjectMapper securityProjectMapper;
    @Value("${validateCodeExpireCount}")
    private Long validateCodeExpireCount;
    @Value("${SMSValdate_timeout}")
    private long msgTimeout;
    @Autowired
    private SmsHistoryMapper smsHistoryMapper;
    @Autowired
    private SecurityUserProjectService securityUserProjectService;
    @Autowired
    private GetAppInfoService getAppInfoService;
    @Autowired
    AppMemoryInfo appMemoryInfo;
    @Value("${platsms.http.url}")
    private String smsUrl;

    @Override
    public UCResponse getImageCaptcha(int length) {
        try {
            JSONObject param = new JSONObject();
            String token = UUID.randomUUID().toString();
            param.put("validate_code_url", hostAddress + URLConstants.PICTURE_VALIDATE_CODE + "?token=" + token + "&length=" + length + "&random=" + Math.random());
            param.put("token", token);
            return new UCResponse(0, "成功获取图片验证码", param);
        } catch (Exception e) {
            log.error("获取图形验证码失败");
            e.printStackTrace();
            return new UCResponse(HttpStatus.INTERNAL_SERVER_ERROR.value(), e.getMessage());
        }
    }

    @Override
    public UCResponse getImageCaptcha(String phone, String type) {
        try {
            beforeValidateCode(0, phone, type);
            JSONObject param = new JSONObject();
            param.put("validate_code_url", hostAddress + URLConstants.PICTURE_VALIDATE_CODE + "?phoneNumber="
                    + phone + "&type=" + type + "&random=" + Math.random());
            return new UCResponse(0, "成功获取图片验证码", param);
        } catch (BusinessException e) {
            return new UCResponse(e.getCode(), e.getMessage());
        }
    }

    @Override
    public UCResponse getImageCaptcha(String phone, String type, int length) {
        try {
            beforeValidateCode(0, phone, type);
            JSONObject param = new JSONObject();
            param.put("validate_code_url", hostAddress + URLConstants.PICTURE_VALIDATE_CODE + "?phoneNumber="
                    + phone + "&type=" + type + "&random=" + Math.random() + "&length=" + length);
            return new UCResponse(0, "成功获取图片验证码", param);
        } catch (BusinessException e) {
            return new UCResponse(e.getCode(), e.getMessage());
        }
    }

    @Override
    public UCResponse getImageCaptcha(int appCode, String phone, String type, int length) {
        try {
            beforeValidateCode(appCode, phone, type);
            JSONObject param = new JSONObject();
            param.put("validate_code_url", hostAddress + URLConstants.PICTURE_VALIDATE_CODE + "?phoneNumber="
                    + phone + "&type=" + type + "&random=" + Math.random() + "&length=" + length);
            return new UCResponse(0, "成功获取图片验证码", param);
        } catch (BusinessException e) {
            return new UCResponse(e.getCode(), e.getMessage());
        }
    }

    /**
     * 生成图形验证码之前的校验
     *
     * @param phone 手机号
     * @param type  验证码类型
     * @throws RepeatedAccountException
     * @throws IllegalPatternException
     * @throws UnknownAccountException
     */
    private void beforeValidateCode(int appCode, String phone, String type) throws BusinessException {
        if (isNotPhonePattern(phone)) {
            throw new IllegalPatternException(641, "手机号不合法");
        }

        if (GlobalConstants.ValidatePhoneType.REGIST.getKey().equals(type)) {
            if (appCode != 0) {
                UC_UserInfo user = getUser(phone);
                SecurityUserProject userProject = null;
                if (user != null) {
                    userProject = securityUserProjectService.getUserProject(user.getUser_id(), getProjCode(appCode));
                }
                if (userProject != null && (isPhoneExist(phone) || isUserExist(phone))) {
                    throw new RepeatedAccountException(641, "该账号已注册");
                }
            } else {
                if (isPhoneExist(phone) || isUserExist(phone)) {
                    throw new RepeatedAccountException(641, "该账号已注册");
                }
            }
        } else if (GlobalConstants.ValidatePhoneType.FORGETPSW.getKey().equals(type)
                || GlobalConstants.ValidatePhoneType.LOGIN.getKey().equals(type)
                || GlobalConstants.ValidatePhoneType.BINDPATIENTVISIT.getKey().equals(type)) {
            if (isPhoneNotExist(phone) && isUserNotExist(phone)) {
                throw new UnknownAccountException(641, "账号未注册");
            }
        } else if (GlobalConstants.ValidatePhoneType.RESETPHONE.getKey().equals(type) ||
                GlobalConstants.ValidatePhoneType.BINDTHIRDAPP.getKey().equals(type)) {
            //DO nothing
        } else if ("99".equals(type)) {
            //DO nothing
        } else {
            throw new IllegalPatternException(643, "type类型错误");
        }
    }

    @Override
    public UCResponse verifyImageCaptcha(String phone, String type, String picCode) {
        String errorTimesCache = (String) redisTemplate.opsForValue().get("validate_code:" + phone + type + "-error");
        int errorTimes = errorTimesCache == null ? 0 : Integer.parseInt(errorTimesCache);
        String code = (String) redisTemplate.opsForValue().get("validate_code:" + phone + type);
        if (code != null && code.equals(picCode) && validateCodeExpireCount > errorTimes) {
            return new UCResponse(0, "图形验证码验证成功");
        } else if (code == null) {
            return new UCResponse(-1, "图形验证码过期，请刷新后重试", hostAddress + URLConstants.PICTURE_VALIDATE_CODE + "?phoneNumber=" + phone + "&type=" + type + "&random=" + Math.random());
        } else {
            errorTimes++;
            if (validateCodeExpireCount <= errorTimes) {
                redisTemplate.delete("validate_code:" + phone + type);
                redisTemplate.delete("validate_code:" + phone + type + "-error");
            } else {
                long expire = redisTemplate.getExpire("validate_code:" + phone + type + "-error");
                redisTemplate.boundValueOps("validate_code:" + phone + type + "-error").set(String.valueOf(errorTimes));
                redisTemplate.boundValueOps("validate_code:" + phone + type + "-error").expire(expire, TimeUnit.SECONDS);
            }
            return new UCResponse(-2, "验证码输入错误，请重新输入", hostAddress + URLConstants.PICTURE_VALIDATE_CODE + "?phoneNumber=" + phone + "&type=" + type + "&random=" + Math.random());
        }
    }

    @Override
    public UCResponse generateMsgCode(String phone, String type, int length) {
        if (StringUtils.isNotBlank(phone)) {
            MsgCache msgCache = (MsgCache) redisTemplate.opsForValue().get(CommonConstant.MSG_CATCH + GlobalConstants.ValidatePhoneType.getValue(type) + phone);
            if (msgCache == null || StringUtils.isEmpty(msgCache.getMsgNo())) {
                msgCache = new MsgCache();
                msgCache.setMsgNo(RandomStringUtils.randomNumeric(length));
                msgCache.setLength(length);
                msgCache.setPhone(phone);
                msgCache.setTag(GlobalConstants.ValidatePhoneType.getValue(type));
                redisTemplate.opsForValue().set(msgCache.getKey(), msgCache, msgTimeout, TimeUnit.SECONDS);
            }
            return new UCResponse(0, "生成验证码成功", msgCache.getMsgNo());
        } else {
            return new UCResponse(-1, "生成验证码失败");
        }
    }

    @Override
    public UCResponse generateMsgCode(String phone, String type, int length, long timeOut) {
        if (StringUtils.isNotBlank(phone)) {
            MsgCache msgCache = (MsgCache) redisTemplate.opsForValue().get(CommonConstant.MSG_CATCH + GlobalConstants.ValidatePhoneType.getValue(type) + phone);
            if (msgCache == null || StringUtils.isEmpty(msgCache.getMsgNo())) {
                msgCache = new MsgCache();
                msgCache.setMsgNo(RandomStringUtils.randomNumeric(length));
                msgCache.setLength(length);
                msgCache.setPhone(phone);
                msgCache.setTag(GlobalConstants.ValidatePhoneType.getValue(type));
                if (timeOut > 0) {
                    redisTemplate.opsForValue().set(msgCache.getKey(), msgCache, timeOut, TimeUnit.SECONDS);
                } else {
                    redisTemplate.opsForValue().set(msgCache.getKey(), msgCache, msgTimeout, TimeUnit.SECONDS);
                }
            }
            return new UCResponse(0, "生成验证码成功", msgCache.getMsgNo());
        } else {
            return new UCResponse(-1, "生成验证码失败");
        }
    }

    @Override
    public UCResponse getCommonImageCaptcha(String phone, String type, int length) {
        if (isNotPhonePattern(phone)) {
            return new UCResponse(641, "手机号不合法");
        }
        JSONObject param = new JSONObject();
        param.put("validate_code_url", hostAddress + URLConstants.PICTURE_VALIDATE_CODE + "?phoneNumber="
                + phone + "&type=" + type + "&random=" + Math.random());
        return new UCResponse(0, "成功获取图片验证码", param);
    }

    @Override
    public UCResponse sendMsgCode(String smsToken, String phone, String type, String picCode) {
        UCResponse ucResponse = verifyImageCaptcha(phone, type, picCode);
        if (0 != ucResponse.getRetCode()) {
            return ucResponse;
        }
        return sendMsgCode(1, smsToken, phone, type, 4);
    }

    @Override
    public UCResponse sendMsgCode(int appCode, String smsToken, String phone, String type, int length) {
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            UCResponse ucResponse = new UCResponse(-1, "应用未注册");
            return ucResponse;
        }
        UCResponse ucResponse = generateMsgCode(phone, type, length);
        if (0 != ucResponse.getRetCode()) {
            return ucResponse;
        }
        String code = (String) ucResponse.getParam();
        String content = "你的验证码是:" + code;
        MessageBody body = new MessageBody();
        body.setToken(smsToken);
        body.setPhone(phone);
        body.setContent(content);
        String params = JSON.toJSONString(body);
        log.info("发送短信->" + params);
//        MessageResult result = SendSmsApi.sendMessage(body);
        String post = HttpUtil.post(smsUrl, "requestData=" + params);
        log.info("发送短信返回->" + post);
        MessageResult result = com.alibaba.fastjson.JSONObject.toJavaObject(JSON.parseObject(post), MessageResult.class);
        if (result.getResultCode() != 100) {
            addSmsHistory(smsToken, phone, content, result.getResultInfo(), "0");
            return new UCResponse(642, "获取验证码失败");
        }
        addSmsHistory(smsToken, phone, content, result.getResultInfo(), "1");
        return new UCResponse(0, "获取验证码成功，请查看您的手机短信");
    }

    @Override
    public UCResponse verifyImageCaptcha(String token, String captchaCode) {
        String code = (String) redisTemplate.opsForValue().get(token);
        if (code != null && code.equals(captchaCode)) {
//            log.info("测试用：验证图形验证码：会删除token：" + token);
            redisTemplate.delete(token);
            return new UCResponse(0, "图形验证码验证成功");
        } else if (code == null) {
            token = UUID.randomUUID().toString();
            return new UCResponse(-1, "图形验证码过期，请刷新后重试", hostAddress + URLConstants.PICTURE_VALIDATE_CODE + "?token=" + token + "&random=" + Math.random());
        } else {
            return new UCResponse(-2, "验证码输入错误，请重新输入", hostAddress + URLConstants.PICTURE_VALIDATE_CODE + "?token=" + token + "&random=" + Math.random());
        }
    }

    private void addSmsHistory(String smsToken, String phone, String content, String resultInfo, String status) {
        SMSHistory model = new SMSHistory();
        model.setContent(content);
        model.setCreatedby(smsToken);
        model.setCreatedon(new Date());
        model.setReceiverMobile(phone);
        model.setSenderMobile(phone);
        model.setSenderScyUserId(phone);
        model.setVcProjectId(smsToken);
        model.setSmsHistoryId(UUID.randomUUID().toString());
        model.setSendStatus(status);
        model.setModifiedby(smsToken);
        model.setModifiedon(new Date());
        model.setDescription(resultInfo);
        smsHistoryMapper.insertSelective(model);
    }
}
