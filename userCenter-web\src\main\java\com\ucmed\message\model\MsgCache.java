package com.ucmed.message.model;

import com.ucmed.common.constant.CommonConstant;

public class MsgCache extends cn.ucmed.common.cache.MsgCache {

    private static final long serialVersionUID = -3364605613451279231L;

    private int errorTimes;

    public int getErrorTimes() {
        return errorTimes;
    }

    public void setErrorTimes(int errorTimes) {
        this.errorTimes = errorTimes;
    }

    @Override
    public Object getKey() {
        return CommonConstant.MSG_CATCH + getTag()
                + getPhone();
    }
}
