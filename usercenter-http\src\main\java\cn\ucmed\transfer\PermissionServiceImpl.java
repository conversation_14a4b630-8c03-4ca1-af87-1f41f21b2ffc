package cn.ucmed.transfer;

import cn.ucmed.utils.HttpClientUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ucmed.bean.Permission;
import com.ucmed.bean.UCResponse;
import com.ucmed.exception.BusinessException;
import com.ucmed.service.PermissionService;
import org.springframework.stereotype.Service;

import static cn.ucmed.constant.UserCenterConstant.USER_CENTER_URL;
@Service
public class PermissionServiceImpl implements PermissionService {
    @Override
    public Permission getPermission(String userId, int appCode) throws BusinessException {
        String url = USER_CENTER_URL + "/permission/getPermission?userId=" + userId
                + "&appCode=" + appCode;
        return (Permission) HttpClientUtils.doGet(url, new JSONObject(), null, Permission.class);
    }
}
