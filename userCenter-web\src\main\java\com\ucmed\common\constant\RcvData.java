package com.ucmed.common.constant;

import static com.ucmed.common.constant.CommonConstant.*;

/**
 * 入参字段
 * Created by QIUJIAHAO on 2016/9/27.
 */
public class RcvData {
    // 注册
    private String[] registration = {USERID, PASSWORD, PHONE, APPCODE, ROLENAME};
    // 登录
    private String[] login = {USERID,PASSWORD,APPCODE,ROLENAME};
    // 修改密码
    private String[] changePwd = {USERID,PASSWORD,NEWPASSWORD,TOKEN};
    // 重置密码
    private String[] reInputPassword = {USERID,NEWPASSWORD};
    // 退出登录
    private String[] logout = {USERID};
    // 修改手机号
    private String[] changePhone = {PHONE,NEWPHONE};
    // 验证token有效性
    private String[] isTokenValid = {USERID,TOKEN};
    // 用户是否注册某角色
    private String[] isUserInRole = {USERID,ROLENAME};

    // 发送短信
    private String[] sendMsgCode = {PHONE,MSGTEXT};

    // 绑定微信
    private String[] bindWechat = {USERID,OPENID,APPCODE,ROLENAME};
    // 微信登录
    private String[] loginByWechat = {OPENID,APPCODE,ROLENAME};
    // 微信解绑
    private String[] unbindWechat = {OPENID};

    // 查询用户信息
    private String[] getUserInfo = {USERID};
    // 修改用户信息
    private String[] updateUserInfo = {USERID,TOKEN};

    // 查询功能权限
    private String[] getAuthority = {USERID,APPCODE};
    // 查询数据权限
    private String[] getDataValue = {APPCODE,USERID};

    // 获取用户注册的应用程序信息
    private String[] getAppsByUser = {USERID};
    // 获取用户注册的角色信息
    private String[] getRolesByUser = {USERID};
    // 获取某应用程序的所有角色信息
    private String[] getRolesByApp = {APPCODE};
    // 获取用户在某应用程序的所有角色信息
    private String[] getRolesByAppAndUser = {USERID,APPCODE};

    // 添加就诊人
    private String[] addPatient = {USERID};
    // 修改就诊人
    private String[] updatePatient = {PATIENTID};
    // 查询就诊人
    private String[] getPatientInfo = {USERID};

    // 根据应用和角色查用户
    private String[] findUserByAppAndRole = {USERID, APPCODE, ROLENAME};

    // 添加功能权限
    private String[] addAuthority = {APPCODE, MODULENAME, MODULEURL};
    // 删除功能权限
    private String[] deleteAuthority = {MODULEID};
    // 更新功能权限
    private String[] updateAuthority = {MODULEID};
    // 查询功能权限
    private String[] queryAuthority = {APPCODE};
    // 绑定功能权限
    private String[] addAuthorityToRole = {ROLEID,MODULEID,APPCODE};
    // 解绑功能权限
    private String[] removeAuthorityFromRole = {ROLEID,MODULEID};

    // 添加数据权限
    private String[] addDataValue = {APPCODE, VALUE, DATAVALUEDESC};
    // 删除数据权限
    private String[] deleteDataValue = {DATAVALUEID};
    // 更新数据权限
    private String[] updateDataValue = {DATAVALUEID};
    // 查询数据权限
    private String[] queryDataValue = {APPCODE};
    // 绑定数据权限
    private String[] addDataValueToRole = {DATAVALUEID,ROLEID,APPCODE};
    // 解绑数据权限
    private String[] removeDataValueFromRole = {DATAVALUEID,ROLEID};

    // 添加角色
    private String[] addRole = {APPCODE,ROLENAME,ROLEDESC};
    // 更新角色
    private String[] updateRole = {ROLEID,APPCODE,ROLENAME,ROLEDESC};
    // 删除角色
    private String[] deleteRole ={ROLEID};
    // 查询角色
    private String[] queryRole = {APPCODE};

    // 生成短信验证码
    private String[] generateMessageCode = {PHONE};
    // 验证短信验证码
    private String[] verifyMessageCode = {PHONE, MESSAGECODE};
    // 免密码登录
    private String[] noPasswordLogin = {PHONE, APPCODE, ROLENAME, MESSAGECODE};

    // 授权登录
    private String[] setPermission = {USERID, APPCODE, ROLENAME};

    private String[] getUserIdByUid = {"id"};

    private String[] getUserInfoByOpenId = {"open_id"};
}
