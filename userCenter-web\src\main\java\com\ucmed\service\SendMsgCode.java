package com.ucmed.service;

import com.ucmed.common.service.CommonService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ucmed.util.JsonFormat;
import com.ucmed.util.SendMsgUtil;

import net.sf.json.JSONObject;

@Service
public class SendMsgCode extends CommonService {

    @Autowired
    private YLXZService ylxzService;
    /**
	 * 发送短信验证码
	 */
	public String sendMsgCode(JSONObject rcv) {

        // 移动远程医生用户
        if(isRemoteMedical(rcv)) {
            return transRetStr(JSONObject.fromObject(ylxzService.sendVerifyCode(rcv)));
        }

		String phone = rcv.getString("phone");
		String msgText = rcv.getString("msgText");
		String code = sendMsgCode(phone, msgText);
		if("".equals(code)) {
			return JsonFormat.retFormat(1, "验证码发送失败");
		} else {
			return JsonFormat.retFormat(0, "验证码发送成功", code);
		}
	}

	/**
	 * 发送短信验证码
	 */
	private String sendMsgCode(String phone, String msgText) {
		if(isNotPhonePattern(phone)) {
            return "";
        }
        JSONObject req = new JSONObject();
        String numStr = "**********";
        String code = "";
        for (int i = 0; i < 4; i++) {
            double r = Math.random() * 10;
            code += numStr.charAt((int)r);
        }
        req.put("msg", msgText + "【" + code + "】");
        req.put("phone", phone);
        JSONObject res = SendMsgUtil.httpRequest(req);
        int ret = res.optInt("R");
        if(ret == 200){
            return code;
        }
        return "";
	}
}
