package com.ucmed.dto;

import java.io.Serializable;

/**
 * 线下门诊管理系统用户信息对象
 *
 * <AUTHOR>
 * @date 2017/10/25 13:43
 */
public class ClinicUserInfo implements Serializable {

    private static final long serialVersionUID = -2021527592316127980L;

    /**
     * 用户ID
     */
    private String userId;
    /**
     * 手机号
     */
    private String phone;
    /**
     * 身份证号码
     */
    private String idCard;
    /**
     * 家属电话
     */
    private String familyPhone;
    /**
     * 家庭地址
     */
    private String address;
    /**
     * 年龄
     */
    private Integer age;
    /**
     * 性别
     */
    private String sex;
    /**
     * 姓名
     */
    private String theName;
    /**
     * 页码
     */
    private Integer pageNo;
    /**
     * 页长
     */
    private Integer pageSize;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    public String getFamilyPhone() {
        return familyPhone;
    }

    public void setFamilyPhone(String familyPhone) {
        this.familyPhone = familyPhone;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getTheName() {
        return theName;
    }

    public void setTheName(String theName) {
        this.theName = theName;
    }

    public Integer getPageNo() {
        return pageNo;
    }

    public void setPageNo(Integer pageNo) {
        this.pageNo = pageNo;
    }

    public Integer getPageSize() {
        return pageSize;
    }

    public void setPageSize(Integer pageSize) {
        this.pageSize = pageSize;
    }

    @Override
    public String toString() {
        return "ClinicUserInfo{" +
                "userId='" + userId + '\'' +
                ", phone='" + phone + '\'' +
                ", idCard='" + idCard + '\'' +
                ", familyPhone='" + familyPhone + '\'' +
                ", address='" + address + '\'' +
                ", age=" + age +
                ", sex='" + sex + '\'' +
                ", theName='" + theName + '\'' +
                ", pageNo=" + pageNo +
                ", pageSize=" + pageSize +
                '}';
    }
}
