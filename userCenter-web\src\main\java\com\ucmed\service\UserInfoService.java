package com.ucmed.service;

import com.ucmed.bean.UCResponse;
import com.ucmed.bean.UserInfo;
import com.ucmed.dto.UserDTO;
import com.ucmed.dto.UserInfoExcelDTO;
import com.ucmed.exception.BusinessException;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * 用户信息服务接口类
 * Created by QIUJIAHAO on 2016/9/18.
 */
public interface UserInfoService {

    /**
     * 根据user_id返回用户信息
     */
    String getUserInfo(JSONObject rcv);

    /**
     * 查询用户信息
     *
     * @param token token
     * @return UserInfo
     * @throws BusinessException 401 会话已失效
     */
    UserInfo getUserInfo(String token) throws BusinessException;

    /**
     * 查询用户信息
     *
     * @param userId 用户名或手机号
     * @return UserInfo
     * @throws BusinessException 1 账号未注册
     */
    UserInfo getUserInfoByUserId(String userId) throws BusinessException;

    /**
     * 更新用户信息
     */
    String updateUserInfo(JSONObject rcv);

    /**
     * 查询用户登录信息、关联信息
     *
     * @param userId 用户名
     * @return UserDTO
     */
    UserDTO getUserDTO(String userId);

    /**
     * 更新用户信息
     *
     * @param userInfo 用户信息
     */
    void updateUserInfo(UserInfo userInfo) throws IllegalArgumentException;

    /**
     * 新建用户信息
     *
     * @param userInfo 用户信息
     */
    void saveUserInfo(UserInfo userInfo) throws IllegalArgumentException;

    /**
     * 解密用户信息
     *
     * @param userInfo 用户信息
     * @return
     */
    UserInfo decryptUserInfo(UserInfo userInfo);

    /**
     * 根据open_id查询用户信息
     *
     * @param openId 第三方的openid
     * @return
     */
    UCResponse getUserInfoByOpenId(String openId, String appCode);

    /**
     * 获取该项目下的一段时间内注册的手机
     *
     * @param startTime
     * @param endTime
     * @param projCode
     * @return
     */
    List<UserInfoExcelDTO> listUserInfoWithinTimeByProjCode(String startTime, String endTime, Integer projCode);

    /**
     * 分页获取该项目下的一段时间内注册的手机
     *
     * @param startTime
     * @param endTime
     * @param projCode
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<UserInfoExcelDTO> listUserInfoWithinTimeByProjCodeLimitPage(String startTime, String endTime,
                                                                     Integer projCode, Integer pageNum, Integer pageSize);

    Integer countRegisteredPhone(String startTime, String endTime, Integer projCode);
}
