package com.ucmed.exception;

public class BusinessException extends Exception {

    private static final long serialVersionUID = -755264994848644387L;

    private Integer code;
    private String message;

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
        this.message = message;
    }

    public Integer getCode() {
        return code;
    }

    @Override
    public String getMessage() {
        return message;
    }
}
