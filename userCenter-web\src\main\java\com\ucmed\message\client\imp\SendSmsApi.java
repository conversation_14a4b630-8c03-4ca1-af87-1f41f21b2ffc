package com.ucmed.message.client.imp;

import cn.ucmed.common.net.HttpClient;
import com.alibaba.fastjson.JSON;
import com.jfinal.json.Json;
import com.ucmed.dto.MessageBody;
import com.ucmed.dto.MessageResult;
import org.apache.log4j.Logger;

/**
 * Created by ucmed on 2017/6/6.
 */
public class SendSmsApi {

    private static final Logger LOG = Logger.getLogger(SendSmsApi.class);
    private static HttpClient httpClient;
    private static final String platSmsHttpUrl = "http://smsplat.zwjk.com:8888/sms/send";

    public static MessageResult sendMessage(MessageBody messageBody) {
        LOG.debug("SendSmsApi-sendMessage:" + messageBody);
        setSingleHttpClient();

        MessageResult messageResult;  //���ز���
        String messageResultStr;  //������Ϣ �ַ�
        messageResultStr = httpClient.sendSynchronousRequest(Json.getJson().toJson(messageBody));
        LOG.debug(JSON.toJSON(messageBody) + "messageResultStr:" + messageResultStr);
        messageResult = JSON.parseObject(messageResultStr, MessageResult.class);
        return messageResult;
    }

    private static void setSingleHttpClient() {
        if (httpClient == null) {
            httpClient = new HttpClient(platSmsHttpUrl);
        }
    }
}
