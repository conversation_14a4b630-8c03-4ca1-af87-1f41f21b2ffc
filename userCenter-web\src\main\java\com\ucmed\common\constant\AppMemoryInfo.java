package com.ucmed.common.constant;

import com.ucmed.bean.AppIndPwd;
import com.ucmed.bean.UserAppToken;
import com.ucmed.mapper.SecurityApplicationMapper;
import com.ucmed.mapper.SecurityProjectMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.Map;

@Component
public class AppMemoryInfo {
    @Autowired
    SecurityApplicationMapper securityApplicationMapper;
    @Autowired
    SecurityProjectMapper securityProjectMapper;

    public static Map<Integer, UserAppToken> appTokenlife = null;

    public long getExpiredTime(int appCode) {
        if (appTokenlife == null) {
            appTokenlife = securityApplicationMapper.seclectAllAppTokenLife();
        }
        return appTokenlife.get(appCode).getExpiredTime();
    }

    public static Map<Integer, AppIndPwd> proIndependentPwd = null;

    public boolean getProIndependentPwd(int proCode) {
        if (proIndependentPwd == null) {
            proIndependentPwd = securityProjectMapper.seclectAllProIndependentPwd();
        }
        return proIndependentPwd.get(proCode).isIndPwd();
    }


    public static Map<Integer, AppIndPwd> appIndependentPwd = null;

    public boolean getAppIndependentPwd(int appCode) {
        if (appIndependentPwd == null) {
            appIndependentPwd = securityProjectMapper.seclectAllAppIndependentPwd();
        }
        return appIndependentPwd.get(appCode).isIndPwd();
    }
}
