package com.ucmed.service;

import com.ucmed.bean.SecurityUserApp;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2017/11/9 9:32
 */
public interface SecurityUserAppService {

    /**
     * 查询用户已注册的应用
     *
     * @param userId 用户名
     * @return
     */
    List<SecurityUserApp> getApps(String userId);

    /**
     * 判断用户是否已注册指定应用
     *
     * @param userId  用户ID
     * @param appCode 应用ID
     * @return 已注册 - true, 未注册 - false
     */
    boolean isUserInApp(String userId, int appCode);

    /**
     * 绑定用户与应用
     *
     * @param userId  用户ID
     * @param appCode 应用ID
     */
    void saveUserApp(String userId, int appCode);
}
