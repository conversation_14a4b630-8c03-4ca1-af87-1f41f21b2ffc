package com.ucmed.mapper;

import com.ucmed.bean.JcUser;
import com.ucmed.bean.controllerbean.ThridUserTestBean;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

/**
 * Created by XXB-QJH-1303.
 * Date: 2017/9/25 15:34
 */
public interface JcUserMapper extends Mapper<JcUser> {
    @Select("select ju.phone,jutp.open_id, jutp.third_party_type from jc_user ju, jc_user_third_party jutp\n" +
            "WHERE\n" +
            "jutp.deletion = '0'\n" +
            "and jutp.create_by in ('734', '735')\n" +
            "and ju.user_id = jutp.user_id")
    @Results({
            @Result(column = "open_id", property = "openId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "third_party_type", property = "type", jdbcType = JdbcType.VARCHAR),
            @Result(column = "phone", property = "phone", jdbcType = JdbcType.VARCHAR)
    })
    List<ThridUserTestBean> listThridUserTestBean();
}
