package com.ucmed.bean.controllerbean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="发送短信",description="发送短信模型")
public class MsgCodeBean {
    @ApiModelProperty(value="登录名", name="userId", required = true, example="string")
    private String phone;
    @ApiModelProperty(value="类型：0:注册 1:忘记密码 2:登录 3:修改手机号", name="type", required = true, example="string")
    private String type;
    @ApiModelProperty(value="图形验证码", name="picCode", required = true, example="string")
    private String picCode;
    @ApiModelProperty(value="是否检查用户为卓健用户", name="checkoutAccount", example="true")
    private Boolean checkoutAccount;
    @ApiModelProperty(value="短信token（checkoutAccount为false时生效）", name="smsToken", example="string")
    private String smsToken;

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    public String getPicCode() {
        return picCode;
    }

    public void setPicCode(String picCode) {
        this.picCode = picCode;
    }

    public Boolean getCheckoutAccount() {
        return checkoutAccount;
    }

    public void setCheckoutAccount(Boolean checkoutAccount) {
        this.checkoutAccount = checkoutAccount;
    }

    public String getSmsToken() {
        return smsToken;
    }

    public void setSmsToken(String smsToken) {
        this.smsToken = smsToken;
    }
}
