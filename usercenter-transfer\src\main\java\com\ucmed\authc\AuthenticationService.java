package com.ucmed.authc;

import com.ucmed.bean.UC_UserInfo;

/**
 * 身份验证操作
 * Created by <PERSON> on 2017/1/17.
 */
public interface AuthenticationService {

    /**
     * 此方法用于初始化缓存token，如token存在，则不做任何事，结构为如下
     * <p/>
     * ---------------------------------
     * - hash：token.tokenKey          -
     * ---------------------------------
     * - token.prodKey  - token.value  -
     * ---------------------------------
     * - token.prodKey  - token.value  -
     * ---------------------------------
     *
     * @param token token中 tokenKey,prodKey,value,user为必传
     */
    void init(AuthenticationToken token);


    /**
     * 此方法用于更新缓存缓存token，如token不存在则不做任何事，结构为如下
     * <p/>
     * ---------------------------------
     * - hash：token.tokenKey          -
     * ---------------------------------
     * - token.prodKey  - token.value  -
     * ---------------------------------
     * - token.prodKey  - token.value  -
     * ---------------------------------
     *
     * @param token token中 tokenKey,prodKey,value,user为必传
     */
    void update(AuthenticationToken token);

    /**
     * 此方法用于保存token，不论token是否存在，都会创建/更新缓存，结构为如下
     * <p/>
     * ---------------------------------
     * - hash：token.tokenKey          -
     * ---------------------------------
     * - token.prodKey  - token.value  -
     * ---------------------------------
     * - token.prodKey  - token.value  -
     * ---------------------------------
     *
     * @param token token中 tokenKey,prodKey,value,user为必传
     */
    void save(AuthenticationToken token);

    /**
     * 校验token对象
     *
     * @param token token中 tokenKey,prodKey为必传
     * @return 登录时存入的value
     */
    Object authenticate(AuthenticationToken token);

    /**
     * 校验用户中心GENERAL token
     *
     * @param tokenkey 用户中心token
     * @return 登录时用户中心存入的value
     */
    Object authenticate(String tokenkey);

    /**
     * 注销某产品线用户
     *
     * @param token token中 tokenKey,prodKey为必传
     */
    void clear(AuthenticationToken token);

    /**
     * 用户中心注册
     *
     * @param userInfo 注册信息
     * @return user_id为官方使用id标识
     * @throws Exception
     */
    String registration(UC_UserInfo userInfo);

}
