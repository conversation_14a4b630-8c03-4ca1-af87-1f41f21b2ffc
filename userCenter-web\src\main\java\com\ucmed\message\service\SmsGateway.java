package com.ucmed.message.service;

import cn.ucmed.common.util.PaginationResult;
import com.ucmed.message.model.TSmsGateway;

/**
 * Created by ucmed on 2015/12/18.
 */
public interface SmsGateway {

    String PROTOCOL_HTTP = "http";
    String PROTOCOL_PLATSMS = "platsms";
    String PROTOCOL_WEBSERVICE = "webservice";
    String PROTOCOL_DUBBO_RENDER = "dubbo_render";

    TSmsGateway loadGateway(String appCode);

    /**
     * 获取短信网关地址
     *
     * @param appCode
     * @return 网关地址
     */
    String loadGatewayUrl(String appCode);

    /**
     * 分页获取列表
     *
     * @param pageNo
     * @param pageSize
     * @return
     */
    PaginationResult<TSmsGateway> getPaginatedList(String appCode, int pageNo, int pageSize);

    /**
     * 新增
     *
     * @param tSmsGateway
     * @return
     */
    int add(String appCode, TSmsGateway tSmsGateway);

    /**
     * 更新
     *
     * @param tSmsGateway
     * @return
     */
    int update(TSmsGateway tSmsGateway);

}
