package com.ucmed.dto;

import java.io.Serializable;

/**
 * Created by XXB-QJH-1303.
 * Date: 2017/9/25 14:14
 */
public class UserProjectDTO implements Serializable {
    private static final long serialVersionUID = 1070637807855084979L;

    private Integer projCode;
    private String openId;
    private Integer appCode;
    private Integer roleId;
    private String roleName;
    private String pushId;
    private String dl_pwd;
    private String securityKey;

    public static long getSerialVersionUID() {
        return serialVersionUID;
    }

    public String getDl_pwd() {
        return dl_pwd;
    }

    public void setDl_pwd(String dl_pwd) {
        this.dl_pwd = dl_pwd;
    }

    public String getSecurityKey() {
        return securityKey;
    }

    public void setSecurityKey(String securityKey) {
        this.securityKey = securityKey;
    }

    public Integer getProjCode() {
        return projCode;
    }

    public void setProjCode(Integer projCode) {
        this.projCode = projCode;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }

    public Integer getAppCode() {
        return appCode;
    }

    public void setAppCode(Integer appCode) {
        this.appCode = appCode;
    }

    public Integer getRoleId() {
        return roleId;
    }

    public void setRoleId(Integer roleId) {
        this.roleId = roleId;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }
}
