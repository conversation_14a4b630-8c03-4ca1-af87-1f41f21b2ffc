package com.ucmed.authc;

import java.io.Serializable;

/**
 * Created by <PERSON> on 2017/1/17.
 */
public interface AuthenticationToken extends Serializable {

    /**
     * @return 获取Token对象对应的Hash
     */
    String getTokenKey();

    /**
     * @return 超时时间，单位为【分钟】，null值为永不过期
     */
    Integer getTimeOut();

    /**
     * @return 产品线对应的key
     */
    EnumProductKey getProdKey();

    /**
     * @return 缓存的value
     */
    Object getValue();
}
