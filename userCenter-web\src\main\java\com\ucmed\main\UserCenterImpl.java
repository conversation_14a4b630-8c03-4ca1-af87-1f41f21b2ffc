package com.ucmed.main;

import javax.jws.WebService;

import org.apache.log4j.Logger;
import com.ucmed.service.*;
import org.springframework.beans.factory.annotation.Autowired;
import com.ucmed.util.JsonFormat;

import net.sf.json.JSONObject;

import java.lang.reflect.Field;

import static com.ucmed.common.constant.CommonConstant.*;
import static com.ucmed.common.constant.InterfaceName.*;
import static com.ucmed.common.constant.YLXZInterface.*;

/**
 * UserCenterUCMED WEB SERVICE
 * <AUTHOR>
 * @vresion 2016-07-19
 */
@WebService(endpointInterface = "com.ucmed.main.UserCenter")
public class UserCenterImpl implements UserCenter {
	private static Logger log4j = Logger.getLogger(UserCenterImpl.class);
	@Autowired private UserService userService;
	@Autowired private UserWebService userWebService;
	@Autowired private SendMsgCode sendMsgCodeService;
	@Autowired private PatientService patientService;
	@Autowired private GetAppInfoService getAppInfoService;
    @Autowired private UserInfoService userInfoService;
    @Autowired private WechatService wechatService;
    @Autowired private AuthorityService authorityService;
    @Autowired private RoleService roleService;
    @Autowired private YLXZService ylxzService;


	public String userCenter(String operation,String rcvData) {
	    if(operation == null || "".equals(operation)) {
            return JsonFormat.retFormat(404,"未找到功能，请检查operation参数");
        }
        if(rcvData == null || "".equals(rcvData)) {
	        return JsonFormat.retFormat(103, "参数有误，请检查参数");
        }
		try {
			JSONObject rcv = JSONObject.fromObject(rcvData);
//            if (!rcv.containsKey(USER_TYPE) && !isRcvDataCorrect(operation, rcv)) {
//                String retJson = JsonFormat.retFormat(103, "参数有误，请检查参数");
//                log(operation, rcvData, retJson);
//                return retJson;
//            }
            if (rcv.containsKey(APPCODE)) {
                int app_code = rcv.optInt(APPCODE);
                if (!getAppInfoService.isAppExists(app_code)) {
                    String retJson = JsonFormat.retFormat(104, "应用未注册，请检查app_code");
                    log(operation, rcvData, retJson);
                    return retJson;
                }
            }
/*            if (rcv.containsKey(APPCODE) && !rcv.containsKey(USER_TYPE)) {
                int app_code = rcv.optInt(APPCODE);
                if (!getAppInfoService.isAppExists(app_code)) {
                    String retJson = JsonFormat.retFormat(104, "应用未注册，请检查app_code");
                    log(operation, rcvData, retJson);
                    return retJson;
                }
            }*/
            if (rcv.containsKey(APPCODE)) {
                int app_code = rcv.optInt(APPCODE);
                if (!getAppInfoService.isAppExists(app_code)) {
                    String retJson = JsonFormat.retFormat(104, "应用未注册，请检查app_code");
                    log(operation, rcvData, retJson);
                    return retJson;
                }
            }
            String return_param = "";
            switch (operation) {
                //注册
                case REGISTRATION:
                    return_param = userWebService.registration(rcv);
                    break;
                //登录
                case LOGIN:
                    return_param = userWebService.login(rcv);
                    break;
                //修改密码
                case CHANGEPASSWORD:
                    return_param = userWebService.changePwd(rcv);
                    break;
                //重置密码
                case REINPUTPASSWORD:
                    return_param = userWebService.reInputPassword(rcv);
                    break;
                //退出登录
                case LOGOUT:
                    return_param = userWebService.logout(rcv);
                    break;
                //token有效性
                case ISTOKENVALID:
                    return_param = userWebService.isTokenValid(rcv);
                    break;
                //用户是否注册某角色
                case ISUSERINROLE:
                    return_param = userWebService.isUserInRole(rcv);
                    break;
                // 2016-10-24
                // 根据应用和角色查用户
                case FINDUSERBYAPPANDROLE:
                    return_param = userWebService.findUserByAppAndRole(rcv);
                    break;

                //修改手机号
                case CHANGEPHONE:
                    return_param = userWebService.changePhone(rcv);
                    break;
                //发送验证码
                case SENDMSGCODE:
                    return_param = sendMsgCodeService.sendMsgCode(rcv);
                    break;
                //查询用户基本信息
                case GETUSERINFO:
                    return_param = userInfoService.getUserInfo(rcv);
                    break;
                //更改用户基本信息
                case UPDATEUSERINFO:
                    return_param = userInfoService.updateUserInfo(rcv);
                    break;
                //绑定微信
                case WECHATBINDING:
                    return_param = wechatService.bindWechat(rcv);
                    break;
                //微信登录
                case WECHATLOGIN:
                    return_param = wechatService.loginByWechat(rcv);
                    break;
                //添加就诊人
                case ADDPATIENT:
                    return_param = patientService.addPatient(rcv);
                    break;
                //修改就诊人
                case UPDATEPATIENT:
                    return_param = patientService.updatePatient(rcv);
                    break;
                //查询就诊人
                case GETPATIENTINFO:
                    return_param = patientService.getPatientInfo(rcv);
                    break;
                //功能权限
                case GETAUTHORITY:
                    return_param = authorityService.getAuthority(rcv);
                    break;
                //数据权限
                case GETDATAVALUE:
                    return_param = authorityService.getDataValue(rcv);
                    break;
                //查询用户应用程序信息
                case GETAPPSBYUSER:
                    return_param = getAppInfoService.getAppsByUser(rcv);
                    break;
                //查询用户角色信息
                case GETROLESBYUSER:
                    return_param = getAppInfoService.getRolesByUser(rcv);
                    break;
                //查询应用程序绑定角色信息
                case GETROLESBYAPP:
                    return_param = getAppInfoService.getRolesByApp(rcv);
                    break;
                //查询用户在应用程序下角色信息
                case GETROLESBYAPPANDUSER:
                    return_param = getAppInfoService.getRolesByAppAndUser(rcv);
                    break;
                // 2016-11-15
//                // 添加功能点
//                case ADDAUTHORITY:
//                    return_param = authorityService.addAuthority(rcv);
//                    break;
//                // 删除功能点
//                case DELETEAUTHORITY:
//                    return_param = authorityService.deleteAuthority(rcv);
//                    break;
//                // 修改功能点
//                case UPDATEAUTHORITY:
//                    return_param = authorityService.updateAuthority(rcv);
//                    break;
//                // 查询功能点
//                case QUERYAUTHORITY:
//                    return_param = authorityService.queryAuthority(rcv);
//                    break;
//                // 绑定功能权限
//                case ADDAUTHORITYTOROLE:
//                    return_param = authorityService.addAuthorityToRole(rcv);
//                    break;
//                // 解绑功能权限
//                case REMOVEAUTHORITYFROMROLE:
//                    return_param = authorityService.removeAuthorityFromRole(rcv);
//                    break;
//
//                // 2016-11-16
//                // 添加数据权限
//                case ADDDATAVALUE:
//                    return_param = authorityService.addDataValue(rcv);
//                    break;
//                // 删除数据权限
//                case DELETEDATAVALUE:
//                    return_param = authorityService.deleteDataValue(rcv);
//                    break;
//                // 修改数据权限
//                case UPDATEDATAVALUE:
//                    return_param = authorityService.updateDataValue(rcv);
//                    break;
//                // 查询数据权限
//                case QUERYDATAVALUE:
//                    return_param = authorityService.queryDataValue(rcv);
//                    break;
//                // 绑定数据权限
//                case ADDDATAVALUETOROLE:
//                    return_param = authorityService.addDataValueToRole(rcv);
//                    break;
//                // 解绑数据权限
//                case REMOVEDATAVALUEFROMROLE:
//                    return_param = authorityService.removeDataValueFromRole(rcv);
//                    break;
//
//                // 2016-11-18
//                // 新建角色
//                case ADDROLE:
//                    return_param = roleService.addRole(rcv);
//                    break;
//                // 修改角色
//                case UPDATEROLE:
//                    return_param = roleService.updateRole(rcv);
//                    break;
//                // 删除角色
//                case DELETEROLE:
//                    return_param = roleService.deleteRole(rcv);
//                    break;
//                // 查询角色
//                case QUERYROLE:
//                    return_param = roleService.queryRole(rcv);
//                    break;
                // 生成短信验证码 2017-04-26
                case GENERATEMESSAGECODE:
                    return_param = userWebService.generateMessageCode(rcv);
                    break;
                // 验证短信验证码 2017-04-26
                case VERIFYMESSAGECODE:
                    return_param = userWebService.verifyMessageCode(rcv);
                    break;
                // 免密码登录 2017-04-27
                case NOPASSWORDLOGIN:
                    return_param = userWebService.noPasswordLogin(rcv);
                    break;
                // 授权登录
                case SETPERMISSION:
                    return_param = userWebService.setPermission(rcv);
                    break;
                // -- 移动远程第三方用户接口 2017-05-22 --
                // 判断医生账号是否存在
                case ISUSEREXIST:
                    return_param = ylxzService.isUserExist(rcv);
                    break;
                // 获取医院列表
                case GETHOSPITALLIST:
                    return_param = ylxzService.getHospitalList(rcv);
                    break;
                // -- 移动远程第三方用户接口 2017-05-22 --
                case "getUserIdByUid":
                    return_param = userWebService.getUserIdByUid(rcv);
                    break;
                case "getUserInfoByOpenId":
                    return_param = userWebService.getUserInfoByOpenId(rcv);
                    break;
                default:
                    return_param = JsonFormat.retFormat(404,"未找到功能，请检查operation参数");
                    break;
            }
            log(operation, rcvData, return_param);
            return return_param;
        } catch (Exception e) {
            e.printStackTrace();
            String retJson = JsonFormat.retFormat(-999,"系统内部错误");
            log(operation, rcvData, retJson);
            return retJson;
        }
	}

    /**
     * 检查参数
     * @param operation
     * @param rcvData
     * @return
     */
    private boolean isRcvDataCorrect(String operation, JSONObject rcvData) {
        // 获取入参规则
        String[] definedRcvData = getDefinedRcvData(operation);
        if(definedRcvData == null) {
            return false;
        }
        for (String field : definedRcvData) {
            if(!rcvData.containsKey(field)) {
                return false;
            }
        }
        return true;
    }

    /**
     * 获取入参规则
     * @param operation
     * @return
     */
    private String[] getDefinedRcvData(String operation) {
        Class commonConstantClass;
        Object commonConstant;
        Field operationField;
        String[] DefinedRcvData = null;
        try {
            commonConstantClass = Class.forName("com.ucmed.common.constant.RcvData");
            commonConstant = commonConstantClass.newInstance();
            operationField = commonConstantClass.getDeclaredField(operation);
            operationField.setAccessible(true);
            DefinedRcvData = (String[]) operationField.get(commonConstant);
        } catch (ClassNotFoundException | InstantiationException | NoSuchFieldException | IllegalAccessException e) {
            //e.printStackTrace();
        }
        return DefinedRcvData;
    }

    private void log(String operation, String rcvData, String retJson) {
        JSONObject rcvDataJson = JSONObject.fromObject(rcvData);
        rcvDataJson.discard(PASSWORD);
        rcvDataJson.discard(NEWPASSWORD);
        log4j.info(operation + " LOGUSERCENTER {\"input_param\":" + rcvDataJson.toString() + ",\"return_param\":" + retJson + "}");
    }
}
