package com.ucmed.transfer;

import cn.ucmed.common.rubikexception.BusinessException;
import com.ucmed.authc.AuthenticationService;
import com.ucmed.authc.AuthenticationServiceImpl;
import com.ucmed.authc.AuthenticationToken;
import com.ucmed.authc.EnumProductKey;
import com.ucmed.bean.SimpleToken;
import com.ucmed.bean.UC_UserInfo;
import com.ucmed.common.constant.CommonConstant;
import com.ucmed.service.UserService;
import com.ucmed.service.UserWebService;
import net.sf.json.JSONObject;
import org.apache.commons.lang.NotImplementedException;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.log4j.Logger;
import org.apache.shiro.authc.AuthenticationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Set;

import static com.ucmed.common.constant.CommonConstant.*;

@Transactional
@Service("userCenterAuthenticationServiceImpl")
public class UserCenterAuthenticationServiceImpl implements AuthenticationService {

    Logger logger = Logger.getLogger(UserCenterAuthenticationServiceImpl.class);

    @Autowired
    private UserWebService userWebService;

    @Autowired
    private RedisTemplate redisTemplate;

    @Override
    public void init(AuthenticationToken token) {
        if (!exists(token)) {
            save(token);
        }
    }

    @Override
    public void update(AuthenticationToken token) {
        if (exists(token)) {
            save(token);
        }
    }

    @Override
    public void save(AuthenticationToken token) {

        validateToken(token);

        if (token.getValue() == null) {
            throw new AuthenticationException("Token value could not be null");
        }

        if (token.getTimeOut() != null) {
            redisTemplate.boundHashOps(token.getTokenKey()).put(
                    token.getProdKey().name() + com.ucmed.common.constants.CommonConstant.POSTFIX_EXPIRED_TIME,
                    DateUtils.addMinutes(new Date(), token.getTimeOut()));
        }

        redisTemplate.boundHashOps(token.getTokenKey()).put(token.getProdKey().name(), token.getValue());
    }

    @Override
    public Object authenticate(AuthenticationToken token) {

        validateToken(token);

        Object cachedToken = redisTemplate.opsForHash().get(
                token.getTokenKey(), token.getProdKey().name());
        if (cachedToken == null) {
            clear(token);
            throw new AuthenticationException();
        }

        Date codeExpiresTime = (Date) redisTemplate.opsForHash().get(
                token.getTokenKey(), token.getProdKey().name() + com.ucmed.common.constants.CommonConstant.POSTFIX_EXPIRED_TIME);
        if (codeExpiresTime != null) {

            if (codeExpiresTime.before(new Date())) {
                clear(token);
                throw new AuthenticationException();
            }

            redisTemplate.boundHashOps(token.getTokenKey()).put(token.getProdKey().name() + com.ucmed.common.constants.CommonConstant.POSTFIX_EXPIRED_TIME,
                    DateUtils.addMinutes(new Date(), token.getTimeOut()));
        }

        Long size = redisTemplate.opsForHash().size(token.getTokenKey());
        if (size == 1) {
//            log4j.info("AuthenticationServiceImpl  测试用：删除了token" + token.getTokenKey());
            redisTemplate.delete(token.getTokenKey());
            throw new AuthenticationException();
        }

        return cachedToken;
    }

    @Override
    public Object authenticate(String tokenkey) {

        return authenticate(new SimpleToken(tokenkey, EnumProductKey.GENERAL));
    }

    @Override
    public void clear(AuthenticationToken token) {

        validateToken(token);
        clearProductToken(token);
        clearGeneral(token);
    }

    private void clearProductToken(AuthenticationToken token) {

        redisTemplate.boundHashOps(token.getTokenKey()).delete(token.getProdKey().name());
        redisTemplate.boundHashOps(token.getTokenKey()).delete(
                token.getProdKey().name() + com.ucmed.common.constants.CommonConstant.POSTFIX_EXPIRED_TIME);
    }


    private void validateToken(AuthenticationToken token) {

        Object generalToken = redisTemplate.opsForHash().get(
                token.getTokenKey(), EnumProductKey.GENERAL.name());
        if (generalToken == null) {
            throw new AuthenticationException(
                    new StringBuffer("When save,authenticate and clear token, ")
                            .append("the GENERAL token generated by usercenter should exists in the cache, ")
                            .append("if not, the possible reason is the application connect to usercenter use the")
                            .append("DIFFERENT USERCENTER WEBSERVICE AND REDIS, token key is ")
                            .append(token.getTokenKey())
                            .append(", token prodkey is ")
                            .append(token.getProdKey().name()).toString());
        }

        if (StringUtils.isBlank(token.getTokenKey())) {
            throw new AuthenticationException("Token key could not be null");
        }

        if (token.getProdKey() == null) {
            throw new AuthenticationException("Token prod key could not be null");
        }
    }

    protected void clearGeneral(AuthenticationToken token) {

        HashMap<EnumProductKey, Object> sessions =
                (HashMap<EnumProductKey, Object>) redisTemplate.opsForHash().entries(token.getTokenKey());
        if (sessions.size() == 1 && sessions.containsKey(EnumProductKey.GENERAL.name())) {
//            log4j.info("AuthenticationServiceImpl  测试用：删除了token" + token.getTokenKey());

            redisTemplate.delete(token.getTokenKey());
        }
    }

    private boolean exists(AuthenticationToken token) {
        return redisTemplate.opsForHash().get(
                token.getTokenKey(), token.getProdKey().name()) != null;
    }

    @Override
    public String registration(UC_UserInfo userInfo) {

        JSONObject ret = new JSONObject();

        ret.put("user_id", userInfo.getUser_id());
        ret.put("password", userInfo.getPassword());
        ret.put("phone", userInfo.getPhone());
        ret.put("app_code", userInfo.getApp_code());
        ret.put("role", userInfo.getRole());

        JSONObject res = JSONObject.fromObject(userWebService.registration(ret));

        assert res != null;

        switch (res.optInt("retCode")) {

            case 0:
            case 1:
            case 2:
                return res.optJSONObject("param").optString(USERID);

            case 3:
                throw new BusinessException(-1, "UCMED:密码强度不够。");

            case 4:
            case 5:
            case 103:
                throw new BusinessException(-1, "Invalid input params.");

            default:
                throw new BusinessException(-1, "Invalid retCode from UCUserService.");
        }
    }

    /**
     * 过滤所有产品线的session，如果有失效（产品code+_expiresAt）的就删除,否则则延期，此方法会更新redis
     *
     * @param token
     * @return
     */
    //TODO　此方法未测试！！！
    public HashMap<EnumProductKey, Object> authenticateAll(AuthenticationToken token) {

        HashMap<EnumProductKey, Object> sessions =
                (HashMap<EnumProductKey, Object>) redisTemplate.opsForHash().entries(token.getTokenKey());

        if (sessions == null) {
            throw new AuthenticationException();
        }

        Set<Object> keys = new HashSet<>();

        for (EnumProductKey key : sessions.keySet()) {
            if (key.name().endsWith(CommonConstant.POSTFIX_EXPIRED_TIME) &&
                    new Date(Long.parseLong(sessions.get(key).toString())).before(new Date())) {
                keys.add(key);
                keys.add(key.name().substring(0, key.name().indexOf(CommonConstant.POSTFIX_EXPIRED_TIME)));
            } else {
                sessions.put(key, DateUtils.addMinutes(new Date(), token.getTimeOut()));
            }
        }
        sessions.keySet().removeAll(keys);

        if (sessions.size() <= 0) {
            redisTemplate.delete(token.getTokenKey());
            throw new AuthenticationException();
        } else {
            redisTemplate.boundHashOps(token.getTokenKey()).putAll(sessions);
        }
        return sessions;
    }

}
