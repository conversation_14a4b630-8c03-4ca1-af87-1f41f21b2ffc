package com.ucmed.transfer;

import com.ucmed.bean.*;
import com.ucmed.common.service.CommonService;
import com.ucmed.dao.GetAppInfoDao;
import com.ucmed.exception.BusinessException;
import com.ucmed.service.AuthorityService;
import com.ucmed.service.GetAppInfoService;
import com.ucmed.service.PermissionService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

import static com.ucmed.common.constants.CommonConstant.USER_PERMISSION_KEY;

/**
 * Created by XXB-QJH-1303.
 * Date: 2017/9/6 16:07
 */
@Service
public class PermissionServiceImpl extends CommonService implements PermissionService {

    @Autowired
    private AuthorityService authorityService;
    @Autowired
    private GetAppInfoService getAppInfoService;

    @Override
    public Permission getPermission(String userId, int appCode) throws BusinessException {
        UC_UserInfo user = getUser(userId);
        if (user == null) {
            throw new BusinessException(3, "该账号未注册");
        }
        if (!getAppInfoService.isAppExists(appCode)) {
            throw new BusinessException(-1, "应用未注册");
        }
        return authorityService.getPermission(user.getUser_id(), appCode);
    }
}
