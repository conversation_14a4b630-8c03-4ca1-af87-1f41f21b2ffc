package com.ucmed.authc;

import com.ucmed.bean.SimpleToken;
import com.ucmed.bean.UC_UserInfo;
import com.ucmed.common.constants.CommonConstant;
import org.apache.commons.lang.NotImplementedException;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang.time.DateUtils;
import org.apache.shiro.authc.AuthenticationException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.HashMap;
import java.util.logging.Logger;

/**
 * Created by <PERSON> on 2017/1/17.
 */
public class AuthenticationServiceImpl implements AuthenticationService {
    private static Logger log4j = Logger.getLogger(AuthenticationServiceImpl.class.getName());

    @Autowired
    private RedisTemplate redisTemplate;

    public void init(AuthenticationToken token) {
        if (!exists(token)) {
            save(token);
        }
    }

    public void update(AuthenticationToken token) {
        if (exists(token)) {
            save(token);
        }
    }

    public void save(AuthenticationToken token) {

        validateToken(token);

        if (token.getValue() == null) {
            throw new AuthenticationException("Token value could not be null");
        }

        if (token.getTimeOut() != null) {
            redisTemplate.boundHashOps(token.getTokenKey()).put(
                    token.getProdKey().name() + CommonConstant.POSTFIX_EXPIRED_TIME,
                    DateUtils.addMinutes(new Date(), token.getTimeOut()));
        }

        redisTemplate.boundHashOps(token.getTokenKey()).put(token.getProdKey().name(), token.getValue());
    }

    public Object authenticate(AuthenticationToken token) {

        validateToken(token);

        Object cachedToken = redisTemplate.opsForHash().get(
                token.getTokenKey(), token.getProdKey().name());
        if (cachedToken == null) {
            clear(token);
            throw new AuthenticationException();
        }

        Date codeExpiresTime = (Date) redisTemplate.opsForHash().get(
                token.getTokenKey(), token.getProdKey().name() + CommonConstant.POSTFIX_EXPIRED_TIME);
        if (codeExpiresTime != null) {

            if (codeExpiresTime.before(new Date())) {
                clear(token);
                throw new AuthenticationException();
            }

            redisTemplate.boundHashOps(token.getTokenKey()).put(token.getProdKey().name() + CommonConstant.POSTFIX_EXPIRED_TIME,
                    DateUtils.addMinutes(new Date(), token.getTimeOut()));
        }

        Long size = redisTemplate.opsForHash().size(token.getTokenKey());
        if (size == 1) {
//            log4j.info("AuthenticationServiceImpl  测试用：删除了token" + token.getTokenKey());
            redisTemplate.delete(token.getTokenKey());
            throw new AuthenticationException();
        }

        return cachedToken;
    }

    public Object authenticate(String tokenkey) {

        return authenticate(new SimpleToken(tokenkey, EnumProductKey.GENERAL));
    }

    public void clear(AuthenticationToken token) {

        validateToken(token);
        clearProductToken(token);
        clearGeneral(token);
    }

    private void clearProductToken(AuthenticationToken token) {

        redisTemplate.boundHashOps(token.getTokenKey()).delete(token.getProdKey().name());
        redisTemplate.boundHashOps(token.getTokenKey()).delete(
                token.getProdKey().name() + CommonConstant.POSTFIX_EXPIRED_TIME);
    }

    public String registration(UC_UserInfo userInfo) {
        throw new NotImplementedException("Registration should be an RPC call, it's would never implemented locally.");
    }

    private void validateToken(AuthenticationToken token) {

        Object generalToken = redisTemplate.opsForHash().get(
                token.getTokenKey(), EnumProductKey.GENERAL.name());
        if (generalToken == null) {
            throw new AuthenticationException(
                    new StringBuffer("When save,authenticate and clear token, ")
                            .append("the GENERAL token generated by usercenter should exists in the cache, ")
                            .append("if not, the possible reason is the application connect to usercenter use the")
                            .append("DIFFERENT USERCENTER WEBSERVICE AND REDIS, token key is ")
                            .append(token.getTokenKey())
                            .append(", token prodkey is ")
                            .append(token.getProdKey().name()).toString());
        }

        if (StringUtils.isBlank(token.getTokenKey())) {
            throw new AuthenticationException("Token key could not be null");
        }

        if (token.getProdKey() == null) {
            throw new AuthenticationException("Token prod key could not be null");
        }
    }

    protected void clearGeneral(AuthenticationToken token) {

        HashMap<EnumProductKey, Object> sessions =
                (HashMap<EnumProductKey, Object>) redisTemplate.opsForHash().entries(token.getTokenKey());
        if (sessions.size() == 1 && sessions.containsKey(EnumProductKey.GENERAL.name())) {
//            log4j.info("AuthenticationServiceImpl  测试用：删除了token" + token.getTokenKey());

            redisTemplate.delete(token.getTokenKey());
        }
    }

    private boolean exists(AuthenticationToken token) {
        return redisTemplate.opsForHash().get(
                token.getTokenKey(), token.getProdKey().name()) != null;
    }
}
