package com.ucmed.filter;

import javax.servlet.*;
import javax.servlet.annotation.WebFilter;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletRequestWrapper;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;

/**
 * Created by <PERSON><PERSON> on 2017/5/24.
 * 用于解决中文编码问题
 */
public class EncodingFilter implements Filter {

    //设置默认编码
    private String defaultCharset = "utf-8";

    public void destroy() {
    }

    public void doFilter(ServletRequest req, ServletResponse resp, FilterChain chain) throws ServletException, IOException {
        HttpServletRequest request = (HttpServletRequest) req;
        HttpServletResponse response = (HttpServletResponse) resp;

        request.setCharacterEncoding(defaultCharset);
        response.setCharacterEncoding(defaultCharset);
        response.setContentType("text/html;charset=" + defaultCharset);
        CharacterRequest characterRequest = new CharacterRequest(request);
        chain.doFilter(characterRequest, response);

    }

    public void init(FilterConfig config) throws ServletException {

    }

}

//针对request对象进行包装

class CharacterRequest extends HttpServletRequestWrapper{

    public CharacterRequest(HttpServletRequest request) {
        super(request);
    }

    @Override
    //重写getParameter方法
    public String getParameter(String name) {
        //调用被包装对象的getParameter()方法获取请求的参数
        String value = super.getParameter(name);
        if (value == null){
            return null;
        }
        String method = super.getMethod();
        if ("get".equalsIgnoreCase(method)){
            //进行重新的编码
            try {
                value = new String(value.getBytes("iso-8859-1"),"utf-8");
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
        }


        return value;

    }
}

