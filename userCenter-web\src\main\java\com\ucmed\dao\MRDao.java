package com.ucmed.dao;


import com.ucmed.bean.DoctorInfo;
import com.ucmed.bean.Hospital;
import com.ucmed.bean.UserInfo;

import java.util.List;

/**
 * Created by HUANGYIXIN on 2016/7/18.
 */
public interface MRDao {

    /**
     * 注册用户登陆验证信息
     * @param uc_userInfo
     */
//    int registrationUser(UC_UserInfo uc_userInfo);

    /**
     * 注册用户基本信息
     * @param userInfo 用户Bean对象
     */
    int addUserInfo(UserInfo userInfo);

    /**
     * 登陆
     * @param user 登录名
     * @param password 密码
     * @return ucmed_id 卓健ID
     */
    String login(String user, String password);

    /**
     * 根据ucmed_id查询普通用户基本信息
     * @param ucmedId 卓健ID
     * @return UserInfo 用户Bean对象
     *
     */
    UserInfo getUserInfo(String ucmedId);

    /**
     * 更新普通用户基本信息
     * @param userInfo 用户Bean对象
     *
     */
    int updateUserInfo(UserInfo userInfo);

    /**
     * 根据card_id查询普通用户基本信息
     */
    UserInfo queryUserInfo(String cardId, String name, String phone);

    /**
     * 根据ucmed_id查询医生基本信息
     * @param ucmedId 卓健ID
     * @return DoctorInfo 医生Bean对象
     */
    DoctorInfo getDoctorInfo(String ucmedId);

    /**
     * 更新医生基本信息
     * @param doctorInfo 医生Bean对象
     */
    int updateDoctorInfo(DoctorInfo doctorInfo);

    /**
     * 验证医生基本信息
     */
    List<DoctorInfo> queryDoctorInfo(String ucmedId);

    DoctorInfo queryDoctorInfo(String ucmdeId, String ucmedUnitDoctorId);

    int addDoctorInfo(DoctorInfo doctorInfo);


    /**
     * 添加医院信息
     * @param hospital 医院Bean对象
     */
    int addHospital(Hospital hospital);

    /**
     * 更新医院信息
     * @param hospital 医院Bean对象
     */
    int updateHospital(Hospital hospital);

    /**
     * 查询医院信息
     */
    List<Hospital> queryHospital(String condition);

}
