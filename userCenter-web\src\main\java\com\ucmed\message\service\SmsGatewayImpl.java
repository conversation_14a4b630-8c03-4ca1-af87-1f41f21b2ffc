package com.ucmed.message.service;

import cn.ucmed.common.cache.RequestData;
import cn.ucmed.common.constants.model.DefaultDBValue;
import cn.ucmed.common.orika.OrikaBeanMapper;
import cn.ucmed.common.util.PaginationResult;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.ucmed.message.dao.SmsGatewayModelMapper;
import com.ucmed.message.model.PSmsGateway;
import com.ucmed.message.model.TSmsGateway;
import org.apache.commons.collections.CollectionUtils;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Created by ucmed on 2015/12/18.
 */
@Repository
public class SmsGatewayImpl implements SmsGateway {

    @Autowired
    private SmsGatewayModelMapper smsGatewayModelMapper;
//    private RequestData requestData;
    private OrikaBeanMapper orikaBeanMapper;

    public void setSmsGatewayModelMapper(SmsGatewayModelMapper smsGatewayModelMapper) {
        this.smsGatewayModelMapper = smsGatewayModelMapper;
    }

//    public void setRequestData(RequestData requestData) {
//        this.requestData = requestData;
//    }

    public void setOrikaBeanMapper(OrikaBeanMapper orikaBeanMapper) {
        this.orikaBeanMapper = orikaBeanMapper;
    }

    @Override
    public TSmsGateway loadGateway(String appCode) {
        List<TSmsGateway> tSmsGatewayList = smsGatewayModelMapper.loadGateway(appCode);
        if (CollectionUtils.isNotEmpty(tSmsGatewayList)) {
            return tSmsGatewayList.get(0);
        }
        return null;
    }


    @Override
    public String loadGatewayUrl(String appCode) {
        PSmsGateway pSmsGateway = new PSmsGateway();
        pSmsGateway.setAppCode(appCode);
        pSmsGateway.setProtocolType(SmsGateway.PROTOCOL_HTTP);
        pSmsGateway.setDeletionState(DefaultDBValue.CREATE_DELETION_STATE);
        PSmsGateway model = smsGatewayModelMapper.selectOne(pSmsGateway);
        if (null != model) {
            return model.getUrl();
        }
        return null;
    }

    @Override
    public PaginationResult<TSmsGateway> getPaginatedList(String appCode, int pageNo, int pageSize) {
        PageHelper.startPage(pageNo, pageSize);
        PSmsGateway pSmsGateway = new PSmsGateway();
        pSmsGateway.setAppCode(appCode);
        List<PSmsGateway> plist = smsGatewayModelMapper.select(pSmsGateway);
        PageInfo<PSmsGateway> page = new PageInfo<PSmsGateway>(plist);
        page.getTotal();
        List<TSmsGateway> tlist = orikaBeanMapper.mapAsList(plist, TSmsGateway.class);
        PaginationResult<TSmsGateway> result = new PaginationResult<TSmsGateway>();
        result.setList(tlist);
        result.setTotalCount(page.getTotal());
        return result;
    }

    @Override
    public int add(String appCode, TSmsGateway tSmsGateway) {
        tSmsGateway.setSmsGatewayId(UUID.randomUUID().toString());
        tSmsGateway.setAppCode(appCode);
        tSmsGateway.setCreatedby(appCode);
        tSmsGateway.setModifiedby(appCode);
        tSmsGateway.setDeletionState(DefaultDBValue.CREATE_DELETION_STATE);
        tSmsGateway.setCreatedon(new Date());
        tSmsGateway.setModifiedon(new Date());
        PSmsGateway pSmsGateway = new PSmsGateway();
        BeanUtils.copyProperties(tSmsGateway, pSmsGateway);
        return smsGatewayModelMapper.insertSelective(pSmsGateway);
    }

    @Override
    public int update(TSmsGateway tSmsGateway) {
        tSmsGateway.setModifiedby(SecurityUtils.getSubject().getPrincipal().toString());
        tSmsGateway.setModifiedon(new Date());
        return smsGatewayModelMapper.updateByPrimaryKeySelective(orikaBeanMapper.map(tSmsGateway, PSmsGateway.class));
    }
}
