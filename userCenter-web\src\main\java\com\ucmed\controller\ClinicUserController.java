package com.ucmed.controller;

import com.ucmed.bean.UCResponse;
import com.ucmed.dto.ClinicUserInfo;
import com.ucmed.service.ClinicUserService;
import com.ucmed.util.SpringHttpClientUtils;
import io.swagger.annotations.*;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.io.UnsupportedEncodingException;
import java.util.logging.Logger;

@Api(value = "医生用户中心", description = "医生用户中心接口")
@RestController
@RequestMapping("/clinicUser")
public class ClinicUserController {
    @Autowired
    ClinicUserService clinicUserService;

    @ApiOperation(
            value = "医生用户注册",
            notes = "医生用户注册\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "电话号码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "password", value = "密码", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "roleName", value = "角色名", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "clinicUserInfo", value = "医生用户信息", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = -3, message = "角色不存在"),
            @ApiResponse(code = -1, message = "应用未注册"),
            @ApiResponse(code = 5, message = "手机号不合法"),
            @ApiResponse(code = 3, message = "密码太简单，建议使用大小写字母、数字和特殊字符"),
            @ApiResponse(code = 6, message = "身份证号码不能为空"),
            @ApiResponse(code = 412, message = "异常信息"),
            @ApiResponse(code = 1, message = "用户已注册"),
            @ApiResponse(code = 2, message = "该用户已在别的医院建档，请授权。"),
            @ApiResponse(code = 0, message = "注册成功"),
    })
    @RequestMapping(value = "registration", method = RequestMethod.POST)
    public ResponseEntity<String> registration(String phone, String password, int appCode, String roleName,
                                   ClinicUserInfo clinicUserInfo) {
        String responseJson = JSONObject.fromObject(clinicUserService.registration(phone, password, appCode, roleName, clinicUserInfo)).toString();
        return createResponseEntity(responseJson);
    }


//    @ApiOperation(
//            value = "详细-查询医生",
//            notes = "以详细信息的方式查找医生\n"
//    )
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "clinicUserInfo", value = "医生用户信息", required = true, paramType = "query", dataType = "String"),
//    })
//    @ApiResponses({
//            @ApiResponse(code = 0, message = "查询成功"),
//    })
//    @RequestMapping(value = "listUserByDetailedConditions", method = RequestMethod.GET)
//    public ResponseEntity<String> listUserByDetailedConditions(Integer appCode, ClinicUserInfo clinicUserInfo) {
//        String responseJson = JSONObject.fromObject(clinicUserService.listUser(appCode, clinicUserInfo)).toString();
//        return createResponseEntity(responseJson);
//    }
//
//
//    @ApiOperation(
//            value = "粗略-查询医生",
//            notes = "以粗略信息的方式查找医生\n"
//    )
//    @ApiImplicitParams({
//            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
//            @ApiImplicitParam(name = "pageNo", value = "页号", required = true, paramType = "query", dataType = "String"),
//            @ApiImplicitParam(name = "conditions", value = "信息", required = true, paramType = "query", dataType = "String"),
//    })
//    @ApiResponses({
//            @ApiResponse(code = 0, message = "查询成功"),
//    })
//    @RequestMapping(value = "listUserByConditions", method = RequestMethod.GET)
//    public ResponseEntity<String> listUserByConditions(Integer appCode, Integer pageNo, Integer pageSize, String conditions) {
//        String responseJson = JSONObject.fromObject(clinicUserService.listUser(appCode, pageNo, pageSize, conditions)).toString();
//        return createResponseEntity(responseJson);
//    }


    @ApiOperation(
            value = "更新医生信息",
            notes = "更新医生信息\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "phone", value = "手机号", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "appCode", value = "appCode", required = true, paramType = "query"),
            @ApiImplicitParam(name = "clinicUserInfo", value = "医生用户信息", required = true, paramType = "query", dataType = "String"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "修改成功"),
            @ApiResponse(code = -1, message = "用户不存在"),
            @ApiResponse(code = -2, message = "用户未授权"),
    })
    @RequestMapping(value = "updateUserInfo", method = RequestMethod.PUT)
    public ResponseEntity<String> updateUserInfo(String phone, Integer appCode, ClinicUserInfo clinicUserInfo) {
        String responseJson = JSONObject.fromObject(clinicUserService.updateUserInfo(phone, appCode, clinicUserInfo)).toString();
        return createResponseEntity(responseJson);
    }




    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }
}
