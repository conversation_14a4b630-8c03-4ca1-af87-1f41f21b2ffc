package com.ucmed.mapper;

import com.ucmed.bean.SecurityUserProject;
import com.ucmed.dto.UserCancelAuthDTO;
import com.ucmed.dto.UserProjectDTO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

public interface SecurityUserProjectMapper {
    @Delete({
            "delete from security_user_project",
            "where user_project_id = #{userProjectId,jdbcType=INTEGER}"
    })
    int deleteByPrimaryKey(Integer userProjectId);

    @Insert({
            "insert into security_user_project (user_id, proj_code, ",
            "open_id, create_time, ",
            "update_time, deletion, create_by, ",
            "update_by, description,dl_pwd,securitykey)",
            "values (#{userId,jdbcType=ARRAY}, #{projCode,jdbcType=INTEGER}, ",
            "#{openId,jdbcType=VARCHAR}, #{createTime,jdbcType=ARRAY}, ",
            "#{updateTime,jdbcType=ARRAY}, #{deletion,jdbcType=CHAR}, #{createBy,jdbcType=VARCHAR}, ",
            "#{updateBy,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}," +
                    "#{password,jdbcType=VARCHAR},#{securitykey,jdbcType=VARCHAR})"
    })
    int insert(SecurityUserProject record);

    @Select({
            "select",
            "user_project_id, user_id, proj_code, open_id, create_time, update_time, deletion, ",
            "create_by, update_by, description,dl_pwd,securitykey ",
            "from security_user_project",
            "where user_id = #{userId,jdbcType=VARCHAR} and proj_code = #{projCode,jdbcType=INTEGER} and deletion = '0'"
    })
    @Results({
            @Result(column = "user_project_id", property = "userProjectId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.ARRAY),
            @Result(column = "proj_code", property = "projCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "open_id", property = "openId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.ARRAY),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.ARRAY),
            @Result(column = "deletion", property = "deletion", jdbcType = JdbcType.CHAR),
            @Result(column = "create_by", property = "createBy", jdbcType = JdbcType.VARCHAR),
            @Result(column = "update_by", property = "updateBy", jdbcType = JdbcType.VARCHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR),
            @Result(column = "dl_pwd", property = "password", jdbcType = JdbcType.VARCHAR),
            @Result(column = "securitykey", property = "securitykey", jdbcType = JdbcType.VARCHAR)
    })
    List<SecurityUserProject> selectByUserIdAndProjCode(@Param("userId") String userId, @Param("projCode") Integer projCode);

    @Select({
            "select",
            "user_project_id, user_id, proj_code, open_id, create_time, update_time, deletion, ",
            "create_by, update_by, description",
            "from security_user_project",
            "where open_id = #{openId,jdbcType=VARCHAR} and deletion = '0'"
    })
    @Results({
            @Result(column = "user_project_id", property = "userProjectId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.ARRAY),
            @Result(column = "proj_code", property = "projCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "open_id", property = "openId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.ARRAY),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.ARRAY),
            @Result(column = "deletion", property = "deletion", jdbcType = JdbcType.CHAR),
            @Result(column = "create_by", property = "createBy", jdbcType = JdbcType.VARCHAR),
            @Result(column = "update_by", property = "updateBy", jdbcType = JdbcType.VARCHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR)
    })
    List<SecurityUserProject> selectByOpenId(@Param("openId") String openId);

    @Update({
            "update security_user_project",
            "set user_id = #{userId,jdbcType=ARRAY},",
            "proj_code = #{projCode,jdbcType=INTEGER},",
            "open_id = #{openId,jdbcType=VARCHAR},",
            "create_time = #{createTime,jdbcType=ARRAY},",
            "update_time = #{updateTime,jdbcType=ARRAY},",
            "deletion = #{deletion,jdbcType=CHAR},",
            "create_by = #{createBy,jdbcType=VARCHAR},",
            "update_by = #{updateBy,jdbcType=VARCHAR},",
            "description = #{description,jdbcType=VARCHAR}",
            "where user_project_id = #{userProjectId,jdbcType=INTEGER}"
    })
    int updateByPrimaryKey(SecurityUserProject record);

    @Update({
            "update security_user_project",
            "set user_id = #{userId,jdbcType=ARRAY},",
            "proj_code = #{projCode,jdbcType=INTEGER},",
            "open_id = #{openId,jdbcType=VARCHAR},",
            "create_time = #{createTime,jdbcType=ARRAY},",
            "update_time = #{updateTime,jdbcType=ARRAY},",
            "deletion = #{deletion,jdbcType=CHAR},",
            "create_by = #{createBy,jdbcType=VARCHAR},",
            "update_by = #{updateBy,jdbcType=VARCHAR},",
            "description = #{description,jdbcType=VARCHAR},",
            "dl_pwd = #{password,jdbcType=VARCHAR},",
            "securitykey = #{securitykey,jdbcType=VARCHAR} ",
            "where user_project_id = #{userProjectId,jdbcType=INTEGER}"
    })
    int updatedlPwdByPrimaryKey(SecurityUserProject record);


    @Update({
            "update security_user_project",
            "set ",
            "dl_pwd = #{dl_pwd,jdbcType=VARCHAR},",
            "securitykey = #{securityKey,jdbcType=VARCHAR} ",
            "where open_id = #{openId,jdbcType=VARCHAR}"
    })
    int adddlPwd(UserProjectDTO userProjectDTO);

    @Update(
            "UPDATE security_user_project  " +
                    "SET deletion = #{deletion}, update_time = #{updateTime}  " +
                    "WHERE " +
                    "user_project_id = #{userProjectId};"
    )
    int updateDelete(SecurityUserProject record);


    @Select({
            "select",
            "user_project_id, user_id, proj_code, open_id, create_time, update_time, deletion, ",
            "create_by, update_by, description",
            "from security_user_project",
            "where user_id = #{userId,jdbcType=VARCHAR} and deletion = '0'"
    })
    @Results({
            @Result(column = "user_project_id", property = "userProjectId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.ARRAY),
            @Result(column = "proj_code", property = "projCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "open_id", property = "openId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.ARRAY),
            @Result(column = "update_time", property = "updateTime", jdbcType = JdbcType.ARRAY),
            @Result(column = "deletion", property = "deletion", jdbcType = JdbcType.CHAR),
            @Result(column = "create_by", property = "createBy", jdbcType = JdbcType.VARCHAR),
            @Result(column = "update_by", property = "updateBy", jdbcType = JdbcType.VARCHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR)
    })
    List<SecurityUserProject> getOpenIdListByUserId(String userId);


    @Select({
            "SELECT " +
                    "DISTINCT " +
                    " sup.user_project_id,sur.t_id as user_role_id,sua.t_id as user_app_id,sup.proj_code,sa.proj_code as app_proj_code, " +
                    " sua.app_code,sup.user_id as proj_user_id,sur.user_id as role_user_id,sua.user_id as app_user_id " +
                    "FROM " +
                    " security_user_role sur,security_role sr,security_user_project sup,security_user_app sua,security_application sa " +
                    "WHERE " +
                    " sup.proj_code = #{projCode} " +
                    " and sup.user_id = #{userId} " +
                    " and sup.deletion = '0' " +
                    " and sur.user_id = sup.user_id " +
                    " and sua.user_id = sup.user_id " +
                    " and sa.proj_code = sup.proj_code " +
                    " and sua.app_code = sa.app_code " +
                    " and sr.app_code = sua.app_code " +
                    " and sr.role_id = sur.role_id"
    })
    @Results({
            @Result(column = "user_project_id", property = "userProjectId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "user_role_id", property = "userRoleId", jdbcType = JdbcType.INTEGER),
            @Result(column = "user_app_id", property = "userAppId", jdbcType = JdbcType.INTEGER),
            @Result(column = "proj_code", property = "projCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "app_proj_code", property = "appProjCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "app_code", property = "appCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "proj_user_id", property = "projUserId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "role_user_id", property = "roleUserId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "app_user_id", property = "appUserId", jdbcType = JdbcType.VARCHAR)
    })
    List<UserCancelAuthDTO> listUserAuthInfo(@Param("userId") String userId, @Param("projCode") Integer projCode);
}