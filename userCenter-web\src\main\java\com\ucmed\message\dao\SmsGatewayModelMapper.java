package com.ucmed.message.dao;

import com.ucmed.message.model.PSmsGateway;
import com.ucmed.message.model.TSmsGateway;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

@Repository
public interface SmsGatewayModelMapper extends Mapper<PSmsGateway> {


    @Select({
            " SELECT ",
            "  smsg.sms_gateway_id, ",
            "  smsg.protocol_type, ",
            "  smsg.platsms_token, ",
            "  smsg.url, ",
            "  smsg.sms_name, ",
            "  smsg.owner, ",
            "  smsg.app_code,  ",
            "  smsg.createdby, ",
            "  smsg.createdon,  smsg.modifiedby,  smsg.modifiedon,  smsg.deletion_state, smsg.description  ",
            //"  ph.platform_hospital_id ",
            " FROM ",
            "  sms_gateway smsg ",
            " INNER JOIN security_application ph  ON smsg.app_code = CAST(ph.app_code AS VARCHAR) ",
            "  AND smsg.app_code =  #{appCode,jdbcType=VARCHAR} ",
            " where smsg.deletion_state = '0'  ",
            " LIMIT 1"})
    @Results({
            @Result(column = "sms_gateway_id", property = "smsGatewayId", jdbcType = JdbcType.VARCHAR, id = true),
            @Result(column = "protocol_type", property = "protocolType", jdbcType = JdbcType.VARCHAR),
            @Result(column = "platsms_token", property = "platsmsToken", jdbcType = JdbcType.VARCHAR),
            @Result(column = "url", property = "url", jdbcType = JdbcType.VARCHAR),
            @Result(column = "sms_name", property = "smsName", jdbcType = JdbcType.VARCHAR),
            @Result(column = "owner", property = "owner", jdbcType = JdbcType.VARCHAR),
            @Result(column = "app_code", property = "appCode", jdbcType = JdbcType.VARCHAR),
            @Result(column = "platform_hospital_id", property = "platformHospitalId", jdbcType = JdbcType.VARCHAR),


            @Result(column = "createdby", property = "createdby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "createdon", property = "createdon", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "modifiedby", property = "modifiedby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "modifiedon", property = "modifiedon", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deletion_state", property = "deletionState", jdbcType = JdbcType.CHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR)})
    List<TSmsGateway> loadGateway(@Param("appCode") String appCode);
}