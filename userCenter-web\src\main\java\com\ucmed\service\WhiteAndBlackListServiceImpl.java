package com.ucmed.service;

import com.ucmed.bean.BlackListApp;
import com.ucmed.bean.BlackListAppUser;
import com.ucmed.bean.WhiteListApp;
import com.ucmed.bean.WhiteListAppUser;
import com.ucmed.mapper.BlackListAppMapper;
import com.ucmed.mapper.BlackListAppUserMapper;
import com.ucmed.mapper.WhiteListAppMapper;
import com.ucmed.mapper.WhiteListAppUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Author: 黄一辛 HUANGYIXIN
 * CreateTime: 2018/5/17 19:47
 * Contract: <EMAIL>
 * Description:
 **/
@Service
public class WhiteAndBlackListServiceImpl implements WhiteAndBlackListService {

    @Autowired
    WhiteListAppMapper whiteListAppMapper;
    @Autowired
    WhiteListAppUserMapper whiteListAppUserMapper;
    @Autowired
    BlackListAppMapper blackListAppMapper;
    @Autowired
    BlackListAppUserMapper blackListAppUserMapper;

    /**
     * 应用是否使用白名单
     * @return
     */
    private boolean appInWhiteList(int appCode) {
        List<WhiteListApp> result = whiteListAppMapper.getIdByAppCode(appCode);
        if(result == null || result.size() == 0)
            return false;
        else
            return true;
    }

    /**
     * 用户是否是白名单用户
     * @param appCode
     * @param user
     * @return
     */
    private boolean userAndAppInWhiteList(int appCode, String user) {
        List<WhiteListAppUser> result = whiteListAppUserMapper.getIdByAppCodeAndUser(appCode, user);
        if(result == null || result.size() == 0)
            return false;
        else
            return true;
    }

    @Override
    public boolean isPassWhitList(int appCode, String user) {
        if(appInWhiteList(appCode)){
            if(!userAndAppInWhiteList(appCode, user))
                return false;
        }
        return true;
    }

    /**
     * 应用是否使用黑名单
     * @param appCode
     * @return
     */
    private boolean appInBlackList(int appCode){
        List<BlackListApp> result = blackListAppMapper.getIdByAppCode(appCode);
        if(result == null || result.size() == 0)
            return false;
        else
            return true;
    }

    /**
     * 手机号是否在黑名单库中
     * @param appCode
     * @param user
     * @return
     */
    private boolean userAndAppInBlackList(int appCode, String user){
        List<BlackListAppUser> result = blackListAppUserMapper.getUserByAppCode(appCode);
        if(result == null || result.size() == 0){
            return false;
        }else{
            for(int i = 0; i < result.size(); i++){
                String regEx = result.get(i).getUser();
                Pattern pattern = Pattern.compile(regEx);
                Matcher matcher = pattern.matcher(user);
                if(matcher.find()){
                    return true;
                }
            }
            return false;
        }
    }


    @Override
    public boolean isPassBlackList(int appCode, String user) {
        if(appInBlackList(appCode)){
            if(userAndAppInBlackList(appCode, user))
                return false;
        }
        return true;
    }
}
