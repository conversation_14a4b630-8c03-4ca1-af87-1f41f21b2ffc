package com.ucmed.bean;

import java.io.Serializable;

public class UserAppToken implements Serializable {
    private int appCode;
    private String token;
    private int expiredTime;

    public UserAppToken() {
    }

    public UserAppToken(int appCode, String token, int expiredTime) {
        this.appCode = appCode;
        this.token = token;
        this.expiredTime = expiredTime;
    }

    public int getAppCode() {
        return appCode;
    }

    public void setAppCode(int appCode) {
        this.appCode = appCode;
    }

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public int getExpiredTime() {
        return expiredTime;
    }

    public void setExpiredTime(int expiredTime) {
        this.expiredTime = expiredTime;
    }

    @Override
    public String toString() {
        return "AppToken{" +
                "appCode=" + appCode +
                ", token='" + token + '\'' +
                ", expiredTime=" + expiredTime +
                '}';
    }
}
