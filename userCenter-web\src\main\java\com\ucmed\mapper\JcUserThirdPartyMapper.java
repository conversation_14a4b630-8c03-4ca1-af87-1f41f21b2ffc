package com.ucmed.mapper;

import com.ucmed.bean.JcUserThirdParty;
import com.ucmed.dto.UserBindInfoDTO;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;

public interface JcUserThirdPartyMapper extends Mapper<JcUserThirdParty> {


    @Update("update jc_user_third_party " +
            "set user_id=#{userId},create_by=#{createBy},update_time=#{updateTime},update_by=#{updateBy}," +
            "third_party_type=#{thirdPartyType}" +
            " where open_id=#{openId}")
    public void updatePhone(JcUserThirdParty jcUserThirdParty);

    @Update("update jc_user_third_party " +
            "set update_time=#{updateTime},update_by=#{updateBy}," +
            " deletion=#{deletion}" +
            " where open_id=#{openId} and deletion='0'")
    void unbind(JcUserThirdParty jcUserThirdParty);

    @Update("update jc_user_third_party " +
            "set update_time=#{updateTime},update_by=#{updateBy}," +
            " deletion=#{deletion}" +
            " where user_id=#{userId} and deletion='0' and create_by=#{createBy}")
    void unbindUserProject(JcUserThirdParty jcUserThirdParty);

    @Select({"<script>",
            "select ",
            "jutp.open_id, ",
            "ju.user_id, ",
            "jutp.third_party_type, ",
            "jutp.create_by, ",
            "jutp.create_time, ",
            "jutp.description ",
            "FROM ",
            "jc_user ju, ",
            "jc_user_third_party jutp ",
            "where ",
            "(ju.phone = #{phone} or ju.user_id = #{userId}) ",
            "and ju.user_id = jutp.user_id ",
            "and jutp.create_by in ",
                "<foreach item='item' index='index' collection='appCodes'",
                    "open='(' separator=',' close=')'>",
                    "#{item}",
                "</foreach>",
            "and jutp.deletion = '0';",
            "</script>"})
    @Results({
            @Result(column = "open_id", property = "openId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "third_party_type", property = "type", jdbcType = JdbcType.CHAR),
            @Result(column = "create_time", property = "createTime", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "create_by", property = "createBy", jdbcType = JdbcType.VARCHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR),
    })
    List<UserBindInfoDTO> getBindInfo(@Param("userId") String userId, @Param("phone") String phone, @Param("appCodes") List<String> appCodes);
}