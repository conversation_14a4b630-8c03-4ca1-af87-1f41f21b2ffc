package com.ucmed.mapper;

import com.ucmed.bean.JcUserPush;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import tk.mybatis.mapper.common.Mapper;

import java.util.Date;

public interface JcUserPushMapper extends Mapper<JcUserPush> {

    @Select({
            "select",
            "user_push_id, open_id, push_id, is_push, is_sound, is_vibrate, is_disturb, ",
            "app_code, createdby, createdon, modifiedby, modifiedon, deletion_state, description",
            "from jc_user_push",
            "where open_id = #{openId,jdbcType=VARCHAR}"
    })
    @Results({
            @Result(column = "user_push_id", property = "userPushId", jdbcType = JdbcType.VARCHAR, id = true),
            @Result(column = "open_id", property = "openId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "push_id", property = "pushId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "is_push", property = "isPush", jdbcType = JdbcType.CHAR),
            @Result(column = "is_sound", property = "isSound", jdbcType = JdbcType.CHAR),
            @Result(column = "is_vibrate", property = "isVibrate", jdbcType = JdbcType.CHAR),
            @Result(column = "is_disturb", property = "isDisturb", jdbcType = JdbcType.CHAR),
            @Result(column = "app_code", property = "appCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "createdby", property = "createdby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "createdon", property = "createdon", jdbcType = JdbcType.VARCHAR),
            @Result(column = "modifiedby", property = "modifiedby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "modifiedon", property = "modifiedon", jdbcType = JdbcType.VARCHAR),
            @Result(column = "deletion_state", property = "deletionState", jdbcType = JdbcType.CHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR)
    })
    JcUserPush selectByOpenId(String openId);

    @Insert({
            "insert into jc_user_push (user_push_id, open_id, push_id, app_code, ",
            "createdby, createdon, ",
            "modifiedby, modifiedon, ",
            "deletion_state, description)",
            "values (#{userPushId,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR}, #{pushId,jdbcType=VARCHAR}, ",
            "#{appCode,jdbcType=INTEGER}, ",
            "#{createdby,jdbcType=VARCHAR}, #{createdon,jdbcType=VARCHAR}, ",
            "#{modifiedby,jdbcType=VARCHAR}, #{modifiedon,jdbcType=VARCHAR}, ",
            "#{deletionState,jdbcType=CHAR}, #{description,jdbcType=VARCHAR})"
    })
    int insert(JcUserPush record);


    @Update({
            "update jc_user_push",
            "set push_id = #{0},",
            "modifiedon = #{1}",
            "where open_id = #{2}"
    })
    void updateByOpenId(String pushId, String modifiedon, String openId);
}