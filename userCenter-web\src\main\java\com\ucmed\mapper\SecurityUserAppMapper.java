package com.ucmed.mapper;

import com.ucmed.bean.SecurityUserApp;
import org.apache.ibatis.annotations.*;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Service;

import java.util.List;

public interface SecurityUserAppMapper {

    @Select({
            "select",
            "t_id, app_code, user_id, oper_date, open_id",
            "from security_user_app",
            "where user_id = #{userId,jdbcType=VARCHAR} and app_code = #{appCode,jdbcType=INTEGER}"
    })
    @Results({
            @Result(column = "t_id", property = "tId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "app_code", property = "appCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "oper_date", property = "operDate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "open_id", property = "openId", jdbcType = JdbcType.VARCHAR)
    })
    List<SecurityUserApp> selectByUserIdAndAppCode(@Param("userId") String userId, @Param("appCode") Integer appCode);

    @Delete({
            "delete from security_user_app",
            "where t_id = #{tId,jdbcType=INTEGER}"
    })
    int deleteByPrimaryKey(Integer tId);

    @Insert({
            "insert into security_user_app (app_code, user_id, ",
            "oper_date, open_id)",
            "values (#{appCode,jdbcType=INTEGER}, #{userId,jdbcType=VARCHAR}, ",
            "#{operDate,jdbcType=VARCHAR}, #{openId,jdbcType=VARCHAR})"
    })
    int insert(SecurityUserApp record);

    @Select({
            "select",
            "t_id, app_code, user_id, oper_date, open_id",
            "from security_user_app",
            "where t_id = #{tId,jdbcType=INTEGER}"
    })
    @Results({
            @Result(column="t_id", property="tId", jdbcType=JdbcType.INTEGER, id=true),
            @Result(column="app_code", property="appCode", jdbcType=JdbcType.INTEGER),
            @Result(column="user_id", property="userId", jdbcType=JdbcType.VARCHAR),
            @Result(column="oper_date", property="operDate", jdbcType=JdbcType.VARCHAR),
            @Result(column="open_id", property="openId", jdbcType=JdbcType.VARCHAR)
    })
    SecurityUserApp selectByPrimaryKey(Integer tId);

    @Update({
            "update security_user_app",
            "set app_code = #{appCode,jdbcType=INTEGER},",
            "user_id = #{userId,jdbcType=VARCHAR},",
            "oper_date = #{operDate,jdbcType=VARCHAR},",
            "open_id = #{openId,jdbcType=VARCHAR}",
            "where t_id = #{tId,jdbcType=INTEGER}"
    })
    int updateByPrimaryKey(SecurityUserApp record);

    @Select({
            "select",
            "t_id, app_code, user_id, oper_date, open_id",
            "from security_user_app",
            "where open_id = #{openId,jdbcType=VARCHAR}"
    })
    @Results({
            @Result(column = "t_id", property = "tId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "app_code", property = "appCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "oper_date", property = "operDate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "open_id", property = "openId", jdbcType = JdbcType.VARCHAR)
    })
    SecurityUserApp selectByOpenId(@Param("openId") String openId);

    @Select({
            "select",
            "t_id, app_code, user_id, oper_date, open_id",
            "from security_user_app",
            "where user_id = #{userId,jdbcType=VARCHAR}"
    })
    @Results({
            @Result(column = "t_id", property = "tId", jdbcType = JdbcType.INTEGER, id = true),
            @Result(column = "app_code", property = "appCode", jdbcType = JdbcType.INTEGER),
            @Result(column = "user_id", property = "userId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "oper_date", property = "operDate", jdbcType = JdbcType.VARCHAR),
            @Result(column = "open_id", property = "openId", jdbcType = JdbcType.VARCHAR)
    })
    List<SecurityUserApp> selectByUserId(@Param("userId") String userId);
}