package com.ucmed.dao;

import com.ucmed.bean.UserDO;
import com.ucmed.util.ThreeDESUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.ucmed.common.constant.CommonConstant.SPKEY;

@Repository("userDao")
public class UserDaoImpl implements UserDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;


    @Override
    public List<UserDO> getUser(String userId) {
        String sql = "SELECT " +
                "u.user_id, u.\"password\", u.phone, u.token, u.\"lock\", u.uid, push.push_id, u.securitykey, u.login_times, u.pass_change_time, " +
                "r.app_code, r.role_name, ur.role_id, up.proj_code, up.open_id, up.dl_pwd, up.securitykey as dl_securitykey, up.update_time as dl_pwd_change_time " +
                "FROM " +
                "jc_user u " +
                "INNER JOIN security_user_role ur ON ur.user_id = u.user_id " +
                "INNER JOIN security_role r ON r.role_id = ur.role_id AND ur.user_id = u.user_id " +
                "INNER JOIN security_application app ON app.app_code = r.app_code " +
                "LEFT JOIN security_user_project up ON up.proj_code = app.proj_code AND up.user_id = u.user_id AND up.deletion = '0' " +
                "LEFT JOIN jc_user_push push ON push.open_id = up.open_id " +
                "WHERE " +
                "(u.user_id = ? OR u.phone = ?)";
        List<UserDO> result = jdbcTemplate.query(sql, new Object[]{userId, ThreeDESUtil.get3DESEncrypt(userId, SPKEY)}, new BeanPropertyRowMapper<>(UserDO.class));
        return result;
    }

    @Override
    public List<UserDO> getUser(String userId, int appCode) {
        String sql = "SELECT " +
                "u.user_id, u.\"password\", u.phone, u.token, u.\"lock\", u.uid, push.push_id, u.securitykey, u.login_times, " +
                "r.app_code, r.role_name, ur.role_id, up.proj_code, up.open_id " +
                "FROM " +
                "jc_user u " +
                "INNER JOIN security_user_role ur ON ur.user_id = u.user_id " +
                "INNER JOIN security_role r ON r.role_id = ur.role_id AND ur.user_id = u.user_id " +
                "INNER JOIN security_application app ON app.app_code = r.app_code AND r.app_code = ? " +
                "LEFT JOIN security_user_project up ON up.proj_code = app.proj_code AND up.user_id = u.user_id AND up.deletion = '0' " +
                "LEFT JOIN jc_user_push push ON push.open_id = up.open_id " +
                "WHERE " +
                "(u.user_id = ? OR u.phone = ?)";
        List<UserDO> result = jdbcTemplate.query(sql, new Object[]{appCode, userId, ThreeDESUtil.get3DESEncrypt(userId, SPKEY)}, new BeanPropertyRowMapper<>(UserDO.class));
        return result;
    }
}
