package com.ucmed.transfer;

import com.ucmed.bean.*;
import com.ucmed.common.service.CommonService;
import com.ucmed.dto.ClinicResponse;
import com.ucmed.dto.ClinicUserInfo;
import com.ucmed.exception.BusinessException;
import com.ucmed.service.*;
import com.ucmed.util.GenerateRandomString;
import com.ucmed.util.SpringHttpClientUtils;
import com.ucmed.util.ThreeDESUtil;
import com.ucmed.util.TimeUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import java.util.ArrayList;
import java.util.List;
import java.util.logging.Logger;

import static com.ucmed.common.constant.CommonConstant.SPKEY;

/**
 * 线下门诊管理系统用户接口实现类
 *
 * <AUTHOR>
 * @date 2017/10/25 16:33
 */
public class ClinicUserServiceImpl extends CommonService implements ClinicUserService {
    private static Logger log4j = Logger.getLogger(ClinicUserService.class.getName());
    String robotUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=0b9c3bd2-8970-43c1-8ca3-1f65bff1be87";
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private GetAppInfoService getAppInfoService;
    @Autowired
    private SecurityUserAppService userAppService;

    @Override
    @Transactional
    public UCResponse registration(String phone, String password, int appCode, String roleName,
                                   ClinicUserInfo clinicUserInfo) {
        com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
        com.alibaba.fastjson.JSONObject request = new com.alibaba.fastjson.JSONObject();
        StringBuffer content = new StringBuffer();
        content.append(appCode).append("Doctor user registration;").append("phone:").append(phone);
        jsonObject.put("content", content);
        log4j.info(content.toString());
        request.put("msgtype", "markdown");
        request.put("markdown", jsonObject);
        SpringHttpClientUtils.doPost(robotUrl, request, null);
        String currentTime = TimeUtil.getCurrentTime();
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            return new UCResponse(-1, "应用未注册");
        }

        // 判断角色是否存在
        if (!roleService.isExists(appCode, roleName)) {
            return new UCResponse(-3, "角色不存在");
        }

        // 手机号合法性
        if (isNotPhonePattern(phone)) {
            return new UCResponse(5, "手机号不合法");
        }

        // 弱密码
        if (isSimplePwd(password)) {
            return new UCResponse(3, "密码太简单，建议使用大小写字母、数字和特殊字符");
        }

        String retUserId;
        // 用户未注册
        if (!isAccountExists(phone)) {

            if (clinicUserInfo.getIdCard() == null || "".equals(clinicUserInfo.getIdCard())) {
                return new UCResponse(6, "身份证号码不能为空");
            }

            UserInfo userInfo = new UserInfo();
            userInfo.setFamilyPhone(clinicUserInfo.getFamilyPhone());
            userInfo.setCard_id(clinicUserInfo.getIdCard());
            userInfo.setAddress(clinicUserInfo.getAddress());
            userInfo.setAge(clinicUserInfo.getAge());
            userInfo.setSex(clinicUserInfo.getSex());
            userInfo.setThe_name(clinicUserInfo.getTheName());

            String UCMEDID = GenerateRandomString.generateRandomString("UCMED", 5);
            String userId = UCMEDID;
            retUserId = userId;
            // 新账户注册
            // 添加用户登录信息
            addUser(userId, password, phone, currentTime, UCMEDID);
            // 添加用户个人资料信息
            userInfo.setUser_id(userId);
            userInfo.setMobile(phone);
            try {
                userInfoService.saveUserInfo(userInfo);
            } catch (IllegalArgumentException e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                return new UCResponse(412, e.getMessage());
            }
            // 添加挂靠关系
            addRelation(userId, appCode, roleName);
        } else {
            // 用户已注册
            UC_UserInfo user = getUser(phone);
            List<SecurityUserApp> userApps = userAppService.getApps(user.getUser_id());
            if (isUserInApp(userApps, appCode)) {
                return new UCResponse(1, "用户已注册");
            } else {
                List<Integer> registApps = new ArrayList<>();
                for (SecurityUserApp userApp : userApps) {
                    registApps.add(userApp.getAppCode());
                }
                return new UCResponse(2, "该用户已在别的医院建档，请授权。", registApps);
            }
        }
        return new UCResponse(0, "注册成功", retUserId);
    }


    @Override
    public ClinicResponse listUser(int appCode, ClinicUserInfo clinicUserInfo) {
        com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
        com.alibaba.fastjson.JSONObject request = new com.alibaba.fastjson.JSONObject();
        StringBuffer content = new StringBuffer();
        content.append(appCode).append("Detailed - check the doctor");
        log4j.info(content.toString());
        jsonObject.put("content", content);
        request.put("msgtype", "markdown");
        request.put("markdown", jsonObject);
        SpringHttpClientUtils.doPost(robotUrl, request, null);
        Integer pageNo = clinicUserInfo == null ? null : clinicUserInfo.getPageNo();
        Integer pageSize = clinicUserInfo == null ? null : clinicUserInfo.getPageSize();
        String name = clinicUserInfo == null ? null : clinicUserInfo.getTheName();
        String phone = clinicUserInfo == null ? null : clinicUserInfo.getPhone();
        String idCard = clinicUserInfo == null ? null : clinicUserInfo.getIdCard();
        List<ClinicUserInfo> users = getJcUserDao().listUser(appCode, pageNo, pageSize, name, phone, idCard);
        int totalCount = getJcUserDao().getTotalUser(appCode, name, phone, idCard);
        users = decryptClinicUserInfo(users);
        return new ClinicResponse(0, "查询成功", pageNo, totalCount, users);
    }

    @Override
    public ClinicResponse listUser(int appCode, Integer pageNo, Integer pageSize, String conditions) {
        com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
        com.alibaba.fastjson.JSONObject request = new com.alibaba.fastjson.JSONObject();
        StringBuffer content = new StringBuffer();
        content.append(appCode).append("Rough - query doctor");
        log4j.info(content.toString());
        jsonObject.put("content", content);
        request.put("msgtype", "markdown");
        request.put("markdown", jsonObject);
        SpringHttpClientUtils.doPost(robotUrl, request, null);
        List<ClinicUserInfo> users = getJcUserDao().listUser(appCode, pageNo, pageSize, conditions);
        int totalCount = getJcUserDao().getTotalUser(appCode, conditions);
        users = decryptClinicUserInfo(users);
        return new ClinicResponse(0, "查询成功", pageNo, totalCount, users);
    }

    private List<ClinicUserInfo> decryptClinicUserInfo(List<ClinicUserInfo> users) {
        for (ClinicUserInfo user : users) {
            if (user.getFamilyPhone() != null) {
                user.setFamilyPhone(ThreeDESUtil.get3DESDecrypt(user.getFamilyPhone(), SPKEY));
            }
            if (user.getPhone() != null) {
                user.setPhone(ThreeDESUtil.get3DESDecrypt(user.getPhone(), SPKEY));
            }
            if (user.getIdCard() != null) {
                user.setIdCard(ThreeDESUtil.get3DESDecrypt(user.getIdCard(), SPKEY));
            }
        }
        return users;
    }

    @Override
    public UCResponse updateUserInfo(String phone, int appCode, ClinicUserInfo clinicUserInfo) {
        com.alibaba.fastjson.JSONObject jsonObject = new com.alibaba.fastjson.JSONObject();
        com.alibaba.fastjson.JSONObject request = new com.alibaba.fastjson.JSONObject();
        StringBuffer content = new StringBuffer();
        content.append(appCode).append("Update doctor information");
        log4j.info(content.toString());
        jsonObject.put("content", content);
        request.put("msgtype", "markdown");
        request.put("markdown", jsonObject);
        SpringHttpClientUtils.doPost(robotUrl, request, null);
        UC_UserInfo user = getUser(phone);
        if (user == null) {
            return new UCResponse(-1, "用户不存在");
        }
        List<SecurityUserApp> userApps = userAppService.getApps(user.getUser_id());
        if (!isUserInApp(userApps, appCode)) {
            return new UCResponse(-2, "用户未授权");
        }
        UserInfo userInfo = new UserInfo();
        userInfo.setUser_id(user.getUser_id());
        userInfo.setFamilyPhone(clinicUserInfo.getFamilyPhone());
        userInfo.setCard_id(clinicUserInfo.getIdCard());
        userInfo.setAddress(clinicUserInfo.getAddress());
        userInfo.setAge(clinicUserInfo.getAge());
        userInfo.setSex(clinicUserInfo.getSex());
        try {
            userInfoService.updateUserInfo(userInfo);
        } catch (IllegalArgumentException e) {
            return new UCResponse(412, e.getMessage());
        }
        return new UCResponse(0, "修改成功");
    }

    private boolean isUserInApp(List<SecurityUserApp> userApps, int appCode) {
        for (SecurityUserApp userApp : userApps) {
            if (userApp.getAppCode() == appCode) {
                return true;
            }
        }
        return false;
    }
}
