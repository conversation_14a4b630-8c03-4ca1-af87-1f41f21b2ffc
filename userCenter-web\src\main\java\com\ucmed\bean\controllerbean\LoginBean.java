package com.ucmed.bean.controllerbean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="登录",description="登录模型")
public class LoginBean {
    @ApiModelProperty(value="登录名", name="userId", required = true, example="string")
    private String userId;
    @ApiModelProperty(value="密码与短信验证码二选一", name="password", example="string")
    private String password;

    @ApiModelProperty(value="角色", name="roleName", required = true, example="string")
    private String roleName;

    @ApiModelProperty(value="短信验证码与密码二选一", name="msgCode", example="string")
    private String msgCode;

    /**
     * 54545
     * @return
     */
    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }

    public String getMsgCode() {
        return msgCode;
    }

    public void setMsgCode(String msgCode) {
        this.msgCode = msgCode;
    }
}
