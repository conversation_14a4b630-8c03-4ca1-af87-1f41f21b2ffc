package com.ucmed.message.dao;

import com.ucmed.message.model.SMSHistory;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;
import org.springframework.stereotype.Repository;
import tk.mybatis.mapper.common.Mapper;

import java.util.List;
@Repository
public interface SmsHistoryMapper extends Mapper<SMSHistory> {

    @Select({"select count(1) from sh_sms_history where deletion_state = '0'"})
    Long countAll();

    @Select({
            "select ",
            "count(1) ",
            "from sh_sms_history ",
            "where receiver_mobile like CONCAT(CONCAT('%', #{keywords,jdbcType=VARCHAR}),'%')"})
    Long countAllByKeywords(@Param("keywords") String keywords);

    @Select({
            "select sh_sms_history_id, sender_scy_user_id, sender_mobile, receiver_mobile, content, send_status, createdby, ",
            "createdon, modifiedby, modifiedon, deletion_state, description",
            " from sh_sms_history order by createdon desc",
            " limit #{start,jdbcType=BIGINT}, #{offset,jdbcType=BIGINT}"})
    @Results({
            @Result(column = "sh_sms_history_id", property = "smsHistoryId", jdbcType = JdbcType.VARCHAR, id = true),
            @Result(column = "sender_scy_user_id", property = "senderScyUserId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "sender_mobile", property = "senderMobile", jdbcType = JdbcType.VARCHAR),
            @Result(column = "receiver_mobile", property = "receiverMobile", jdbcType = JdbcType.VARCHAR),
            @Result(column = "content", property = "content", jdbcType = JdbcType.VARCHAR),
            @Result(column = "send_status", property = "sendStatus", jdbcType = JdbcType.CHAR),
            @Result(column = "createdby", property = "createdby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "createdon", property = "createdon", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "modifiedby", property = "modifiedby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "modifiedon", property = "modifiedon", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deletion_state", property = "deletionState", jdbcType = JdbcType.CHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR)})
    List<SMSHistory> getSMSHistoryList(@Param("start") Long start,
                                       @Param("offset") Long offset);

    @Select({
            "select sh_sms_history_id, sender_scy_user_id, sender_mobile, receiver_mobile, content, send_status, createdby, ",
            "createdon, modifiedby, modifiedon, deletion_state, description",
            " from sh_sms_history ",
            " where content like CONCAT(CONCAT('%', #{keywords,jdbcType=VARCHAR}),'%')",
            " order by createdon desc",
            " limit #{start,jdbcType=BIGINT}, #{offset,jdbcType=BIGINT}"})
    @Results({
            @Result(column = "sh_sms_history_id", property = "smsHistoryId", jdbcType = JdbcType.VARCHAR, id = true),
            @Result(column = "sender_scy_user_id", property = "senderScyUserId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "sender_mobile", property = "senderMobile", jdbcType = JdbcType.VARCHAR),
            @Result(column = "receiver_mobile", property = "receiverMobile", jdbcType = JdbcType.VARCHAR),
            @Result(column = "content", property = "content", jdbcType = JdbcType.VARCHAR),
            @Result(column = "send_status", property = "sendStatus", jdbcType = JdbcType.CHAR),
            @Result(column = "createdby", property = "createdby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "createdon", property = "createdon", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "modifiedby", property = "modifiedby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "modifiedon", property = "modifiedon", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deletion_state", property = "deletionState", jdbcType = JdbcType.CHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR)})
    List<SMSHistory> getSMSHistoryByKeywords(@Param("start") Long start,
                                             @Param("offset") Long offset, @Param("keywords") String keywords);


    @Select({
            "select sh_sms_history_id, sender_scy_user_id, sender_mobile, receiver_mobile, content, send_status, createdby, ",
            "createdon, modifiedby, modifiedon, deletion_state, description, vc_project_id",
            " from sh_sms_history ",
            " where content like CONCAT(CONCAT('%', #{keywords,jdbcType=VARCHAR}),'%') and sender_mobile=#{phone}" +
            " and send_status = '1'",
            " order by createdon desc",
            ""})
    @Results({
            @Result(column = "sh_sms_history_id", property = "smsHistoryId", jdbcType = JdbcType.VARCHAR, id = true),
            @Result(column = "sender_scy_user_id", property = "senderScyUserId", jdbcType = JdbcType.VARCHAR),
            @Result(column = "sender_mobile", property = "senderMobile", jdbcType = JdbcType.VARCHAR),
            @Result(column = "receiver_mobile", property = "receiverMobile", jdbcType = JdbcType.VARCHAR),
            @Result(column = "content", property = "content", jdbcType = JdbcType.VARCHAR),
            @Result(column = "send_status", property = "sendStatus", jdbcType = JdbcType.CHAR),
            @Result(column = "createdby", property = "createdby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "createdon", property = "createdon", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "modifiedby", property = "modifiedby", jdbcType = JdbcType.VARCHAR),
            @Result(column = "modifiedon", property = "modifiedon", jdbcType = JdbcType.TIMESTAMP),
            @Result(column = "deletion_state", property = "deletionState", jdbcType = JdbcType.CHAR),
            @Result(column = "description", property = "description", jdbcType = JdbcType.VARCHAR),
            @Result(column = "vc_project_id", property = "vcProjectId", jdbcType = JdbcType.VARCHAR)
    })
    List<SMSHistory> getAppcodeByPhoneAndKeywords(@Param("phone") String phone, @Param("keywords") String keywords);

}