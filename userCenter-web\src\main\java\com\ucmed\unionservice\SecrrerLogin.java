package com.ucmed.unionservice;

public class SecrrerLogin {
    private String token;
    private String username;
    private String phone;
    private int unionid;
    private String ip;
    private String devicetoken;
    private int devicetype;
    private boolean getdefaultphoto = true;

    public String getToken() {
        return token;
    }

    public void setToken(String token) {
        this.token = token;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public int getUnionid() {
        return unionid;
    }

    public void setUnionid(int unionid) {
        this.unionid = unionid;
    }

    public String getIp() {
        return ip;
    }

    public void setIp(String ip) {
        this.ip = ip;
    }

    public String getDevicetoken() {
        return devicetoken;
    }

    public void setDevicetoken(String devicetoken) {
        this.devicetoken = devicetoken;
    }

    public int getDevicetype() {
        return devicetype;
    }

    public void setDevicetype(int devicetype) {
        this.devicetype = devicetype;
    }

    public boolean getGetdefaultphoto() {
        return getdefaultphoto;
    }

    public void setGetdefaultphoto(boolean getdefaultphoto) {
        this.getdefaultphoto = getdefaultphoto;
    }

    @Override
    public String toString() {
        return "SecrrerLogin{" +
                "token='" + token + '\'' +
                ", username='" + username + '\'' +
                ", phone='" + phone + '\'' +
                ", unionid=" + unionid +
                ", ip='" + ip + '\'' +
                ", devicetoken='" + devicetoken + '\'' +
                ", devicetype='" + devicetype + '\'' +
                ", getdefaultphoto='" + getdefaultphoto + '\'' +
                '}';
    }
}
