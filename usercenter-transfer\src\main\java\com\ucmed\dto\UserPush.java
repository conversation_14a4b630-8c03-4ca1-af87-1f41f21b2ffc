package com.ucmed.dto;

import java.io.Serializable;

public class UserPush implements Serializable{
    private static final long serialVersionUID = -1625855971295393248L;

    private String pushId;
    private String phone;
    private String realName;
    private String idCard;

    public String getPushId() {
        return pushId;
    }

    public void setPushId(String pushId) {
        this.pushId = pushId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getRealName() {
        return realName;
    }

    public void setRealName(String realName) {
        this.realName = realName;
    }

    public String getIdCard() {
        return idCard;
    }

    public void setIdCard(String idCard) {
        this.idCard = idCard;
    }

    @Override
    public String toString() {
        return "UserPush{" +
                "pushId='" + pushId + '\'' +
                ", phone='" + phone + '\'' +
                ", realName='" + realName + '\'' +
                ", idCard='" + idCard + '\'' +
                '}';
    }
}
