package com.ucmed.bean;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

@JsonIgnoreProperties(ignoreUnknown = true)
public class Application {
    private String app_code;
    private String app_name;
    private String app_desc;
    private String oper_date;
    private String oper_user;
    private String proj_code;
    private String app_device;
    private String special_flag;

    public String getApp_code() {
        return app_code;
    }

    public void setApp_code(String app_code) {
        this.app_code = app_code;
    }

    public String getApp_name() {
        return app_name;
    }

    public void setApp_name(String app_name) {
        this.app_name = app_name;
    }

    public String getApp_desc() {
        return app_desc;
    }

    public void setApp_desc(String app_desc) {
        this.app_desc = app_desc;
    }

    public String getOper_date() {
        return oper_date;
    }

    public void setOper_date(String oper_date) {
        this.oper_date = oper_date;
    }

    public String getOper_user() {
        return oper_user;
    }

    public void setOper_user(String oper_user) {
        this.oper_user = oper_user;
    }

    public String getProj_code() {
        return proj_code;
    }

    public void setProj_code(String proj_code) {
        this.proj_code = proj_code;
    }


    public String getApp_device() {
        return app_device;
    }

    public void setApp_device(String app_device) {
        this.app_device = app_device;
    }

    public String getSpecial_flag() {
        return special_flag;
    }

    public void setSpecial_flag(String special_flag) {
        this.special_flag = special_flag;
    }
}
