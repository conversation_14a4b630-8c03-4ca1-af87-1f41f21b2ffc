package com.ucmed.dao;

import com.ucmed.bean.Role;

import java.util.List;

/**
 * Created by QIUJIAHAO on 2016/8/16.
 */

public interface SecurityRoleDao {

    /**
     * 根据roleid查询角色
     * @param roleId
     * @return
     */
    Role getRoleByRoleId(int roleId);

    /**
     * 根据appcode和rolename查roleid
     * @param appCode
     * @param roleName
     * @return
     */
    int getRoleId(int appCode, String roleName);

    /**
     * 更新角色
     * @param role
     * @return
     */
    int updateRole(Role role);

    /**
     * 根据角色ID删除角色
     * @param roleId
     * @return
     */
    int deleteRoleByRoleId(int roleId);

    /**
     * 新增角色
     * @param role
     * @return
     */
    int addRole(Role role);

    /**
     * 用户是否注册角色
     * @param user_id
     * @param role_name
     * @return
     */
    int isUserInRole(String user_id, String role_name);

    /**
     * 查询app下的所有角色信息
     * @param appCode
     * @return
     */
    List<Role> queryRoleByAppCode(int appCode);

    List<Role> queryUserRole(String userId, int appCode);
}
