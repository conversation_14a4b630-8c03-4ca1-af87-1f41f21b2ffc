{
  "swagger": "2.0",
  "info": {
    "description": "usercenter restful api\n",
    "version": "0.0.6-SNAPSHOT",
    "title": "用户中心接口文档",
    "contact": {
      "email": "<PERSON><PERSON><PERSON><PERSON><PERSON>@zhuojianchina.com"
    }
  },
  "host": "test.yhzx.ucmed.cn/userCenterUCMED",
//  "host": "localhost:48080",
  "basePath": "/",
  "tags": [
    {
      "name": "user",
      "description": "用户相关接口"
    },
    {
      "name": "captcha",
      "description": "验证码公共接口"
    }
  ],
  "schemes": [
    "http"
  ],
  "paths": {
    "/user/registration": {
      "post": {
        "tags": [
          "user"
        ],
        "summary": "用户注册（普通注册）",
        "description": "无需短信验证码的注册功能",
        "operationId": "registrationUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "description": "用户名（如只有手机号则填手机号）",
            "required": true,
            "type": "string"
          },
          {
            "name": "phone",
            "in": "query",
            "description": "手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "password",
            "in": "query",
            "description": "密码（明文hash一次后传入）",
            "required": true,
            "type": "string"
          },
          {
            "name": "roleName",
            "in": "query",
            "description": "角色名称（基础角色如患者、普通用户等）",
            "required": true,
            "type": "string"
          },
          {
            "name": "appCode",
            "in": "query",
            "description": "应用ID",
            "required": true,
            "type": "integer",
            "format": "int32"
          }
        ],
        "responses": {
          "0": {
            "description": "注册成功，param中返回user_id，openId"
          },
          "1": {
            "description": "该账号已注册，请使用原账号密码登录，param中返回user_id，openId"
          },
          "3": {
            "description": "密码太简单，建议使用大小写字母、数字和特殊字符"
          },
          "4": {
            "description": "用户名不合法"
          },
          "5": {
            "description": "手机号不合法"
          },
          "6": {
            "description": "此用户不在白名单内，无法注册"
          },
          "-1": {
            "description": "应用未注册"
          }
        }
      }
    },
    "/user/smsRegistration": {
      "post": {
        "tags": [
          "user"
        ],
        "summary": "用户注册（短信注册）",
        "description": "需要短信验证码的注册功能",
        "operationId": "registrationWithMsgCodeUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "description": "用户名（如只有手机号则填手机号）",
            "required": true,
            "type": "string"
          },
          {
            "name": "phone",
            "in": "query",
            "description": "手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "password",
            "in": "query",
            "description": "密码（明文hash一次后传入）",
            "required": true,
            "type": "string"
          },
          {
            "name": "roleName",
            "in": "query",
            "description": "角色名称（基础角色如患者、普通用户等）",
            "required": true,
            "type": "string"
          },
          {
            "name": "appCode",
            "in": "query",
            "description": "应用ID",
            "required": true,
            "type": "integer",
            "format": "int32"
          },
          {
            "name": "msgCode",
            "in": "query",
            "description": "短信验证码",
            "required": true,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "注册成功，param中返回user_id，openId"
          },
          "1": {
            "description": "该账号已注册，请使用原账号密码登录，param中返回user_id，openId"
          },
          "3": {
            "description": "密码太简单，建议使用大小写字母、数字和特殊字符"
          },
          "4": {
            "description": "用户名不合法"
          },
          "5": {
            "description": "手机号不合法"
          },
          "-1": {
            "description": "应用未注册"
          },
          "-2": {
            "description": "验证码不正确"
          }
        }
      }
    },
    "/user/login": {
      "post": {
        "tags": [
          "user"
        ],
        "summary": "密码登录",
        "description": "密码登录",
        "operationId": "loginUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "description": "用户名或手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "password",
            "in": "query",
            "description": "密码（明文hash一次后传入）",
            "required": true,
            "type": "string"
          },
          {
            "name": "roleName",
            "in": "query",
            "description": "角色名称",
            "required": true,
            "type": "string"
          },
          {
            "name": "appCode",
            "in": "query",
            "description": "应用ID",
            "required": true,
            "type": "integer",
            "format": "int32"
          }
        ],
        "responses": {
          "0": {
            "description": "登录成功，param中返回user_id（用户唯一标识）, token, realName（真实姓名）, idCard（身份证号码）, pushId（推送ID）, openId"
          },
          "1": {
            "description": "密码错误"
          },
          "2": {
            "description": "账号被锁定,请联系管理员"
          },
          "4": {
            "description": "账号未注册"
          },
          "5": {
            "description": "用户未授权，是否确定使用该应用？, param中返回user_id, phone"
          },
          "-1": {
            "description": "应用未注册"
          }
        }
      }
    },
    "/user/smsLogin": {
      "post": {
        "tags": [
          "user"
        ],
        "summary": "短信登录",
        "description": "短信登录",
        "operationId": "smsLoginUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "description": "用户名或手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "msgCode",
            "in": "query",
            "description": "短信验证码",
            "required": true,
            "type": "string"
          },
          {
            "name": "roleName",
            "in": "query",
            "description": "角色名称",
            "required": true,
            "type": "string"
          },
          {
            "name": "appCode",
            "in": "query",
            "description": "应用ID",
            "required": true,
            "type": "integer",
            "format": "int32"
          }
        ],
        "responses": {
          "0": {
            "description": "登录成功，param中返回user_id（用户唯一标识）, token, realName（真实姓名）, idCard（身份证号码）, pushId（推送ID）, openId"
          },
          "2": {
            "description": "账号被锁定,请联系管理员"
          },
          "4": {
            "description": "账号未注册"
          },
          "5": {
            "description": "用户未授权，是否确定使用该应用？, param中返回user_id, phone"
          },
          "-1": {
            "description": "应用未注册"
          },
          "-2": {
            "description": "验证码不正确"
          }
        }
      }
    },
    "/user/logout": {
      "post": {
        "tags": [
          "user"
        ],
        "summary": "退出登录",
        "description": "注销功能",
        "operationId": "logoutUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "query",
            "description": "token",
            "required": true,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "退出登录成功"
          }
        }
      }
    },
    "/user/userInfo": {
      "get": {
        "tags": [
          "user"
        ],
        "summary": "获取用户资料",
        "description": "获取用户个人信息",
        "operationId": "getUserInfoUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "query",
            "description": "token",
            "required": true,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "查询成功，param中返回userInfo对象"
          },
          "401": {
            "description": "会话已失效",
            "/user/userInfo": null
          }
        }
      }
    },
    "/user/updateUserInfo": {
      "post": {
        "tags": [
          "user"
        ],
        "summary": "修改用户资料",
        "description": "修改用户个人信息",
        "operationId": "updateUserInfoUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "query",
            "description": "token",
            "required": true,
            "type": "string"
          },
          {
            "name": "address",
            "in": "query",
            "description": "地址",
            "required": false,
            "type": "string"
          },
          {
            "name": "birthday",
            "in": "query",
            "description": "出生日期",
            "required": false,
            "type": "string"
          },
          {
            "name": "card_id",
            "in": "query",
            "description": "证件号码",
            "required": false,
            "type": "string"
          },
          {
            "name": "card_type",
            "in": "query",
            "description": "证件类型（111 - 居民身份证，112 - 临时居民身份证，113 - 户口簿， 114 - 中国人民解放军军官证， 990 - 其他）",
            "required": false,
            "type": "string"
          },
          {
            "name": "city_id",
            "in": "query",
            "description": "城市代码",
            "required": false,
            "type": "string"
          },
          {
            "name": "email",
            "in": "query",
            "description": "电子邮箱",
            "required": false,
            "type": "string"
          },
          {
            "name": "medicare",
            "in": "query",
            "description": "医保卡类型（1-市医保，2-省医保，3-健康卡，4-市医保（大学生），5-市医保（儿童），7-其他）",
            "required": false,
            "type": "string"
          },
          {
            "name": "medicare_id",
            "in": "query",
            "description": "医保卡号",
            "required": false,
            "type": "string"
          },
          {
            "name": "mobile",
            "in": "query",
            "description": "手机号",
            "required": false,
            "type": "string"
          },
          {
            "name": "sex",
            "in": "query",
            "description": "性别（0 - 未知的性别，1 - 男性，2 - 女性，5 - 女性改（变）为男性，6 - 男性改（变）为女性，9 - 未说明的性别）",
            "required": false,
            "type": "string"
          },
          {
            "name": "source_ids",
            "in": "query",
            "description": "来源",
            "required": false,
            "type": "string"
          },
          {
            "name": "the_name",
            "in": "query",
            "description": "真实姓名",
            "required": false,
            "type": "string"
          },
          {
            "name": "w_chat",
            "in": "query",
            "description": "微信号",
            "required": false,
            "type": "string"
          },
          {
            "name": "familyPhone",
            "in": "query",
            "description": "家属电话",
            "required": false,
            "type": "string"
          },
          {
            "name": "age",
            "in": "query",
            "description": "年龄",
            "required": false,
            "type": "integer"
          },
          {
            "name": "medicareExpenseTypes",
            "in": "query",
            "description": "医疗报销类型（1-社会基本医疗保险，2-商业医疗保险，3-大病统筹，4-新型农村合作医疗，5-城镇居民基本医疗保险，6-公费医疗，7-其他）",
            "required": false,
            "type": "string"
          },
          {
            "name": "emergencyContactType",
            "in": "query",
            "description": "紧急联系人类别（1-配偶电话、2-监护人电话、3-家庭电话、4-工作单位电话、5-居委会电话、6-其他）",
            "required": false,
            "type": "string"
          },
          {
            "name": "emergencyContactName",
            "in": "query",
            "description": "紧急联系人姓名",
            "required": false,
            "type": "string"
          },
          {
            "name": "emergencyContactNumber",
            "in": "query",
            "description": "紧急联系号码",
            "required": false,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "更新成功"
          },
          "401": {
            "description": "会话已失效"
          }
        }
      }
    },
    "/user/changePassword": {
      "put": {
        "tags": [
          "user"
        ],
        "summary": "修改密码",
        "description": "修改密码",
        "operationId": "changePasswordUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "newPwd",
            "in": "query",
            "description": "新密码，明文hash一次后传入",
            "required": true,
            "type": "string"
          },
          {
            "name": "oldPwd",
            "in": "query",
            "description": "原密码，明文hash一次后传入",
            "required": true,
            "type": "string"
          },
          {
            "name": "token",
            "in": "query",
            "description": "token",
            "required": true,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "修改密码成功"
          },
          "1": {
            "description": "新密码太简单，建议使用大小写字母、数字和特殊字符"
          },
          "2": {
            "description": "修改密码失败，旧密码错误"
          },
          "3": {
            "description": "账号未注册"
          },
          "401": {
            "description": "会话已失效"
          }
        }
      }
    },
    "/user/changePhone": {
      "post": {
        "tags": [
          "user"
        ],
        "summary": "修改手机号",
        "description": "发送短信验证码至新手机号，验证通过后方可修改",
        "operationId": "changePhoneUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "query",
            "description": "token",
            "required": true,
            "type": "string"
          },
          {
            "name": "newPhone",
            "in": "query",
            "description": "新手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "msgCode",
            "in": "query",
            "description": "短信验证码",
            "required": true,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "手机号修改成功"
          },
          "2": {
            "description": "新手机号不合法"
          },
          "3": {
            "description": "新手机号已注册"
          },
          "200": {
            "description": "OK",
            "schema": {
              "$ref": "#/definitions/UCResponse"
            }
          },
          "401": {
            "description": "会话已失效"
          },
          "-1": {
            "description": "验证码不正确"
          }
        }
      }
    },
    "/user/reInputPassword": {
      "put": {
        "tags": [
          "user"
        ],
        "summary": "重置密码",
        "description": "适用于忘记密码功能",
        "operationId": "reInputPasswordUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "phone",
            "in": "query",
            "description": "手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "newPwd",
            "in": "query",
            "description": "新密码，明文hash一次后传入",
            "required": true,
            "type": "string"
          },
          {
            "name": "msgCode",
            "in": "query",
            "description": "短信验证码",
            "required": true,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "重置密码成功"
          },
          "1": {
            "description": "密码太简单，建议使用大小写字母、数字和特殊字符"
          },
          "2": {
            "description": "账号未注册"
          },
          "-1": {
            "description": "验证码不正确"
          }
        }
      }
    },
    "/user/setPermission": {
      "post": {
        "tags": [
          "user"
        ],
        "summary": "授权",
        "description": "短信登录",
        "operationId": "setPermissionUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "userId",
            "in": "query",
            "description": "用户名或手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "roleName",
            "in": "query",
            "description": "角色名称",
            "required": true,
            "type": "string"
          },
          {
            "name": "appCode",
            "in": "query",
            "description": "应用ID",
            "required": true,
            "type": "integer",
            "format": "int32"
          }
        ],
        "responses": {
          "0": {
            "description": "授权成功"
          },
          "1": {
            "description": "账号未注册"
          }
        }
      }
    },
    "/picturecode": {
      "get": {
        "tags": [
          "captcha"
        ],
        "summary": "生成图形验证码",
        "description": "图形验证码，在发送短信验证码时校验。未接入用户中心的项目请置checkoutAccount为false，默认会校验账号信息，例如type为注册时，会校验手机号是否已注册。",
        "operationId": "picturecodeUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "phone",
            "in": "query",
            "description": "手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "type",
            "in": "query",
            "description": "验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "length",
            "in": "query",
            "description": "验证码长度，默认4位",
            "required": false,
            "type": "string"
          },
          {
            "name": "checkoutAccount",
            "in": "query",
            "description": "是否校验账号信息，默认true，用户未接入用户中心的项目请传false",
            "required": false,
            "type": "boolean"
          }
        ],
        "responses": {
          "0": {
            "description": "成功获取图片验证码，param中返回validate_code_url"
          }
        }
      }
    },
    "/picturecode/verify": {
      "get": {
        "tags": [
          "captcha"
        ],
        "summary": "校验图形验证码",
        "description": "校验图形验证码，发送短信验证码接口中会自动校验，无需调用此接口",
        "operationId": "verifyPictureValidateCodeUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "phone",
            "in": "query",
            "description": "手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "type",
            "in": "query",
            "description": "验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "picCode",
            "in": "query",
            "description": "图形验证码",
            "required": true,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "图形验证码验证成功"
          },
          "-1": {
            "description": "图形验证码过期，请刷新后重试, param: validate_code_url"
          },
          "-2": {
            "description": "验证码输入错误，请重新输入, param: validate_code_url"
          }
        }
      }
    },
    "/messagecode": {
      "get": {
        "tags": [
          "captcha"
        ],
        "summary": "发送短信验证码",
        "description": "发送短信时会校验图形验证码，请先获取图形验证码。未接入用户中心的项目请置checkoutAccount为false，默认会校验账号信息，例如type为注册时，会校验手机号是否已注册。",
        "operationId": "messagecodeUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "phone",
            "in": "query",
            "description": "手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "type",
            "in": "query",
            "description": "验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "appCode",
            "in": "query",
            "description": "应用ID（checkoutAccount为true时必传）",
            "required": false,
            "type": "integer",
            "format": "int32"
          },
          {
            "name": "picCode",
            "in": "query",
            "description": "图形验证码",
            "required": true,
            "type": "string"
          },
          {
            "name": "checkoutAccount",
            "in": "query",
            "description": "是否校验账号信息，默认true，用户未接入用户中心的项目请传false",
            "required": false,
            "type": "boolean"
          },
          {
            "name": "smsToken",
            "in": "query",
            "description": "短信平台token，用户未接入用户中心的项目请传该项",
            "required": false,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "获取验证码成功，请查看您的手机短信"
          },
          "1": {
            "description": "账号已注册"
          },
          "2": {
            "description": "账号未注册"
          },
          "3": {
            "description": "type类型错误"
          },
          "642": {
            "description": "获取验证码失败"
          }
        }
      }
    },
    "/messagecode/verify": {
      "get": {
        "tags": [
          "captcha"
        ],
        "summary": "校验短信验证码",
        "description": "需要校验短信验证码的接口内会自动校验，无需调用此接口",
        "operationId": "verifyMessagecodeUsingPOST",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "phone",
            "in": "query",
            "description": "手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "type",
            "in": "query",
            "description": "验证码类型 0:注册 1:忘记密码 2:登录 3:修改手机号",
            "required": true,
            "type": "string"
          },
          {
            "name": "msgCode",
            "in": "query",
            "description": "短信验证码",
            "required": true,
            "type": "string"
          },
          {
            "name": "invalidWhenVerify",
            "in": "query",
            "description": "验证后立即过期",
            "default": "false",
            "required": true,
            "type": "boolean"
          }
        ],
        "responses": {
          "0": {
            "description": "验证成功"
          },
          "-1": {
            "description": "验证码失效或者输入不正确,请重新获取"
          },
          "-2": {
            "description": "验证验证码失败，电话号码不正确"
          }
        }
      }
    },
    "/captcha/image": {
      "get": {
        "tags": [
          "captcha"
        ],
        "summary": "获取图形验证码（不绑定手机号）",
        "description": "发送短信验证码时请勿使用该接口",
        "operationId": "getImageCaptchaGET",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "length",
            "in": "query",
            "description": "验证码长度（默认4）",
            "required": false,
            "type": "integer"
          }
        ],
        "responses": {
          "0": {
            "description": "成功获取图片验证码,param中返回validate_code_url, token"
          },
          "500": {
            "description": "获取验证码失败"
          }
        }
      }
    },
    "/captcha/image/verify": {
      "get": {
        "tags": [
          "captcha"
        ],
        "summary": "校验图形验证码（不绑定手机号）",
        "description": "校验图形验证码",
        "operationId": "verifyImageCaptchaGET",
        "consumes": [
          "application/x-www-form-urlencoded"
        ],
        "produces": [
          "application/x-www-form-urlencoded"
        ],
        "parameters": [
          {
            "name": "token",
            "in": "query",
            "description": "token",
            "required": true,
            "type": "string"
          },
          {
            "name": "captchaCode",
            "in": "query",
            "description": "图形验证码",
            "required": true,
            "type": "string"
          }
        ],
        "responses": {
          "0": {
            "description": "图形验证码验证成功"
          },
          "-2": {
            "description": "验证码输入错误，请重新输入,param中返回validate_code_url"
          },
          "-1": {
            "description": "图形验证码过期，请刷新后重试,param中返回validate_code_url"
          }
        }
      }
    }
  },
  "definitions": {
    "UCResponse": {
      "type": "object",
      "properties": {
        "param": {
          "type": "object"
        },
        "retCode": {
          "type": "integer",
          "format": "int32"
        },
        "retInfo": {
          "type": "string"
        }
      }
    }
  }
}