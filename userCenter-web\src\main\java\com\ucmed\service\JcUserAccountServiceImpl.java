package com.ucmed.service;

import com.ucmed.bean.JcUserAccount;
import com.ucmed.mapper.JcUserAccountMapper;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import tk.mybatis.mapper.entity.Example;

import java.util.List;

/**
 * Author: 黄一辛 HUANGYIXIN
 * CreateTime: 2018/7/9 20:29
 * Contract: <EMAIL>
 * Description:
 **/
@Service
public class JcUserAccountServiceImpl implements JcUserAccountService {
    private static Logger log = Logger.getLogger(JcUserAccountServiceImpl.class.getName());

    @Autowired
    JcUserAccountMapper jcUserAccountMapper;

    @Override
    public boolean isAccountIdExists(String accountId, int appCode, String roleName) {
        Example example = new Example(JcUserAccount.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("accountId", accountId);
        criteria.andEqualTo("roleName", roleName);
        criteria.andEqualTo("createBy", String.valueOf(appCode));
        criteria.andEqualTo("deletion", "0");
        List<JcUserAccount> result = jcUserAccountMapper.selectByExample(example);
        return result.size() > 0;
    }

    @Override
    public JcUserAccount getJcUserAccount(String accountId, int appCode, String roleName) {
        Example example = new Example(JcUserAccount.class);
        Example.Criteria criteria = example.createCriteria();
        criteria.andEqualTo("accountId", accountId);
        criteria.andEqualTo("roleName", roleName);
        criteria.andEqualTo("createBy", String.valueOf(appCode));
        criteria.andEqualTo("deletion", "0");
        List<JcUserAccount> result = jcUserAccountMapper.selectByExample(example);
        return result.get(0);
    }

    @Override
    public int save(JcUserAccount jcUserAccount) {
        log.info("user_id:" + jcUserAccount.getUserId() + " account_id:" + jcUserAccount.getAccountId());
        return jcUserAccountMapper.insertSelective(jcUserAccount);
    }
}
