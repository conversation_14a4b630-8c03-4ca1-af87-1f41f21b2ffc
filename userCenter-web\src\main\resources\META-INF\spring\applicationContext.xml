<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:jaxws="http://cxf.apache.org/jaxws"
       xmlns:context="http://www.springframework.org/schema/context"
       xmlns:tx="http://www.springframework.org/schema/tx" xmlns:aop="http://www.springframework.org/schema/aop"
       xmlns:task="http://www.springframework.org/schema/task"
        xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans.xsd
	http://cxf.apache.org/jaxws http://cxf.apache.org/schemas/jaxws.xsd
	http://www.springframework.org/schema/context
    http://www.springframework.org/schema/context/spring-context-3.2.xsd
    http://www.springframework.org/schema/tx
  	http://www.springframework.org/schema/tx/spring-tx-3.1.xsd http://www.springframework.org/schema/aop http://www.springframework.org/schema/aop/spring-aop.xsd
       http://www.springframework.org/schema/task
        http://www.springframework.org/schema/task/spring-task-3.2.xsd">

    <context:component-scan base-package="com.ucmed">
        <context:exclude-filter type="annotation" expression="org.springframework.stereotype.Controller"/>
    </context:component-scan>
    <task:annotation-driven></task:annotation-driven>

    <!-- CXF SERVER BEAN DECLAR -->
    <bean id="userCenterImpl" class="com.ucmed.main.UserCenterImpl"/>
    <jaxws:endpoint id="UserCenter" implementor="#userCenterImpl" address="/UserCenter"/>
    <jaxws:client id="client" serviceClass="com.ucmed.main.UserCenter"
                  address="http://localhost:8080/userCenterUCMED/services/UserCenter"/>

    <!-- 引入*.properties -->
    <context:property-placeholder location="classpath:*.properties" ignore-unresolvable="true"/>
    <!-- 配置文件 -->
    <bean id="configProperties" class="org.springframework.beans.factory.config.PropertiesFactoryBean">
        <property name="locations">
            <list>
                <value>classpath*:*.properties</value>
            </list>
        </property>
    </bean>
    <bean id="propertyConfigurer" class="org.springframework.beans.factory.config.PreferencesPlaceholderConfigurer">
        <property name="properties" ref="configProperties"/>
    </bean>
    <import resource="spring-http.xml"></import>

    <!-- redis -->
    <bean id="jedisPoolConfig" class="redis.clients.jedis.JedisPoolConfig">
        <property name="maxIdle" value="${redis.pool.maxIdle}"/>
        <property name="maxTotal" value="${redis.pool.maxActive}"/>
        <property name="maxWaitMillis" value="${redis.pool.maxWait}"/>
        <property name="testOnBorrow" value="${redis.pool.testOnBorrow}"/>
        <property name="testOnReturn" value="${redis.pool.testOnReturn}"/>
    </bean>

    <bean id="jedisConnectionFactory"
          class="org.springframework.data.redis.connection.jedis.JedisConnectionFactory">
        <property name="hostName" value="${redis.host}"/>
        <property name="port" value="${redis.port}"/>
        <property name="password" value="${redis.password}"/>
        <property name="timeout" value="${redis.timeout}"/>
        <property name="poolConfig" ref="jedisPoolConfig"/>
        <property name="usePool" value="true"/>
    </bean>

    <bean id="stringRedisTemplate" class="org.springframework.data.redis.core.StringRedisTemplate">
        <property name="connectionFactory" ref="jedisConnectionFactory"/>
    </bean>

    <bean id="redisTemplate" class="org.springframework.data.redis.core.RedisTemplate">
        <property name="connectionFactory" ref="jedisConnectionFactory"/>
        <property name="keySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="valueSerializer" ref="genericJackson2JsonRedisSerializer"/>

        <property name="hashKeySerializer">
            <bean class="org.springframework.data.redis.serializer.StringRedisSerializer"/>
        </property>
        <property name="hashValueSerializer" ref="genericJackson2JsonRedisSerializer"/>
    </bean>
    <bean id="genericJackson2JsonRedisSerializer"
          class="org.springframework.data.redis.serializer.GenericJackson2JsonRedisSerializer"/>

    <!-- dataSource -->
    <bean id="dataSource" class="com.alibaba.druid.pool.DruidDataSource" init-method="init" destroy-method="close">
        <property name="driverClassName" value="${jdbc.driverClassName}"/>
        <property name="url" value="${jdbc.url}"/>
        <property name="username" value="${jdbc.username}"/>
        <property name="password" value="${jdbc.password}"/>

        <!-- 配置初始化大小、最小、最大 -->
        <property name="initialSize" value="1"/>
        <property name="maxActive" value="20"/>
        <property name="minIdle" value="1"/>

        <!-- 配置获取连接等待超时的时间 -->
        <property name="maxWait" value="60000"/>

        <!-- 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒 -->
        <property name="timeBetweenEvictionRunsMillis" value="60000"/>

        <!-- 配置一个连接在池中最小生存的时间，单位是毫秒 -->
        <property name="minEvictableIdleTimeMillis" value="300000"/>

        <property name="validationQuery" value="SELECT 1"/>
        <property name="testWhileIdle" value="true"/>
        <property name="testOnBorrow" value="false"/>
        <property name="testOnReturn" value="false"/>

        <property name="filters" value="stat"/>

        <property name="proxyFilters">
            <list>
                <ref bean="wall-filter"/>
            </list>
        </property>
    </bean>

    <bean id="wall-filter" class="com.alibaba.druid.wall.WallFilter">
        <property name="logViolation" value="true"/>
        <property name="throwException" value="false"/>
    </bean>

    <bean id="jdbcTemplate" class="org.springframework.jdbc.core.JdbcTemplate" autowire="default">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <!-- 事务管理 -->
    <bean id="transactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <!-- 开启事务注解 -->
    <tx:annotation-driven transaction-manager="transactionManager"/>
    <!--<tx:advice id="txAdvice" transaction-manager="transactionManager">-->
        <!--<tx:attributes>-->
            <!--<tx:method name="*" propagation="REQUIRED" rollback-for="Exception.class"/>-->
        <!--</tx:attributes>-->
    <!--</tx:advice>-->
    <aop:aspectj-autoproxy expose-proxy="true" />
    <bean name="AppCodeHandler" class="com.ucmed.aop.AppCodeHandler"/>

    <tx:advice id="txAdvice" transaction-manager="transactionManager">
        <tx:attributes>
            <tx:method name="get*" read-only="true"/>
            <tx:method name="*"/>
        </tx:attributes>
    </tx:advice>
    <aop:config>
        <aop:pointcut expression="execution(* com.ucmed.transfer.*.*(..) )" id="txPointcut"/>
        <aop:advisor advice-ref="txAdvice" pointcut-ref="txPointcut"/>
    </aop:config>

    <bean id="mapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.ucmed.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="mybatisSqlSessionFactory"/>
    </bean>

    <bean class="tk.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.ucmed"/>
        <!-- 3.2.2版本新特性，markerInterface可以起到mappers配置的作用，详细情况需要看Marker接口类 -->
        <property name="markerInterface" value="tk.mybatis.mapper.common.Mapper"/>
        <property name="sqlSessionFactoryBeanName" value="mybatisSqlSessionFactory"/>
        <!-- 通用Mapper通过属性注入进行配置，默认不配置时会注册Mapper<T>接口
        <property name="properties">
            <value>
                mappers=tk.mybatis.mapper.common.Mapper
            </value>
        </property>
        -->
    </bean>

    <bean id="mybatisSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="configLocation" value="classpath:META-INF/spring/mybatis/mybatis-config.xml"/>
        <!--<property name="mapperLocations">-->
        <!--<array>-->
        <!--<value>classpath*:META-INF/xml/*Mapper.xml</value>-->
        <!--</array>-->
        <!--</property>-->
        <!--<property name="plugins">-->
            <!--<array>-->
                <!--<bean class="com.github.pagehelper.PageHelper">-->
                    <!--&lt;!&ndash; 这里的几个配置主要演示如何使用，如果不理解，一定要去掉下面的配置 &ndash;&gt;-->
                    <!--<property name="properties">-->
                        <!--<value>-->
                            <!--dialect=postgresql-->
                            <!--offsetAsPageNum=true-->
                            <!--reasonable=true-->
                            <!--supportMethodsArguments=true-->
                            <!--returnPageInfo=check-->
                            <!--params=count=countSql-->
                        <!--</value>-->
                    <!--</property>-->
                <!--</bean>-->
            <!--</array>-->
        <!--</property>-->
    </bean>

    <bean id="authenticationService" class="com.ucmed.authc.AuthenticationServiceImpl"/>



    <!--<import resource="mybatis-db.xml"/>-->

    <bean id="xuanWuUtil" class="cn.ucmed.util.XuanWuUtil"
          init-method="init">
        <property name="account" value="${sms.account}"/>
        <property name="password" value="${sms.password}"/>
        <property name="cmHost" value="${sms.cm.host}"/>
        <property name="cmPort" value="${sms.cm.port}"/>
        <property name="wsHost" value="${sms.ws.host}"/>
        <property name="wsPort" value="${sms.ws.port}"/>
    </bean>

    <bean id="smsHistoryService" class="com.ucmed.message.service.SmsHistoryService"/>

    <bean id="smsClient" class="com.ucmed.message.client.SmsClientImpl">
        <property name="testPhone" value="${testphone}"/>
        <property name="smsGatewayHttpClient" ref="smsGatewayHttpClient"/>
    </bean>

    <!--<bean id="smsGateway" class="com.ucmed.message.service.SmsGatewayImpl"/>-->

    <bean id="smsGatewayHttpClient" class="cn.ucmed.util.SmsGatewayHttpClient"/>

    <bean id="sendMsgUtil" class="cn.ucmed.util.SendMsgUtil"/>

    <!--<bean id="msgUtil" class="com.ucmed.common.util.MsgUtil"/>-->

    <bean id="ratelimitingScript" class="org.springframework.data.redis.core.script.DefaultRedisScript">
        <property name="location" value="classpath:META-INF/lua/ratelimiting.lua"/>
        <property name="resultType" value="java.lang.Boolean"/>
    </bean>

    <bean id="rateLimit" class="cn.ucmed.common.ratelimit.RateLimit" init-method="init">
        <property name="ratelimitingScript" ref="ratelimitingScript"/>
        <property name="redisTemplate" ref="redisTemplate"/>
    </bean>

    <bean id="frequencyFilter" class="cn.ucmed.common.ratelimit.FrequencyFilter" init-method="init"
          destroy-method="destroy">
    </bean>

    <bean id="dailyCountFilter" class="cn.ucmed.common.ratelimit.DailyCountFilter" init-method="init"
          destroy-method="destroy">
    </bean>

    <bean id="dubboReference" class="cn.ucmed.rubik.dubbo.DubboReference"/>

    <!--<bean id="commonCatFilter" class="com.ucmed.filter.CommonCatFilter">-->
        <!--<property name="appName" value="${spring.application.name}"/>-->
    <!--</bean>-->
    <import resource="rocketmq.xml" />
</beans>
