package com.ucmed.sso;

import com.ucmed.bean.UCResponse;
import org.springframework.stereotype.Service;

import javax.servlet.http.Cookie;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
@Service("SingleSignOn")
public class SingleSignOnImpl implements SingleSignOn{
    @Override
    public UCResponse SsoVerify(String ssocallbackurl, String ssologinurl, Cookie[] cookies) {
        if (ssocallbackurl == null || ssocallbackurl.isEmpty()){
            return new UCResponse(1, "返回地址不存在");
        }
        try {
            ssocallbackurl = URLDecoder.decode(ssocallbackurl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        if (ssocallbackurl.contains("?")){
            ssocallbackurl += "&ssoverified=1";
        }else {
            ssocallbackurl += "?ssoverified=1";
        }
        String objToken = getTargetFromCookies(cookies, "CSSOCenterToken");
        if (objToken != null && !objToken.isEmpty()) {
            ssocallbackurl += "&ssotoken=" + objToken;
            return new UCResponse(0,"返回吧！", ssocallbackurl);
        } else {
            if (ssologinurl == null && ssologinurl.isEmpty()){
                return new UCResponse(0,"走好！", ssocallbackurl);
            } else {
                try {
                    ssologinurl = URLDecoder.decode(ssologinurl, "UTF-8");
                } catch (UnsupportedEncodingException e) {
                    e.printStackTrace();
                }
                return new UCResponse(0, "返回吧！", ssologinurl);
            }
        }
    }

    private String getTargetFromCookies(Cookie[] cookies, String target){
        try {
            for (Cookie cookie : cookies){
                if (cookie.getName().equals(target)){
                    String value = cookie.getValue();
                    if (value != null && !value.isEmpty()){
                        return value;
                    }
                }
            }
        } catch (Exception e) {
        }
        return "";
    }
}
