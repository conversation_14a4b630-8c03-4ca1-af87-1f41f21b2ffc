package com.ucmed.dao;

import com.ucmed.bean.DataValue;
import com.ucmed.common.dao.BaseDaoImpl;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Created by QIUJIAHAO on 2016/9/12.
 */
@Repository
public class DataValueDaoImpl extends BaseDaoImpl implements DataValueDao {

    /**
     * 查询数据权限
     */
    public List<DataValue> findeDataValueByRoleId(int roleId, int appCode) {
        String sql = "select datavalue_id,value,datavalue_desc,template_id from security_datavalue where datavalue_id in(select datavalue_id from security_template_value a where role_id = " + roleId + ") and app_code = " + appCode;
        return getJdbcTemplate().query(sql,new BeanPropertyRowMapper(DataValue.class));
    }

    /**
     * 查询数据模板
     */
    public String findTemplate(int templateId) {
        String sql = "select object_name from security_datatemplate where template_id = " + templateId;
        return getJdbcTemplate().queryForObject(sql, String.class);
    }

    @Override
    public List<DataValue> getUserDataValue(int appCode, String userId) {
        String sql = "SELECT " +
                "  d.datavalue_id, d.value, d.datavalue_desc, d.template_id, dt.object_name as datavalueName " +
                "FROM " +
                "security_user_role ur " +
                "INNER JOIN security_template_value tv ON tv.role_id = ur.role_id " +
                "INNER JOIN security_datavalue d ON d.datavalue_id = tv.datavalue_id AND d.app_code = ? " +
                "INNER JOIN security_datatemplate dt ON dt.template_id = d.template_id " +
                "WHERE " +
                "(ur.user_id = ?) GROUP BY d.datavalue_id, dt.object_name";
        return getJdbcTemplate().query(sql, new Object[]{appCode, userId}, new BeanPropertyRowMapper(DataValue.class));
    }
}
