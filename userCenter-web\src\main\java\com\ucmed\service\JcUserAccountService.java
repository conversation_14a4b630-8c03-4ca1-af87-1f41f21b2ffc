package com.ucmed.service;

import com.ucmed.bean.JcUserAccount;

/**
 * Author: 黄一辛 HUANGYIXIN
 * CreateTime: 2018/7/9 20:29
 * Contract: <EMAIL>
 * Description:
 **/
public interface JcUserAccountService {
    boolean isAccountIdExists(String accountId, int appCode, String roleName);

    JcUserAccount getJcUserAccount(String accountId, int appCode, String roleName);

    int save(JcUserAccount jcUserAccount);

}
