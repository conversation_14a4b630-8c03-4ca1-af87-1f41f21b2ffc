package com.ucmed.mapper;

import com.ucmed.bean.BlackListAppUser;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import java.util.List;

/**
 * Author: 黄一辛 HUANGYIXIN
 * CreateTime: 2018/5/25 10:16
 * Contract: <EMAIL>
 * Description:
 **/
public interface BlackListAppUserMapper {

    @Select({
            "select",
            "id, user_id",
            "from blacklist_app_user",
            "where app_code = #{appCode,jdbcType=INTEGER} and status = '1'"
    })
    @Results({
            @Result(column = "id", property="id", jdbcType = JdbcType.INTEGER, id=true),
            @Result(column = "user_id", property = "user", jdbcType = JdbcType.VARCHAR)
    })
    List<BlackListAppUser> getUserByAppCode(@Param("appCode") Integer appCode);
}
