package com.ucmed.bean.retbean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/01/09 16:38
 */
@ApiModel(value="用户中心接口返回值下划线版本",description="用户中心接口返回值下划线版本")
public class UnderlineUCResponse<T> implements Serializable {
    private static final long serialVersionUID = -4345612602298647175L;
    @ApiModelProperty(value="响应码", name="ret_code", example="0")
    int ret_code;
    @ApiModelProperty(value="返回信息", name="ret_info", example="操作成功")
    String ret_info;
    @ApiModelProperty(value="返回数据", name="param", example="someMessage")
    T param;

    public UnderlineUCResponse(int ret_code, String ret_info, T param) {
        this.ret_code = ret_code;
        this.ret_info = ret_info;
        this.param = param;
    }

    public UnderlineUCResponse(int retCode, String retInfo) {
        this.ret_code = retCode;
        this.ret_info = retInfo;
    }

    public static <T> UnderlineUCResponse<T> createBySuccess() {
        return new UnderlineUCResponse<T>(1, "返回成功");
    }

    public static <T> UnderlineUCResponse<T> createBySuccess(T data) {
        return new UnderlineUCResponse<T>(1, "返回成功", data);
    }

    public static <T> UnderlineUCResponse<T> createByError(int retCode, String retInfo) {
        return new UnderlineUCResponse<T>(retCode, retInfo);
    }

    public static <T> UnderlineUCResponse<T> createByError(int retCode, String retInfo, T data) {
        return new UnderlineUCResponse<T>(retCode, retInfo, data);
    }

    public int getRet_code() {
        return ret_code;
    }

    public void setRet_code(int ret_code) {
        this.ret_code = ret_code;
    }

    public String getRet_info() {
        return ret_info;
    }

    public void setRet_info(String ret_info) {
        this.ret_info = ret_info;
    }

    public T getParam() {
        return param;
    }

    public void setParam(T param) {
        this.param = param;
    }
}
