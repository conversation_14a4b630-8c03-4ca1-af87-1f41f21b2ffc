/*
 Navicat Premium Data Transfer

 Source Server         : 3.C端用户中心--生产
 Source Server Type    : PostgreSQL
 Source Server Version : 90406
 Source Host           : ***************:5432
 Source Catalog        : usercenter
 Source Schema         : public

 Target Server Type    : PostgreSQL
 Target Server Version : 90406
 File Encoding         : 65001

 Date: 24/04/2020 10:13:04
*/


-- ----------------------------
-- Sequence structure for adduser_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "adduser_t_id_seq";
CREATE SEQUENCE "adduser_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for appdownload_ios_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "appdownload_ios_t_id_seq";
CREATE SEQUENCE "appdownload_ios_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for appdownload_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "appdownload_t_id_seq";
CREATE SEQUENCE "appdownload_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for assay_details_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "assay_details_t_id_seq";
CREATE SEQUENCE "assay_details_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for assay_list_item_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "assay_list_item_t_id_seq";
CREATE SEQUENCE "assay_list_item_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for assay_list_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "assay_list_t_id_seq";
CREATE SEQUENCE "assay_list_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for blacklist_app_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "blacklist_app_id_seq";
CREATE SEQUENCE "blacklist_app_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for blacklist_app_user_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "blacklist_app_user_id_seq";
CREATE SEQUENCE "blacklist_app_user_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for bussinessoperation_projectid_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "bussinessoperation_projectid_t_id_seq";
CREATE SEQUENCE "bussinessoperation_projectid_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for emr_inhospital_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "emr_inhospital_t_id_seq";
CREATE SEQUENCE "emr_inhospital_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for emr_inhospitalfee_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "emr_inhospitalfee_t_id_seq";
CREATE SEQUENCE "emr_inhospitalfee_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for emr_opsdetails_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "emr_opsdetails_t_id_seq";
CREATE SEQUENCE "emr_opsdetails_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for emr_opsrecord_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "emr_opsrecord_t_id_seq";
CREATE SEQUENCE "emr_opsrecord_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for emr_outhospitaldrug_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "emr_outhospitaldrug_t_id_seq";
CREATE SEQUENCE "emr_outhospitaldrug_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for emr_outhospitalrecord_followup_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "emr_outhospitalrecord_followup_t_id_seq";
CREATE SEQUENCE "emr_outhospitalrecord_followup_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for emr_outhospitalrecord_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "emr_outhospitalrecord_t_id_seq";
CREATE SEQUENCE "emr_outhospitalrecord_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for emr_query_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "emr_query_t_id_seq";
CREATE SEQUENCE "emr_query_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for emr_user_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "emr_user_t_id_seq";
CREATE SEQUENCE "emr_user_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for exam_list_details_item_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "exam_list_details_item_t_id_seq";
CREATE SEQUENCE "exam_list_details_item_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for exam_list_details_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "exam_list_details_t_id_seq";
CREATE SEQUENCE "exam_list_details_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for healthrecord_addassay_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "healthrecord_addassay_t_id_seq";
CREATE SEQUENCE "healthrecord_addassay_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for healthrecord_adddiscomfort_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "healthrecord_adddiscomfort_t_id_seq";
CREATE SEQUENCE "healthrecord_adddiscomfort_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for healthrecord_adddrug_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "healthrecord_adddrug_t_id_seq";
CREATE SEQUENCE "healthrecord_adddrug_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for healthrecord_addmedicalhistory_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "healthrecord_addmedicalhistory_t_id_seq";
CREATE SEQUENCE "healthrecord_addmedicalhistory_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for healthrecord_query_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "healthrecord_query_t_id_seq";
CREATE SEQUENCE "healthrecord_query_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for healthrecord_user_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "healthrecord_user_t_id_seq";
CREATE SEQUENCE "healthrecord_user_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_common_section_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_common_section_t_id_seq";
CREATE SEQUENCE "jc_common_section_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_doctor_info_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_doctor_info_t_id_seq";
CREATE SEQUENCE "jc_doctor_info_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_hospital_section_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_hospital_section_t_id_seq";
CREATE SEQUENCE "jc_hospital_section_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_hospital_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_hospital_t_id_seq";
CREATE SEQUENCE "jc_hospital_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_platform_source_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_platform_source_t_id_seq";
CREATE SEQUENCE "jc_platform_source_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_user_account_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_user_account_id_seq";
CREATE SEQUENCE "jc_user_account_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_user_info_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_user_info_t_id_seq";
CREATE SEQUENCE "jc_user_info_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_user_log_logid_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_user_log_logid_seq";
CREATE SEQUENCE "jc_user_log_logid_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_user_patient_patient_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_user_patient_patient_id_seq";
CREATE SEQUENCE "jc_user_patient_patient_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_user_patient_patient_id_seq1
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_user_patient_patient_id_seq1";
CREATE SEQUENCE "jc_user_patient_patient_id_seq1" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_user_third_party_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_user_third_party_id_seq";
CREATE SEQUENCE "jc_user_third_party_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for jc_user_uid_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "jc_user_uid_seq";
CREATE SEQUENCE "jc_user_uid_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for login_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "login_t_id_seq";
CREATE SEQUENCE "login_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for logsearch_background_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "logsearch_background_t_id_seq";
CREATE SEQUENCE "logsearch_background_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for payment_cancel_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "payment_cancel_t_id_seq";
CREATE SEQUENCE "payment_cancel_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for payment_details_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "payment_details_t_id_seq";
CREATE SEQUENCE "payment_details_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for payment_refund_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "payment_refund_t_id_seq";
CREATE SEQUENCE "payment_refund_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for payment_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "payment_t_id_seq";
CREATE SEQUENCE "payment_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for reservation_cancel_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "reservation_cancel_t_id_seq";
CREATE SEQUENCE "reservation_cancel_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for reservation_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "reservation_t_id_seq";
CREATE SEQUENCE "reservation_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for security_datatemplate_template_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "security_datatemplate_template_id_seq";
CREATE SEQUENCE "security_datatemplate_template_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for security_datavalue_datavalue_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "security_datavalue_datavalue_id_seq";
CREATE SEQUENCE "security_datavalue_datavalue_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for security_module_module_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "security_module_module_id_seq";
CREATE SEQUENCE "security_module_module_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for security_module_type_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "security_module_type_t_id_seq";
CREATE SEQUENCE "security_module_type_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for security_project_proj_code_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "security_project_proj_code_seq";
CREATE SEQUENCE "security_project_proj_code_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for security_role_role_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "security_role_role_id_seq";
CREATE SEQUENCE "security_role_role_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for security_user_app_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "security_user_app_t_id_seq";
CREATE SEQUENCE "security_user_app_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for security_user_project_user_project_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "security_user_project_user_project_id_seq";
CREATE SEQUENCE "security_user_project_user_project_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for security_user_role_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "security_user_role_t_id_seq";
CREATE SEQUENCE "security_user_role_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_adduser_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_adduser_t_id_seq";
CREATE SEQUENCE "statistic_adduser_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_assay_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_assay_t_id_seq";
CREATE SEQUENCE "statistic_assay_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_exam_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_exam_t_id_seq";
CREATE SEQUENCE "statistic_exam_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_hospital_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_hospital_t_id_seq";
CREATE SEQUENCE "statistic_hospital_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_login_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_login_t_id_seq";
CREATE SEQUENCE "statistic_login_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_payment_inhospital_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_payment_inhospital_t_id_seq";
CREATE SEQUENCE "statistic_payment_inhospital_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_payment_ondoctor_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_payment_ondoctor_t_id_seq";
CREATE SEQUENCE "statistic_payment_ondoctor_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_payment_reservation_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_payment_reservation_t_id_seq";
CREATE SEQUENCE "statistic_payment_reservation_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_payment_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_payment_t_id_seq";
CREATE SEQUENCE "statistic_payment_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for statistic_registration_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "statistic_registration_t_id_seq";
CREATE SEQUENCE "statistic_registration_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for to_messagbox_table_xuhao_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "to_messagbox_table_xuhao_seq";
CREATE SEQUENCE "to_messagbox_table_xuhao_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for user_recycle_bin_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "user_recycle_bin_id_seq";
CREATE SEQUENCE "user_recycle_bin_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for user_transfer_ezpt_1228_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "user_transfer_ezpt_1228_id_seq";
CREATE SEQUENCE "user_transfer_ezpt_1228_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for user_transfer_guangdong_0328_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "user_transfer_guangdong_0328_id_seq";
CREATE SEQUENCE "user_transfer_guangdong_0328_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for user_transfer_jxpt_1204_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "user_transfer_jxpt_1204_id_seq";
CREATE SEQUENCE "user_transfer_jxpt_1204_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for user_transfer_ztt_0504_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "user_transfer_ztt_0504_id_seq";
CREATE SEQUENCE "user_transfer_ztt_0504_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for whitelist_app_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "whitelist_app_id_seq";
CREATE SEQUENCE "whitelist_app_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for whitelist_app_user_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "whitelist_app_user_id_seq";
CREATE SEQUENCE "whitelist_app_user_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for yilian_adduser_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "yilian_adduser_t_id_seq";
CREATE SEQUENCE "yilian_adduser_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for yilian_identify_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "yilian_identify_t_id_seq";
CREATE SEQUENCE "yilian_identify_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for yilian_login_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "yilian_login_t_id_seq";
CREATE SEQUENCE "yilian_login_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for yilian_logout_t_id_seq
-- ----------------------------
DROP SEQUENCE IF EXISTS "yilian_logout_t_id_seq";
CREATE SEQUENCE "yilian_logout_t_id_seq" 
INCREMENT 1
MINVALUE  1
MAXVALUE 9223372036854775807
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for “statistic_adduser_t_id_seq”
-- ----------------------------
DROP SEQUENCE IF EXISTS "“statistic_adduser_t_id_seq”";
CREATE SEQUENCE "“statistic_adduser_t_id_seq”" 
INCREMENT 1
MINVALUE  1
MAXVALUE 99999999
START 1
CACHE 1;

-- ----------------------------
-- Sequence structure for “statistic_addusert_id_seq”
-- ----------------------------
DROP SEQUENCE IF EXISTS "“statistic_addusert_id_seq”";
CREATE SEQUENCE "“statistic_addusert_id_seq”" 
INCREMENT 1
MINVALUE  1
MAXVALUE 99999999
START 1
CACHE 1;

-- ----------------------------
-- Table structure for adduser
-- ----------------------------
DROP TABLE IF EXISTS "adduser";
CREATE TABLE "adduser" (
  "t_id" int4 NOT NULL DEFAULT nextval('adduser_t_id_seq'::regclass),
  "reg_time" timestamp(6),
  "app_id" varchar(255) COLLATE "pg_catalog"."default",
  "treat_cardno" varchar(255) COLLATE "pg_catalog"."default",
  "phone" varchar(255) COLLATE "pg_catalog"."default",
  "card_type" varchar(255) COLLATE "pg_catalog"."default",
  "card_no" varchar(255) COLLATE "pg_catalog"."default",
  "card_type2" varchar(255) COLLATE "pg_catalog"."default",
  "card_no2" varchar(255) COLLATE "pg_catalog"."default",
  "user_name" varchar(255) COLLATE "pg_catalog"."default",
  "address" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "adduser"."t_id" IS '序号';
COMMENT ON COLUMN "adduser"."reg_time" IS '注册时间';
COMMENT ON COLUMN "adduser"."app_id" IS '应用ID';
COMMENT ON COLUMN "adduser"."treat_cardno" IS '就诊卡号';
COMMENT ON COLUMN "adduser"."phone" IS '手机号';
COMMENT ON COLUMN "adduser"."card_type" IS '证件类型';
COMMENT ON COLUMN "adduser"."card_no" IS '证件号';
COMMENT ON COLUMN "adduser"."card_type2" IS '使用卡类型';
COMMENT ON COLUMN "adduser"."card_no2" IS '使用卡号';
COMMENT ON COLUMN "adduser"."user_name" IS '用户名';
COMMENT ON COLUMN "adduser"."address" IS '居住地址';
COMMENT ON COLUMN "adduser"."user_id" IS '用户ID';
COMMENT ON COLUMN "adduser"."ret_code" IS '返回码';
COMMENT ON COLUMN "adduser"."ret_info" IS '返回信息';
COMMENT ON TABLE "adduser" IS '注册表';

-- ----------------------------
-- Table structure for appdownload
-- ----------------------------
DROP TABLE IF EXISTS "appdownload";
CREATE TABLE "appdownload" (
  "t_id" int4 NOT NULL DEFAULT nextval('appdownload_t_id_seq'::regclass),
  "recordtime" varchar(255) COLLATE "pg_catalog"."default",
  "app_name" varchar(255) COLLATE "pg_catalog"."default",
  "timesperday" int4
)
;
COMMENT ON COLUMN "appdownload"."t_id" IS '序列号';
COMMENT ON COLUMN "appdownload"."recordtime" IS '时间';
COMMENT ON COLUMN "appdownload"."app_name" IS 'app名称';
COMMENT ON COLUMN "appdownload"."timesperday" IS '每日下载量';
COMMENT ON TABLE "appdownload" IS 'app下载量';

-- ----------------------------
-- Table structure for appdownload_ios
-- ----------------------------
DROP TABLE IF EXISTS "appdownload_ios";
CREATE TABLE "appdownload_ios" (
  "t_id" int4 NOT NULL DEFAULT nextval('appdownload_ios_t_id_seq'::regclass),
  "recordtime" varchar(255) COLLATE "pg_catalog"."default",
  "app_name" varchar(255) COLLATE "pg_catalog"."default",
  "timesperday" int4
)
;
COMMENT ON COLUMN "appdownload_ios"."t_id" IS '序列号';
COMMENT ON COLUMN "appdownload_ios"."recordtime" IS '时间';
COMMENT ON COLUMN "appdownload_ios"."app_name" IS '应用名称';
COMMENT ON COLUMN "appdownload_ios"."timesperday" IS '每日下载量';
COMMENT ON TABLE "appdownload_ios" IS 'ios版app下载量';

-- ----------------------------
-- Table structure for appwebelement
-- ----------------------------
DROP TABLE IF EXISTS "appwebelement";
CREATE TABLE "appwebelement" (
  "webname" varchar(255) COLLATE "pg_catalog"."default",
  "element" varchar(255) COLLATE "pg_catalog"."default",
  "value" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for appwebinfo
-- ----------------------------
DROP TABLE IF EXISTS "appwebinfo";
CREATE TABLE "appwebinfo" (
  "webname" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "url" varchar(255) COLLATE "pg_catalog"."default",
  "username" varchar(255) COLLATE "pg_catalog"."default",
  "password" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for assay_details
-- ----------------------------
DROP TABLE IF EXISTS "assay_details";
CREATE TABLE "assay_details" (
  "t_id" int4 NOT NULL DEFAULT nextval('assay_details_t_id_seq'::regclass),
  "query_time" timestamp(6),
  "app_id" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "treat_cardno" varchar(255) COLLATE "pg_catalog"."default",
  "assay_id" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(255) COLLATE "pg_catalog"."default",
  "dept_code" varchar(255) COLLATE "pg_catalog"."default",
  "dept_name" varchar(255) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "assay_details"."t_id" IS '序号';
COMMENT ON COLUMN "assay_details"."query_time" IS '查询时间';
COMMENT ON COLUMN "assay_details"."app_id" IS '应用ID';
COMMENT ON COLUMN "assay_details"."user_id" IS '用户ID';
COMMENT ON COLUMN "assay_details"."treat_cardno" IS '就诊卡号';
COMMENT ON COLUMN "assay_details"."assay_id" IS '检验单号';
COMMENT ON COLUMN "assay_details"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "assay_details"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "assay_details"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "assay_details"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "assay_details"."dept_code" IS '科室代码';
COMMENT ON COLUMN "assay_details"."dept_name" IS '科室名称';
COMMENT ON COLUMN "assay_details"."ret_code" IS '返回码';
COMMENT ON COLUMN "assay_details"."ret_info" IS '返回信息';
COMMENT ON TABLE "assay_details" IS '检验单详情查询';

-- ----------------------------
-- Table structure for assay_list
-- ----------------------------
DROP TABLE IF EXISTS "assay_list";
CREATE TABLE "assay_list" (
  "t_id" int4 NOT NULL DEFAULT nextval('assay_list_t_id_seq'::regclass),
  "query_time" timestamp(6),
  "app_id" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "treat_cardno" varchar(255) COLLATE "pg_catalog"."default",
  "patient_name" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(255) COLLATE "pg_catalog"."default",
  "dept_name" varchar(255) COLLATE "pg_catalog"."default",
  "dept_code" varchar(255) COLLATE "pg_catalog"."default",
  "special" varchar(255) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "assay_list"."t_id" IS '序号';
COMMENT ON COLUMN "assay_list"."query_time" IS '查询时间';
COMMENT ON COLUMN "assay_list"."app_id" IS '应用ID';
COMMENT ON COLUMN "assay_list"."user_id" IS '用户ID';
COMMENT ON COLUMN "assay_list"."treat_cardno" IS '就诊卡号';
COMMENT ON COLUMN "assay_list"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "assay_list"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "assay_list"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "assay_list"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "assay_list"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "assay_list"."dept_name" IS '科室名称';
COMMENT ON COLUMN "assay_list"."dept_code" IS '科室代码';
COMMENT ON COLUMN "assay_list"."special" IS '专科';
COMMENT ON COLUMN "assay_list"."ret_code" IS '返回码';
COMMENT ON COLUMN "assay_list"."ret_info" IS '返回信息';
COMMENT ON TABLE "assay_list" IS '检验单列表查询';

-- ----------------------------
-- Table structure for assay_list_item
-- ----------------------------
DROP TABLE IF EXISTS "assay_list_item";
CREATE TABLE "assay_list_item" (
  "t_id" int4 NOT NULL DEFAULT nextval('assay_list_item_t_id_seq'::regclass),
  "assay_id" varchar(255) COLLATE "pg_catalog"."default",
  "app_id" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(255) COLLATE "pg_catalog"."default",
  "treat_cardno" varchar(255) COLLATE "pg_catalog"."default",
  "patient_name" varchar(255) COLLATE "pg_catalog"."default",
  "specimn" varchar(2048) COLLATE "pg_catalog"."default",
  "spcm" varchar(2048) COLLATE "pg_catalog"."default",
  "assay_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "assay_list_item"."t_id" IS '序号';
COMMENT ON COLUMN "assay_list_item"."assay_id" IS '检验单号';
COMMENT ON COLUMN "assay_list_item"."app_id" IS '应用ID';
COMMENT ON COLUMN "assay_list_item"."user_id" IS '用户ID';
COMMENT ON COLUMN "assay_list_item"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "assay_list_item"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "assay_list_item"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "assay_list_item"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "assay_list_item"."treat_cardno" IS '就诊卡号';
COMMENT ON COLUMN "assay_list_item"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "assay_list_item"."specimn" IS '检验内容';
COMMENT ON COLUMN "assay_list_item"."spcm" IS '检验样本';
COMMENT ON COLUMN "assay_list_item"."assay_time" IS '检验时间';
COMMENT ON TABLE "assay_list_item" IS '检验单列表查询明细';

-- ----------------------------
-- Table structure for blacklist_app
-- ----------------------------
DROP TABLE IF EXISTS "blacklist_app";
CREATE TABLE "blacklist_app" (
  "id" int2 NOT NULL DEFAULT nextval('blacklist_app_id_seq'::regclass),
  "app_code" int2 NOT NULL,
  "status" int2 NOT NULL DEFAULT 1
)
;
COMMENT ON COLUMN "blacklist_app"."status" IS '状态字段，0已失效状态，1有效状态';

-- ----------------------------
-- Table structure for blacklist_app_user
-- ----------------------------
DROP TABLE IF EXISTS "blacklist_app_user";
CREATE TABLE "blacklist_app_user" (
  "id" int2 NOT NULL DEFAULT nextval('blacklist_app_user_id_seq'::regclass),
  "app_code" int2 NOT NULL,
  "user_id" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "status" int2 NOT NULL DEFAULT 1
)
;
COMMENT ON COLUMN "blacklist_app_user"."app_code" IS '需要拦截的用户手机号正则表达式规则';
COMMENT ON COLUMN "blacklist_app_user"."status" IS '状态字段，0已失效状态，1有效状态';

-- ----------------------------
-- Table structure for bussinessoperation_projectid
-- ----------------------------
DROP TABLE IF EXISTS "bussinessoperation_projectid";
CREATE TABLE "bussinessoperation_projectid" (
  "t_id" int4 NOT NULL DEFAULT nextval('bussinessoperation_projectid_t_id_seq'::regclass),
  "projectname" varchar(255) COLLATE "pg_catalog"."default",
  "projectid" varchar(255) COLLATE "pg_catalog"."default",
  "gps" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for dict_app_device
-- ----------------------------
DROP TABLE IF EXISTS "dict_app_device";
CREATE TABLE "dict_app_device" (
  "app_device_id" int4 NOT NULL,
  "app_device_desc" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for emr_inhospital
-- ----------------------------
DROP TABLE IF EXISTS "emr_inhospital";
CREATE TABLE "emr_inhospital" (
  "t_id" int4 NOT NULL DEFAULT nextval('emr_inhospital_t_id_seq'::regclass),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "inhospital_id" varchar(30) COLLATE "pg_catalog"."default",
  "inhospital_time" varchar(30) COLLATE "pg_catalog"."default",
  "case_id" varchar(30) COLLATE "pg_catalog"."default",
  "pathology_no" varchar(30) COLLATE "pg_catalog"."default",
  "age" int4,
  "sex" varchar(10) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "ward_name" varchar(30) COLLATE "pg_catalog"."default",
  "ward_code" varchar(30) COLLATE "pg_catalog"."default",
  "doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "doc_code" varchar(30) COLLATE "pg_catalog"."default",
  "patient_name" varchar(30) COLLATE "pg_catalog"."default",
  "identity_number" varchar(30) COLLATE "pg_catalog"."default",
  "medreport_id" varchar(30) COLLATE "pg_catalog"."default",
  "bed_no" varchar(30) COLLATE "pg_catalog"."default",
  "inhospital_count" int4,
  "inhospital_reason" varchar(200) COLLATE "pg_catalog"."default",
  "inhospital_diagnosis" varchar(500) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "emr_inhospital"."t_id" IS '序号';
COMMENT ON COLUMN "emr_inhospital"."app_id" IS '应用ID';
COMMENT ON COLUMN "emr_inhospital"."user_id" IS '用户ID';
COMMENT ON COLUMN "emr_inhospital"."inhospital_id" IS '住院号';
COMMENT ON COLUMN "emr_inhospital"."inhospital_time" IS '住院时间';
COMMENT ON COLUMN "emr_inhospital"."case_id" IS '病历ID';
COMMENT ON COLUMN "emr_inhospital"."pathology_no" IS '病理号';
COMMENT ON COLUMN "emr_inhospital"."age" IS '年龄';
COMMENT ON COLUMN "emr_inhospital"."sex" IS '性别';
COMMENT ON COLUMN "emr_inhospital"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "emr_inhospital"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "emr_inhospital"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "emr_inhospital"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "emr_inhospital"."ward_name" IS '病区名称';
COMMENT ON COLUMN "emr_inhospital"."ward_code" IS '病区ID';
COMMENT ON COLUMN "emr_inhospital"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "emr_inhospital"."doc_code" IS '医生工号';
COMMENT ON COLUMN "emr_inhospital"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "emr_inhospital"."identity_number" IS '身份证号';
COMMENT ON COLUMN "emr_inhospital"."medreport_id" IS '病案号';
COMMENT ON COLUMN "emr_inhospital"."bed_no" IS '床号';
COMMENT ON COLUMN "emr_inhospital"."inhospital_count" IS '住院次数';
COMMENT ON COLUMN "emr_inhospital"."inhospital_reason" IS '住院原因';
COMMENT ON COLUMN "emr_inhospital"."inhospital_diagnosis" IS '住院诊断';
COMMENT ON COLUMN "emr_inhospital"."ret_code" IS '返回码';
COMMENT ON COLUMN "emr_inhospital"."ret_info" IS '返回信息';
COMMENT ON TABLE "emr_inhospital" IS '电子病历_住院记录';

-- ----------------------------
-- Table structure for emr_inhospitalfee
-- ----------------------------
DROP TABLE IF EXISTS "emr_inhospitalfee";
CREATE TABLE "emr_inhospitalfee" (
  "t_id" int4 NOT NULL DEFAULT nextval('emr_inhospitalfee_t_id_seq'::regclass),
  "charge_id" varchar(30) COLLATE "pg_catalog"."default",
  "inhospital_id" varchar(30) COLLATE "pg_catalog"."default",
  "charge_type" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "case_id" varchar(30) COLLATE "pg_catalog"."default",
  "medicare_grade" varchar(30) COLLATE "pg_catalog"."default",
  "charge_name" varchar(60) COLLATE "pg_catalog"."default",
  "charge_price" numeric(8,2),
  "count" int4,
  "charge_totalpay" numeric(8,2),
  "pay_ratio" varchar(20) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "emr_inhospitalfee"."t_id" IS '序号';
COMMENT ON COLUMN "emr_inhospitalfee"."charge_id" IS '费用记录ID';
COMMENT ON COLUMN "emr_inhospitalfee"."inhospital_id" IS '住院号';
COMMENT ON COLUMN "emr_inhospitalfee"."charge_type" IS '费用类型';
COMMENT ON COLUMN "emr_inhospitalfee"."user_id" IS '用户ID';
COMMENT ON COLUMN "emr_inhospitalfee"."case_id" IS '病历ID';
COMMENT ON COLUMN "emr_inhospitalfee"."medicare_grade" IS '医保等级';
COMMENT ON COLUMN "emr_inhospitalfee"."charge_name" IS '费用名称';
COMMENT ON COLUMN "emr_inhospitalfee"."charge_price" IS '费用单价';
COMMENT ON COLUMN "emr_inhospitalfee"."count" IS '数量';
COMMENT ON COLUMN "emr_inhospitalfee"."charge_totalpay" IS '费用总额';
COMMENT ON COLUMN "emr_inhospitalfee"."pay_ratio" IS '支付比例';
COMMENT ON COLUMN "emr_inhospitalfee"."create_time" IS '产生时间';
COMMENT ON TABLE "emr_inhospitalfee" IS '电子病历_住院费用';

-- ----------------------------
-- Table structure for emr_opsdetails
-- ----------------------------
DROP TABLE IF EXISTS "emr_opsdetails";
CREATE TABLE "emr_opsdetails" (
  "t_id" int4 NOT NULL DEFAULT nextval('emr_opsdetails_t_id_seq'::regclass),
  "serial_no" varchar(30) COLLATE "pg_catalog"."default",
  "operrecord_no" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "case_id" varchar(30) COLLATE "pg_catalog"."default",
  "inhospital_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "patient_name" varchar(30) COLLATE "pg_catalog"."default",
  "preoperative_code" varchar(30) COLLATE "pg_catalog"."default",
  "preoperative_diagnosis" varchar(600) COLLATE "pg_catalog"."default",
  "postoperative_code" varchar(30) COLLATE "pg_catalog"."default",
  "postoperative_diagnosis" varchar(600) COLLATE "pg_catalog"."default",
  "operative_name" varchar(30) COLLATE "pg_catalog"."default",
  "operative_process" varchar(100) COLLATE "pg_catalog"."default",
  "operative_pics" varchar(30) COLLATE "pg_catalog"."default",
  "cryostat_section_diagnosis_code" varchar(30) COLLATE "pg_catalog"."default",
  "cryostat_section_diagnosis" varchar(600) COLLATE "pg_catalog"."default",
  "operative_specimen" varchar(30) COLLATE "pg_catalog"."default",
  "blood_loss" varchar(30) COLLATE "pg_catalog"."default",
  "asa_grade" varchar(30) COLLATE "pg_catalog"."default",
  "prbc" varchar(30) COLLATE "pg_catalog"."default",
  "ffp" varchar(30) COLLATE "pg_catalog"."default",
  "plates" varchar(30) COLLATE "pg_catalog"."default",
  "operative_class" varchar(50) COLLATE "pg_catalog"."default",
  "operative_incision_grade" varchar(30) COLLATE "pg_catalog"."default",
  "nnis_grade" varchar(30) COLLATE "pg_catalog"."default",
  "anesthesia_method" varchar(30) COLLATE "pg_catalog"."default",
  "anesthesia_doc_code" varchar(15) COLLATE "pg_catalog"."default",
  "anesthesia_doc_name" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "emr_opsdetails"."t_id" IS '序号';
COMMENT ON COLUMN "emr_opsdetails"."serial_no" IS '序列号';
COMMENT ON COLUMN "emr_opsdetails"."operrecord_no" IS '手术记录编号';
COMMENT ON COLUMN "emr_opsdetails"."app_id" IS '应用ID';
COMMENT ON COLUMN "emr_opsdetails"."user_id" IS '用户ID';
COMMENT ON COLUMN "emr_opsdetails"."case_id" IS '病历ID';
COMMENT ON COLUMN "emr_opsdetails"."inhospital_id" IS '住院号';
COMMENT ON COLUMN "emr_opsdetails"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "emr_opsdetails"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "emr_opsdetails"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "emr_opsdetails"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "emr_opsdetails"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "emr_opsdetails"."preoperative_code" IS '术前诊断代码';
COMMENT ON COLUMN "emr_opsdetails"."preoperative_diagnosis" IS '术前诊断';
COMMENT ON COLUMN "emr_opsdetails"."postoperative_code" IS '术后诊断代码';
COMMENT ON COLUMN "emr_opsdetails"."postoperative_diagnosis" IS '术后诊断';
COMMENT ON COLUMN "emr_opsdetails"."operative_name" IS '手术名称';
COMMENT ON COLUMN "emr_opsdetails"."operative_process" IS '手术经过';
COMMENT ON COLUMN "emr_opsdetails"."operative_pics" IS '手术图片';
COMMENT ON COLUMN "emr_opsdetails"."cryostat_section_diagnosis_code" IS '冰冻切片诊断代码';
COMMENT ON COLUMN "emr_opsdetails"."cryostat_section_diagnosis" IS '冰冻切片诊断';
COMMENT ON COLUMN "emr_opsdetails"."operative_specimen" IS '手术标本';
COMMENT ON COLUMN "emr_opsdetails"."blood_loss" IS '失血量';
COMMENT ON COLUMN "emr_opsdetails"."asa_grade" IS 'ASA分级';
COMMENT ON COLUMN "emr_opsdetails"."prbc" IS 'PRBC';
COMMENT ON COLUMN "emr_opsdetails"."ffp" IS 'FFP';
COMMENT ON COLUMN "emr_opsdetails"."plates" IS 'PLATES';
COMMENT ON COLUMN "emr_opsdetails"."operative_class" IS '手术类别';
COMMENT ON COLUMN "emr_opsdetails"."operative_incision_grade" IS '手术切口分级';
COMMENT ON COLUMN "emr_opsdetails"."nnis_grade" IS 'NNIS分级';
COMMENT ON COLUMN "emr_opsdetails"."anesthesia_method" IS '麻醉方式';
COMMENT ON COLUMN "emr_opsdetails"."anesthesia_doc_code" IS '麻醉医生工号';
COMMENT ON COLUMN "emr_opsdetails"."anesthesia_doc_name" IS '麻醉医生';
COMMENT ON TABLE "emr_opsdetails" IS '电子病历_手术详情';

-- ----------------------------
-- Table structure for emr_opsrecord
-- ----------------------------
DROP TABLE IF EXISTS "emr_opsrecord";
CREATE TABLE "emr_opsrecord" (
  "t_id" int4 NOT NULL DEFAULT nextval('emr_opsrecord_t_id_seq'::regclass),
  "serial_no" varchar(30) COLLATE "pg_catalog"."default",
  "operrecord_no" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "case_id" varchar(30) COLLATE "pg_catalog"."default",
  "inhospital_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "ward_name" varchar(30) COLLATE "pg_catalog"."default",
  "ward_code" varchar(30) COLLATE "pg_catalog"."default",
  "bed_no" varchar(30) COLLATE "pg_catalog"."default",
  "patient_name" varchar(30) COLLATE "pg_catalog"."default",
  "age" int4,
  "sex" varchar(10) COLLATE "pg_catalog"."default",
  "identity_number" varchar(30) COLLATE "pg_catalog"."default",
  "oper_starttime" varchar(30) COLLATE "pg_catalog"."default",
  "oper_endtime" varchar(30) COLLATE "pg_catalog"."default",
  "oper_duration" varchar(30) COLLATE "pg_catalog"."default",
  "oper_surgeon_id" varchar(30) COLLATE "pg_catalog"."default",
  "oper_ surgeon _name" varchar(30) COLLATE "pg_catalog"."default",
  "oper_firstassist_id" varchar(15) COLLATE "pg_catalog"."default",
  "oper_firstassist_name" varchar(30) COLLATE "pg_catalog"."default",
  "oper_secondassist_id" varchar(15) COLLATE "pg_catalog"."default",
  "oper_secondassist_name" varchar(30) COLLATE "pg_catalog"."default",
  "oper_thirdassist_id" varchar(15) COLLATE "pg_catalog"."default",
  "oper_thirdassist_name" varchar(30) COLLATE "pg_catalog"."default",
  "oper_doc_id" varchar(15) COLLATE "pg_catalog"."default",
  "oper_doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "record_doc_id" varchar(15) COLLATE "pg_catalog"."default",
  "record_doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "record_time" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "emr_opsrecord"."t_id" IS '序号';
COMMENT ON COLUMN "emr_opsrecord"."serial_no" IS '序列号';
COMMENT ON COLUMN "emr_opsrecord"."operrecord_no" IS '手术记录编号';
COMMENT ON COLUMN "emr_opsrecord"."app_id" IS '应用ID';
COMMENT ON COLUMN "emr_opsrecord"."user_id" IS '用户ID';
COMMENT ON COLUMN "emr_opsrecord"."case_id" IS '病历ID';
COMMENT ON COLUMN "emr_opsrecord"."inhospital_id" IS '住院号';
COMMENT ON COLUMN "emr_opsrecord"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "emr_opsrecord"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "emr_opsrecord"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "emr_opsrecord"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "emr_opsrecord"."ward_name" IS '病区名称';
COMMENT ON COLUMN "emr_opsrecord"."ward_code" IS '病区ID';
COMMENT ON COLUMN "emr_opsrecord"."bed_no" IS '床号';
COMMENT ON COLUMN "emr_opsrecord"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "emr_opsrecord"."age" IS '年龄';
COMMENT ON COLUMN "emr_opsrecord"."sex" IS '性别';
COMMENT ON COLUMN "emr_opsrecord"."identity_number" IS '身份证号';
COMMENT ON COLUMN "emr_opsrecord"."oper_starttime" IS '手术开始时间';
COMMENT ON COLUMN "emr_opsrecord"."oper_endtime" IS '手术结束时间';
COMMENT ON COLUMN "emr_opsrecord"."oper_duration" IS '手术持续时间';
COMMENT ON COLUMN "emr_opsrecord"."oper_surgeon_id" IS '主刀医生工号';
COMMENT ON COLUMN "emr_opsrecord"."oper_ surgeon _name" IS '主刀医生姓名';
COMMENT ON COLUMN "emr_opsrecord"."oper_firstassist_id" IS '手术I助工号';
COMMENT ON COLUMN "emr_opsrecord"."oper_firstassist_name" IS '手术I助';
COMMENT ON COLUMN "emr_opsrecord"."oper_secondassist_id" IS '手术II助工号';
COMMENT ON COLUMN "emr_opsrecord"."oper_secondassist_name" IS '手术II助';
COMMENT ON COLUMN "emr_opsrecord"."oper_thirdassist_id" IS '手术III助工号';
COMMENT ON COLUMN "emr_opsrecord"."oper_thirdassist_name" IS '手术III助';
COMMENT ON COLUMN "emr_opsrecord"."oper_doc_id" IS '手术医生工号';
COMMENT ON COLUMN "emr_opsrecord"."oper_doc_name" IS '手术医生';
COMMENT ON COLUMN "emr_opsrecord"."record_doc_id" IS '记录医生工号';
COMMENT ON COLUMN "emr_opsrecord"."record_doc_name" IS '记录医生';
COMMENT ON COLUMN "emr_opsrecord"."create_time" IS '创建时间';
COMMENT ON COLUMN "emr_opsrecord"."record_time" IS '记录时间';
COMMENT ON COLUMN "emr_opsrecord"."ret_code" IS '返回码';
COMMENT ON COLUMN "emr_opsrecord"."ret_info" IS '返回信息';
COMMENT ON TABLE "emr_opsrecord" IS '电子病历_手术记录';

-- ----------------------------
-- Table structure for emr_outhospitaldrug
-- ----------------------------
DROP TABLE IF EXISTS "emr_outhospitaldrug";
CREATE TABLE "emr_outhospitaldrug" (
  "t_id" int4 NOT NULL DEFAULT nextval('emr_outhospitaldrug_t_id_seq'::regclass),
  "serial_no" varchar(30) COLLATE "pg_catalog"."default",
  "inhospital_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "outhospital_time" varchar(30) COLLATE "pg_catalog"."default",
  "doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "case_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "medreport_id" varchar(30) COLLATE "pg_catalog"."default",
  "pathology_no" varchar(30) COLLATE "pg_catalog"."default",
  "drug_name" varchar(100) COLLATE "pg_catalog"."default",
  "drug_spec_quantity" varchar(100) COLLATE "pg_catalog"."default",
  "perusage" varchar(100) COLLATE "pg_catalog"."default",
  "note" varchar(100) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "emr_outhospitaldrug"."t_id" IS '序号';
COMMENT ON COLUMN "emr_outhospitaldrug"."serial_no" IS '出院编号';
COMMENT ON COLUMN "emr_outhospitaldrug"."inhospital_id" IS '住院号';
COMMENT ON COLUMN "emr_outhospitaldrug"."user_id" IS '用户ID';
COMMENT ON COLUMN "emr_outhospitaldrug"."app_id" IS '应用ID';
COMMENT ON COLUMN "emr_outhospitaldrug"."outhospital_time" IS '出院时间';
COMMENT ON COLUMN "emr_outhospitaldrug"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "emr_outhospitaldrug"."case_id" IS '病历ID';
COMMENT ON COLUMN "emr_outhospitaldrug"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "emr_outhospitaldrug"."medreport_id" IS '病案号';
COMMENT ON COLUMN "emr_outhospitaldrug"."pathology_no" IS '病理号';
COMMENT ON COLUMN "emr_outhospitaldrug"."drug_name" IS '药品名称';
COMMENT ON COLUMN "emr_outhospitaldrug"."drug_spec_quantity" IS '规格/数量';
COMMENT ON COLUMN "emr_outhospitaldrug"."perusage" IS '单次用量/用法';
COMMENT ON COLUMN "emr_outhospitaldrug"."note" IS '备注';
COMMENT ON COLUMN "emr_outhospitaldrug"."create_time" IS '创建时间';
COMMENT ON COLUMN "emr_outhospitaldrug"."ret_code" IS '返回码';
COMMENT ON COLUMN "emr_outhospitaldrug"."ret_info" IS '返回信息';
COMMENT ON TABLE "emr_outhospitaldrug" IS '电子病历_出院带药';

-- ----------------------------
-- Table structure for emr_outhospitalrecord
-- ----------------------------
DROP TABLE IF EXISTS "emr_outhospitalrecord";
CREATE TABLE "emr_outhospitalrecord" (
  "t_id" int4 NOT NULL DEFAULT nextval('emr_outhospitalrecord_t_id_seq'::regclass),
  "serial_no" varchar(30) COLLATE "pg_catalog"."default",
  "patient_name" varchar(30) COLLATE "pg_catalog"."default",
  "inhospital_id" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "case_id" varchar(30) COLLATE "pg_catalog"."default",
  "outhospital_time" varchar(30) COLLATE "pg_catalog"."default",
  "outhospital_diagnosis" varchar(500) COLLATE "pg_catalog"."default",
  "treat_course" varchar(500) COLLATE "pg_catalog"."default",
  "outhospital_state" varchar(500) COLLATE "pg_catalog"."default",
  "outhospital_state_temperature" varchar(64) COLLATE "pg_catalog"."default",
  "outhospital_state_pulse" varchar(64) COLLATE "pg_catalog"."default",
  "outhospital_state_breath" varchar(64) COLLATE "pg_catalog"."default",
  "outhospital_state_pressure" varchar(64) COLLATE "pg_catalog"."default",
  "outhospital_destination" varchar(300) COLLATE "pg_catalog"."default",
  "tips_selfcare" varchar(200) COLLATE "pg_catalog"."default",
  "doc_signature" varchar(50) COLLATE "pg_catalog"."default",
  "record_time" varchar(30) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "operative_position" varchar(200) COLLATE "pg_catalog"."default",
  "outhospital_docadvice" varchar(300) COLLATE "pg_catalog"."default",
  "inhospital_time" varchar(30) COLLATE "pg_catalog"."default",
  "medreport_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "ward_code" varchar(30) COLLATE "pg_catalog"."default",
  "ward_name" varchar(30) COLLATE "pg_catalog"."default",
  "bed_no" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "emr_outhospitalrecord"."t_id" IS '序号';
COMMENT ON COLUMN "emr_outhospitalrecord"."serial_no" IS '出院编号';
COMMENT ON COLUMN "emr_outhospitalrecord"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "emr_outhospitalrecord"."inhospital_id" IS '住院号';
COMMENT ON COLUMN "emr_outhospitalrecord"."app_id" IS '应用ID';
COMMENT ON COLUMN "emr_outhospitalrecord"."user_id" IS '用户ID';
COMMENT ON COLUMN "emr_outhospitalrecord"."case_id" IS '病历ID';
COMMENT ON COLUMN "emr_outhospitalrecord"."outhospital_time" IS '出院时间';
COMMENT ON COLUMN "emr_outhospitalrecord"."outhospital_diagnosis" IS '出院诊断';
COMMENT ON COLUMN "emr_outhospitalrecord"."treat_course" IS '诊治经过';
COMMENT ON COLUMN "emr_outhospitalrecord"."outhospital_state" IS '出院状况';
COMMENT ON COLUMN "emr_outhospitalrecord"."outhospital_state_temperature" IS '出院状况_体温';
COMMENT ON COLUMN "emr_outhospitalrecord"."outhospital_state_pulse" IS '出院状况_脉搏';
COMMENT ON COLUMN "emr_outhospitalrecord"."outhospital_state_breath" IS '出院状况_呼吸';
COMMENT ON COLUMN "emr_outhospitalrecord"."outhospital_state_pressure" IS '出院状况_血压';
COMMENT ON COLUMN "emr_outhospitalrecord"."outhospital_destination" IS '出院去向';
COMMENT ON COLUMN "emr_outhospitalrecord"."tips_selfcare" IS '生活自理';
COMMENT ON COLUMN "emr_outhospitalrecord"."doc_signature" IS '医生签名';
COMMENT ON COLUMN "emr_outhospitalrecord"."record_time" IS '记录时间';
COMMENT ON COLUMN "emr_outhospitalrecord"."create_time" IS '创建时间';
COMMENT ON COLUMN "emr_outhospitalrecord"."operative_position" IS '手术部位监测';
COMMENT ON COLUMN "emr_outhospitalrecord"."outhospital_docadvice" IS '出院医嘱';
COMMENT ON COLUMN "emr_outhospitalrecord"."inhospital_time" IS '住院时间';
COMMENT ON COLUMN "emr_outhospitalrecord"."medreport_id" IS '病案号';
COMMENT ON COLUMN "emr_outhospitalrecord"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "emr_outhospitalrecord"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "emr_outhospitalrecord"."ward_code" IS '病区ID';
COMMENT ON COLUMN "emr_outhospitalrecord"."ward_name" IS '病区名称';
COMMENT ON COLUMN "emr_outhospitalrecord"."bed_no" IS '床号';
COMMENT ON COLUMN "emr_outhospitalrecord"."ret_code" IS '返回码';
COMMENT ON COLUMN "emr_outhospitalrecord"."ret_info" IS '返回信息';
COMMENT ON TABLE "emr_outhospitalrecord" IS '电子病历_出院记录';

-- ----------------------------
-- Table structure for emr_outhospitalrecord_followup
-- ----------------------------
DROP TABLE IF EXISTS "emr_outhospitalrecord_followup";
CREATE TABLE "emr_outhospitalrecord_followup" (
  "t_id" int4 NOT NULL DEFAULT nextval('emr_outhospitalrecord_followup_t_id_seq'::regclass),
  "serial_no" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "case_id" varchar(30) COLLATE "pg_catalog"."default",
  "patient_name" varchar(30) COLLATE "pg_catalog"."default",
  "inhospital_id" varchar(30) COLLATE "pg_catalog"."default",
  "doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "age" int4,
  "sex" varchar(10) COLLATE "pg_catalog"."default",
  "outhospital_time" varchar(30) COLLATE "pg_catalog"."default",
  "followup_time" varchar(30) COLLATE "pg_catalog"."default",
  "followup_location" varchar(30) COLLATE "pg_catalog"."default",
  "followup_purpose" varchar(30) COLLATE "pg_catalog"."default",
  "followup_dept" varchar(30) COLLATE "pg_catalog"."default",
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."t_id" IS '序号';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."serial_no" IS '出院编号';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."app_id" IS '应用ID';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."user_id" IS '用户ID';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."case_id" IS '病历ID';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."inhospital_id" IS '住院号';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."age" IS '年龄';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."sex" IS '性别';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."outhospital_time" IS '出院时间';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."followup_time" IS '复诊时间';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."followup_location" IS '复诊地点';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."followup_purpose" IS '复诊目的';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."followup_dept" IS '复诊科室';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."create_time" IS '创建时间';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."ret_code" IS '返回码';
COMMENT ON COLUMN "emr_outhospitalrecord_followup"."ret_info" IS '返回信息';
COMMENT ON TABLE "emr_outhospitalrecord_followup" IS '电子病历_出院记录_复诊';

-- ----------------------------
-- Table structure for emr_query
-- ----------------------------
DROP TABLE IF EXISTS "emr_query";
CREATE TABLE "emr_query" (
  "t_id" int4 NOT NULL DEFAULT nextval('emr_query_t_id_seq'::regclass),
  "query_time" timestamp(6),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "case_id" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "emr_query"."t_id" IS '序号';
COMMENT ON COLUMN "emr_query"."query_time" IS '查询时间';
COMMENT ON COLUMN "emr_query"."app_id" IS '应用ID';
COMMENT ON COLUMN "emr_query"."user_id" IS '用户ID';
COMMENT ON COLUMN "emr_query"."case_id" IS '病历ID';
COMMENT ON COLUMN "emr_query"."ret_code" IS '返回码';
COMMENT ON COLUMN "emr_query"."ret_info" IS '返回信息';
COMMENT ON TABLE "emr_query" IS '电子病历_查询表';

-- ----------------------------
-- Table structure for emr_user
-- ----------------------------
DROP TABLE IF EXISTS "emr_user";
CREATE TABLE "emr_user" (
  "t_id" int4 NOT NULL DEFAULT nextval('emr_user_t_id_seq'::regclass),
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "case_id" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "sex" varchar(10) COLLATE "pg_catalog"."default",
  "identity_number" varchar(30) COLLATE "pg_catalog"."default",
  "doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "doc_code" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "emr_user"."t_id" IS '序号';
COMMENT ON COLUMN "emr_user"."user_id" IS '用户ID';
COMMENT ON COLUMN "emr_user"."case_id" IS '病历ID';
COMMENT ON COLUMN "emr_user"."app_id" IS '应用ID';
COMMENT ON COLUMN "emr_user"."sex" IS '性别';
COMMENT ON COLUMN "emr_user"."identity_number" IS '身份证号';
COMMENT ON COLUMN "emr_user"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "emr_user"."doc_code" IS '医生工号';
COMMENT ON COLUMN "emr_user"."ret_code" IS '返回码';
COMMENT ON COLUMN "emr_user"."ret_info" IS '返回信息';
COMMENT ON TABLE "emr_user" IS '电子病历_用户信息表';

-- ----------------------------
-- Table structure for exam_list_details
-- ----------------------------
DROP TABLE IF EXISTS "exam_list_details";
CREATE TABLE "exam_list_details" (
  "t_id" int4 NOT NULL DEFAULT nextval('exam_list_details_t_id_seq'::regclass),
  "query_time" timestamp(6),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "dept_name" varchar(30) COLLATE "pg_catalog"."default",
  "dept_code" varchar(30) COLLATE "pg_catalog"."default",
  "special" varchar(30) COLLATE "pg_catalog"."default",
  "treat_cardno" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "exam_list_details"."t_id" IS '序号';
COMMENT ON COLUMN "exam_list_details"."query_time" IS '查询时间';
COMMENT ON COLUMN "exam_list_details"."app_id" IS '应用ID';
COMMENT ON COLUMN "exam_list_details"."user_id" IS '用户ID';
COMMENT ON COLUMN "exam_list_details"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "exam_list_details"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "exam_list_details"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "exam_list_details"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "exam_list_details"."dept_name" IS '科室名称';
COMMENT ON COLUMN "exam_list_details"."dept_code" IS '科室代码';
COMMENT ON COLUMN "exam_list_details"."special" IS '专科';
COMMENT ON COLUMN "exam_list_details"."treat_cardno" IS '就诊卡号';
COMMENT ON COLUMN "exam_list_details"."ret_code" IS '返回码';
COMMENT ON COLUMN "exam_list_details"."ret_info" IS '返回信息';
COMMENT ON TABLE "exam_list_details" IS '检查单列表详情查询';

-- ----------------------------
-- Table structure for exam_list_details_item
-- ----------------------------
DROP TABLE IF EXISTS "exam_list_details_item";
CREATE TABLE "exam_list_details_item" (
  "t_id" int4 NOT NULL DEFAULT nextval('exam_list_details_item_t_id_seq'::regclass),
  "exam_id" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "patient_name" varchar(30) COLLATE "pg_catalog"."default",
  "exam_name" varchar(100) COLLATE "pg_catalog"."default",
  "exam_position" varchar(30) COLLATE "pg_catalog"."default",
  "exam_result" varchar(1000) COLLATE "pg_catalog"."default",
  "exam_time" varchar(30) COLLATE "pg_catalog"."default",
  "is_save" int2
)
;
COMMENT ON COLUMN "exam_list_details_item"."t_id" IS '序号';
COMMENT ON COLUMN "exam_list_details_item"."exam_id" IS '检查单号';
COMMENT ON COLUMN "exam_list_details_item"."app_id" IS '应用ID';
COMMENT ON COLUMN "exam_list_details_item"."user_id" IS '用户ID';
COMMENT ON COLUMN "exam_list_details_item"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "exam_list_details_item"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "exam_list_details_item"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "exam_list_details_item"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "exam_list_details_item"."exam_name" IS '检查名称';
COMMENT ON COLUMN "exam_list_details_item"."exam_position" IS '检查部位';
COMMENT ON COLUMN "exam_list_details_item"."exam_result" IS '检查结果';
COMMENT ON COLUMN "exam_list_details_item"."exam_time" IS '检查时间';
COMMENT ON COLUMN "exam_list_details_item"."is_save" IS '保存';
COMMENT ON TABLE "exam_list_details_item" IS '检查单列表详情查询明细';

-- ----------------------------
-- Table structure for healthrecord_addassay
-- ----------------------------
DROP TABLE IF EXISTS "healthrecord_addassay";
CREATE TABLE "healthrecord_addassay" (
  "t_id" int4 NOT NULL DEFAULT nextval('healthrecord_addassay_t_id_seq'::regclass),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "report_no" varchar(30) COLLATE "pg_catalog"."default",
  "add_time" timestamp(6),
  "report_type" int4,
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "healthrecord_addassay"."t_id" IS '序号';
COMMENT ON COLUMN "healthrecord_addassay"."app_id" IS '应用ID';
COMMENT ON COLUMN "healthrecord_addassay"."user_id" IS '用户ID';
COMMENT ON COLUMN "healthrecord_addassay"."report_no" IS '单号';
COMMENT ON COLUMN "healthrecord_addassay"."add_time" IS '添加时间';
COMMENT ON COLUMN "healthrecord_addassay"."report_type" IS '类别';
COMMENT ON COLUMN "healthrecord_addassay"."ret_code" IS '返回码';
COMMENT ON COLUMN "healthrecord_addassay"."ret_info" IS '返回信息';
COMMENT ON TABLE "healthrecord_addassay" IS '健康档案_添加检查检验单';

-- ----------------------------
-- Table structure for healthrecord_adddiscomfort
-- ----------------------------
DROP TABLE IF EXISTS "healthrecord_adddiscomfort";
CREATE TABLE "healthrecord_adddiscomfort" (
  "t_id" int4 NOT NULL DEFAULT nextval('healthrecord_adddiscomfort_t_id_seq'::regclass),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "record_id" varchar(30) COLLATE "pg_catalog"."default",
  "discomfort_id" varchar(30) COLLATE "pg_catalog"."default",
  "body_position" varchar(30) COLLATE "pg_catalog"."default",
  "sympton" varchar(30) COLLATE "pg_catalog"."default",
  "discomfort_time" varchar(30) COLLATE "pg_catalog"."default",
  "url" varchar(30) COLLATE "pg_catalog"."default",
  "details" varchar(100) COLLATE "pg_catalog"."default",
  "add_time" timestamp(6),
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "healthrecord_adddiscomfort"."t_id" IS '序号';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."app_id" IS '应用ID';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."user_id" IS '用户ID';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."record_id" IS '档案ID';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."discomfort_id" IS '不适记录ID';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."body_position" IS '身体部位';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."sympton" IS '症状';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."discomfort_time" IS '不适时间';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."url" IS '上传文件地址';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."details" IS '详情';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."add_time" IS '添加时间';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."ret_code" IS '返回码';
COMMENT ON COLUMN "healthrecord_adddiscomfort"."ret_info" IS '返回信息';
COMMENT ON TABLE "healthrecord_adddiscomfort" IS '健康档案_添加不适纪录表';

-- ----------------------------
-- Table structure for healthrecord_adddrug
-- ----------------------------
DROP TABLE IF EXISTS "healthrecord_adddrug";
CREATE TABLE "healthrecord_adddrug" (
  "t_id" int4 NOT NULL DEFAULT nextval('healthrecord_adddrug_t_id_seq'::regclass),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "record_id" varchar(30) COLLATE "pg_catalog"."default",
  "drug_id" varchar(30) COLLATE "pg_catalog"."default",
  "add_time" timestamp(6),
  "drug_name" varchar(50) COLLATE "pg_catalog"."default",
  "drug_dose" varchar(30) COLLATE "pg_catalog"."default",
  "drug_perdose" varchar(30) COLLATE "pg_catalog"."default",
  "drug_rate" varchar(30) COLLATE "pg_catalog"."default",
  "unit" varchar(30) COLLATE "pg_catalog"."default",
  "drug_way" varchar(30) COLLATE "pg_catalog"."default",
  "start_time" varchar(30) COLLATE "pg_catalog"."default",
  "stop_time" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "healthrecord_adddrug"."t_id" IS '序号';
COMMENT ON COLUMN "healthrecord_adddrug"."app_id" IS '应用ID';
COMMENT ON COLUMN "healthrecord_adddrug"."user_id" IS '用户ID';
COMMENT ON COLUMN "healthrecord_adddrug"."record_id" IS '档案ID';
COMMENT ON COLUMN "healthrecord_adddrug"."drug_id" IS '服药记录ID';
COMMENT ON COLUMN "healthrecord_adddrug"."add_time" IS '添加时间';
COMMENT ON COLUMN "healthrecord_adddrug"."drug_name" IS '药物名称';
COMMENT ON COLUMN "healthrecord_adddrug"."drug_dose" IS '剂量';
COMMENT ON COLUMN "healthrecord_adddrug"."drug_perdose" IS '每次剂量';
COMMENT ON COLUMN "healthrecord_adddrug"."drug_rate" IS '服药频率';
COMMENT ON COLUMN "healthrecord_adddrug"."unit" IS '单位';
COMMENT ON COLUMN "healthrecord_adddrug"."drug_way" IS '给药方式';
COMMENT ON COLUMN "healthrecord_adddrug"."start_time" IS '开始时间';
COMMENT ON COLUMN "healthrecord_adddrug"."stop_time" IS '结束时间';
COMMENT ON COLUMN "healthrecord_adddrug"."ret_code" IS '返回码';
COMMENT ON COLUMN "healthrecord_adddrug"."ret_info" IS '返回信息';
COMMENT ON TABLE "healthrecord_adddrug" IS '健康档案_添加服药记录表';

-- ----------------------------
-- Table structure for healthrecord_addmedicalhistory
-- ----------------------------
DROP TABLE IF EXISTS "healthrecord_addmedicalhistory";
CREATE TABLE "healthrecord_addmedicalhistory" (
  "t_id" int4 NOT NULL DEFAULT nextval('healthrecord_addmedicalhistory_t_id_seq'::regclass),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "record_id" varchar(30) COLLATE "pg_catalog"."default",
  "history_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "patient_name" varchar(30) COLLATE "pg_catalog"."default",
  "add_time" timestamp(6),
  "history_time" varchar(30) COLLATE "pg_catalog"."default",
  "history_name" varchar(50) COLLATE "pg_catalog"."default",
  "history_type" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."t_id" IS '序号';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."app_id" IS '应用ID';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."user_id" IS '用户ID';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."record_id" IS '档案ID';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."history_id" IS '病史记录ID';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."add_time" IS '添加时间';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."history_time" IS '病史时间';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."history_name" IS '病史名称';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."history_type" IS '病史类型';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."ret_code" IS '返回码';
COMMENT ON COLUMN "healthrecord_addmedicalhistory"."ret_info" IS '返回信息';
COMMENT ON TABLE "healthrecord_addmedicalhistory" IS '健康档案_添加病史表';

-- ----------------------------
-- Table structure for healthrecord_query
-- ----------------------------
DROP TABLE IF EXISTS "healthrecord_query";
CREATE TABLE "healthrecord_query" (
  "t_id" int4 NOT NULL DEFAULT nextval('healthrecord_query_t_id_seq'::regclass),
  "query_time" timestamp(6),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "record_id" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "healthrecord_query"."t_id" IS '序号';
COMMENT ON COLUMN "healthrecord_query"."query_time" IS '查询时间';
COMMENT ON COLUMN "healthrecord_query"."app_id" IS '应用ID';
COMMENT ON COLUMN "healthrecord_query"."user_id" IS '用户ID';
COMMENT ON COLUMN "healthrecord_query"."record_id" IS '档案ID';
COMMENT ON COLUMN "healthrecord_query"."ret_code" IS '返回码';
COMMENT ON COLUMN "healthrecord_query"."ret_info" IS '返回信息';
COMMENT ON TABLE "healthrecord_query" IS '健康档案_查询表';

-- ----------------------------
-- Table structure for healthrecord_user
-- ----------------------------
DROP TABLE IF EXISTS "healthrecord_user";
CREATE TABLE "healthrecord_user" (
  "t_id" int4 NOT NULL DEFAULT nextval('healthrecord_user_t_id_seq'::regclass),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "record_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "patient_name" varchar(30) COLLATE "pg_catalog"."default",
  "identity_number" varchar(30) COLLATE "pg_catalog"."default",
  "sex" varchar(10) COLLATE "pg_catalog"."default",
  "address" varchar(200) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "healthrecord_user"."t_id" IS '序号';
COMMENT ON COLUMN "healthrecord_user"."app_id" IS '应用ID';
COMMENT ON COLUMN "healthrecord_user"."user_id" IS '用户ID';
COMMENT ON COLUMN "healthrecord_user"."record_id" IS '档案ID';
COMMENT ON COLUMN "healthrecord_user"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "healthrecord_user"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "healthrecord_user"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "healthrecord_user"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "healthrecord_user"."patient_name" IS '患者姓名';
COMMENT ON COLUMN "healthrecord_user"."identity_number" IS '身份证号';
COMMENT ON COLUMN "healthrecord_user"."sex" IS '性别';
COMMENT ON COLUMN "healthrecord_user"."address" IS '居住地';
COMMENT ON COLUMN "healthrecord_user"."ret_code" IS '返回码';
COMMENT ON COLUMN "healthrecord_user"."ret_info" IS '返回信息';
COMMENT ON TABLE "healthrecord_user" IS '健康档案_用户信息表';

-- ----------------------------
-- Table structure for jc_common_section
-- ----------------------------
DROP TABLE IF EXISTS "jc_common_section";
CREATE TABLE "jc_common_section" (
  "t_id" int4 NOT NULL DEFAULT nextval('jc_common_section_t_id_seq'::regclass),
  "section_no" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "section_name" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "fid" varchar(64) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "description" text COLLATE "pg_catalog"."default",
  "remark" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "rank" char(1) COLLATE "pg_catalog"."default" DEFAULT NULL::bpchar,
  "seq" int4,
  "is_delete" char(1) COLLATE "pg_catalog"."default" DEFAULT NULL::bpchar
)
;
COMMENT ON COLUMN "jc_common_section"."t_id" IS '序号';
COMMENT ON COLUMN "jc_common_section"."section_no" IS '标准科室编号';
COMMENT ON COLUMN "jc_common_section"."section_name" IS '科室名称';
COMMENT ON COLUMN "jc_common_section"."fid" IS '上一级id';
COMMENT ON COLUMN "jc_common_section"."description" IS '描述';
COMMENT ON COLUMN "jc_common_section"."remark" IS '备注';
COMMENT ON COLUMN "jc_common_section"."rank" IS '级别';
COMMENT ON COLUMN "jc_common_section"."seq" IS '排序';
COMMENT ON COLUMN "jc_common_section"."is_delete" IS '是否删除，0已删除，1未删除';
COMMENT ON TABLE "jc_common_section" IS '科室表';

-- ----------------------------
-- Table structure for jc_doctor_info
-- ----------------------------
DROP TABLE IF EXISTS "jc_doctor_info";
CREATE TABLE "jc_doctor_info" (
  "t_id" int4 NOT NULL DEFAULT nextval('jc_doctor_info_t_id_seq'::regclass),
  "user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "ucmed_hospital_id" int4,
  "hospital_org_code" char(9) COLLATE "pg_catalog"."default",
  "section_name" varchar(60) COLLATE "pg_catalog"."default",
  "common_section_no" varchar(20) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
  "work_no" varchar(8) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "ucmed_unit_doctor_id" varchar(255) COLLATE "pg_catalog"."default",
  "source_ids" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" varchar(255) COLLATE "pg_catalog"."default",
  "update_time" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "jc_doctor_info"."t_id" IS '序号';
COMMENT ON COLUMN "jc_doctor_info"."user_id" IS '登录名';
COMMENT ON COLUMN "jc_doctor_info"."ucmed_hospital_id" IS '医院编号';
COMMENT ON COLUMN "jc_doctor_info"."hospital_org_code" IS '医院组织机构代码';
COMMENT ON COLUMN "jc_doctor_info"."section_name" IS '科室名称';
COMMENT ON COLUMN "jc_doctor_info"."common_section_no" IS '标准科室ID';
COMMENT ON COLUMN "jc_doctor_info"."work_no" IS '医生工号';
COMMENT ON COLUMN "jc_doctor_info"."ucmed_unit_doctor_id" IS '卓健医生标识id';
COMMENT ON COLUMN "jc_doctor_info"."source_ids" IS '数据来源';
COMMENT ON COLUMN "jc_doctor_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "jc_doctor_info"."update_time" IS '修改时间';
COMMENT ON TABLE "jc_doctor_info" IS '医生信息';

-- ----------------------------
-- Table structure for jc_hospital
-- ----------------------------
DROP TABLE IF EXISTS "jc_hospital";
CREATE TABLE "jc_hospital" (
  "t_id" int4 NOT NULL DEFAULT nextval('jc_hospital_t_id_seq'::regclass),
  "ucmed_hospital_id" int4 NOT NULL,
  "org_code" char(9) COLLATE "pg_catalog"."default" NOT NULL,
  "hospital_name" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "short_name" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "spell_code" varchar(250) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "hospital_isuure_code" varchar(8) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "grade_of_unit" varchar(1) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "level_of_unit" varchar(1) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "org_type_code" varchar(4) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "org_class_code" varchar(1) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "area_code" varchar(6) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "province" varchar(6) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "city" varchar(6) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "area" varchar(6) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "econ_code" varchar(4) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "address" varchar(70) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "street" varchar(3) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "zip_code" varchar(6) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "phone" varchar(16) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "e_mail" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "domain" varchar(60) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "create_date" varchar(4) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "set_unit_code" varchar(1) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "sub_ordinate" varchar(10) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "fund" int4,
  "deputy" varchar(10) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "is_nation" varchar(1) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "is_sub_unit" varchar(1) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "subunit_num" int4,
  "subcommunity_num" int4,
  "import_subjection" varchar(20) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "strong_subjection" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "source_id" int4 NOT NULL,
  "create_time" varchar COLLATE "pg_catalog"."default",
  "update_time" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "jc_hospital"."t_id" IS '序号';
COMMENT ON COLUMN "jc_hospital"."ucmed_hospital_id" IS '医院编号';
COMMENT ON COLUMN "jc_hospital"."org_code" IS '医院组织机构代码';
COMMENT ON COLUMN "jc_hospital"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "jc_hospital"."short_name" IS '医院简称';
COMMENT ON COLUMN "jc_hospital"."spell_code" IS '医院拼音';
COMMENT ON COLUMN "jc_hospital"."hospital_isuure_code" IS '医疗机构医保代码';
COMMENT ON COLUMN "jc_hospital"."grade_of_unit" IS '医院级别代码';
COMMENT ON COLUMN "jc_hospital"."level_of_unit" IS '医院等次代码';
COMMENT ON COLUMN "jc_hospital"."org_type_code" IS '卫生机构类别代码';
COMMENT ON COLUMN "jc_hospital"."org_class_code" IS '机构分类管理代码';
COMMENT ON COLUMN "jc_hospital"."area_code" IS '行政区域代码';
COMMENT ON COLUMN "jc_hospital"."province" IS '省';
COMMENT ON COLUMN "jc_hospital"."city" IS '地级市';
COMMENT ON COLUMN "jc_hospital"."area" IS '县市';
COMMENT ON COLUMN "jc_hospital"."econ_code" IS '经济类型代码';
COMMENT ON COLUMN "jc_hospital"."address" IS '地址';
COMMENT ON COLUMN "jc_hospital"."street" IS '单位所在地街道/乡镇代码';
COMMENT ON COLUMN "jc_hospital"."zip_code" IS '邮政编码';
COMMENT ON COLUMN "jc_hospital"."phone" IS '电话号码';
COMMENT ON COLUMN "jc_hospital"."e_mail" IS '单位电子邮件';
COMMENT ON COLUMN "jc_hospital"."domain" IS '单位网站域名';
COMMENT ON COLUMN "jc_hospital"."create_date" IS '单位开业/成立时间';
COMMENT ON COLUMN "jc_hospital"."set_unit_code" IS '设置主办单位代码';
COMMENT ON COLUMN "jc_hospital"."sub_ordinate" IS '政府办卫生机构隶属关系';
COMMENT ON COLUMN "jc_hospital"."fund" IS '注册资金(万元)';
COMMENT ON COLUMN "jc_hospital"."deputy" IS '法人代表(单位负责人)';
COMMENT ON COLUMN "jc_hospital"."is_nation" IS '是否民族自治地方';
COMMENT ON COLUMN "jc_hospital"."is_sub_unit" IS '是否分支机构';
COMMENT ON COLUMN "jc_hospital"."subunit_num" IS '下设直属分站(院、所)个数';
COMMENT ON COLUMN "jc_hospital"."subcommunity_num" IS '其中：社区卫生服务中心(站)个数';
COMMENT ON COLUMN "jc_hospital"."import_subjection" IS '重点学科';
COMMENT ON COLUMN "jc_hospital"."strong_subjection" IS '专科特长';
COMMENT ON COLUMN "jc_hospital"."source_id" IS '数据来源，如医链，医联体';
COMMENT ON COLUMN "jc_hospital"."create_time" IS '创建时间';
COMMENT ON COLUMN "jc_hospital"."update_time" IS '修改时间';
COMMENT ON TABLE "jc_hospital" IS '医院详细信息表';

-- ----------------------------
-- Table structure for jc_hospital_section
-- ----------------------------
DROP TABLE IF EXISTS "jc_hospital_section";
CREATE TABLE "jc_hospital_section" (
  "t_id" int4 NOT NULL DEFAULT nextval('jc_hospital_section_t_id_seq'::regclass),
  "ucmed_hospital_id" varchar(10) COLLATE "pg_catalog"."default" NOT NULL,
  "hospital_name" varchar(40) COLLATE "pg_catalog"."default",
  "org_code" varchar(9) COLLATE "pg_catalog"."default" NOT NULL,
  "section_no" varchar(64) COLLATE "pg_catalog"."default" NOT NULL,
  "section_name" varchar(64) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "jc_hospital_section"."t_id" IS '序号';
COMMENT ON COLUMN "jc_hospital_section"."ucmed_hospital_id" IS '医院编号';
COMMENT ON COLUMN "jc_hospital_section"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "jc_hospital_section"."org_code" IS '医院组织机构代码';
COMMENT ON COLUMN "jc_hospital_section"."section_no" IS '标准科室编号';
COMMENT ON COLUMN "jc_hospital_section"."section_name" IS '科室名称';
COMMENT ON TABLE "jc_hospital_section" IS '医院科室关系表';

-- ----------------------------
-- Table structure for jc_platform_source
-- ----------------------------
DROP TABLE IF EXISTS "jc_platform_source";
CREATE TABLE "jc_platform_source" (
  "t_id" int4 NOT NULL DEFAULT nextval('jc_platform_source_t_id_seq'::regclass),
  "source_id" int4 NOT NULL,
  "source_name" varchar(16) COLLATE "pg_catalog"."default" NOT NULL,
  "source_url" varchar(250) COLLATE "pg_catalog"."default" NOT NULL,
  "source_token" char(36) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "jc_platform_source"."t_id" IS '序号';
COMMENT ON COLUMN "jc_platform_source"."source_id" IS '来源编号';
COMMENT ON COLUMN "jc_platform_source"."source_name" IS '来源名称，如医链，医联体';
COMMENT ON COLUMN "jc_platform_source"."source_url" IS '来源url，调用接口时用';
COMMENT ON COLUMN "jc_platform_source"."source_token" IS 'token，调用接口时用';

-- ----------------------------
-- Table structure for jc_user
-- ----------------------------
DROP TABLE IF EXISTS "jc_user";
CREATE TABLE "jc_user" (
  "uid" int4 NOT NULL DEFAULT nextval('jc_user_uid_seq'::regclass),
  "user_id" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "password" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "phone" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "latesttime" varchar COLLATE "pg_catalog"."default",
  "failnum" int4 DEFAULT 0,
  "create_time" varchar(30) COLLATE "pg_catalog"."default",
  "login_times" int8 DEFAULT 0,
  "pass_change_time" varchar COLLATE "pg_catalog"."default",
  "lock" char(1) COLLATE "pg_catalog"."default" DEFAULT 'n'::bpchar,
  "token" varchar COLLATE "pg_catalog"."default",
  "token_time" varchar COLLATE "pg_catalog"."default",
  "wechat_id" varchar(255) COLLATE "pg_catalog"."default",
  "valid" int4 DEFAULT 1,
  "securitykey" varchar(255) COLLATE "pg_catalog"."default",
  "ucmed_id" varchar(255) COLLATE "pg_catalog"."default",
  "description" varchar(255) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;
COMMENT ON COLUMN "jc_user"."uid" IS '用户序号';
COMMENT ON COLUMN "jc_user"."user_id" IS '用户名';
COMMENT ON COLUMN "jc_user"."password" IS '密码';
COMMENT ON COLUMN "jc_user"."phone" IS '电话号码';
COMMENT ON COLUMN "jc_user"."latesttime" IS '最后一次登录的时间';
COMMENT ON COLUMN "jc_user"."failnum" IS '密码错误次数';
COMMENT ON COLUMN "jc_user"."create_time" IS '注册时间';
COMMENT ON COLUMN "jc_user"."login_times" IS '登录次数';
COMMENT ON COLUMN "jc_user"."pass_change_time" IS '密码修改时间';
COMMENT ON COLUMN "jc_user"."lock" IS '是否锁定';
COMMENT ON COLUMN "jc_user"."token" IS 'token验证';
COMMENT ON COLUMN "jc_user"."token_time" IS 'token产生时间';
COMMENT ON COLUMN "jc_user"."valid" IS '用户有效性 1--有效 0--无效';
COMMENT ON COLUMN "jc_user"."ucmed_id" IS 'UCMEDID';

-- ----------------------------
-- Table structure for jc_user_account
-- ----------------------------
DROP TABLE IF EXISTS "jc_user_account";
CREATE TABLE "jc_user_account" (
  "id" int4 NOT NULL DEFAULT nextval('jc_user_account_id_seq'::regclass),
  "user_id" varchar(50) COLLATE "pg_catalog"."default",
  "account_id" varchar(50) COLLATE "pg_catalog"."default",
  "third_party_type" char(3) COLLATE "pg_catalog"."default",
  "create_time" timestamp(6),
  "create_by" varchar(50) COLLATE "pg_catalog"."default",
  "update_time" timestamp(6),
  "update_by" varchar(50) COLLATE "pg_catalog"."default",
  "deletion" char(1) COLLATE "pg_catalog"."default" DEFAULT 0,
  "role_name" varchar(50) COLLATE "pg_catalog"."default",
  "open_id" varchar(50) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "jc_user_account"."deletion" IS '删除标记（0 - 未删除，1 - 已删除）';
COMMENT ON COLUMN "jc_user_account"."open_id" IS '用户中心生成的open_id';

-- ----------------------------
-- Table structure for jc_user_info
-- ----------------------------
DROP TABLE IF EXISTS "jc_user_info";
CREATE TABLE "jc_user_info" (
  "t_id" int8 NOT NULL DEFAULT nextval('jc_user_info_t_id_seq'::regclass),
  "user_id" varchar(32) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying,
  "the_name" varchar(50) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "sex" varchar(10) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "birthday" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "card_type" varchar(20) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "card_id" varchar COLLATE "pg_catalog"."default",
  "medicare" varchar(1) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "medicare_id" varchar COLLATE "pg_catalog"."default",
  "city_id" varchar(6) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "address" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "w_chat" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "mobile" varchar COLLATE "pg_catalog"."default",
  "e_mail" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "source_ids" varchar(250) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "create_time" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "update_time" varchar(100) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "ucmed_id" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "family_phone" varchar(255) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "age" int4,
  "medicare_expense_types" char(3) COLLATE "pg_catalog"."default",
  "emergency_contact_type" char(3) COLLATE "pg_catalog"."default",
  "emergency_contact_name" varchar(30) COLLATE "pg_catalog"."default",
  "emergency_contact_number" varchar(3) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "jc_user_info"."t_id" IS '序号';
COMMENT ON COLUMN "jc_user_info"."user_id" IS '登录名';
COMMENT ON COLUMN "jc_user_info"."the_name" IS '姓名';
COMMENT ON COLUMN "jc_user_info"."sex" IS '性别';
COMMENT ON COLUMN "jc_user_info"."card_type" IS '证件类型';
COMMENT ON COLUMN "jc_user_info"."card_id" IS '证件号码';
COMMENT ON COLUMN "jc_user_info"."medicare" IS '是否有医保';
COMMENT ON COLUMN "jc_user_info"."medicare_id" IS '医保卡号';
COMMENT ON COLUMN "jc_user_info"."city_id" IS '区县代码';
COMMENT ON COLUMN "jc_user_info"."address" IS '居住地址';
COMMENT ON COLUMN "jc_user_info"."w_chat" IS '微信号';
COMMENT ON COLUMN "jc_user_info"."mobile" IS '手机号';
COMMENT ON COLUMN "jc_user_info"."e_mail" IS '电子邮箱';
COMMENT ON COLUMN "jc_user_info"."source_ids" IS '数据来源，如医链，医联体';
COMMENT ON COLUMN "jc_user_info"."create_time" IS '创建时间';
COMMENT ON COLUMN "jc_user_info"."update_time" IS '修改时间';
COMMENT ON COLUMN "jc_user_info"."medicare_expense_types" IS '医疗报销类型（1-社会基本医疗保险，2-商业医疗保险，3-大病统筹，4-新型农村合作医疗，5-城镇居民基本医疗保险，6-公费医疗，7-其他）';
COMMENT ON COLUMN "jc_user_info"."emergency_contact_type" IS '紧急联系人类别（1-配偶电话、2-监护人电话、3-家庭电话、4-工作单位电话、5-居委会电话、6-其他）';
COMMENT ON COLUMN "jc_user_info"."emergency_contact_name" IS '联系人姓名';
COMMENT ON COLUMN "jc_user_info"."emergency_contact_number" IS '紧急联系号码';
COMMENT ON TABLE "jc_user_info" IS '用户详细信息表';

-- ----------------------------
-- Table structure for jc_user_log
-- ----------------------------
DROP TABLE IF EXISTS "jc_user_log";
CREATE TABLE "jc_user_log" (
  "logid" int4 NOT NULL DEFAULT nextval('jc_user_log_logid_seq'::regclass),
  "user_id" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "loginfo" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "logtime" varchar(30) COLLATE "pg_catalog"."default" NOT NULL
)
;
COMMENT ON COLUMN "jc_user_log"."logid" IS '日志序号';
COMMENT ON COLUMN "jc_user_log"."user_id" IS '用户名或手机号';
COMMENT ON COLUMN "jc_user_log"."loginfo" IS '日志详细信息';
COMMENT ON COLUMN "jc_user_log"."logtime" IS '日志时间';

-- ----------------------------
-- Table structure for jc_user_patient
-- ----------------------------
DROP TABLE IF EXISTS "jc_user_patient";
CREATE TABLE "jc_user_patient" (
  "patient_id" int4 NOT NULL DEFAULT nextval('jc_user_patient_patient_id_seq1'::regclass),
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "patient_name" varchar(50) COLLATE "pg_catalog"."default",
  "patient_sex" varchar(10) COLLATE "pg_catalog"."default",
  "patient_birthday" varchar(100) COLLATE "pg_catalog"."default",
  "patient_card_type" varchar(255) COLLATE "pg_catalog"."default",
  "patient_card_id" varchar(255) COLLATE "pg_catalog"."default",
  "patient_medicare" varchar(1) COLLATE "pg_catalog"."default",
  "patient_medicare_id" varchar(255) COLLATE "pg_catalog"."default",
  "patient_city_id" varchar(100) COLLATE "pg_catalog"."default",
  "patient_address" varchar(255) COLLATE "pg_catalog"."default",
  "patient_w_chat" varchar(100) COLLATE "pg_catalog"."default",
  "patient_mobile" varchar(255) COLLATE "pg_catalog"."default",
  "patient_e_mail" varchar(100) COLLATE "pg_catalog"."default",
  "create_time" varchar(100) COLLATE "pg_catalog"."default",
  "update_time" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "jc_user_patient"."patient_id" IS '序号';
COMMENT ON COLUMN "jc_user_patient"."user_id" IS '用户id';
COMMENT ON COLUMN "jc_user_patient"."patient_name" IS '就诊人姓名';
COMMENT ON COLUMN "jc_user_patient"."patient_sex" IS '就诊人性别';
COMMENT ON COLUMN "jc_user_patient"."patient_birthday" IS '就诊人出生日期';
COMMENT ON COLUMN "jc_user_patient"."patient_card_type" IS '就诊人证件类型';
COMMENT ON COLUMN "jc_user_patient"."patient_card_id" IS '就诊人证件号码';
COMMENT ON COLUMN "jc_user_patient"."patient_medicare" IS '是否有医保';
COMMENT ON COLUMN "jc_user_patient"."patient_medicare_id" IS '医保卡号';
COMMENT ON COLUMN "jc_user_patient"."patient_address" IS '就诊人居住地址';
COMMENT ON COLUMN "jc_user_patient"."patient_w_chat" IS '就诊人微信号';
COMMENT ON COLUMN "jc_user_patient"."patient_mobile" IS '就诊人手机号';
COMMENT ON COLUMN "jc_user_patient"."patient_e_mail" IS '就诊人电子邮箱';
COMMENT ON COLUMN "jc_user_patient"."create_time" IS '创建时间';
COMMENT ON COLUMN "jc_user_patient"."update_time" IS '修改时间';
COMMENT ON TABLE "jc_user_patient" IS '就诊人信息表';

-- ----------------------------
-- Table structure for jc_user_push
-- ----------------------------
DROP TABLE IF EXISTS "jc_user_push";
CREATE TABLE "jc_user_push" (
  "user_push_id" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "open_id" varchar(40) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "push_id" varchar(40) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "is_push" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
  "is_sound" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
  "is_vibrate" char(1) COLLATE "pg_catalog"."default" DEFAULT '1'::bpchar,
  "is_disturb" char(1) COLLATE "pg_catalog"."default" DEFAULT '0'::bpchar,
  "app_code" int4 NOT NULL,
  "createdby" varchar(40) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "createdon" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "modifiedby" varchar(40) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "modifiedon" varchar COLLATE "pg_catalog"."default" NOT NULL,
  "deletion_state" char(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '0'::bpchar,
  "description" varchar(500) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;
COMMENT ON COLUMN "jc_user_push"."is_push" IS '是否开启提醒消息 0:不是;1:是';
COMMENT ON COLUMN "jc_user_push"."is_sound" IS '是否开启声音 0:不是;1:是';
COMMENT ON COLUMN "jc_user_push"."is_vibrate" IS '是否开启振动 0:不是;1:是';
COMMENT ON COLUMN "jc_user_push"."is_disturb" IS '是否开启免打扰 0:不是;1:是';
COMMENT ON COLUMN "jc_user_push"."deletion_state" IS '删除状态';

-- ----------------------------
-- Table structure for jc_user_third_party
-- ----------------------------
DROP TABLE IF EXISTS "jc_user_third_party";
CREATE TABLE "jc_user_third_party" (
  "id" int4 NOT NULL DEFAULT nextval('jc_user_third_party_id_seq'::regclass),
  "user_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "open_id" varchar(50) COLLATE "pg_catalog"."default" NOT NULL,
  "third_party_type" char(2) COLLATE "pg_catalog"."default",
  "create_time" timestamp(0),
  "create_by" varchar(50) COLLATE "pg_catalog"."default",
  "update_time" timestamp(0),
  "update_by" varchar(50) COLLATE "pg_catalog"."default",
  "deletion" char(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 0,
  "description" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "jc_user_third_party"."id" IS '序号';
COMMENT ON COLUMN "jc_user_third_party"."user_id" IS '用户ID';
COMMENT ON COLUMN "jc_user_third_party"."open_id" IS 'openId';
COMMENT ON COLUMN "jc_user_third_party"."third_party_type" IS '第三方类型（1-微信，2-企业微信，3-支付宝）';
COMMENT ON COLUMN "jc_user_third_party"."deletion" IS '删除标记（0 - 未删除，1 - 已删除）';

-- ----------------------------
-- Table structure for jc_user_wechat
-- ----------------------------
DROP TABLE IF EXISTS "jc_user_wechat";
CREATE TABLE "jc_user_wechat" (
  "openid" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for login
-- ----------------------------
DROP TABLE IF EXISTS "login";
CREATE TABLE "login" (
  "t_id" int4 NOT NULL DEFAULT nextval('login_t_id_seq'::regclass),
  "login_time" timestamp(6),
  "app_id" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "user_name" varchar(255) COLLATE "pg_catalog"."default",
  "identity_number" varchar(255) COLLATE "pg_catalog"."default",
  "address" varchar(200) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "login"."t_id" IS '序号';
COMMENT ON COLUMN "login"."login_time" IS '登陆时间';
COMMENT ON COLUMN "login"."app_id" IS '应用ID';
COMMENT ON COLUMN "login"."user_id" IS '用户ID';
COMMENT ON COLUMN "login"."user_name" IS '用户名';
COMMENT ON COLUMN "login"."identity_number" IS '身份证号';
COMMENT ON COLUMN "login"."address" IS '居住地';
COMMENT ON COLUMN "login"."ret_code" IS '返回码';
COMMENT ON COLUMN "login"."ret_info" IS '返回信息';
COMMENT ON TABLE "login" IS '登陆表';

-- ----------------------------
-- Table structure for logsearch_background
-- ----------------------------
DROP TABLE IF EXISTS "logsearch_background";
CREATE TABLE "logsearch_background" (
  "t_id" int4 NOT NULL DEFAULT nextval('logsearch_background_t_id_seq'::regclass),
  "background" varchar(255) COLLATE "pg_catalog"."default",
  "indexname" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "logsearch_background"."background" IS '后台名称';
COMMENT ON COLUMN "logsearch_background"."indexname" IS 'elasticsearch index字段名';
COMMENT ON TABLE "logsearch_background" IS '日志搜索后台列表';

-- ----------------------------
-- Table structure for payment
-- ----------------------------
DROP TABLE IF EXISTS "payment";
CREATE TABLE "payment" (
  "t_id" int4 NOT NULL DEFAULT nextval('payment_t_id_seq'::regclass),
  "flowid" varchar(255) COLLATE "pg_catalog"."default",
  "app_id" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(255) COLLATE "pg_catalog"."default",
  "dept_name" varchar(255) COLLATE "pg_catalog"."default",
  "dept_code" varchar(255) COLLATE "pg_catalog"."default",
  "special" varchar(255) COLLATE "pg_catalog"."default",
  "doc_name" varchar(255) COLLATE "pg_catalog"."default",
  "doc_code" varchar(255) COLLATE "pg_catalog"."default",
  "treat_cardno" varchar(255) COLLATE "pg_catalog"."default",
  "trade_type" varchar(255) COLLATE "pg_catalog"."default",
  "patient_type" varchar(255) COLLATE "pg_catalog"."default",
  "trade_subject" varchar(255) COLLATE "pg_catalog"."default",
  "item_type" varchar(255) COLLATE "pg_catalog"."default",
  "item_no" varchar(255) COLLATE "pg_catalog"."default",
  "expense" numeric(10,2),
  "update_time" timestamp(6),
  "create_time" timestamp(6),
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "payment"."t_id" IS '序号';
COMMENT ON COLUMN "payment"."flowid" IS '支付流水号';
COMMENT ON COLUMN "payment"."app_id" IS '应用ID';
COMMENT ON COLUMN "payment"."user_id" IS '用户ID';
COMMENT ON COLUMN "payment"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "payment"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "payment"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "payment"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "payment"."dept_name" IS '科室名称';
COMMENT ON COLUMN "payment"."dept_code" IS '科室代码';
COMMENT ON COLUMN "payment"."special" IS '专科';
COMMENT ON COLUMN "payment"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "payment"."doc_code" IS '医生工号';
COMMENT ON COLUMN "payment"."treat_cardno" IS '就诊卡号';
COMMENT ON COLUMN "payment"."trade_type" IS '支付方式';
COMMENT ON COLUMN "payment"."patient_type" IS '患者类型';
COMMENT ON COLUMN "payment"."trade_subject" IS '支付项目';
COMMENT ON COLUMN "payment"."item_type" IS '项目类别';
COMMENT ON COLUMN "payment"."item_no" IS '项目单号';
COMMENT ON COLUMN "payment"."expense" IS '支付金额';
COMMENT ON COLUMN "payment"."update_time" IS '更新时间';
COMMENT ON COLUMN "payment"."create_time" IS '产生时间';
COMMENT ON COLUMN "payment"."ret_code" IS '返回码';
COMMENT ON COLUMN "payment"."ret_info" IS '返回信息';
COMMENT ON TABLE "payment" IS '支付表';

-- ----------------------------
-- Table structure for payment_cancel
-- ----------------------------
DROP TABLE IF EXISTS "payment_cancel";
CREATE TABLE "payment_cancel" (
  "t_id" int4 NOT NULL DEFAULT nextval('payment_cancel_t_id_seq'::regclass),
  "cancel_time" timestamp(6),
  "flowid" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "payment_cancel"."t_id" IS '序号';
COMMENT ON COLUMN "payment_cancel"."cancel_time" IS '取消时间';
COMMENT ON COLUMN "payment_cancel"."flowid" IS '支付流水号';
COMMENT ON COLUMN "payment_cancel"."app_id" IS '应用ID';
COMMENT ON COLUMN "payment_cancel"."user_id" IS '用户ID';
COMMENT ON COLUMN "payment_cancel"."ret_code" IS '返回码';
COMMENT ON COLUMN "payment_cancel"."ret_info" IS '返回信息';
COMMENT ON TABLE "payment_cancel" IS '支付取消表';

-- ----------------------------
-- Table structure for payment_details
-- ----------------------------
DROP TABLE IF EXISTS "payment_details";
CREATE TABLE "payment_details" (
  "t_id" int4 NOT NULL DEFAULT nextval('payment_details_t_id_seq'::regclass),
  "flowid" varchar(255) COLLATE "pg_catalog"."default",
  "app_id" varchar(255) COLLATE "pg_catalog"."default",
  "item_code" varchar(255) COLLATE "pg_catalog"."default",
  "item_name" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "exec_dept" varchar(255) COLLATE "pg_catalog"."default",
  "price" numeric(8,2),
  "count" int4
)
;
COMMENT ON COLUMN "payment_details"."t_id" IS '序号';
COMMENT ON COLUMN "payment_details"."flowid" IS '支付流水号';
COMMENT ON COLUMN "payment_details"."app_id" IS '应用ID';
COMMENT ON COLUMN "payment_details"."item_code" IS '项目号';
COMMENT ON COLUMN "payment_details"."item_name" IS '项目名称';
COMMENT ON COLUMN "payment_details"."user_id" IS '用户ID';
COMMENT ON COLUMN "payment_details"."exec_dept" IS '执行科室';
COMMENT ON COLUMN "payment_details"."price" IS '单价';
COMMENT ON COLUMN "payment_details"."count" IS '数量';
COMMENT ON TABLE "payment_details" IS '支付详情表';

-- ----------------------------
-- Table structure for payment_refund
-- ----------------------------
DROP TABLE IF EXISTS "payment_refund";
CREATE TABLE "payment_refund" (
  "t_id" int4 NOT NULL DEFAULT nextval('payment_refund_t_id_seq'::regclass),
  "refund_time" timestamp(6),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "flowid" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "refund_amount" numeric(8,2),
  "refund_reason" varchar(100) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "payment_refund"."t_id" IS '序号';
COMMENT ON COLUMN "payment_refund"."refund_time" IS '退款时间';
COMMENT ON COLUMN "payment_refund"."app_id" IS '应用ID';
COMMENT ON COLUMN "payment_refund"."flowid" IS '支付流水号';
COMMENT ON COLUMN "payment_refund"."user_id" IS '用户ID';
COMMENT ON COLUMN "payment_refund"."refund_amount" IS '退款金额';
COMMENT ON COLUMN "payment_refund"."refund_reason" IS '退款原因';
COMMENT ON COLUMN "payment_refund"."ret_code" IS '返回码';
COMMENT ON COLUMN "payment_refund"."ret_info" IS '返回信息';
COMMENT ON TABLE "payment_refund" IS '支付退款表';

-- ----------------------------
-- Table structure for reservation
-- ----------------------------
DROP TABLE IF EXISTS "reservation";
CREATE TABLE "reservation" (
  "t_id" int4 NOT NULL DEFAULT nextval('reservation_t_id_seq'::regclass),
  "opt_time" timestamp(6),
  "rsv_time" timestamp(6),
  "app_id" varchar(255) COLLATE "pg_catalog"."default",
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(255) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(255) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(255) COLLATE "pg_catalog"."default",
  "dept_name" varchar(255) COLLATE "pg_catalog"."default",
  "dept_code" varchar(255) COLLATE "pg_catalog"."default",
  "special" varchar(255) COLLATE "pg_catalog"."default",
  "doc_name" varchar(255) COLLATE "pg_catalog"."default",
  "doc_code" varchar(255) COLLATE "pg_catalog"."default",
  "rsv_no" varchar(255) COLLATE "pg_catalog"."default",
  "rsv_type" varchar(255) COLLATE "pg_catalog"."default",
  "rsv_channel" varchar(255) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default",
  "type" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "reservation"."t_id" IS '序号';
COMMENT ON COLUMN "reservation"."opt_time" IS '操作时间';
COMMENT ON COLUMN "reservation"."rsv_time" IS '预约时间';
COMMENT ON COLUMN "reservation"."app_id" IS '应用ID';
COMMENT ON COLUMN "reservation"."user_id" IS '用户ID';
COMMENT ON COLUMN "reservation"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "reservation"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "reservation"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "reservation"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "reservation"."dept_name" IS '科室名称';
COMMENT ON COLUMN "reservation"."dept_code" IS '科室代码';
COMMENT ON COLUMN "reservation"."special" IS '专科';
COMMENT ON COLUMN "reservation"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "reservation"."doc_code" IS '医生工号';
COMMENT ON COLUMN "reservation"."rsv_no" IS '预约单号';
COMMENT ON COLUMN "reservation"."rsv_type" IS '预约类型';
COMMENT ON COLUMN "reservation"."rsv_channel" IS '预约渠道';
COMMENT ON COLUMN "reservation"."ret_code" IS '返回码';
COMMENT ON COLUMN "reservation"."ret_info" IS '返回信息';
COMMENT ON COLUMN "reservation"."type" IS '预约/挂号类型';
COMMENT ON TABLE "reservation" IS '挂号表';

-- ----------------------------
-- Table structure for reservation_cancel
-- ----------------------------
DROP TABLE IF EXISTS "reservation_cancel";
CREATE TABLE "reservation_cancel" (
  "t_id" int4 NOT NULL DEFAULT nextval('reservation_cancel_t_id_seq'::regclass),
  "cancel_time" timestamp(6),
  "rsv_no" varchar(30) COLLATE "pg_catalog"."default",
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "dept_name" varchar(30) COLLATE "pg_catalog"."default",
  "dept_code" varchar(30) COLLATE "pg_catalog"."default",
  "special" varchar(30) COLLATE "pg_catalog"."default",
  "doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "doc_code" varchar(30) COLLATE "pg_catalog"."default",
  "rsv_time" timestamp(6),
  "rsv_type" varchar(10) COLLATE "pg_catalog"."default",
  "rsv_channel" varchar(10) COLLATE "pg_catalog"."default",
  "cancel_reason" varchar(50) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "reservation_cancel"."t_id" IS '序号';
COMMENT ON COLUMN "reservation_cancel"."cancel_time" IS '取消时间';
COMMENT ON COLUMN "reservation_cancel"."rsv_no" IS '预约单号';
COMMENT ON COLUMN "reservation_cancel"."app_id" IS '应用ID';
COMMENT ON COLUMN "reservation_cancel"."user_id" IS '用户ID';
COMMENT ON COLUMN "reservation_cancel"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "reservation_cancel"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "reservation_cancel"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "reservation_cancel"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "reservation_cancel"."dept_name" IS '科室名称';
COMMENT ON COLUMN "reservation_cancel"."dept_code" IS '科室代码';
COMMENT ON COLUMN "reservation_cancel"."special" IS '专科';
COMMENT ON COLUMN "reservation_cancel"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "reservation_cancel"."doc_code" IS '医生工号';
COMMENT ON COLUMN "reservation_cancel"."rsv_time" IS '预约时间';
COMMENT ON COLUMN "reservation_cancel"."rsv_type" IS '预约类型';
COMMENT ON COLUMN "reservation_cancel"."rsv_channel" IS '预约渠道';
COMMENT ON COLUMN "reservation_cancel"."cancel_reason" IS '取消原因';
COMMENT ON COLUMN "reservation_cancel"."ret_code" IS '返回码';
COMMENT ON COLUMN "reservation_cancel"."ret_info" IS '返回信息';
COMMENT ON TABLE "reservation_cancel" IS '挂号取消表';

-- ----------------------------
-- Table structure for scy_user
-- ----------------------------
DROP TABLE IF EXISTS "scy_user";
CREATE TABLE "scy_user" (
  "scy_user_id" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "user_name" varchar(50) COLLATE "pg_catalog"."default",
  "password" varchar(300) COLLATE "pg_catalog"."default",
  "security_key" varchar(150) COLLATE "pg_catalog"."default",
  "vc_project_id" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "ucmed_user_id" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "trade_type" varchar(10) COLLATE "pg_catalog"."default",
  "createdby" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "createdon" timestamp(6) NOT NULL,
  "modifiedby" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "modifiedon" timestamp(6) NOT NULL,
  "deletion_state" char(1) COLLATE "pg_catalog"."default" NOT NULL,
  "description" varchar(500) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "scy_user"."user_name" IS '用户账号';
COMMENT ON COLUMN "scy_user"."password" IS '登录密码';
COMMENT ON COLUMN "scy_user"."security_key" IS '秘钥';
COMMENT ON COLUMN "scy_user"."trade_type" IS '注册用户的来源weixin zhifubao, app,unknow';
COMMENT ON COLUMN "scy_user"."deletion_state" IS '删除状态';

-- ----------------------------
-- Table structure for security_application
-- ----------------------------
DROP TABLE IF EXISTS "security_application";
CREATE TABLE "security_application" (
  "app_code" int4 NOT NULL,
  "app_name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "app_desc" varchar(255) COLLATE "pg_catalog"."default",
  "app_device" varchar(20) COLLATE "pg_catalog"."default" NOT NULL,
  "app_valid" int4 NOT NULL,
  "oper_date" varchar(30) COLLATE "pg_catalog"."default",
  "oper_user" varchar(30) COLLATE "pg_catalog"."default",
  "proj_code" int4,
  "proj_name" varchar COLLATE "pg_catalog"."default" DEFAULT 720,
  "token_life_cycle" int4 DEFAULT 720,
  "app_token" int4,
  "special_flag" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_application"."app_code" IS '应用程序代码';
COMMENT ON COLUMN "security_application"."app_name" IS '应用程序名称';
COMMENT ON COLUMN "security_application"."app_desc" IS '应用程序描述';
COMMENT ON COLUMN "security_application"."app_device" IS '应用程序设备类型';
COMMENT ON COLUMN "security_application"."app_valid" IS '是否有效';
COMMENT ON COLUMN "security_application"."oper_date" IS '操作时间';
COMMENT ON COLUMN "security_application"."oper_user" IS '操作人';
COMMENT ON COLUMN "security_application"."proj_code" IS '项目id';
COMMENT ON COLUMN "security_application"."proj_name" IS '项目名称';
COMMENT ON COLUMN "security_application"."token_life_cycle" IS 'token有效期（单位为小时）';
COMMENT ON COLUMN "security_application"."special_flag" IS '特殊标记';
COMMENT ON TABLE "security_application" IS '应用程序表';

-- ----------------------------
-- Table structure for security_application_secret
-- ----------------------------
DROP TABLE IF EXISTS "security_application_secret";
CREATE TABLE "security_application_secret" (
  "app_code" int4 NOT NULL,
  "app_secret" varchar(255) COLLATE "pg_catalog"."default",
  "create_time" timestamp(0),
  "create_by" varchar(100) COLLATE "pg_catalog"."default",
  "update_time" timestamp(0),
  "update_by" varchar(100) COLLATE "pg_catalog"."default",
  "deletion" char(1) COLLATE "pg_catalog"."default" DEFAULT 0,
  "description" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_application_secret"."deletion" IS '删除标记，0-未删除，1-已删除';

-- ----------------------------
-- Table structure for security_datatemplate
-- ----------------------------
DROP TABLE IF EXISTS "security_datatemplate";
CREATE TABLE "security_datatemplate" (
  "template_id" int4 NOT NULL DEFAULT nextval('security_datatemplate_template_id_seq'::regclass),
  "app_code" int4,
  "object_name" varchar(255) COLLATE "pg_catalog"."default",
  "object_type" varchar(255) COLLATE "pg_catalog"."default",
  "object_desc" varchar(255) COLLATE "pg_catalog"."default",
  "oper_date" timestamp(6),
  "oper_user" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_datatemplate"."template_id" IS '函数模板ID';
COMMENT ON COLUMN "security_datatemplate"."app_code" IS '应用程序ID';
COMMENT ON COLUMN "security_datatemplate"."object_name" IS '数据模板名称';
COMMENT ON COLUMN "security_datatemplate"."object_type" IS '数据模板类型';
COMMENT ON COLUMN "security_datatemplate"."object_desc" IS '模板描述';
COMMENT ON COLUMN "security_datatemplate"."oper_date" IS '操作时间';
COMMENT ON COLUMN "security_datatemplate"."oper_user" IS '操作人';
COMMENT ON TABLE "security_datatemplate" IS '数据权限模板定义';

-- ----------------------------
-- Table structure for security_datavalue
-- ----------------------------
DROP TABLE IF EXISTS "security_datavalue";
CREATE TABLE "security_datavalue" (
  "datavalue_id" int4 NOT NULL DEFAULT nextval('security_datavalue_datavalue_id_seq'::regclass),
  "app_code" int4,
  "template_id" int4,
  "value" text COLLATE "pg_catalog"."default",
  "datavalue_desc" varchar(255) COLLATE "pg_catalog"."default",
  "valid" int4,
  "oper_date" varchar(30) COLLATE "pg_catalog"."default",
  "oper_user" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_datavalue"."datavalue_id" IS '数据ID';
COMMENT ON COLUMN "security_datavalue"."app_code" IS '应用ID';
COMMENT ON COLUMN "security_datavalue"."template_id" IS '模板ID';
COMMENT ON COLUMN "security_datavalue"."value" IS 'where条件定义';
COMMENT ON COLUMN "security_datavalue"."datavalue_desc" IS 'where条件描述';
COMMENT ON COLUMN "security_datavalue"."valid" IS '有效性';
COMMENT ON COLUMN "security_datavalue"."oper_date" IS '操作时间';
COMMENT ON COLUMN "security_datavalue"."oper_user" IS '操作人';
COMMENT ON TABLE "security_datavalue" IS '数据表';

-- ----------------------------
-- Table structure for security_module
-- ----------------------------
DROP TABLE IF EXISTS "security_module";
CREATE TABLE "security_module" (
  "module_id" int4 NOT NULL DEFAULT nextval('security_module_module_id_seq'::regclass),
  "app_code" int4,
  "module_name" varchar(255) COLLATE "pg_catalog"."default",
  "module_type" varchar(255) COLLATE "pg_catalog"."default",
  "module_url" varchar(255) COLLATE "pg_catalog"."default",
  "module_desc" varchar(255) COLLATE "pg_catalog"."default",
  "parent_id" int4,
  "is_leaf" int4,
  "valid" int4,
  "oper_date" varchar(255) COLLATE "pg_catalog"."default",
  "oper_user" varchar(30) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_module"."module_id" IS '功能点ID';
COMMENT ON COLUMN "security_module"."app_code" IS '应用ID';
COMMENT ON COLUMN "security_module"."module_name" IS '功能点名称';
COMMENT ON COLUMN "security_module"."module_type" IS '功能点类型';
COMMENT ON COLUMN "security_module"."module_url" IS '功能点URL';
COMMENT ON COLUMN "security_module"."module_desc" IS '功能点描述';
COMMENT ON COLUMN "security_module"."parent_id" IS '父节点ID';
COMMENT ON COLUMN "security_module"."is_leaf" IS '是否叶子节点';
COMMENT ON COLUMN "security_module"."valid" IS '有效性';
COMMENT ON COLUMN "security_module"."oper_date" IS '操作时间';
COMMENT ON COLUMN "security_module"."oper_user" IS '操作人';
COMMENT ON TABLE "security_module" IS '功能点表';

-- ----------------------------
-- Table structure for security_module_type
-- ----------------------------
DROP TABLE IF EXISTS "security_module_type";
CREATE TABLE "security_module_type" (
  "t_id" int4 NOT NULL DEFAULT nextval('security_module_type_t_id_seq'::regclass),
  "app_code" int4 NOT NULL,
  "module_type" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "valid" int4,
  "oper_date" varchar(255) COLLATE "pg_catalog"."default",
  "oper_user" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_module_type"."t_id" IS 't_id';
COMMENT ON COLUMN "security_module_type"."app_code" IS '应用ID';
COMMENT ON COLUMN "security_module_type"."module_type" IS '功能类型';
COMMENT ON COLUMN "security_module_type"."valid" IS '有效性';
COMMENT ON COLUMN "security_module_type"."oper_date" IS '操作时间';
COMMENT ON COLUMN "security_module_type"."oper_user" IS '操作人';
COMMENT ON TABLE "security_module_type" IS '功能类型';

-- ----------------------------
-- Table structure for security_project
-- ----------------------------
DROP TABLE IF EXISTS "security_project";
CREATE TABLE "security_project" (
  "proj_code" int4 NOT NULL DEFAULT nextval('security_project_proj_code_seq'::regclass),
  "proj_name" varchar(255) COLLATE "pg_catalog"."default" NOT NULL,
  "oper_date" varchar(255) COLLATE "pg_catalog"."default",
  "oper_user" varchar(255) COLLATE "pg_catalog"."default",
  "dl_pwd" int2 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "security_project"."proj_code" IS '项目ID';
COMMENT ON COLUMN "security_project"."proj_name" IS '项目名称';
COMMENT ON COLUMN "security_project"."oper_date" IS '操作时间';
COMMENT ON COLUMN "security_project"."oper_user" IS '操作人';
COMMENT ON COLUMN "security_project"."dl_pwd" IS '是否独立密码';
COMMENT ON TABLE "security_project" IS '项目表';

-- ----------------------------
-- Table structure for security_role
-- ----------------------------
DROP TABLE IF EXISTS "security_role";
CREATE TABLE "security_role" (
  "role_id" int4 NOT NULL DEFAULT nextval('security_role_role_id_seq'::regclass),
  "app_code" int4,
  "role_name" varchar(32) COLLATE "pg_catalog"."default",
  "role_desc" varchar(255) COLLATE "pg_catalog"."default",
  "valid" varchar(4) COLLATE "pg_catalog"."default",
  "oper_user" varchar(20) COLLATE "pg_catalog"."default",
  "oper_date" varchar(30) COLLATE "pg_catalog"."default",
  "role_type" char(1) COLLATE "pg_catalog"."default" DEFAULT 1
)
;
COMMENT ON COLUMN "security_role"."role_id" IS '角色id';
COMMENT ON COLUMN "security_role"."app_code" IS '应用程序id';
COMMENT ON COLUMN "security_role"."role_name" IS '角色名称';
COMMENT ON COLUMN "security_role"."role_desc" IS '角色描述';
COMMENT ON COLUMN "security_role"."valid" IS '有效性';
COMMENT ON COLUMN "security_role"."oper_user" IS '操作人';
COMMENT ON COLUMN "security_role"."oper_date" IS '操作时间';
COMMENT ON COLUMN "security_role"."role_type" IS '角色类型';
COMMENT ON TABLE "security_role" IS '角色表';

-- ----------------------------
-- Table structure for security_role_module
-- ----------------------------
DROP TABLE IF EXISTS "security_role_module";
CREATE TABLE "security_role_module" (
  "role_id" int4 NOT NULL,
  "module_id" int4 NOT NULL,
  "valid" int4,
  "oper_user" varchar(255) COLLATE "pg_catalog"."default",
  "oper_date" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_role_module"."role_id" IS '角色ID';
COMMENT ON COLUMN "security_role_module"."module_id" IS '功能点ID';
COMMENT ON COLUMN "security_role_module"."valid" IS '有效性';
COMMENT ON COLUMN "security_role_module"."oper_user" IS '操作人';
COMMENT ON COLUMN "security_role_module"."oper_date" IS '操作时间';
COMMENT ON TABLE "security_role_module" IS '角色-功能点挂靠表';

-- ----------------------------
-- Table structure for security_template_value
-- ----------------------------
DROP TABLE IF EXISTS "security_template_value";
CREATE TABLE "security_template_value" (
  "datavalue_id" int8 NOT NULL,
  "role_id" int8 NOT NULL,
  "valid" int4,
  "oper_user" varchar(255) COLLATE "pg_catalog"."default",
  "oper_date" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_template_value"."datavalue_id" IS '函数授权ID';
COMMENT ON COLUMN "security_template_value"."role_id" IS '角色ID';
COMMENT ON COLUMN "security_template_value"."valid" IS '有效性';
COMMENT ON COLUMN "security_template_value"."oper_user" IS '操作人';
COMMENT ON COLUMN "security_template_value"."oper_date" IS '操作时间';
COMMENT ON TABLE "security_template_value" IS '模板与授权挂靠关系表';

-- ----------------------------
-- Table structure for security_user_app
-- ----------------------------
DROP TABLE IF EXISTS "security_user_app";
CREATE TABLE "security_user_app" (
  "t_id" int4 NOT NULL DEFAULT nextval('security_user_app_t_id_seq'::regclass),
  "app_code" int4 NOT NULL,
  "user_id" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "oper_date" varchar COLLATE "pg_catalog"."default",
  "open_id" varchar(40) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_user_app"."t_id" IS '序号';
COMMENT ON COLUMN "security_user_app"."app_code" IS '应用代码';
COMMENT ON COLUMN "security_user_app"."user_id" IS '用户ID';
COMMENT ON COLUMN "security_user_app"."oper_date" IS '操作时间';
COMMENT ON COLUMN "security_user_app"."open_id" IS 'user-app唯一标识';
COMMENT ON TABLE "security_user_app" IS '用户-应用挂靠表';

-- ----------------------------
-- Table structure for security_user_project
-- ----------------------------
DROP TABLE IF EXISTS "security_user_project";
CREATE TABLE "security_user_project" (
  "user_project_id" int4 NOT NULL DEFAULT nextval('security_user_project_user_project_id_seq'::regclass),
  "user_id" varchar(50) COLLATE "pg_catalog"."default",
  "proj_code" int4,
  "open_id" varchar(100) COLLATE "pg_catalog"."default",
  "create_time" varchar(20) COLLATE "pg_catalog"."default",
  "update_time" varchar(20) COLLATE "pg_catalog"."default",
  "deletion" char(5) COLLATE "pg_catalog"."default" DEFAULT 0,
  "create_by" varchar(50) COLLATE "pg_catalog"."default",
  "update_by" varchar(50) COLLATE "pg_catalog"."default",
  "description" varchar(1000) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "dl_pwd" varchar COLLATE "pg_catalog"."default",
  "securitykey" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_user_project"."user_project_id" IS '主键';
COMMENT ON COLUMN "security_user_project"."user_id" IS '用户ID';
COMMENT ON COLUMN "security_user_project"."proj_code" IS '项目code';
COMMENT ON COLUMN "security_user_project"."open_id" IS '用户在该项目的openId';
COMMENT ON COLUMN "security_user_project"."create_time" IS '创建时间';
COMMENT ON COLUMN "security_user_project"."update_time" IS '修改时间';
COMMENT ON COLUMN "security_user_project"."deletion" IS '删除标识';
COMMENT ON COLUMN "security_user_project"."dl_pwd" IS '独立密码';
COMMENT ON COLUMN "security_user_project"."securitykey" IS '盐';
COMMENT ON TABLE "security_user_project" IS '用户与项目关系表';

-- ----------------------------
-- Table structure for security_user_role
-- ----------------------------
DROP TABLE IF EXISTS "security_user_role";
CREATE TABLE "security_user_role" (
  "t_id" int4 NOT NULL DEFAULT nextval('security_user_role_t_id_seq'::regclass),
  "user_id" varchar(30) COLLATE "pg_catalog"."default" NOT NULL,
  "role_id" int4 NOT NULL,
  "valid" varchar(4) COLLATE "pg_catalog"."default",
  "oper_user" varchar(40) COLLATE "pg_catalog"."default",
  "oper_date" varchar COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "security_user_role"."t_id" IS '序号';
COMMENT ON COLUMN "security_user_role"."user_id" IS '用户id';
COMMENT ON COLUMN "security_user_role"."role_id" IS '角色id';
COMMENT ON COLUMN "security_user_role"."valid" IS '有效性';
COMMENT ON COLUMN "security_user_role"."oper_user" IS '操作人';
COMMENT ON COLUMN "security_user_role"."oper_date" IS '操作时间';
COMMENT ON TABLE "security_user_role" IS '用户-角色挂靠表';

-- ----------------------------
-- Table structure for sh_sms_history
-- ----------------------------
DROP TABLE IF EXISTS "sh_sms_history";
CREATE TABLE "sh_sms_history" (
  "sh_sms_history_id" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "vc_project_id" varchar(40) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "sender_scy_user_id" varchar(40) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "sender_mobile" varchar(40) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "receiver_mobile" varchar(40) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "template_key" varchar(40) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "content" varchar(2048) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "send_status" char(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '1'::bpchar,
  "createdby" varchar(40) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "createdon" timestamp(6) NOT NULL,
  "modifiedby" varchar(40) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "modifiedon" timestamp(6) NOT NULL,
  "deletion_state" char(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT '0'::bpchar,
  "description" varchar(500) COLLATE "pg_catalog"."default" DEFAULT ''::character varying
)
;
COMMENT ON COLUMN "sh_sms_history"."sh_sms_history_id" IS '主键，uuid';
COMMENT ON COLUMN "sh_sms_history"."vc_project_id" IS 'vcprojectId';
COMMENT ON COLUMN "sh_sms_history"."template_key" IS 'template_key';
COMMENT ON COLUMN "sh_sms_history"."send_status" IS '发送状态 1：成功，0：失败';
COMMENT ON COLUMN "sh_sms_history"."createdby" IS '新建者';
COMMENT ON COLUMN "sh_sms_history"."modifiedby" IS '修改者';
COMMENT ON COLUMN "sh_sms_history"."modifiedon" IS '修改日期';
COMMENT ON COLUMN "sh_sms_history"."deletion_state" IS '删除状态,0未删除，1已删除';
COMMENT ON COLUMN "sh_sms_history"."description" IS '备注';

-- ----------------------------
-- Table structure for sms_gateway
-- ----------------------------
DROP TABLE IF EXISTS "sms_gateway";
CREATE TABLE "sms_gateway" (
  "sms_gateway_id" varchar(40) COLLATE "pg_catalog"."default" NOT NULL,
  "protocol_type" varchar(12) COLLATE "pg_catalog"."default" DEFAULT 'http'::character varying,
  "platsms_token" varchar(40) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "url" varchar(128) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "sms_name" varchar(128) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "owner" varchar(128) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "createdby" varchar(40) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "createdon" timestamp(6) NOT NULL,
  "modifiedby" varchar(40) COLLATE "pg_catalog"."default" NOT NULL DEFAULT ''::character varying,
  "modifiedon" timestamp(6) NOT NULL,
  "deletion_state" char(1) COLLATE "pg_catalog"."default" NOT NULL DEFAULT 0,
  "description" varchar(500) COLLATE "pg_catalog"."default" DEFAULT ''::character varying,
  "app_code" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "seq" int4 NOT NULL DEFAULT 0
)
;
COMMENT ON COLUMN "sms_gateway"."sms_gateway_id" IS '主键';
COMMENT ON COLUMN "sms_gateway"."protocol_type" IS '协议类型';
COMMENT ON COLUMN "sms_gateway"."platsms_token" IS '短信平台token';
COMMENT ON COLUMN "sms_gateway"."url" IS '短信网关地址';
COMMENT ON COLUMN "sms_gateway"."sms_name" IS '短信厂商名称';
COMMENT ON COLUMN "sms_gateway"."owner" IS '短信拥有单位';
COMMENT ON COLUMN "sms_gateway"."createdby" IS '创建者';
COMMENT ON COLUMN "sms_gateway"."createdon" IS '创建时间';
COMMENT ON COLUMN "sms_gateway"."modifiedby" IS '修改者';
COMMENT ON COLUMN "sms_gateway"."modifiedon" IS '最后修改时间';
COMMENT ON COLUMN "sms_gateway"."deletion_state" IS '删除状态,0未删除，1已删除';
COMMENT ON COLUMN "sms_gateway"."seq" IS '排序，越大越排前';

-- ----------------------------
-- Table structure for statistic_adduser
-- ----------------------------
DROP TABLE IF EXISTS "statistic_adduser";
CREATE TABLE "statistic_adduser" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_adduser_t_id_seq'::regclass),
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "iphone" int8 DEFAULT 0,
  "android" int8 DEFAULT 0,
  "wechat" int8 DEFAULT 0,
  "alipay" int8 DEFAULT 0,
  "other" int8 DEFAULT 0,
  "total" int8 DEFAULT 0,
  "projectid" varchar(255) COLLATE "pg_catalog"."default",
  "web" int8 DEFAULT 0
)
;
COMMENT ON TABLE "statistic_adduser" IS '数据统计_注册量';

-- ----------------------------
-- Table structure for statistic_assay
-- ----------------------------
DROP TABLE IF EXISTS "statistic_assay";
CREATE TABLE "statistic_assay" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_assay_t_id_seq'::regclass),
  "projectid" varchar(255) COLLATE "pg_catalog"."default",
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "iphone" int8 DEFAULT 0,
  "android" int8 DEFAULT 0,
  "wechat" int8 DEFAULT 0,
  "alipay" int8 DEFAULT 0,
  "other" int8 DEFAULT 0,
  "total" int8 DEFAULT 0
)
;
COMMENT ON TABLE "statistic_assay" IS '数据统计_检验';

-- ----------------------------
-- Table structure for statistic_exam
-- ----------------------------
DROP TABLE IF EXISTS "statistic_exam";
CREATE TABLE "statistic_exam" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_exam_t_id_seq'::regclass),
  "projectid" varchar(255) COLLATE "pg_catalog"."default",
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "iphone" int8 DEFAULT 0,
  "android" int8 DEFAULT 0,
  "wechat" int8 DEFAULT 0,
  "alipay" int8 DEFAULT 0,
  "other" int8 DEFAULT 0,
  "total" int8 DEFAULT 0
)
;
COMMENT ON TABLE "statistic_exam" IS '数据统计_检查';

-- ----------------------------
-- Table structure for statistic_hospital
-- ----------------------------
DROP TABLE IF EXISTS "statistic_hospital";
CREATE TABLE "statistic_hospital" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_hospital_t_id_seq'::regclass),
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_id" varchar(255) COLLATE "pg_catalog"."default",
  "operationtype" varchar(30) COLLATE "pg_catalog"."default",
  "cnt" int8
)
;
COMMENT ON COLUMN "statistic_hospital"."t_id" IS '序号';
COMMENT ON COLUMN "statistic_hospital"."date" IS '日期';
COMMENT ON COLUMN "statistic_hospital"."hospital_id" IS '医院id';
COMMENT ON COLUMN "statistic_hospital"."operationtype" IS '操作类型(adduse,login,reservation,assay)';
COMMENT ON COLUMN "statistic_hospital"."cnt" IS '统计量';
COMMENT ON TABLE "statistic_hospital" IS '分医院统计数据量';

-- ----------------------------
-- Table structure for statistic_login
-- ----------------------------
DROP TABLE IF EXISTS "statistic_login";
CREATE TABLE "statistic_login" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_login_t_id_seq'::regclass),
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "iphone" int8 DEFAULT 0,
  "android" int8 DEFAULT 0,
  "wechat" int8 DEFAULT 0,
  "alipay" int8 DEFAULT 0,
  "other" int8 DEFAULT 0,
  "total" int8 DEFAULT 0,
  "projectid" varchar(255) COLLATE "pg_catalog"."default",
  "web" int8 DEFAULT 0
)
;
COMMENT ON COLUMN "statistic_login"."t_id" IS '序号';
COMMENT ON COLUMN "statistic_login"."date" IS '日期';
COMMENT ON COLUMN "statistic_login"."iphone" IS '渠道ios';
COMMENT ON COLUMN "statistic_login"."android" IS '渠道android';
COMMENT ON COLUMN "statistic_login"."wechat" IS '渠道微信';
COMMENT ON COLUMN "statistic_login"."alipay" IS '渠道支付宝';
COMMENT ON COLUMN "statistic_login"."other" IS '其他';
COMMENT ON COLUMN "statistic_login"."total" IS '总量';
COMMENT ON COLUMN "statistic_login"."web" IS '渠道web';
COMMENT ON TABLE "statistic_login" IS '数据统计_登录量';

-- ----------------------------
-- Table structure for statistic_payment
-- ----------------------------
DROP TABLE IF EXISTS "statistic_payment";
CREATE TABLE "statistic_payment" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_payment_t_id_seq'::regclass),
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "iphone" int8 DEFAULT 0,
  "android" int8 DEFAULT 0,
  "wechat" int8 DEFAULT 0,
  "alipay" int8 DEFAULT 0,
  "other" int8 DEFAULT 0,
  "total" int8 DEFAULT 0,
  "iphone_money" float8 DEFAULT 0,
  "android_money" float8 DEFAULT 0,
  "wechat_money" float8 DEFAULT 0,
  "alipay_money" float8 DEFAULT 0,
  "other_money" float8 DEFAULT 0,
  "total_money" float8 DEFAULT 0,
  "projectid" varchar(255) COLLATE "pg_catalog"."default",
  "type" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "statistic_payment"."type" IS '1.住院；2.诊间；3.挂号';
COMMENT ON TABLE "statistic_payment" IS '数据统计_支付';

-- ----------------------------
-- Table structure for statistic_payment_inhospital
-- ----------------------------
DROP TABLE IF EXISTS "statistic_payment_inhospital";
CREATE TABLE "statistic_payment_inhospital" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_payment_inhospital_t_id_seq'::regclass),
  "projectid" varchar(255) COLLATE "pg_catalog"."default",
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "iphone" int8,
  "android" int8,
  "wechat" int8,
  "alipay" int8,
  "other" int8,
  "total" int8,
  "iphone_money" money,
  "android_money" money,
  "wechat_money" money,
  "alipay_money" money,
  "other_money" money,
  "total_money" money
)
;
COMMENT ON TABLE "statistic_payment_inhospital" IS '数据统计_支付_住院';

-- ----------------------------
-- Table structure for statistic_payment_ondoctor
-- ----------------------------
DROP TABLE IF EXISTS "statistic_payment_ondoctor";
CREATE TABLE "statistic_payment_ondoctor" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_payment_ondoctor_t_id_seq'::regclass),
  "projectid" varchar(255) COLLATE "pg_catalog"."default",
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "iphone" int8,
  "android" int8,
  "wechat" int8,
  "alipay" int8,
  "other" int8,
  "total" int8,
  "iphone_money" money,
  "android_money" money,
  "wechat_money" money,
  "alipay_money" money,
  "other_money" money,
  "total_money" money
)
;
COMMENT ON COLUMN "statistic_payment_ondoctor"."t_id" IS '序号';
COMMENT ON COLUMN "statistic_payment_ondoctor"."date" IS '日期';
COMMENT ON COLUMN "statistic_payment_ondoctor"."iphone" IS 'iphone';
COMMENT ON COLUMN "statistic_payment_ondoctor"."android" IS 'android';
COMMENT ON COLUMN "statistic_payment_ondoctor"."wechat" IS '微信';
COMMENT ON COLUMN "statistic_payment_ondoctor"."alipay" IS '支付宝';
COMMENT ON COLUMN "statistic_payment_ondoctor"."other" IS '其他';
COMMENT ON COLUMN "statistic_payment_ondoctor"."total" IS '总计';
COMMENT ON COLUMN "statistic_payment_ondoctor"."iphone_money" IS 'iphone_金额';
COMMENT ON COLUMN "statistic_payment_ondoctor"."android_money" IS 'android_金额';
COMMENT ON COLUMN "statistic_payment_ondoctor"."wechat_money" IS '微信_金额';
COMMENT ON COLUMN "statistic_payment_ondoctor"."alipay_money" IS '支付宝_金额';
COMMENT ON COLUMN "statistic_payment_ondoctor"."other_money" IS '其他_金额';
COMMENT ON COLUMN "statistic_payment_ondoctor"."total_money" IS '总计_金额';
COMMENT ON TABLE "statistic_payment_ondoctor" IS '数据统计_支付_诊间';

-- ----------------------------
-- Table structure for statistic_payment_reservation
-- ----------------------------
DROP TABLE IF EXISTS "statistic_payment_reservation";
CREATE TABLE "statistic_payment_reservation" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_payment_reservation_t_id_seq'::regclass),
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "iphone" int8,
  "android" int8,
  "wechat" int8,
  "alipay" int8,
  "other" int8,
  "total" int8,
  "iphone_money" money,
  "android_money" money,
  "wechat_money" money,
  "alipay_money" money,
  "other_money" money,
  "total_money" money,
  "projectid" varchar(255) COLLATE "pg_catalog"."default"
)
;
COMMENT ON TABLE "statistic_payment_reservation" IS '数据统计_支付_挂号';

-- ----------------------------
-- Table structure for statistic_registration
-- ----------------------------
DROP TABLE IF EXISTS "statistic_registration";
CREATE TABLE "statistic_registration" (
  "t_id" int4 NOT NULL DEFAULT nextval('statistic_registration_t_id_seq'::regclass),
  "projectid" varchar(255) COLLATE "pg_catalog"."default",
  "date" varchar(30) COLLATE "pg_catalog"."default",
  "iphone" int4 DEFAULT 0,
  "android" int4 DEFAULT 0,
  "wechat" int4 DEFAULT 0,
  "alipay" int4 DEFAULT 0,
  "total" int4 DEFAULT 0,
  "expert_appointment" int4 DEFAULT 0,
  "common_appointment" int4 DEFAULT 0,
  "expert_registration" int4 DEFAULT 0,
  "common_registration" int4 DEFAULT 0
)
;
COMMENT ON COLUMN "statistic_registration"."projectid" IS '项目id';
COMMENT ON COLUMN "statistic_registration"."date" IS '日期';
COMMENT ON TABLE "statistic_registration" IS '挂号量统计';

-- ----------------------------
-- Table structure for temp
-- ----------------------------
DROP TABLE IF EXISTS "temp";
CREATE TABLE "temp" (
  "user_id" varchar(255) COLLATE "pg_catalog"."default",
  "password" varchar(255) COLLATE "pg_catalog"."default",
  "securitykey" varchar(255) COLLATE "pg_catalog"."default",
  "phone" varchar(255) COLLATE "pg_catalog"."default",
  "ucmde_id" varchar(255) COLLATE "pg_catalog"."default",
  "id_card" varchar(255) COLLATE "pg_catalog"."default",
  "id_card_yhzx" varchar(255) COLLATE "pg_catalog"."default",
  "open_id" varchar(255) COLLATE "pg_catalog"."default"
)
;

-- ----------------------------
-- Table structure for to_messagbox_table
-- ----------------------------
DROP TABLE IF EXISTS "to_messagbox_table";
CREATE TABLE "to_messagbox_table" (
  "job_id" int4 NOT NULL,
  "module" varchar(4) COLLATE "pg_catalog"."default" NOT NULL,
  "file_name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "col_seperator" varchar(8) COLLATE "pg_catalog"."default" NOT NULL,
  "table_name" varchar(32) COLLATE "pg_catalog"."default" NOT NULL,
  "sp_name" varchar(32) COLLATE "pg_catalog"."default",
  "mailto" varchar(64) COLLATE "pg_catalog"."default",
  "internal_time" int4 NOT NULL,
  "run_time" timestamp(6),
  "time_loop" char(1) COLLATE "pg_catalog"."default",
  "last_time" timestamp(6),
  "next_jobs" varchar(32) COLLATE "pg_catalog"."default",
  "valid" char(1) COLLATE "pg_catalog"."default" NOT NULL,
  "job_name" varchar(128) COLLATE "pg_catalog"."default",
  "job_position" varchar(4) COLLATE "pg_catalog"."default",
  "duty_tech" varchar(50) COLLATE "pg_catalog"."default",
  "duty_busi" varchar(50) COLLATE "pg_catalog"."default",
  "duty_email" varchar(250) COLLATE "pg_catalog"."default",
  "monitor_loop" int4,
  "hour_from" int4,
  "hour_to" int4,
  "email_loop" int4,
  "email_last_time" timestamp(6),
  "if_monitor" char(1) COLLATE "pg_catalog"."default",
  "send_num" int4,
  "column_desc" text COLLATE "pg_catalog"."default",
  "opername" varchar(50) COLLATE "pg_catalog"."default",
  "the_key" varchar(200) COLLATE "pg_catalog"."default",
  "xuhao" int4 NOT NULL DEFAULT nextval('to_messagbox_table_xuhao_seq'::regclass),
  "messageoffset" int8
)
;

-- ----------------------------
-- Table structure for user_recycle_bin
-- ----------------------------
DROP TABLE IF EXISTS "user_recycle_bin";
CREATE TABLE "user_recycle_bin" (
  "id" int4 NOT NULL DEFAULT nextval('user_recycle_bin_id_seq'::regclass),
  "user_id" varchar(32) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "phone" varchar(60) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "table_data" json NOT NULL,
  "create_time" varchar(60) COLLATE "pg_catalog"."default" DEFAULT NULL::character varying,
  "is_delete" varchar(1) COLLATE "pg_catalog"."default" DEFAULT 0,
  "table_name" varchar(30) COLLATE "pg_catalog"."default" NOT NULL DEFAULT NULL::character varying
)
;
COMMENT ON COLUMN "user_recycle_bin"."user_id" IS '用户id';
COMMENT ON COLUMN "user_recycle_bin"."phone" IS '手机号';
COMMENT ON COLUMN "user_recycle_bin"."table_data" IS '表数据';
COMMENT ON COLUMN "user_recycle_bin"."create_time" IS '创建时间';
COMMENT ON COLUMN "user_recycle_bin"."is_delete" IS '是否删除（0：未删除，1：已删除）';
COMMENT ON COLUMN "user_recycle_bin"."table_name" IS '表名';

-- ----------------------------
-- Table structure for whitelist_app
-- ----------------------------
DROP TABLE IF EXISTS "whitelist_app";
CREATE TABLE "whitelist_app" (
  "id" int2 NOT NULL DEFAULT nextval('whitelist_app_id_seq'::regclass),
  "app_code" int4 NOT NULL,
  "status" int2 NOT NULL DEFAULT 1
)
;
COMMENT ON COLUMN "whitelist_app"."status" IS '状态字段，0已失效状态，1有效状态';
COMMENT ON TABLE "whitelist_app" IS '应用白名单';

-- ----------------------------
-- Table structure for whitelist_app_user
-- ----------------------------
DROP TABLE IF EXISTS "whitelist_app_user";
CREATE TABLE "whitelist_app_user" (
  "id" int2 NOT NULL DEFAULT nextval('whitelist_app_user_id_seq'::regclass),
  "app_code" int4 NOT NULL,
  "user_id" varchar(128) COLLATE "pg_catalog"."default" NOT NULL,
  "status" int2 NOT NULL DEFAULT 1
)
;
COMMENT ON COLUMN "whitelist_app_user"."status" IS '状态字段，0已失效状态，1有效状态';

-- ----------------------------
-- Table structure for yilian_adduser
-- ----------------------------
DROP TABLE IF EXISTS "yilian_adduser";
CREATE TABLE "yilian_adduser" (
  "t_id" int4 NOT NULL DEFAULT nextval('yilian_adduser_t_id_seq'::regclass),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "reg_time" timestamp(6),
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "dept_name" varchar(30) COLLATE "pg_catalog"."default",
  "dept_code" varchar(30) COLLATE "pg_catalog"."default",
  "special" varchar(30) COLLATE "pg_catalog"."default",
  "doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "doc_code" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_name" varchar(20) COLLATE "pg_catalog"."default",
  "area" varchar(30) COLLATE "pg_catalog"."default",
  "user_source" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "yilian_adduser"."t_id" IS '序号';
COMMENT ON COLUMN "yilian_adduser"."app_id" IS '应用ID';
COMMENT ON COLUMN "yilian_adduser"."reg_time" IS '注册时间';
COMMENT ON COLUMN "yilian_adduser"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "yilian_adduser"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "yilian_adduser"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "yilian_adduser"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "yilian_adduser"."dept_name" IS '科室名称';
COMMENT ON COLUMN "yilian_adduser"."dept_code" IS '科室代码';
COMMENT ON COLUMN "yilian_adduser"."special" IS '专科';
COMMENT ON COLUMN "yilian_adduser"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "yilian_adduser"."doc_code" IS '医生工号';
COMMENT ON COLUMN "yilian_adduser"."user_id" IS '用户ID';
COMMENT ON COLUMN "yilian_adduser"."user_name" IS '用户名';
COMMENT ON COLUMN "yilian_adduser"."area" IS '地域';
COMMENT ON COLUMN "yilian_adduser"."user_source" IS '用户来源';
COMMENT ON COLUMN "yilian_adduser"."ret_code" IS '返回码';
COMMENT ON COLUMN "yilian_adduser"."ret_info" IS '返回信息';
COMMENT ON TABLE "yilian_adduser" IS '医链_注册表';

-- ----------------------------
-- Table structure for yilian_identify
-- ----------------------------
DROP TABLE IF EXISTS "yilian_identify";
CREATE TABLE "yilian_identify" (
  "t_id" int4 NOT NULL DEFAULT nextval('yilian_identify_t_id_seq'::regclass),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "identify_time" timestamp(6),
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "dept_name" varchar(30) COLLATE "pg_catalog"."default",
  "dept_code" varchar(30) COLLATE "pg_catalog"."default",
  "special" varchar(30) COLLATE "pg_catalog"."default",
  "doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "doc_code" varchar(30) COLLATE "pg_catalog"."default",
  "area" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "yilian_identify"."t_id" IS '序号';
COMMENT ON COLUMN "yilian_identify"."app_id" IS '应用ID';
COMMENT ON COLUMN "yilian_identify"."identify_time" IS '认证时间';
COMMENT ON COLUMN "yilian_identify"."user_id" IS '用户ID';
COMMENT ON COLUMN "yilian_identify"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "yilian_identify"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "yilian_identify"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "yilian_identify"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "yilian_identify"."dept_name" IS '科室名称';
COMMENT ON COLUMN "yilian_identify"."dept_code" IS '科室代码';
COMMENT ON COLUMN "yilian_identify"."special" IS '专科';
COMMENT ON COLUMN "yilian_identify"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "yilian_identify"."doc_code" IS '医生工号';
COMMENT ON COLUMN "yilian_identify"."area" IS '地域';
COMMENT ON COLUMN "yilian_identify"."ret_code" IS '返回码';
COMMENT ON COLUMN "yilian_identify"."ret_info" IS '返回信息';
COMMENT ON TABLE "yilian_identify" IS '医链_认证表';

-- ----------------------------
-- Table structure for yilian_login
-- ----------------------------
DROP TABLE IF EXISTS "yilian_login";
CREATE TABLE "yilian_login" (
  "t_id" int4 NOT NULL DEFAULT nextval('yilian_login_t_id_seq'::regclass),
  "login_time" timestamp(6),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "user_name" varchar(20) COLLATE "pg_catalog"."default",
  "hospital_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospital_code" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_name" varchar(30) COLLATE "pg_catalog"."default",
  "hospitaldist_code" varchar(30) COLLATE "pg_catalog"."default",
  "dept_name" varchar(30) COLLATE "pg_catalog"."default",
  "dept_code" varchar(30) COLLATE "pg_catalog"."default",
  "special" varchar(30) COLLATE "pg_catalog"."default",
  "doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "doc_code" varchar(30) COLLATE "pg_catalog"."default",
  "area" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "yilian_login"."t_id" IS '序号';
COMMENT ON COLUMN "yilian_login"."login_time" IS '登陆时间';
COMMENT ON COLUMN "yilian_login"."app_id" IS '应用ID';
COMMENT ON COLUMN "yilian_login"."user_id" IS '用户ID';
COMMENT ON COLUMN "yilian_login"."user_name" IS '用户名';
COMMENT ON COLUMN "yilian_login"."hospital_name" IS '医院名称';
COMMENT ON COLUMN "yilian_login"."hospital_code" IS '医院代码';
COMMENT ON COLUMN "yilian_login"."hospitaldist_name" IS '院区名称';
COMMENT ON COLUMN "yilian_login"."hospitaldist_code" IS '院区ID';
COMMENT ON COLUMN "yilian_login"."dept_name" IS '科室名称';
COMMENT ON COLUMN "yilian_login"."dept_code" IS '科室代码';
COMMENT ON COLUMN "yilian_login"."special" IS '专科';
COMMENT ON COLUMN "yilian_login"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "yilian_login"."doc_code" IS '医生工号';
COMMENT ON COLUMN "yilian_login"."area" IS '地域';
COMMENT ON COLUMN "yilian_login"."ret_code" IS '返回码';
COMMENT ON COLUMN "yilian_login"."ret_info" IS '返回信息';
COMMENT ON TABLE "yilian_login" IS '医链_登陆表';

-- ----------------------------
-- Table structure for yilian_logout
-- ----------------------------
DROP TABLE IF EXISTS "yilian_logout";
CREATE TABLE "yilian_logout" (
  "t_id" int4 NOT NULL DEFAULT nextval('yilian_logout_t_id_seq'::regclass),
  "app_id" varchar(30) COLLATE "pg_catalog"."default",
  "cancel_time" timestamp(6),
  "user_id" varchar(30) COLLATE "pg_catalog"."default",
  "doc_name" varchar(30) COLLATE "pg_catalog"."default",
  "ret_code" int4,
  "ret_info" varchar(100) COLLATE "pg_catalog"."default"
)
;
COMMENT ON COLUMN "yilian_logout"."t_id" IS '序号';
COMMENT ON COLUMN "yilian_logout"."app_id" IS '应用ID';
COMMENT ON COLUMN "yilian_logout"."cancel_time" IS '退出时间';
COMMENT ON COLUMN "yilian_logout"."user_id" IS '用户ID';
COMMENT ON COLUMN "yilian_logout"."doc_name" IS '医生姓名';
COMMENT ON COLUMN "yilian_logout"."ret_code" IS '返回码';
COMMENT ON COLUMN "yilian_logout"."ret_info" IS '返回信息';
COMMENT ON TABLE "yilian_logout" IS '医链_登陆退出表';

-- ----------------------------
-- Function structure for addrole
-- ----------------------------
DROP FUNCTION IF EXISTS "addrole"(varchar, varchar, timestamp);
CREATE OR REPLACE FUNCTION "addrole"(varchar, varchar, timestamp)
  RETURNS "pg_catalog"."int4" AS $BODY$
	declare i bigint; 
	begin
		i=(select count(*) from security_role where app_code=$1 and role_name=$2);
		if i=0 then insert into security_role(app_code,role_name,oper_date) values($1,$2,$3);
		end if;
		return i;
	end
$BODY$
  LANGUAGE plpgsql VOLATILE
  COST 100;

-- ----------------------------
-- View structure for view_security_user_project_statistics
-- ----------------------------
DROP VIEW IF EXISTS "view_security_user_project_statistics";
CREATE VIEW "view_security_user_project_statistics" AS  SELECT proj.user_id,
    proj.create_time,
    proj.proj_code,
    ( SELECT app.app_code
           FROM (security_user_app app
             JOIN security_application sp ON ((sp.app_code = app.app_code)))
          WHERE (((proj.user_id)::text = (app.user_id)::text) AND (sp.proj_code = proj.proj_code))
          ORDER BY app.oper_date
         OFFSET 0
         LIMIT 1) AS app_code
   FROM security_user_project proj;

-- ----------------------------
-- Alter sequences owned by
-- ----------------------------
ALTER SEQUENCE "adduser_t_id_seq"
OWNED BY "adduser"."t_id";
SELECT setval('"adduser_t_id_seq"', 75230, true);
ALTER SEQUENCE "appdownload_ios_t_id_seq"
OWNED BY "appdownload_ios"."t_id";
SELECT setval('"appdownload_ios_t_id_seq"', 56819, true);
ALTER SEQUENCE "appdownload_t_id_seq"
OWNED BY "appdownload"."t_id";
SELECT setval('"appdownload_t_id_seq"', 54428, true);
ALTER SEQUENCE "assay_details_t_id_seq"
OWNED BY "assay_details"."t_id";
SELECT setval('"assay_details_t_id_seq"', 82101, true);
ALTER SEQUENCE "assay_list_item_t_id_seq"
OWNED BY "assay_list_item"."t_id";
SELECT setval('"assay_list_item_t_id_seq"', 223319, true);
ALTER SEQUENCE "assay_list_t_id_seq"
OWNED BY "assay_list"."t_id";
SELECT setval('"assay_list_t_id_seq"', 97567, true);
ALTER SEQUENCE "blacklist_app_id_seq"
OWNED BY "blacklist_app"."id";
SELECT setval('"blacklist_app_id_seq"', 7, true);
ALTER SEQUENCE "blacklist_app_user_id_seq"
OWNED BY "blacklist_app_user"."id";
SELECT setval('"blacklist_app_user_id_seq"', 18, true);
ALTER SEQUENCE "bussinessoperation_projectid_t_id_seq"
OWNED BY "bussinessoperation_projectid"."t_id";
SELECT setval('"bussinessoperation_projectid_t_id_seq"', 11, true);
ALTER SEQUENCE "emr_inhospital_t_id_seq"
OWNED BY "emr_inhospital"."t_id";
SELECT setval('"emr_inhospital_t_id_seq"', 2, false);
ALTER SEQUENCE "emr_inhospitalfee_t_id_seq"
OWNED BY "emr_inhospitalfee"."t_id";
SELECT setval('"emr_inhospitalfee_t_id_seq"', 2, false);
ALTER SEQUENCE "emr_opsdetails_t_id_seq"
OWNED BY "emr_opsdetails"."t_id";
SELECT setval('"emr_opsdetails_t_id_seq"', 2, false);
ALTER SEQUENCE "emr_opsrecord_t_id_seq"
OWNED BY "emr_opsrecord"."t_id";
SELECT setval('"emr_opsrecord_t_id_seq"', 2, false);
ALTER SEQUENCE "emr_outhospitaldrug_t_id_seq"
OWNED BY "emr_outhospitaldrug"."t_id";
SELECT setval('"emr_outhospitaldrug_t_id_seq"', 2, false);
ALTER SEQUENCE "emr_outhospitalrecord_followup_t_id_seq"
OWNED BY "emr_outhospitalrecord_followup"."t_id";
SELECT setval('"emr_outhospitalrecord_followup_t_id_seq"', 2, false);
ALTER SEQUENCE "emr_outhospitalrecord_t_id_seq"
OWNED BY "emr_outhospitalrecord"."t_id";
SELECT setval('"emr_outhospitalrecord_t_id_seq"', 2, false);
ALTER SEQUENCE "emr_query_t_id_seq"
OWNED BY "emr_query"."t_id";
SELECT setval('"emr_query_t_id_seq"', 2, false);
ALTER SEQUENCE "emr_user_t_id_seq"
OWNED BY "emr_user"."t_id";
SELECT setval('"emr_user_t_id_seq"', 2, false);
ALTER SEQUENCE "exam_list_details_item_t_id_seq"
OWNED BY "exam_list_details_item"."t_id";
SELECT setval('"exam_list_details_item_t_id_seq"', 280, true);
ALTER SEQUENCE "exam_list_details_t_id_seq"
OWNED BY "exam_list_details"."t_id";
SELECT setval('"exam_list_details_t_id_seq"', 31851, true);
ALTER SEQUENCE "healthrecord_addassay_t_id_seq"
OWNED BY "healthrecord_addassay"."t_id";
SELECT setval('"healthrecord_addassay_t_id_seq"', 2, false);
ALTER SEQUENCE "healthrecord_adddiscomfort_t_id_seq"
OWNED BY "healthrecord_adddiscomfort"."t_id";
SELECT setval('"healthrecord_adddiscomfort_t_id_seq"', 2, false);
ALTER SEQUENCE "healthrecord_adddrug_t_id_seq"
OWNED BY "healthrecord_adddrug"."t_id";
SELECT setval('"healthrecord_adddrug_t_id_seq"', 2, false);
ALTER SEQUENCE "healthrecord_addmedicalhistory_t_id_seq"
OWNED BY "healthrecord_addmedicalhistory"."t_id";
SELECT setval('"healthrecord_addmedicalhistory_t_id_seq"', 2, false);
ALTER SEQUENCE "healthrecord_query_t_id_seq"
OWNED BY "healthrecord_query"."t_id";
SELECT setval('"healthrecord_query_t_id_seq"', 2, false);
ALTER SEQUENCE "healthrecord_user_t_id_seq"
OWNED BY "healthrecord_user"."t_id";
SELECT setval('"healthrecord_user_t_id_seq"', 2, false);
ALTER SEQUENCE "jc_common_section_t_id_seq"
OWNED BY "jc_common_section"."t_id";
SELECT setval('"jc_common_section_t_id_seq"', 2, false);
ALTER SEQUENCE "jc_doctor_info_t_id_seq"
OWNED BY "jc_doctor_info"."t_id";
SELECT setval('"jc_doctor_info_t_id_seq"', 96, true);
ALTER SEQUENCE "jc_hospital_section_t_id_seq"
OWNED BY "jc_hospital_section"."t_id";
SELECT setval('"jc_hospital_section_t_id_seq"', 2, false);
ALTER SEQUENCE "jc_hospital_t_id_seq"
OWNED BY "jc_hospital"."t_id";
SELECT setval('"jc_hospital_t_id_seq"', 2, false);
ALTER SEQUENCE "jc_platform_source_t_id_seq"
OWNED BY "jc_platform_source"."t_id";
SELECT setval('"jc_platform_source_t_id_seq"', 2, false);
ALTER SEQUENCE "jc_user_account_id_seq"
OWNED BY "jc_user_account"."id";
SELECT setval('"jc_user_account_id_seq"', 36221, true);
ALTER SEQUENCE "jc_user_info_t_id_seq"
OWNED BY "jc_user_info"."t_id";
SELECT setval('"jc_user_info_t_id_seq"', 6667206, true);
ALTER SEQUENCE "jc_user_log_logid_seq"
OWNED BY "jc_user_log"."logid";
SELECT setval('"jc_user_log_logid_seq"', 163151, true);
SELECT setval('"jc_user_patient_patient_id_seq"', 2, false);
ALTER SEQUENCE "jc_user_patient_patient_id_seq1"
OWNED BY "jc_user_patient"."patient_id";
SELECT setval('"jc_user_patient_patient_id_seq1"', 2, false);
ALTER SEQUENCE "jc_user_third_party_id_seq"
OWNED BY "jc_user_third_party"."id";
SELECT setval('"jc_user_third_party_id_seq"', 3550118, true);
ALTER SEQUENCE "jc_user_uid_seq"
OWNED BY "jc_user"."uid";
SELECT setval('"jc_user_uid_seq"', 6717254, true);
ALTER SEQUENCE "login_t_id_seq"
OWNED BY "login"."t_id";
SELECT setval('"login_t_id_seq"', 69745, true);
ALTER SEQUENCE "logsearch_background_t_id_seq"
OWNED BY "logsearch_background"."t_id";
SELECT setval('"logsearch_background_t_id_seq"', 179, true);
ALTER SEQUENCE "payment_cancel_t_id_seq"
OWNED BY "payment_cancel"."t_id";
SELECT setval('"payment_cancel_t_id_seq"', 7206, true);
ALTER SEQUENCE "payment_details_t_id_seq"
OWNED BY "payment_details"."t_id";
SELECT setval('"payment_details_t_id_seq"', 2, false);
ALTER SEQUENCE "payment_refund_t_id_seq"
OWNED BY "payment_refund"."t_id";
SELECT setval('"payment_refund_t_id_seq"', 2, false);
ALTER SEQUENCE "payment_t_id_seq"
OWNED BY "payment"."t_id";
SELECT setval('"payment_t_id_seq"', 5883, true);
ALTER SEQUENCE "reservation_cancel_t_id_seq"
OWNED BY "reservation_cancel"."t_id";
SELECT setval('"reservation_cancel_t_id_seq"', 3126, true);
ALTER SEQUENCE "reservation_t_id_seq"
OWNED BY "reservation"."t_id";
SELECT setval('"reservation_t_id_seq"', 126829, true);
ALTER SEQUENCE "security_datatemplate_template_id_seq"
OWNED BY "security_datatemplate"."template_id";
SELECT setval('"security_datatemplate_template_id_seq"', 6, true);
ALTER SEQUENCE "security_datavalue_datavalue_id_seq"
OWNED BY "security_datavalue"."datavalue_id";
SELECT setval('"security_datavalue_datavalue_id_seq"', 104, true);
ALTER SEQUENCE "security_module_module_id_seq"
OWNED BY "security_module"."module_id";
SELECT setval('"security_module_module_id_seq"', 10, true);
ALTER SEQUENCE "security_module_type_t_id_seq"
OWNED BY "security_module_type"."t_id";
SELECT setval('"security_module_type_t_id_seq"', 2, true);
ALTER SEQUENCE "security_project_proj_code_seq"
OWNED BY "security_project"."proj_code";
SELECT setval('"security_project_proj_code_seq"', 487, true);
ALTER SEQUENCE "security_role_role_id_seq"
OWNED BY "security_role"."role_id";
SELECT setval('"security_role_role_id_seq"', 1366, true);
ALTER SEQUENCE "security_user_app_t_id_seq"
OWNED BY "security_user_app"."t_id";
SELECT setval('"security_user_app_t_id_seq"', 7561564, true);
ALTER SEQUENCE "security_user_project_user_project_id_seq"
OWNED BY "security_user_project"."user_project_id";
SELECT setval('"security_user_project_user_project_id_seq"', 6647837, true);
ALTER SEQUENCE "security_user_role_t_id_seq"
OWNED BY "security_user_role"."t_id";
SELECT setval('"security_user_role_t_id_seq"', 7754227, true);
ALTER SEQUENCE "statistic_adduser_t_id_seq"
OWNED BY "statistic_adduser"."t_id";
SELECT setval('"statistic_adduser_t_id_seq"', 91420, true);
ALTER SEQUENCE "statistic_assay_t_id_seq"
OWNED BY "statistic_assay"."t_id";
SELECT setval('"statistic_assay_t_id_seq"', 1493, true);
ALTER SEQUENCE "statistic_exam_t_id_seq"
OWNED BY "statistic_exam"."t_id";
SELECT setval('"statistic_exam_t_id_seq"', 2178, true);
ALTER SEQUENCE "statistic_hospital_t_id_seq"
OWNED BY "statistic_hospital"."t_id";
SELECT setval('"statistic_hospital_t_id_seq"', 19776, true);
ALTER SEQUENCE "statistic_login_t_id_seq"
OWNED BY "statistic_login"."t_id";
SELECT setval('"statistic_login_t_id_seq"', 28111, true);
ALTER SEQUENCE "statistic_payment_inhospital_t_id_seq"
OWNED BY "statistic_payment_inhospital"."t_id";
SELECT setval('"statistic_payment_inhospital_t_id_seq"', 2, false);
ALTER SEQUENCE "statistic_payment_ondoctor_t_id_seq"
OWNED BY "statistic_payment_ondoctor"."t_id";
SELECT setval('"statistic_payment_ondoctor_t_id_seq"', 2, false);
ALTER SEQUENCE "statistic_payment_reservation_t_id_seq"
OWNED BY "statistic_payment_reservation"."t_id";
SELECT setval('"statistic_payment_reservation_t_id_seq"', 2, false);
ALTER SEQUENCE "statistic_payment_t_id_seq"
OWNED BY "statistic_payment"."t_id";
SELECT setval('"statistic_payment_t_id_seq"', 476, true);
ALTER SEQUENCE "statistic_registration_t_id_seq"
OWNED BY "statistic_registration"."t_id";
SELECT setval('"statistic_registration_t_id_seq"', 2259, true);
ALTER SEQUENCE "to_messagbox_table_xuhao_seq"
OWNED BY "to_messagbox_table"."xuhao";
SELECT setval('"to_messagbox_table_xuhao_seq"', 5889115, true);
SELECT setval('"user_recycle_bin_id_seq"', 1829, true);
ALTER SEQUENCE "user_transfer_ezpt_1228_id_seq"
OWNED BY "user_transfer_ezpt_1228"."id";
SELECT setval('"user_transfer_ezpt_1228_id_seq"', 2, false);
ALTER SEQUENCE "user_transfer_guangdong_0328_id_seq"
OWNED BY "user_transfer_guangdong_0328"."id";
SELECT setval('"user_transfer_guangdong_0328_id_seq"', 2, false);
ALTER SEQUENCE "user_transfer_jxpt_1204_id_seq"
OWNED BY "user_transfer_jxpt_1204"."id";
SELECT setval('"user_transfer_jxpt_1204_id_seq"', 79569, true);
ALTER SEQUENCE "user_transfer_ztt_0504_id_seq"
OWNED BY "user_transfer_ztt_0504"."id";
SELECT setval('"user_transfer_ztt_0504_id_seq"', 2, false);
ALTER SEQUENCE "whitelist_app_id_seq"
OWNED BY "whitelist_app"."id";
SELECT setval('"whitelist_app_id_seq"', 7, true);
ALTER SEQUENCE "whitelist_app_user_id_seq"
OWNED BY "whitelist_app_user"."id";
SELECT setval('"whitelist_app_user_id_seq"', 21, true);
ALTER SEQUENCE "yilian_adduser_t_id_seq"
OWNED BY "yilian_adduser"."t_id";
SELECT setval('"yilian_adduser_t_id_seq"', 2, false);
ALTER SEQUENCE "yilian_identify_t_id_seq"
OWNED BY "yilian_identify"."t_id";
SELECT setval('"yilian_identify_t_id_seq"', 2, false);
ALTER SEQUENCE "yilian_login_t_id_seq"
OWNED BY "yilian_login"."t_id";
SELECT setval('"yilian_login_t_id_seq"', 2, false);
ALTER SEQUENCE "yilian_logout_t_id_seq"
OWNED BY "yilian_logout"."t_id";
SELECT setval('"yilian_logout_t_id_seq"', 2, false);
SELECT setval('"“statistic_adduser_t_id_seq”"', 2, false);
SELECT setval('"“statistic_addusert_id_seq”"', 2, false);

-- ----------------------------
-- Primary Key structure for table adduser
-- ----------------------------
ALTER TABLE "adduser" ADD CONSTRAINT "pk_adduser" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table appdownload
-- ----------------------------
ALTER TABLE "appdownload" ADD CONSTRAINT "appdownload_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table appdownload_ios
-- ----------------------------
ALTER TABLE "appdownload_ios" ADD CONSTRAINT "appdownload_ios_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table appwebinfo
-- ----------------------------
ALTER TABLE "appwebinfo" ADD CONSTRAINT "appwebinfo_pkey" PRIMARY KEY ("webname");

-- ----------------------------
-- Primary Key structure for table assay_details
-- ----------------------------
ALTER TABLE "assay_details" ADD CONSTRAINT "pk_assay_details" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table assay_list
-- ----------------------------
ALTER TABLE "assay_list" ADD CONSTRAINT "pk_assay_list" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table assay_list_item
-- ----------------------------
ALTER TABLE "assay_list_item" ADD CONSTRAINT "pk_assay_list_item" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table blacklist_app
-- ----------------------------
ALTER TABLE "blacklist_app" ADD CONSTRAINT "blacklist_app_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table blacklist_app_user
-- ----------------------------
ALTER TABLE "blacklist_app_user" ADD CONSTRAINT "blacklist_app_user_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table bussinessoperation_projectid
-- ----------------------------
ALTER TABLE "bussinessoperation_projectid" ADD CONSTRAINT "bussinessoperation_projectid_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table dict_app_device
-- ----------------------------
ALTER TABLE "dict_app_device" ADD CONSTRAINT "dict_app_device_pkey" PRIMARY KEY ("app_device_id");

-- ----------------------------
-- Primary Key structure for table emr_inhospital
-- ----------------------------
ALTER TABLE "emr_inhospital" ADD CONSTRAINT "pk_emr_inhospital" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table emr_inhospitalfee
-- ----------------------------
ALTER TABLE "emr_inhospitalfee" ADD CONSTRAINT "pk_emr_inhospitalfee" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table emr_opsdetails
-- ----------------------------
ALTER TABLE "emr_opsdetails" ADD CONSTRAINT "pk_emr_opsdetails" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table emr_opsrecord
-- ----------------------------
ALTER TABLE "emr_opsrecord" ADD CONSTRAINT "pk_emr_opsrecord" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table emr_outhospitaldrug
-- ----------------------------
ALTER TABLE "emr_outhospitaldrug" ADD CONSTRAINT "pk_emr_outhospitaldrug" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table emr_outhospitalrecord
-- ----------------------------
ALTER TABLE "emr_outhospitalrecord" ADD CONSTRAINT "pk_emr_outhospitalrecord" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table emr_outhospitalrecord_followup
-- ----------------------------
ALTER TABLE "emr_outhospitalrecord_followup" ADD CONSTRAINT "pk_emr_outhospitalrecord_follo" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table emr_query
-- ----------------------------
ALTER TABLE "emr_query" ADD CONSTRAINT "pk_emr_query" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table emr_user
-- ----------------------------
ALTER TABLE "emr_user" ADD CONSTRAINT "pk_emr_user" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table exam_list_details
-- ----------------------------
ALTER TABLE "exam_list_details" ADD CONSTRAINT "pk_exam_list_details" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table exam_list_details_item
-- ----------------------------
ALTER TABLE "exam_list_details_item" ADD CONSTRAINT "pk_exam_list_details_item" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table healthrecord_addassay
-- ----------------------------
ALTER TABLE "healthrecord_addassay" ADD CONSTRAINT "pk_healthrecord_addassay" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table healthrecord_adddiscomfort
-- ----------------------------
ALTER TABLE "healthrecord_adddiscomfort" ADD CONSTRAINT "pk_healthrecord_adddiscomfort" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table healthrecord_adddrug
-- ----------------------------
ALTER TABLE "healthrecord_adddrug" ADD CONSTRAINT "pk_healthrecord_adddrug" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table healthrecord_addmedicalhistory
-- ----------------------------
ALTER TABLE "healthrecord_addmedicalhistory" ADD CONSTRAINT "pk_healthrecord_addmedicalhist" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table healthrecord_query
-- ----------------------------
ALTER TABLE "healthrecord_query" ADD CONSTRAINT "pk_healthrecord_query" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table healthrecord_user
-- ----------------------------
ALTER TABLE "healthrecord_user" ADD CONSTRAINT "pk_healthrecord_user" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table jc_common_section
-- ----------------------------
ALTER TABLE "jc_common_section" ADD CONSTRAINT "jc_common_section_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table jc_doctor_info
-- ----------------------------
ALTER TABLE "jc_doctor_info" ADD CONSTRAINT "jc_doctor_info_pkey" PRIMARY KEY ("user_id", "common_section_no");

-- ----------------------------
-- Primary Key structure for table jc_hospital
-- ----------------------------
ALTER TABLE "jc_hospital" ADD CONSTRAINT "jc_hospital_pkey" PRIMARY KEY ("ucmed_hospital_id");

-- ----------------------------
-- Primary Key structure for table jc_hospital_section
-- ----------------------------
ALTER TABLE "jc_hospital_section" ADD CONSTRAINT "jc_hospital_section_pkey" PRIMARY KEY ("ucmed_hospital_id", "section_no");

-- ----------------------------
-- Primary Key structure for table jc_platform_source
-- ----------------------------
ALTER TABLE "jc_platform_source" ADD CONSTRAINT "jc_platform_source_pkey" PRIMARY KEY ("source_id");

-- ----------------------------
-- Indexes structure for table jc_user
-- ----------------------------
CREATE INDEX "jc_user_phone_idx" ON "jc_user" USING btree (
  "phone" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "jc_user_uid_idx" ON "jc_user" USING hash (
  "uid" "pg_catalog"."int4_ops"
);
CREATE INDEX "jc_user_user_id_idx" ON "jc_user" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table jc_user
-- ----------------------------
ALTER TABLE "jc_user" ADD CONSTRAINT "jc_user_user_id_key" UNIQUE ("user_id");
ALTER TABLE "jc_user" ADD CONSTRAINT "jc_user_phone_key" UNIQUE ("phone");

-- ----------------------------
-- Primary Key structure for table jc_user
-- ----------------------------
ALTER TABLE "jc_user" ADD CONSTRAINT "jc_user_pkey" PRIMARY KEY ("uid");

-- ----------------------------
-- Primary Key structure for table jc_user_account
-- ----------------------------
ALTER TABLE "jc_user_account" ADD CONSTRAINT "jc_user_account_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Indexes structure for table jc_user_info
-- ----------------------------
CREATE INDEX "jc_user_info_card_id_idx" ON "jc_user_info" USING btree (
  "card_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "jc_user_info_t_id_idx" ON "jc_user_info" USING btree (
  "t_id" "pg_catalog"."int8_ops" ASC NULLS LAST
);
CREATE INDEX "jc_user_info_user_id_idx" ON "jc_user_info" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Uniques structure for table jc_user_info
-- ----------------------------
ALTER TABLE "jc_user_info" ADD CONSTRAINT "jc_user_info_user_id_key" UNIQUE ("user_id");

-- ----------------------------
-- Primary Key structure for table jc_user_info
-- ----------------------------
ALTER TABLE "jc_user_info" ADD CONSTRAINT "jc_user_info_pkey" PRIMARY KEY ("user_id");

-- ----------------------------
-- Primary Key structure for table jc_user_log
-- ----------------------------
ALTER TABLE "jc_user_log" ADD CONSTRAINT "jc_user_log_pkey" PRIMARY KEY ("logid");

-- ----------------------------
-- Primary Key structure for table jc_user_patient
-- ----------------------------
ALTER TABLE "jc_user_patient" ADD CONSTRAINT "jc_user_patient_pkey" PRIMARY KEY ("patient_id");

-- ----------------------------
-- Indexes structure for table jc_user_push
-- ----------------------------
CREATE INDEX "jc_user_push_open_id_idx" ON "jc_user_push" USING btree (
  "open_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "jc_user_push_user_push_id_idx" ON "jc_user_push" USING btree (
  "user_push_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table jc_user_push
-- ----------------------------
ALTER TABLE "jc_user_push" ADD CONSTRAINT "user_push_pkey" PRIMARY KEY ("user_push_id");

-- ----------------------------
-- Indexes structure for table jc_user_third_party
-- ----------------------------
CREATE INDEX "jc_user_third_party_create_by_idx" ON "jc_user_third_party" USING btree (
  "create_by" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "jc_user_third_party_open_id_idx" ON "jc_user_third_party" USING btree (
  "open_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "jc_user_third_party_user_id_create_by_idx" ON "jc_user_third_party" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "create_by" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "jc_user_third_party_user_id_idx" ON "jc_user_third_party" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table jc_user_third_party
-- ----------------------------
ALTER TABLE "jc_user_third_party" ADD CONSTRAINT "jc_user_third_party_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table jc_user_wechat
-- ----------------------------
ALTER TABLE "jc_user_wechat" ADD CONSTRAINT "jc_user_wechat_pkey" PRIMARY KEY ("openid");

-- ----------------------------
-- Primary Key structure for table login
-- ----------------------------
ALTER TABLE "login" ADD CONSTRAINT "pk_login" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table logsearch_background
-- ----------------------------
ALTER TABLE "logsearch_background" ADD CONSTRAINT "logsearch_background_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table payment
-- ----------------------------
ALTER TABLE "payment" ADD CONSTRAINT "pk_payment" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table payment_cancel
-- ----------------------------
ALTER TABLE "payment_cancel" ADD CONSTRAINT "pk_payment_cancel" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table payment_details
-- ----------------------------
ALTER TABLE "payment_details" ADD CONSTRAINT "pk_payment_details" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table payment_refund
-- ----------------------------
ALTER TABLE "payment_refund" ADD CONSTRAINT "pk_payment_refund" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table reservation
-- ----------------------------
ALTER TABLE "reservation" ADD CONSTRAINT "pk_reservation" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table reservation_cancel
-- ----------------------------
ALTER TABLE "reservation_cancel" ADD CONSTRAINT "pk_reservation_cancel" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table scy_user
-- ----------------------------
ALTER TABLE "scy_user" ADD CONSTRAINT "scy_user_pkey" PRIMARY KEY ("scy_user_id");

-- ----------------------------
-- Primary Key structure for table security_application
-- ----------------------------
ALTER TABLE "security_application" ADD CONSTRAINT "security_application_pkey" PRIMARY KEY ("app_code");

-- ----------------------------
-- Indexes structure for table security_application_secret
-- ----------------------------
CREATE INDEX "security_application_secret_app_code_idx" ON "security_application_secret" USING btree (
  "app_code" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table security_application_secret
-- ----------------------------
ALTER TABLE "security_application_secret" ADD CONSTRAINT "security_application_secret_pkey" PRIMARY KEY ("app_code");

-- ----------------------------
-- Primary Key structure for table security_datatemplate
-- ----------------------------
ALTER TABLE "security_datatemplate" ADD CONSTRAINT "security_datatemplate_pkey" PRIMARY KEY ("template_id");

-- ----------------------------
-- Primary Key structure for table security_datavalue
-- ----------------------------
ALTER TABLE "security_datavalue" ADD CONSTRAINT "security_datavalue_pkey" PRIMARY KEY ("datavalue_id");

-- ----------------------------
-- Primary Key structure for table security_module
-- ----------------------------
ALTER TABLE "security_module" ADD CONSTRAINT "security_module_pkey" PRIMARY KEY ("module_id");

-- ----------------------------
-- Primary Key structure for table security_module_type
-- ----------------------------
ALTER TABLE "security_module_type" ADD CONSTRAINT "security_module_type_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table security_project
-- ----------------------------
ALTER TABLE "security_project" ADD CONSTRAINT "security_project_pkey" PRIMARY KEY ("proj_code");

-- ----------------------------
-- Primary Key structure for table security_role
-- ----------------------------
ALTER TABLE "security_role" ADD CONSTRAINT "security_role_pkey" PRIMARY KEY ("role_id");

-- ----------------------------
-- Indexes structure for table security_role_module
-- ----------------------------
CREATE INDEX "security_role_module_role_id_module_id_idx" ON "security_role_module" USING btree (
  "role_id" "pg_catalog"."int4_ops" ASC NULLS LAST,
  "module_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table security_role_module
-- ----------------------------
ALTER TABLE "security_role_module" ADD CONSTRAINT "security_role_module_pkey" PRIMARY KEY ("role_id", "module_id");

-- ----------------------------
-- Primary Key structure for table security_template_value
-- ----------------------------
ALTER TABLE "security_template_value" ADD CONSTRAINT "security_template_value_pkey" PRIMARY KEY ("datavalue_id", "role_id");

-- ----------------------------
-- Indexes structure for table security_user_app
-- ----------------------------
CREATE INDEX "security_user_app_app_code_idx" ON "security_user_app" USING btree (
  "app_code" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_app_t_id_idx" ON "security_user_app" USING btree (
  "t_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_app_user_id_idx" ON "security_user_app" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table security_user_app
-- ----------------------------
ALTER TABLE "security_user_app" ADD CONSTRAINT "security_user_app_pkey" PRIMARY KEY ("app_code", "user_id");

-- ----------------------------
-- Indexes structure for table security_user_project
-- ----------------------------
CREATE INDEX "security_user_project_createtime_idx" ON "security_user_project" USING btree (
  "create_time" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_project_open_id_idx" ON "security_user_project" USING btree (
  "open_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_project_proj_id" ON "security_user_project" USING btree (
  "proj_code" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_project_user_id_idx" ON "security_user_project" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_project_user_id_proj_code_idx" ON "security_user_project" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "proj_code" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_project_user_project_id_idx" ON "security_user_project" USING btree (
  "user_project_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table security_user_project
-- ----------------------------
ALTER TABLE "security_user_project" ADD CONSTRAINT "security_user_project_pkey" PRIMARY KEY ("user_project_id");

-- ----------------------------
-- Indexes structure for table security_user_role
-- ----------------------------
CREATE INDEX "security_user_role_role_id_idx" ON "security_user_role" USING btree (
  "role_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_role_t_id_idx" ON "security_user_role" USING btree (
  "t_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_role_user_id_idx" ON "security_user_role" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST
);
CREATE INDEX "security_user_role_user_id_role_id_idx" ON "security_user_role" USING btree (
  "user_id" COLLATE "pg_catalog"."default" "pg_catalog"."text_ops" ASC NULLS LAST,
  "role_id" "pg_catalog"."int4_ops" ASC NULLS LAST
);

-- ----------------------------
-- Primary Key structure for table security_user_role
-- ----------------------------
ALTER TABLE "security_user_role" ADD CONSTRAINT "security_user_role_pkey" PRIMARY KEY ("user_id", "role_id");

-- ----------------------------
-- Primary Key structure for table sh_sms_history
-- ----------------------------
ALTER TABLE "sh_sms_history" ADD CONSTRAINT "sh_sms_history_pkey" PRIMARY KEY ("sh_sms_history_id");

-- ----------------------------
-- Primary Key structure for table sms_gateway
-- ----------------------------
ALTER TABLE "sms_gateway" ADD CONSTRAINT "sms_gateway_pkey" PRIMARY KEY ("sms_gateway_id");

-- ----------------------------
-- Primary Key structure for table statistic_adduser
-- ----------------------------
ALTER TABLE "statistic_adduser" ADD CONSTRAINT "statistic_adduser_pkey1" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table statistic_assay
-- ----------------------------
ALTER TABLE "statistic_assay" ADD CONSTRAINT "statistic_assay_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table statistic_exam
-- ----------------------------
ALTER TABLE "statistic_exam" ADD CONSTRAINT "statistic_exam_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table statistic_login
-- ----------------------------
ALTER TABLE "statistic_login" ADD CONSTRAINT "statistic_login_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table statistic_payment
-- ----------------------------
ALTER TABLE "statistic_payment" ADD CONSTRAINT "statistic_payment_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table statistic_payment_inhospital
-- ----------------------------
ALTER TABLE "statistic_payment_inhospital" ADD CONSTRAINT "statistic_payment_inhospital_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table statistic_payment_ondoctor
-- ----------------------------
ALTER TABLE "statistic_payment_ondoctor" ADD CONSTRAINT "statistic_payment_ondoctor_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table statistic_payment_reservation
-- ----------------------------
ALTER TABLE "statistic_payment_reservation" ADD CONSTRAINT "statistic_payment_reservation_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table statistic_registration
-- ----------------------------
ALTER TABLE "statistic_registration" ADD CONSTRAINT "statistic_registration_pkey" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table to_messagbox_table
-- ----------------------------
ALTER TABLE "to_messagbox_table" ADD CONSTRAINT "to_messagbox_table_pkey" PRIMARY KEY ("job_id");

-- ----------------------------
-- Primary Key structure for table whitelist_app
-- ----------------------------
ALTER TABLE "whitelist_app" ADD CONSTRAINT "whitelist_app_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table whitelist_app_user
-- ----------------------------
ALTER TABLE "whitelist_app_user" ADD CONSTRAINT "whitelist_app_user_pkey" PRIMARY KEY ("id");

-- ----------------------------
-- Primary Key structure for table yilian_adduser
-- ----------------------------
ALTER TABLE "yilian_adduser" ADD CONSTRAINT "pk_yilian_adduser" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table yilian_identify
-- ----------------------------
ALTER TABLE "yilian_identify" ADD CONSTRAINT "pk_yilian_identify" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table yilian_login
-- ----------------------------
ALTER TABLE "yilian_login" ADD CONSTRAINT "pk_yilian_login" PRIMARY KEY ("t_id");

-- ----------------------------
-- Primary Key structure for table yilian_logout
-- ----------------------------
ALTER TABLE "yilian_logout" ADD CONSTRAINT "pk_yilian_logout" PRIMARY KEY ("t_id");
