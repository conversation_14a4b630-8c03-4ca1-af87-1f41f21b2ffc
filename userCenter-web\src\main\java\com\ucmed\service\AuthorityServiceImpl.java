package com.ucmed.service;

import com.ucmed.bean.*;
import com.ucmed.common.service.CommonService;
import com.ucmed.dao.DataValueDao;
import com.ucmed.dao.GetAppInfoDao;
import com.ucmed.dao.ModuleDao;
import com.ucmed.util.JsonFormat;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.ucmed.common.constant.CommonConstant.*;
import static com.ucmed.common.constants.CommonConstant.USER_PERMISSION_KEY;

/**
 * 权限服务实现类
 * Created by QIUJIAHAO on 2016/9/18.
 */
@Service
@Transactional
public class AuthorityServiceImpl extends CommonService implements AuthorityService {

    @Autowired
    private ModuleDao moduleDao;
    @Autowired
    private GetAppInfoDao getAppInfoDao;
    @Autowired
    private DataValueDao dataValueDao;
    @Autowired
    private RoleService roleService;
    @Autowired
    private RedisTemplate redisTemplate;

    /**
     * 获取功能权限
     */
    public String getAuthority(JSONObject rcv) {
        String user_id = rcv.getString(USERID);
        int app_code = rcv.getInt(APPCODE);
        UC_UserInfo user = getUser(user_id);
        if (user == null) {
            return JsonFormat.retFormat(1, "用户不存在");
        }
        user_id = user.getUser_id();
        if (rcv.containsKey(ROLENAME)) {
            String role_name = rcv.getString(ROLENAME);
            int roleId = getRoleId(role_name, app_code);
            if (!isUserInRole(user_id, roleId)) {
                return JsonFormat.retFormat(2, "用户未注册该角色");
            }
            List<Module> moduleList = moduleDao.findModuleByRoleId(roleId);
            return JsonFormat.retFormat(0, "查询成功", JSONArray.fromObject(moduleList));
        } else {
            Map<String, Object> moduleMap = new HashMap<>();
            List<AppAndRole> roleList = getAppInfoDao.getRoles(user_id);
            for (AppAndRole role : roleList) {
                if (app_code == Integer.parseInt(role.getApp_code())) {
                    int user_roleId = Integer.parseInt(role.getRole_id());
                    List<Module> moduleList = moduleDao.findModuleByRoleId(user_roleId);
                    for (Module module : moduleList) {
                        moduleMap.put(String.valueOf(module.getModuleId()), module);
                    }
                }
            }
            if (moduleMap.size() == 0) {
                return JsonFormat.retFormat(3, "该用户没有功能权限");
            }
            List<Module> retList = new ArrayList<>();
            for (Map.Entry<String, Object> module : moduleMap.entrySet()) {
                retList.add((Module) module.getValue());
            }
            return JsonFormat.retFormat(0, "查询成功", JSONArray.fromObject(retList));
        }
    }

    public List<Module> getAuthority(String userId, int appCode, String roleName) {
        int roleId = getRoleId(roleName, appCode);
        if (!isUserInRole(userId, roleId)) {
            return null;
        }
        List<Module> moduleList = moduleDao.findModuleByRoleId(roleId);
        return moduleList;
    }

    public List<Module> getAuthority(String userId, int appCode) {
        return moduleDao.listUserModules(appCode, userId);
    }

    /**
     * 获取数据权限
     */
    public String getDataValue(JSONObject rcv) {
//        String role_name = rcv.getString(ROLENAME);
        String user_id = rcv.getString(USERID);
        int app_code = rcv.getInt(APPCODE);
        UC_UserInfo user = getUser(user_id);
        if (user == null) {
            return JsonFormat.retFormat(1, "用户不存在");
        }
        user_id = user.getUser_id();
        Map<String, Object> dataValueMap = new HashMap<>();
        List<AppAndRole> roleList = getAppInfoDao.getRoles(user_id);
        for (AppAndRole role : roleList) {
            if (app_code == Integer.parseInt(role.getApp_code())) {
                List<DataValue> datavalueList = dataValueDao.findeDataValueByRoleId(Integer.parseInt(role.getRole_id()), app_code);

                for (DataValue dataValue : datavalueList) {
                    dataValueMap.put(String.valueOf(dataValue.getDatavalueId()), dataValue);
                }
            }
        }
        if (dataValueMap.size() == 0) {
            return JsonFormat.retFormat(2, "该角色没有数据权限");
        }
        List<DataValue> retList = new ArrayList<>();
        for (Map.Entry<String, Object> module : dataValueMap.entrySet()) {
            retList.add((DataValue) module.getValue());
        }
        return JsonFormat.retFormat(0, "查询成功", JSONArray.fromObject(retList));
    }

    public List<DataValue> getDataValue(String userId, int appCode) {
        return dataValueDao.getUserDataValue(appCode, userId);
    }

    public List<String> getRoles(String userId, int appCode) {
        List<String> roles = new ArrayList<>();
        List<Role> roleList = roleService.queryUserRole(userId, appCode);
        if (roleList == null || roleList.size() == 0)
            return null;
        else {
            for (Role role : roleList) {
                roles.add(role.getRoleName());
            }
        }
        return roles;
    }

    @Override
    public Permission getPermission(String userId, int appCode) {
        Permission permission = (Permission) redisTemplate.opsForValue().get(USER_PERMISSION_KEY + userId + ":" + appCode);
        if (permission == null) {
            permission = new Permission();
            List<Module> modules = getAuthority(userId, appCode);
            List<DataValue> dataValues = getDataValue(userId, appCode);
            List<String> roles = getRoles(userId, appCode);
            permission.setDataValues(dataValues);
            permission.setModules(modules);
            permission.setRoles(roles);
            redisTemplate.opsForValue().set(USER_PERMISSION_KEY + userId + ":" + appCode, permission, 1, TimeUnit.DAYS);
        }
        return permission;
    }
}
