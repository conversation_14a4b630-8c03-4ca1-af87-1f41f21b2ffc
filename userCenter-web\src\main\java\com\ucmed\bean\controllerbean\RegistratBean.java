package com.ucmed.bean.controllerbean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

@ApiModel(value="注册",description="注册模型")
public class RegistratBean {
    @ApiModelProperty(value="用户名", name="userId", required = true, example="string")
    private String userId;
    @ApiModelProperty(value="手机号", name="phone", required = true, example="string")
    private String phone;
    @ApiModelProperty(value="密码", name="password", required = true, example="string")
    private String password;
    @ApiModelProperty(value="角色", name="roleName", required = true, example="string")
    private String roleName;


    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getRoleName() {
        return roleName;
    }

    public void setRoleName(String roleName) {
        this.roleName = roleName;
    }
}
