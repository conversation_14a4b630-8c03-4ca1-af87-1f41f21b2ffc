package cn.ucmed.transfer;

import cn.ucmed.utils.BeanHelper;
import cn.ucmed.utils.HttpClientUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.ucmed.bean.NUCResponse;
import com.ucmed.bean.UCResponse;
import com.ucmed.bean.UserInfo;
import com.ucmed.dto.User;
import com.ucmed.dto.UserPush;
import com.ucmed.exception.UnknownAccountException;
import com.ucmed.service.UserService;
import org.springframework.stereotype.Service;

import javax.servlet.http.HttpServletResponse;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static cn.ucmed.constant.UserCenterConstant.USER_CENTER_URL;

@Service
public class UserServiceImpl implements UserService {

    @Override
    public UCResponse registration(String userId, String phone, String password, int appCode, String roleName) {
        String url = USER_CENTER_URL + "/user/registration?userId=" + userId + "&phone=" + phone
                + "&password=" + password + "&appCode=" + appCode + "&roleName=" + roleName;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse registration(String userId, String phone, String password, int appCode, String roleName, String name, String email) {
        String url = USER_CENTER_URL + "/user/registrationWithEmail?userId=" + userId + "&phone=" + phone
                + "&password=" + password + "&appCode=" + appCode + "&roleName=" + roleName + "&name=" + name + "&email=" + email;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse registration(String userId, String phone, String password, int appCode, String roleName, String msgCode) {
        String url = USER_CENTER_URL + "/user/smsRegistration?userId=" + userId + "&phone=" + phone
                + "&password=" + password + "&appCode=" + appCode + "&roleName=" + roleName + "&msgCode=" + msgCode;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse login(String userId, String password, int appCode, String roleName) {
        String url = USER_CENTER_URL + "/user/login?userId=" + userId
                + "&password=" + password + "&appCode=" + appCode + "&roleName=" + roleName;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse smsLogin(String phone, String msgCode, int appCode, String roleName) {
        String url = USER_CENTER_URL + "/user/smsLogin?userId=" + phone
                + "&msgCode=" + msgCode + "&appCode=" + appCode + "&roleName=" + roleName;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse changePwd(String newPwd, String oldPwd, String token) {
        String url = USER_CENTER_URL + "/user/changePassword?newPwd=" + newPwd
                + "&oldPwd=" + oldPwd + "&token=" + token;
        return (UCResponse) HttpClientUtils.doPut(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse reInputPassword(String phone, String msgCode, String newPassword) {
        String url = USER_CENTER_URL + "/user/reInputPassword?phone=" + phone
                + "&msgCode=" + msgCode + "&newPwd=" + newPassword;
        return (UCResponse) HttpClientUtils.doPut(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse reInputPassword(int appCode, String phone, String msgCode, String newPassword) {
        String url = USER_CENTER_URL + "/user/reInputPassword?phone=" + phone
                + "&msgCode=" + msgCode + "&newPwd=" + newPassword + "&appCode=" + appCode;
        return (UCResponse) HttpClientUtils.doPut(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse logout(String token) {
        String url = USER_CENTER_URL + "/user/logout?token=" + token;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse changePhone(String token, String msgCode, String newPhone) {
        String url = USER_CENTER_URL + "/user/changePhone?token=" + token
                + "&msgCode=" + msgCode + "&newPhone=" + newPhone;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse changePhoneByOpenId(String openId, String msgCode, String newPhone) {
        String url = USER_CENTER_URL + "/user/changePhoneByOpenId?openId=" + openId
                + "&msgCode=" + msgCode + "&newPhone=" + newPhone;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse setPermission(String userId, int appCode, String roleName) {
        String url = USER_CENTER_URL + "/user/setPermission?userId=" + userId
                + "&appCode=" + appCode + "&roleName=" + roleName;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse sendMsgCode(String phone, int appCode, String type, String picCode) {
        String url = USER_CENTER_URL + "/messagecode?phone=" + phone
                + "&appCode=" + appCode + "&type=" + type + "&picCode=" + picCode + "&checkoutAccount=true";
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse sendMsgCode(String phone, int appCode, String type) {
        String url = USER_CENTER_URL + "/getmessagecode?phone=" + phone
                + "&appCode=" + appCode + "&type=" + type;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse verifyMessageCode(String phone, String msgCode, String type, boolean invalidWhenVerify) {
        String url = USER_CENTER_URL + "/messagecode/verify?phone=" + phone
                + "&type=" + type + "&msgCode=" + msgCode + "&invalidWhenVerify=" + invalidWhenVerify;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse generatePictureValidateCode(String phone, String type) {
        String url = USER_CENTER_URL + "/picturecode?phone=" + phone
                + "&type=" + type + "&checkoutAccount=true";
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse generatePictureValidateCode(String phone, String type, int length) {
        String url = USER_CENTER_URL + "/picturecode?phone=" + phone
                + "&type=" + type + "&checkoutAccount=true" + "&length=" + length;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse generatePictureValidateCode(String phone, int appCode, String type, int length) {
        String url = USER_CENTER_URL + "/picturecodeWithAppCode?phone=" + phone
                + "&type=" + type + "&checkoutAccount=true" + "&length=" + length + "&appCode=" + appCode;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse verifyPictureValidateCode(String phone, String type, String picCode) {
        String url = USER_CENTER_URL + "/picturecode/verify?phone=" + phone
                + "&type=" + type + "&checkoutAccount=true" + "&picCode=" + picCode;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse getUserInfo(String token) {
        String url = USER_CENTER_URL + "/user/userInfo?token=" + token;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse updateUserInfo(String token, UserInfo userInfo) {
        String url = USER_CENTER_URL + "/user/updateUserInfo?token=" + token;
        BeanHelper.emptyToNull(userInfo);
//        String userInfoQuery = BeanHelper.toUrlQuery(userInfo);
//        url = url + userInfoQuery;
        return (UCResponse) HttpClientUtils.doPost(url, JSON.parseObject(JSON.toJSONString(userInfo)), null, UCResponse.class);
    }

    @Override
    public UCResponse updateUserInfoByOpenId(String openId, UserInfo userInfo) {
        String url = USER_CENTER_URL + "/user/updateUserInfoByOpenId?openId=" + openId;
        BeanHelper.emptyToNull(userInfo);
//        String userInfoQuery = BeanHelper.toUrlQuery(userInfo);
//        url = url + userInfoQuery;
        return (UCResponse) HttpClientUtils.doPost(url, JSON.parseObject(JSON.toJSONString(userInfo)), null, UCResponse.class);
    }

    @Override
    public User getUserByOpenId(String openId) throws UnknownAccountException {
        String url = USER_CENTER_URL + "/user/getUserByOpenId?openId=" + openId;
        return (User) HttpClientUtils.doGet(url, new JSONObject(), null, User.class);
    }

    @Override
    public List<UserPush> getUserPushByOpenId(List<String> openIds) throws UnknownAccountException {
        String url = USER_CENTER_URL + "/user/getUserPushByOpenId?openIds=" + openIds;
        JSONObject jsonObject = JSON.parseObject(HttpClientUtils.doPost(url, JSON.parseArray(JSON.toJSONString(openIds)), null, JSONObject.class).toString());
        return JSON.parseObject(jsonObject.getString("userPush"), new TypeReference<List<UserPush>>(){});
    }

    @Override
    public UCResponse verifyUserToken(String token) {
        String url = USER_CENTER_URL + "/user/verifyUserToken?token=" + token;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse getOpenIdByToken(String token) {
        String url = USER_CENTER_URL + "/user/getOpenIdListByToken?token=" + token;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse getOpenIdByToken(int appCode, String token) {
        String url = USER_CENTER_URL + "/user/getOpenIdByToken?token=" + token + "&appCode=" + appCode;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse resetCache(String password) {
        String url = USER_CENTER_URL + "/user/resetCache?password=" + password;
        return (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse downloadRegisteredPhoneExcel(String startTime, String endTime, Integer appCode, Integer mouth) {
        String url = USER_CENTER_URL + "/user/registeredPhoneExcel?startTime=" + startTime + "&endTime=" + endTime
                + "&appCode=" + appCode + "&mouth=" + mouth;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public UCResponse listRegisteredPhone(Integer pageNum, Integer pageSize, String startTime, String endTime, Integer appCode, Integer mouth) {
        String url = USER_CENTER_URL + "/user/listRegisteredPhone?startTime=" + startTime + "&endTime=" + endTime
                + "&appCode=" + appCode + "&mouth=" + mouth + "&pageNum=" + pageNum + "&pageSize=" + pageSize;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public void downloadRegisteredPhoneExcel(String startTime, String endTime, String projCode, HttpServletResponse response) {
        String url = USER_CENTER_URL + "/user/listRegisteredPhone?startTime=" + startTime + "&endTime=" + endTime
                + "&projCode=" + projCode;
        HttpClientUtils.doGet(url, new JSONObject(), null);
    }

    @Override
    public UCResponse countRegisteredPhone(String startTime, String endTime, Integer appCode, Integer pageSize, Integer mouth) {
        String url = USER_CENTER_URL + "/user/listRegisteredPhone?startTime=" + startTime + "&endTime=" + endTime
                + "&appCode=" + appCode + "&pageSize=" + pageSize + "&mouth=" + mouth;
        return (UCResponse) HttpClientUtils.doGet(url, new JSONObject(), null, UCResponse.class);
    }

    @Override
    public NUCResponse<Boolean> checkUserInProject(String phone, Integer appCode) {
        String url = USER_CENTER_URL + "/checkUserInProject?phone=" + phone + "&appCode=" + appCode;
        return JSONObject.parseObject(JSONObject.toJSONString(HttpClientUtils.doPost(url, new JSONObject(), null, NUCResponse.class)), new TypeReference<NUCResponse<Boolean>>() {
        });
    }
}
