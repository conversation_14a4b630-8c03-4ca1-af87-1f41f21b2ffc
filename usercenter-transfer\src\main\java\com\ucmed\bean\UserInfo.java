package com.ucmed.bean;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import javax.persistence.Id;
import javax.persistence.Table;
import javax.persistence.Transient;
import java.io.Serializable;

@Table(name = "jc_user_info")
@ApiModel(value="用户信息",description="用户信息")
public class UserInfo implements Serializable {
    private static final long serialVersionUID = -2240116502255740344L;
    @Transient
    private int id;
    @Transient
    private int t_id;

    /**
     * 用户名
     */
    @Id
    @ApiModelProperty(value="用户名", name="user_id", required = true, example="string")
    private String user_id;
    /**
     * 真实姓名
     */
    @ApiModelProperty(value="真实姓名", name="the_name", required = true, example="string")
    private String the_name;
    /**
     * 性别（0 - 未知的性别，1 - 男性，2 - 女性，5 - 女性改（变）为男性，6 - 男性改（变）为女性，9 - 未说明的性别）
     */
    @ApiModelProperty(value="性别（0 - 未知的性别，1 - 男性，2 - 女性，5 - 女性改（变）为男性，6 - 男性改（变）为女性，9 - 未说明的性别）",
            name="sex", required = true, example="string")
    private String sex;
    /**
     * 证件类型（111 - 居民身份证，112 - 临时居民身份证，113 - 户口簿， 114 - 中国人民解放军军官证， 990 - 其他）
     */
    @ApiModelProperty(value="证件类型（111 - 居民身份证，112 - 临时居民身份证，113 - 户口簿， 114 - 中国人民解放军军官证， 990 - 其他）",
            name="card_type", required = true, example="string")
    private String card_type;
    /**
     * 证件号
     */
    @ApiModelProperty(value="证件号", name="card_id", required = true, example="string")
    private String card_id;
    /**
     * 是否有医保
     */
    @ApiModelProperty(value="是否有医保", name="medicare", required = true, example="string")
    private String medicare;
    /**
     * 医保卡号
     */
    @ApiModelProperty(value="医保卡号", name="medicare_id", required = true, example="string")
    private String medicare_id;
    /**
     * 区县代码
     */
    @ApiModelProperty(value="区县代码", name="city_id", required = true, example="string")
    private String city_id;
    /**
     * 居住地址
     */
    @ApiModelProperty(value="居住地址", name="address", required = true, example="string")
    private String address;
    /**
     * 微信号
     */
    @ApiModelProperty(value="微信号", name="w_chat", required = true, example="string")
    private String w_chat;
    /**
     * 联系方式
     */
    @ApiModelProperty(value="联系方式", name="mobile", required = true, example="string")
    private String mobile;
    /**
     * 电子邮箱
     */
    @ApiModelProperty(value="电子邮箱", name="e_mail", example="string")
    private String e_mail;
    /**
     * 出生日期
     */
    @ApiModelProperty(value="出生日期", name="birthday", example="string")
    private String birthday;
    /**
     * 数据来源，如医链，医联体
     */
    @ApiModelProperty(value="数据来源，如医链，医联体", name="source_ids", example="string")
    private String source_ids;
    /**
     * 创建时间
     */
    @ApiModelProperty(value="创建时间", name="create_time", example="string")
    private String create_time;
    /**
     * 修改时间
     */
    @ApiModelProperty(value="修改时间", name="update_time", example="string", hidden = true)
    private String update_time;

    @ApiModelProperty(value="ucmed_id", name="ucmed_id", example="string")
    private String ucmed_id;
    /**
     * 家属电话
     */
    @ApiModelProperty(value="家属电话", name="familyPhone", example="string")
    private String familyPhone;
    /**
     * 年龄
     */
    @ApiModelProperty(value="年龄", name="age", example="0")
    private Integer age;
    /**
     * 医疗报销类型（1-社会基本医疗保险，2-商业医疗保险，3-大病统筹，4-新型农村合作医疗，5-城镇居民基本医疗保险，6-公费医疗，0-其他）
     */
    @ApiModelProperty(value="医疗报销类型（1-社会基本医疗保险，2-商业医疗保险，3-大病统筹，4-新型农村合作医疗，5-城镇居民基本医疗保险，6-公费医疗，0-其他）",
            name="medicareExpenseTypes", example="string")
    private String medicareExpenseTypes;
    /**
     * 紧急联系人类型（1-配偶电话、2-监护人电话、3-家庭电话、4-工作单位电话、5-居委会电话、0-其他）
     */
    @ApiModelProperty(value="紧急联系人类型（1-配偶电话、2-监护人电话、3-家庭电话、4-工作单位电话、5-居委会电话、0-其他）",
            name="emergencyContactType", example="string")
    private String emergencyContactType;
    /**
     * 紧急联系人姓名
     */
    @ApiModelProperty(value="紧急联系人姓名", name="emergencyContactName", example="string")
    private String emergencyContactName;
    /**
     * 紧急联系人号码
     */
    @ApiModelProperty(value="紧急联系人号码", name="emergencyContactNumber", example="string")
    private String emergencyContactNumber;

    public int getT_id() {
        return t_id;
    }

    public void setT_id(int t_id) {
        this.t_id = t_id;
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUser_id() {
        return user_id;
    }

    public void setUser_id(String user_id) {
        this.user_id = user_id;
    }

    public String getThe_name() {
        return the_name;
    }

    public void setThe_name(String the_name) {
        this.the_name = the_name;
    }

    public String getSex() {
        return sex;
    }

    public void setSex(String sex) {
        this.sex = sex;
    }

    public String getCard_type() {
        return card_type;
    }

    public void setCard_type(String card_type) {
        this.card_type = card_type;
    }

    public String getCard_id() {
        return card_id;
    }

    public void setCard_id(String card_id) {
        this.card_id = card_id;
    }

    public String getMedicare() {
        return medicare;
    }

    public void setMedicare(String medicare) {
        this.medicare = medicare;
    }

    public String getMedicare_id() {
        return medicare_id;
    }

    public void setMedicare_id(String medicare_id) {
        this.medicare_id = medicare_id;
    }

    public String getCity_id() {
        return city_id;
    }

    public void setCity_id(String city_id) {
        this.city_id = city_id;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getW_chat() {
        return w_chat;
    }

    public void setW_chat(String w_chat) {
        this.w_chat = w_chat;
    }

    public String getMobile() {
        return mobile;
    }

    public void setMobile(String mobile) {
        this.mobile = mobile;
    }

    public String getE_mail() {
        return e_mail;
    }

    public void setE_mail(String e_mail) {
        this.e_mail = e_mail;
    }

    public String getBirthday() {
        return birthday;
    }

    public void setBirthday(String birthday) {
        this.birthday = birthday;
    }

    public String getSource_ids() {
        return source_ids;
    }

    public void setSource_ids(String source_ids) {
        this.source_ids = source_ids;
    }

    public String getCreate_time() {
        return create_time;
    }

    public void setCreate_time(String create_time) {
        this.create_time = create_time;
    }

    public String getUpdate_time() {
        return update_time;
    }

    public void setUpdate_time(String update_time) {
        this.update_time = update_time;
    }

    public String getUcmed_id() {
        return ucmed_id;
    }

    public void setUcmed_id(String ucmed_id) {
        this.ucmed_id = ucmed_id;
    }

    public String getFamilyPhone() {
        return familyPhone;
    }

    public void setFamilyPhone(String familyPhone) {
        this.familyPhone = familyPhone;
    }

    public Integer getAge() {
        return age;
    }

    public void setAge(Integer age) {
        this.age = age;
    }

    public String getMedicareExpenseTypes() {
        return medicareExpenseTypes;
    }

    public void setMedicareExpenseTypes(String medicareExpenseTypes) {
        this.medicareExpenseTypes = medicareExpenseTypes;
    }

    public String getEmergencyContactType() {
        return emergencyContactType;
    }

    public void setEmergencyContactType(String emergencyContactType) {
        this.emergencyContactType = emergencyContactType;
    }

    public String getEmergencyContactName() {
        return emergencyContactName;
    }

    public void setEmergencyContactName(String emergencyContactName) {
        this.emergencyContactName = emergencyContactName;
    }

    public String getEmergencyContactNumber() {
        return emergencyContactNumber;
    }

    public void setEmergencyContactNumber(String emergencyContactNumber) {
        this.emergencyContactNumber = emergencyContactNumber;
    }

    @Override
    public String toString() {
        return "UserInfo{" +
                "id=" + id +
                ", t_id=" + t_id +
                ", user_id='" + user_id + '\'' +
                ", the_name='" + the_name + '\'' +
                ", sex='" + sex + '\'' +
                ", card_type='" + card_type + '\'' +
                ", card_id='" + card_id + '\'' +
                ", medicare='" + medicare + '\'' +
                ", medicare_id='" + medicare_id + '\'' +
                ", city_id='" + city_id + '\'' +
                ", address='" + address + '\'' +
                ", w_chat='" + w_chat + '\'' +
                ", mobile='" + mobile + '\'' +
                ", e_mail='" + e_mail + '\'' +
                ", birthday='" + birthday + '\'' +
                ", source_ids='" + source_ids + '\'' +
                ", create_time='" + create_time + '\'' +
                ", update_time='" + update_time + '\'' +
                ", ucmed_id='" + ucmed_id + '\'' +
                ", familyPhone='" + familyPhone + '\'' +
                ", age=" + age +
                ", medicareExpenseTypes='" + medicareExpenseTypes + '\'' +
                ", emergencyContactType='" + emergencyContactType + '\'' +
                ", emergencyContactName='" + emergencyContactName + '\'' +
                ", emergencyContactNumber='" + emergencyContactNumber + '\'' +
                '}';
    }
}