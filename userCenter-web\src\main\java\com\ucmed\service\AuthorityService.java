package com.ucmed.service;

import com.ucmed.bean.DataValue;
import com.ucmed.bean.Module;
import com.ucmed.bean.Permission;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;

import java.util.List;

/**
 * 权限服务接口类
 *
 * 获取功能权限
 * 获取数据权限
 *
 * Created by QIUJIAHAO on 2016/9/18.
 */
public interface AuthorityService {

    /**
     * 查询角色的功能权限
     */
    String getAuthority(JSONObject rcv);

    List<Module> getAuthority(String userId, int appCode, String roleName);

    List<Module> getAuthority(String userId, int appCode);

    /**
     * 查询角色的数据权限
     */
    String getDataValue(JSONObject rcv);

    List<DataValue> getDataValue(String userId, int appCode);

    List<String> getRoles(String userId, int appCode);

    Permission getPermission(String userId, int appCode);
}
