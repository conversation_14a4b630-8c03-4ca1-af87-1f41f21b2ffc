package com.ucmed.message.client.imp;

import cn.ucmed.rubik.sms.view.SmsInfo;
import com.ucmed.dto.MessageResult;
import com.ucmed.message.client.SmsThirdService;
import com.ucmed.message.model.TSmsGateway;
import org.apache.log4j.Logger;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ucmed on 2017/2/20.
 */
@Service("smsThirdWebService")
public class SmsThirdWebServiceImpl implements SmsThirdService {
    private static final Logger LOG = Logger.getLogger(SmsThirdWebServiceImpl.class);

    @Override
    public MessageResult sendMessage(TSmsGateway tSmsGateway, String platformHospitalId, List<SmsInfo> lstSmsInfo) {
      LOG.debug("SmsThirdWebServiceImpl-sendMessage:"+platformHospitalId);
        return new MessageResult();
    }
}
