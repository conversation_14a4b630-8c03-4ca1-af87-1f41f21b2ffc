package com.ucmed.util;

import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.Iterator;

public class ExcelUtil {
    public static Workbook timesheetDownloadExcel(String fileName, JSONObject json, Integer columnWidth) {
        Workbook wb = new XSSFWorkbook();
        Sheet sheet = wb.createSheet(fileName);
        Row row = sheet.createRow(0);

        JSONObject cols = json.getJSONObject("columns");
        JSONArray rows = json.getJSONArray("rows");
        int len = rows.size();

        Iterator<String> it = cols.keys();
        int i = 0;
        i = createExcelRows(row, cols, it, i);
        // 设置行宽，在标题设置之后设置
        for (int j = 0; j < i; j++) {
            sheet.setColumnWidth(j,  columnWidth * 256);
        }
        row.createCell(i + 1).setCellValue("总数：" + len);

        JSONObject tmp;
        for (int j = 0; j < len; j++) {
            tmp = rows.getJSONObject(j);
            row = sheet.createRow(j + 1);
            i = 0;
            it = tmp.keys();
            createExcelRows(row, tmp, it, i);
        }

        return wb;
    }

    private static int createExcelRows(Row row, JSONObject cols, Iterator<String> it, int i) {
        while (it.hasNext()) {
            String key = it.next();
            String value = cols.getString(key);
            row.createCell(i).setCellValue(value);
            i++;
        }
        return i;
    }


    public static void output(HttpServletResponse response, Workbook wb, String fileName) {
        ByteArrayOutputStream os = new ByteArrayOutputStream();
        //输出文件
        try {
            wb.write(os);
            byte[] content = os.toByteArray();
            InputStream is = new ByteArrayInputStream(content);
            response.reset();
            BufferedInputStream bis;
            BufferedOutputStream bos;
            response.setContentType("application/vnd.ms-excel;charset=utf-8");
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            // 指定允许其他域名访问
            response.setHeader("Access-Control-Allow-Origin", "*");
            // 响应类型
            response.setHeader("Access-Control-Allow-Methods", "POST, GET, DELETE, OPTIONS, DELETE");
            // 响应头设置
            response.setHeader("Access-Control-Allow-Headers", "Content-Type, x-requested-with, X-Custom-Header, HaiYi-Access-Token");
            ServletOutputStream out = response.getOutputStream();
            bis = new BufferedInputStream(is);
            bos = new BufferedOutputStream(out);
            byte[] buff = new byte[2048];
            int bytesRead;
            while (-1 != (bytesRead = bis.read(buff, 0, buff.length))) {
                bos.write(buff, 0, bytesRead);
            }
            if (bis != null) {
                bis.close();
            }
            if (bos != null) {
                bos.close();
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
