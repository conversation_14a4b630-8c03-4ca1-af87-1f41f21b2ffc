package com.ucmed.service;

import com.ucmed.bean.SecurityUserProject;
import com.ucmed.dto.UserCancelAuthDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface SecurityUserProjectService {
    SecurityUserProject getUserProject(String userId, int projectId);
    SecurityUserProject getUserProjectByOpenId(String openId);
    void addUserProject(SecurityUserProject userProject);
    void updateUserProject(SecurityUserProject userProject);
    void updatedlPwd(SecurityUserProject userProject);
    List<SecurityUserProject> getOpenIdByUserId(String userId);
    List<UserCancelAuthDTO> listUserAuthInfo(String userId, Integer projCode);

}
