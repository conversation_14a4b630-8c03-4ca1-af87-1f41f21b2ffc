package cn.ucmed.transfer;

import cn.ucmed.utils.HttpClientUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ucmed.authc.AuthenticationService;
import com.ucmed.authc.AuthenticationToken;
import com.ucmed.bean.SimpleTokenParam;
import com.ucmed.bean.UCResponse;
import com.ucmed.bean.UC_UserInfo;
import org.apache.shiro.authc.AuthenticationException;
import org.springframework.stereotype.Service;

import java.util.HashMap;
import java.util.Map;

import static cn.ucmed.constant.UserCenterConstant.USER_CENTER_URL;

@Service
public class AuthenticationServiceImpl  implements AuthenticationService {
    @Override
    public void init(AuthenticationToken token) {
        String url = USER_CENTER_URL + "/authentication/init";
        String jsonString = null;
        if (token.getValue() != null) {
            jsonString = JSONObject.toJSONString(token.getValue());
        }
        SimpleTokenParam simpleTokenParam = new SimpleTokenParam(token.getTokenKey(), token.getProdKey().name(), jsonString, token.getTimeOut());
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(simpleTokenParam));
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        UCResponse ucResponse = (UCResponse) HttpClientUtils.doPost(url, jsonObject, headers, UCResponse.class);
        if (ucResponse.getRetCode() != 0) {
            throw new AuthenticationException(ucResponse.getRetInfo());
        }
    }

    @Override
    public void update(AuthenticationToken token) {
        String url = USER_CENTER_URL + "/authentication/update";
        String jsonString = null;
        if (token.getValue() != null) {
            jsonString = JSONObject.toJSONString(token.getValue());
        }
        SimpleTokenParam simpleTokenParam = new SimpleTokenParam(token.getTokenKey(), token.getProdKey().name(), jsonString, token.getTimeOut());
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(simpleTokenParam));
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        UCResponse ucResponse = (UCResponse) HttpClientUtils.doPost(url, jsonObject, headers, UCResponse.class);
        if (ucResponse.getRetCode() != 0) {
            throw new AuthenticationException(ucResponse.getRetInfo());
        }
    }

    @Override
    public void save(AuthenticationToken token) {
        String url = USER_CENTER_URL + "/authentication/save";
        String jsonString = null;
        if (token.getValue() != null) {
            jsonString = JSONObject.toJSONString(token.getValue());
        }
        SimpleTokenParam simpleTokenParam = new SimpleTokenParam(token.getTokenKey(), token.getProdKey().name(), jsonString, token.getTimeOut());
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(simpleTokenParam));
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        UCResponse ucResponse = (UCResponse) HttpClientUtils.doPost(url, jsonObject, headers, UCResponse.class);
        if (ucResponse.getRetCode() != 0) {
            throw new AuthenticationException(ucResponse.getRetInfo());
        }
    }

    @Override
    public Object authenticate(AuthenticationToken token) {
        String url = USER_CENTER_URL + "/authentication/authenticate";
        String jsonString = null;
        if (token.getValue() != null) {
            jsonString = JSONObject.toJSONString(token.getValue());
        }
        SimpleTokenParam simpleTokenParam = new SimpleTokenParam(token.getTokenKey(), token.getProdKey().name(), jsonString, token.getTimeOut());
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(simpleTokenParam));
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        UCResponse ucResponse = (UCResponse) HttpClientUtils.doPost(url, jsonObject, headers, UCResponse.class);
        if (ucResponse.getRetCode() != 0) {
            throw new AuthenticationException(ucResponse.getRetInfo());
        }
        return ucResponse.getParam();
    }

    @Override
    public Object authenticate(String tokenkey) {
        String url = USER_CENTER_URL + "/authentication/authenticateToken?token=" + tokenkey;
        UCResponse ucResponse = (UCResponse) HttpClientUtils.doPost(url, new JSONObject(), null, UCResponse.class);
        if (ucResponse.getRetCode() != 0) {
            throw new AuthenticationException(ucResponse.getRetInfo());
        }
        return ucResponse.getParam();
    }

    @Override
    public void clear(AuthenticationToken token) {
        String url = USER_CENTER_URL + "/authentication/clear";
        String jsonString = null;
        if (token.getValue() != null) {
            jsonString = JSONObject.toJSONString(token);
        }
        SimpleTokenParam simpleTokenParam = new SimpleTokenParam(token.getTokenKey(), token.getProdKey().name(), jsonString, token.getTimeOut());
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(simpleTokenParam));
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        UCResponse ucResponse = (UCResponse) HttpClientUtils.doPost(url, jsonObject, headers, UCResponse.class);
        if (ucResponse.getRetCode() != 0) {
            throw new AuthenticationException(ucResponse.getRetInfo());
        }
    }

    @Override
    public String registration(UC_UserInfo userInfo) {
        String url = USER_CENTER_URL + "/authentication/registration";
        JSONObject jsonObject = JSONObject.parseObject(JSONObject.toJSONString(userInfo));
        Map<String, String> headers = new HashMap<>();
        headers.put("Content-Type", "application/json");
        UCResponse ucResponse = (UCResponse) HttpClientUtils.doPost(url, jsonObject, headers, UCResponse.class);
        if (ucResponse.getRetCode() != 0) {
            throw new AuthenticationException(ucResponse.getRetInfo());
        }
        return ucResponse.getParam().toString();
    }
}
