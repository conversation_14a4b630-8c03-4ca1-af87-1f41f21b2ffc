package com.ucmed.bean;

import java.io.Serializable;

public class DataValue implements Serializable {

    private static final long serialVersionUID = -3951723807106675707L;
    private int datavalueId;
    private String value;
    private String datavalueDesc;
    private int templateId;
    private String datavalueName;

    public int getDatavalueId() {
        return datavalueId;
    }

    public void setDatavalueId(int datavalueId) {
        this.datavalueId = datavalueId;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDatavalueDesc() {
        return datavalueDesc;
    }

    public void setDatavalueDesc(String datavalueDesc) {
        this.datavalueDesc = datavalueDesc;
    }

    public int getTemplateId() {
        return templateId;
    }

    public void setTemplateId(int templateId) {
        this.templateId = templateId;
    }

    public String getDatavalueName() {
        return datavalueName;
    }

    public void setDatavalueName(String datavalueName) {
        this.datavalueName = datavalueName;
    }
}
