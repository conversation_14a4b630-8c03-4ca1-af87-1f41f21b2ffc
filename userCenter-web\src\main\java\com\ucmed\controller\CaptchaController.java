package com.ucmed.controller;

import com.ucmed.service.CaptchaService;
import io.swagger.annotations.*;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

/**
 * Created by XXB-QJH-1303.
 * Date: 2017/9/4 10:10
 */
@Controller
@RequestMapping(value = "/captcha")
@Api(value = "验证码", description = "验证码公共接口")
public class CaptchaController {

    @Autowired
    private CaptchaService captchaService;

    @ApiOperation(
            value = "获取图形验证码",
            notes = "获取图形验证码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "length", value = "验证码长度", required = true, paramType = "query"),
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "成功获取图片验证码"),
            @ApiResponse(code = 500, message = "获取验证码失败"),
    })
    @RequestMapping(value = "/image", method = RequestMethod.GET)
    public @ResponseBody
    ResponseEntity<String> getImageCaptcha(Integer length) {
        String responseJson = JSONObject.fromObject(captchaService.getImageCaptcha(length == null ? 4 : length)).toString();
        return createResponseEntity(responseJson);
    }

    @ApiOperation(
            value = "校验图形验证码",
            notes = "校验图形验证码\n"
    )
    @ApiImplicitParams({
            @ApiImplicitParam(name = "token", value = "token", required = true, paramType = "query", dataType = "String"),
            @ApiImplicitParam(name = "captchaCode", value = "图形验证码", required = true, paramType = "query", dataType = "String")
    })
    @ApiResponses({
            @ApiResponse(code = 0, message = "图形验证码验证成功"),
            @ApiResponse(code = -1, message = "图形验证码过期"),
            @ApiResponse(code = -2, message = "验证码输入错误"),
    })
    @RequestMapping(value = "/image/verify", method = RequestMethod.GET)
    public @ResponseBody
    ResponseEntity<String> verifyImageCaptcha(String token, String captchaCode) {
        String responseJson = JSONObject.fromObject(captchaService.verifyImageCaptcha(token, captchaCode)).toString();
        return createResponseEntity(responseJson);
    }

    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }
}
