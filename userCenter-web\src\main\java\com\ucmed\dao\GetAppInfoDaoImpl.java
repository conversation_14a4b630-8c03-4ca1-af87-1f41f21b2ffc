package com.ucmed.dao;

import com.ucmed.bean.AppAndRole;
import com.ucmed.bean.Application;
import com.ucmed.bean.Role;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Repository;

import java.util.List;

import static com.ucmed.common.constant.TableName.SECURITYAPPLICATION;

@Repository("getAppInfoDao")
@SuppressWarnings({"unchecked", "rawtypes"})
public class GetAppInfoDaoImpl implements GetAppInfoDao {
    @Autowired
    private JdbcTemplate jdbcTemplate;

    /**
     * 获取用户注册的应用程序信息
     */
    public List<Application> getApps(String user_id) {
        String sql = "SELECT a.app_code,a.app_name,a.app_desc,"
                + "a.oper_date,a.oper_user "
                + "FROM security_application a,security_user_app b "
                + "WHERE a.app_code=b.app_code AND b.user_id='" + user_id + "'";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper(Application.class));
    }

    /**
     * 获取用户注册的角色信息
     */
    public List<AppAndRole> getRoles(String user_id) {
        String sql = "select a.app_code,a.app_name,a.app_desc,a.proj_code,a.proj_name,"
                + "a.oper_date,a.oper_user,"
                + "c.role_id,c.role_name,c.role_desc,c.valid,c.oper_user as role_oper_user,c.oper_date as role_oper_date "
                + "from security_application a,security_user_role b,security_role c "
                + "where c.role_id=b.role_id and c.app_code=a.app_code and b.user_id='" + user_id + "'";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper(AppAndRole.class));
    }

    /**
     * 获取某应用程序的所有角色信息
     */
    public List<Role> getRolesByApp(String app_code) {
        String sql = "select role_id,app_code,role_name,role_desc,valid,oper_user as role_oper_user,oper_date as role_oper_date "
                + "from security_role "
                + "where app_code='" + app_code + "'";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper(Role.class));
    }

    /**
     * 获取用户在某应用程序的所有角色信息
     */
    public List<Role> getRolesByAppAndUser(String app_code, String user_id) {
        String sql = "select b.role_id,b.role_name,b.app_code,b.role_desc,b.valid,b.oper_user as role_oper_user,b.oper_date as role_oper_date "
                + "from security_user_role a,security_role b "
                + "where b.app_code='" + app_code + "' and a.user_id='" + user_id + "' and a.role_id=b.role_id";
        return jdbcTemplate.query(sql, new BeanPropertyRowMapper(Role.class));
    }

    @Override
    public Application getAppByCode(int app_code) {
        String sql = "SELECT " +
                "app_code,app_name,app_desc,app_device,app_valid,oper_date,oper_user,proj_code,special_flag " +
                "FROM " + SECURITYAPPLICATION + " WHERE app_code = ?";
        List<Application> result = jdbcTemplate.query(sql, new Object[]{app_code}, new BeanPropertyRowMapper<>(Application.class));
        if (result.size() == 0)
            return null;
        return result.get(0);
    }

    @Override
    public List<Application> getAppsByProjCode(int projCode) {
        String sql = "SELECT " +
                "app_code,app_name,app_desc,app_device,app_valid,oper_date,oper_user,proj_code " +
                "FROM security_application " +
                "WHERE proj_code = ?";
        List<Application> result = jdbcTemplate.query(sql, new Object[]{projCode}, new BeanPropertyRowMapper<>(Application.class));
        if (result.size() == 0)
            return null;
        return result;
    }
}
