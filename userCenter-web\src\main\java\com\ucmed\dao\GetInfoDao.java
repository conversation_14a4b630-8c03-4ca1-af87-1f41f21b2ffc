package com.ucmed.dao;

import java.util.List;

import com.ucmed.bean.*;

/**
 * 返回用户信息
 */
public interface GetInfoDao {
	/**
	 * 根据user_id返回用户信息
	 */
	List<UserInfo> getUserInfo(String user_id);
	
	/**
	 * 获取医生信息
	 */
	List<DoctorInfo> getDoctorInfo(String user_id);
	
	/**
	 * 获取科室信息
	 */
	List<Section> getSectionInfo(String section_no);
	
	/**
	 * 医院信息
	 */
	List<Hospital> getHospitalInfo(String ucmed_hospital_id);
	
	/**
	 * 数据来源信息
	 */
	List<PlatformSource> getPlatformInfo(String source_id);
	
	/**
	 * 医院科室信息
	 */
	List<HospitalSection> getHospitalSectionInfo(String ucmed_hospital_id);
	
	/**
	 * 就诊人信息
	 */
	List<Patient> getPatientInfo(String user_id);
	
	/**
	 * 用户登录信息
	 */
	List<UC_UserInfo> getUC_UserInfo(String user_id);
}
