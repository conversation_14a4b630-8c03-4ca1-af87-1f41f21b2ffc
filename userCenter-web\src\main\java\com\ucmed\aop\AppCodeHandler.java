package com.ucmed.aop;

import com.alibaba.dubbo.rpc.RpcContext;
import com.ucmed.common.service.CommonService;
import com.ucmed.mapper.SecurityApplicationMapper;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.reflect.MethodSignature;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

import static com.ucmed.common.constant.CommonConstant.APPCODE;
import static com.ucmed.common.constant.CommonConstant.APP_CACHE;

/**
 * 废弃中，未用上
 */

@Aspect
public class AppCodeHandler extends CommonService {

    private static Logger log = Logger.getLogger(AppCodeHandler.class.getName());

    @Autowired
    SecurityApplicationMapper securityApplicationMapper;
    @Autowired
    private RedisTemplate redisTemplate;

    //    @Around("execution(* com.ucmed.transfer.*.*(..)) && !execution(* com.ucmed.transfer.ThirdPartyServiceImpl.*(..))")
    public Object permissionCheck(ProceedingJoinPoint point) throws Throwable {
        Object obj = null;
        Integer appCode = null;
        try {
            Object[] paramValues = point.getArgs();
            String[] paramNames = ((MethodSignature) point.getSignature()).getParameterNames();

            for (int i = 0; i < paramValues.length; i++) {
                if ("appCode".equals(paramNames[i])) {
                    paramValues[i] = getAppCodeByAppToken((Integer) paramValues[i]);
                    appCode = (Integer) paramValues[i];
                    break;
                }
            }
            obj = point.proceed(paramValues);
        } catch (Exception e) {
            log.info(((MethodSignature) point.getSignature()).getMethod().getName() + "报异常了");
            log.info("快快快，appCodeHandler异常了：" + e.getMessage());
            obj = point.proceed();
        }
        if (obj != null) {
            JSONObject jsonObject = JSONObject.fromObject(obj);
            if (jsonObject.containsKey("param")) {
                if (jsonObject.getJSONObject("param").containsKey(APPCODE) && appCode != null) {
                    jsonObject.getJSONObject("param").put(APPCODE, appCode);
                    obj = JSONObject.toBean(jsonObject, ((MethodSignature) point.getSignature()).getReturnType());
                }
            }

        }
        return obj;
    }

    //    @Around("execution(* com.ucmed.transfer.*.*(..))")
    public void dubboContext(JoinPoint point) {
        Object[] paramValues = point.getArgs();
        String[] paramNames = ((MethodSignature) point.getSignature()).getParameterNames();
        Map<String, String> context = new HashMap<>();
        for (int i = 0; i < paramValues.length; i++) {
            context.put(paramNames[i], String.valueOf(paramValues[i]));
        }
        RpcContext.getContext().setAttachments(context);
    }


}
