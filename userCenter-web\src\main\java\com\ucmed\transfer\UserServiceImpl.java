package com.ucmed.transfer;

import cn.ucmed.common.constants.GlobalConstants;
import cn.ucmed.common.ratelimit.SendSmsFailException;
import cn.ucmed.rubik.sms.view.SmsInfo;
import com.ucmed.bean.*;
import com.ucmed.bean.retbean.UserProjectInfoVO;
import com.ucmed.common.constant.AppMemoryInfo;
import com.ucmed.common.constant.CommonConstant;
import com.ucmed.common.constants.controller.URLConstants;
import com.ucmed.common.exception.IllegalPatternException;
import com.ucmed.common.exception.RepeatedAccountException;
import com.ucmed.common.exception.UnknownAppException;
import com.ucmed.common.service.CommonService;
import com.ucmed.dto.*;
import com.ucmed.exception.BusinessException;
import com.ucmed.exception.UnknownAccountException;
import com.ucmed.mapper.JcUserPushMapper;
import com.ucmed.mapper.SecurityApplicationMapper;
import com.ucmed.mapper.SecurityProjectMapper;
import com.ucmed.message.client.SmsClient;
import com.ucmed.message.dao.SmsHistoryMapper;
import com.ucmed.message.model.MsgCache;
import com.ucmed.message.model.SMSHistory;
import com.ucmed.service.*;
import com.ucmed.util.*;
import net.sf.json.JSONObject;
import org.apache.commons.lang.RandomStringUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.poi.ss.usermodel.Workbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.http.HttpStatus;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.io.UnsupportedEncodingException;
import java.lang.reflect.Field;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static com.ucmed.common.constant.CommonConstant.*;
import static com.ucmed.common.constants.CommonConstant.APPCODE;
import static com.ucmed.common.constants.CommonConstant.PHONE;
import static com.ucmed.common.constants.CommonConstant.USER_PERMISSION_KEY;

/**
 * 用户服务实现类
 *
 * <AUTHOR>
 * @date 2016/9/18
 */

@Service("userService")
//@Transactional
public class UserServiceImpl extends CommonService implements UserService {
    private static Logger logger = LoggerFactory.getLogger(UserServiceImpl.class.getName());
    @Autowired
    private StringRedisTemplate stringRedisTemplate;
    @Autowired
    private GetAppInfoService getAppInfoService;
    @Autowired
    private RedisTemplate redisTemplate;
    @Autowired
    private SmsClient smsClient;
    @Autowired
    private JcUserPushMapper jcUserPushMapper;
    @Autowired
    private SecurityUserProjectService securityUserProjectService;
    @Autowired
    private CaptchaService captchaService;
    @Autowired
    private Login login;
    @Autowired
    private UserInfoService userInfoService;
    @Autowired
    private RoleService roleService;
    @Autowired
    private RocketMQUtil rocketMQUtil;
    @Autowired
    private WhiteAndBlackListService whiteAndBlackListService;

    @Autowired
    SecurityProjectMapper securityProjectMapper;
    @Autowired
    private SmsHistoryMapper smsHistoryMapper;
    @Autowired
    SecurityApplicationMapper securityApplicationMapper;
    @Autowired
    AppMemoryInfo appMemoryInfo;

    @Value("${SMSValdate_timeout}")
    private long msgTimeout;
    @Value("${SMSValdate_error_times}")
    private int msgErrorTimes;
    @Value("${master_msgcode}")
    private String mastermsgcode;
    @Value("${host}")
    private String hostAddress;

    public static final String MTKEY_INVITE_MESSAGE_CODE = "invite_message_code";


    @Override
    public UCResponse registration(String userId, String phone, String password, int appCode, String roleName, String msgCode) {
        UCResponse response = verifyMessageCode(phone, msgCode, GlobalConstants.ValidatePhoneType.REGIST.getKey(), false);
        if (response.getRetCode() != 0) {
            return new UCResponse(-2, response.getRetInfo());
        }
        response = registration(userId, phone, password, appCode, roleName);
        if (response.getRetCode() == 0) {
            redisTemplate.delete(CommonConstant.MSG_CATCH + GlobalConstants.ValidatePhoneType.REGIST.getValue() + phone);
        }
        return response;
    }

    @Override
    @Transactional
    public UCResponse registration(String userId, String phone, String password, int appCode, String roleName) {
        try {
            boolean indPwd = appMemoryInfo.getAppIndependentPwd(appCode);
//            int dl = securityProjectMapper.selectIndePwdByAppCode(appCode);
            if (indPwd) {
                return dlregistration(userId, phone, password, appCode, roleName);
            }
        } catch (Exception e) {
            return new UCResponse(-1, "应用未注册");
        }

        logger.info("registration service input:userId=" + userId + ",phone=" + phone + ",password=" + password + ",appCode=" + appCode + ",roleName=" + roleName);
        String currentTime = TimeUtil.getCurrentTime();

        if (!whiteAndBlackListService.isPassWhitList(appCode, userId)) {
            return new UCResponse(6, "此用户不在白名单内，无法注册");
        }

        if (!whiteAndBlackListService.isPassBlackList(appCode, userId)) {
            return new UCResponse(7, "此用户在黑名单内，无法注册");
        }

        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            return new UCResponse(-1, "应用未注册");
        }

        // 判断角色是否存在
        if (!roleService.isExists(appCode, roleName)) {
            return new UCResponse(-3, "角色不存在");
        }

        // 用户名合法性
        if (isNotUsernamePattern(userId)) {
            return new UCResponse(4, "用户名不合法");
        }

        // 手机号合法性
        if (isNotPhonePattern(phone)) {
            return new UCResponse(5, "手机号不合法");
        }

        // 弱密码
        if (isSimplePwd(password)) {
            return new UCResponse(3, "密码太简单，建议使用大小写字母、数字和特殊字符");
        }

        String retUserId;
        // 账户未注册
        if (!isAccountExists(userId) && !isAccountExists(phone)) {
            String UCMEDID = GenerateRandomString.generateRandomString("UCMED", 5);
            if (userId.equals(phone)) {
                retUserId = UCMEDID;
            } else {
                retUserId = userId;
            }
            // 新账户注册
            normalRegistration(retUserId, phone, password, appCode, roleName, currentTime, UCMEDID);
            //获取UID
            int uid = getUid(userId);
            if (uid == 0) {
                uid = getUid(phone);
            }

            JSONObject retJson = new JSONObject();
            retJson.put("id", uid);
            retJson.put(USERID, retUserId);
            // 返回OpenId
            SecurityUserProject userProject = securityUserProjectService.getUserProject(retUserId, Integer.parseInt(application.getProj_code()));
            if (userProject != null) {
                retJson.put("openId", userProject.getOpenId());
            }
            logger.info("registration service output:注册成功 " + retJson);
            try{
                JSONObject messageJson = new JSONObject();
                messageJson.put(USERID, retUserId);
                messageJson.put(PHONE, phone);
                messageJson.put(APPCODE, appCode);
                messageJson.put("proj_code", Integer.parseInt(application.getProj_code()));
                messageJson.put("create_time", TimeUtil.getCurrentTime());
                messageJson.put("open_id", userProject.getOpenId());
                rocketMQUtil.registerMessage(messageJson);
            }
            catch (Exception e){
                logger.error("推送rocketMQ消息失败：{}", e.getMessage());
            }
            return new UCResponse(0, "注册成功", retJson);

        // 账户已注册，要授权
        } else {
            UC_UserInfo user = getUser(userId);
            if (user == null) {
                user = getUser(phone);
            }
            retUserId = user.getUser_id();
            JSONObject retJson = new JSONObject();
            retJson.put(USERID, retUserId);
            // 返回OpenId
            SecurityUserProject userProject = securityUserProjectService.getUserProject(retUserId, Integer.parseInt(application.getProj_code()));
            if (userProject != null) {
                retJson.put("openId", userProject.getOpenId());
                logger.info("registration service output:账号已注册 " + retJson);
                return new UCResponse(1, "该账号已注册，请使用原账号密码登录", retJson);

            } else {
                //生成securityKey
                String securityKey = SecurityKeyUtil.generateSecurityKey();
                //密码加密
                password = encryptPassword(password, securityKey);
                user.setPassword(password);
                user.setSecuritykey(securityKey);
                getJcUserDao().updateUser(user);
                setPermission(retUserId, appCode, roleName);
                userProject = securityUserProjectService.getUserProject(retUserId, Integer.parseInt(application.getProj_code()));
                if (userProject != null) {
                    retJson.put("openId", userProject.getOpenId());
                }
                logger.info("registration service output:账号已注册,授权 " + retJson);
                return new UCResponse(0, "注册成功", retJson);
            }

        }

    }

    @Override
    public UCResponse registration(String userId, String phone, String password,
                                   int appCode, String roleName, String name, String email) {
        logger.info("registration service input:userId=" + userId + ",phone=" + phone + ",password=" +
                password + ",appCode=" + appCode + ",roleName=" + roleName + ",address=" +
                name + ",email=" + email);
        String currentTime = TimeUtil.getCurrentTime();
        Application application = getAppInfoService.getAppByCode(appCode);
        UCResponse ucResponse = registrationUserInfoJudge(application, userId, phone,
                password, appCode, roleName, name, email);
        if (ucResponse.getRetCode() != 0) {
            return ucResponse;
        }
        String retUserId;

        boolean indPwd = appMemoryInfo.getAppIndependentPwd(appCode);
//        int dl = securityProjectMapper.selectIndePwdByAppCode(appCode);
        // 账户未注册
        if (!isAccountExists(userId) && !isAccountExists(phone)) {
            String UCMEDID = GenerateRandomString.generateRandomString("UCMED", 5);
            if (userId.equals(phone)) {
                retUserId = UCMEDID;
            } else {
                retUserId = userId;
            }
            // 新账户注册
            if (indPwd) {
                normalRegistration(retUserId, phone, password, appCode, roleName, currentTime, UCMEDID, name, email, true);
            } else {
                normalRegistration(retUserId, phone, password, appCode, roleName, currentTime, UCMEDID, name, email, false);
            }
        } else {
            if (indPwd) {
                return alreadyRegistered(application, userId, phone, appCode, roleName, password, true);
            } else {
                return alreadyRegistered(application, userId, phone, appCode, roleName, password, false);
            }

        }
        //获取UID
        int uid = getUid(userId);
        if (uid == 0) {
            uid = getUid(phone);
        }
        JSONObject retJson = new JSONObject();
        retJson.put("id", uid);
        retJson.put(USERID, retUserId);
        // 返回OpenId
        SecurityUserProject userProject = securityUserProjectService.getUserProject(retUserId, Integer.parseInt(application.getProj_code()));
        if (userProject != null) {
            retJson.put("openId", userProject.getOpenId());
        }
        logger.info("registration service output:注册成功 " + retJson);

        registerSuccessMessage(application, retUserId, phone, appCode, userProject);
        return new UCResponse(0, "注册成功", retJson);
    }

    public UCResponse dlregistration(String userId, String phone, String password, int appCode, String roleName) {
        logger.info("registration service input:userId=" + userId + ",phone=" + phone + ",password=" + password + ",appCode=" + appCode + ",roleName=" + roleName);
        String currentTime = TimeUtil.getCurrentTime();

        Application application = getAppInfoService.getAppByCode(appCode);
        UCResponse ucResponse = registrationUserInfoJudge(application, userId, phone,
                password, appCode, roleName);
        if (ucResponse.getRetCode() != 0) {
            return ucResponse;
        }

        String retUserId;
        // 账户未注册
        UserDTO user = userInfoService.getUserDTO(userId);
        if (user == null) {
            user = userInfoService.getUserDTO(phone);
        }
        if (user == null) {
            String UCMEDID = GenerateRandomString.generateRandomString("UCMED", 5);
            if (userId.equals(phone)) {
                retUserId = UCMEDID;
            } else {
                retUserId = userId;
            }
            // 独立密码项目新账户注册
            dlnormalRegistration(retUserId, phone, password, appCode, roleName, currentTime, UCMEDID);
        } else {
            return alreadyRegistered(application, userId, phone, appCode, roleName, password, true);
        }
        //获取UID
        int uid = getUid(userId);
        if (uid == 0) {
            uid = getUid(phone);
        }

        JSONObject retJson = new JSONObject();
        retJson.put("id", uid);
        retJson.put(USERID, retUserId);
        // 返回OpenId
        SecurityUserProject userProject = securityUserProjectService.getUserProject(retUserId, Integer.parseInt(application.getProj_code()));
        if (userProject != null) {
            retJson.put("openId", userProject.getOpenId());
        }
        logger.info("registration service output:注册成功 " + retJson);
        registerSuccessMessage(application, retUserId, phone, appCode, userProject);
        return new UCResponse(0, "注册成功", retJson);
    }


    @Override
    public UCResponse login(String userId, String password, int appCode, String roleName) {
        logger.info("login service input: userId=" + userId + ",password=" + password + ",appCode=" + appCode + ",roleName=" + roleName);
        UCResponse response = login.login(userId, password, appCode, roleName, false);
        logger.info("login service output:" + response.getRetInfo());
//        log4j.info("login service output:" + response.getRetInfo() + "测试用：" + response.getParam());
        return response;
    }

    @Override
    public UCResponse smsLogin(String phone, String msgCode, int appCode, String roleName) {
        logger.info("login service input: phone=" + phone + ",msgCode=" + msgCode + ",appCode=" + appCode + ",roleName=" + roleName);
        UCResponse response = verifyMessageCode(phone, msgCode, GlobalConstants.ValidatePhoneType.LOGIN.getKey(), false);
        if (response.getRetCode() != 0) {
            logger.info("smsLogin service output:" + response.getRetInfo());
            return new UCResponse(-2, response.getRetInfo());
        }
        response = login.login(phone, "", appCode, roleName, true);
        logger.info("smsLogin service output:" + response.getRetInfo());
//        log4j.info("smsLogin service output:" + response.getRetInfo() + " 测试用：" + response.getParam());
        if (response.getRetCode() == 0) {
            redisTemplate.delete(CommonConstant.MSG_CATCH + GlobalConstants.ValidatePhoneType.LOGIN.getValue() + phone);
        }
        return response;
    }

    @Override
    public UCResponse changePwd(String newPwd, String oldPwd, String token) {
        String currentTime = TimeUtil.getCurrentTime();
        String userId = verifyToken(token);
        if (userId == null) {
            return new UCResponse(401, "会话已失效");
        }
        UC_UserInfo user = getUser(userId);

        if (user == null) {
            return new UCResponse(3, "该账号未注册");
        }
        logger.info("用户修改请求密码： userId:" + user.getUser_id());
        String projCode = getProjCodeByToken(token);
        boolean indPwd = appMemoryInfo.getProIndependentPwd(Integer.parseInt(projCode));
        if (indPwd) {
            user.setProject_code(projCode);
            return dlchangePwd(user, newPwd, oldPwd, currentTime);
        }
        // 验证密码是否正确
        if (passwordMatched(oldPwd, user)) {
            // 检查新密码强度
            if (isSimplePwd(newPwd)) {
                return new UCResponse(1, "新密码太简单，建议使用大小写字母、数字和特殊字符");
            }
            changePwd(user, newPwd, currentTime);
            return new UCResponse(0, "修改密码成功");
        } else {
            return new UCResponse(2, "修改密码失败，旧密码错误");
        }
    }

    private UCResponse dlchangePwd(UC_UserInfo user, String newPwd, String oldPwd, String currentTime) {
        SecurityUserProject userProject = securityUserProjectService.getUserProject(user.getUser_id(), Integer.parseInt(user.getProject_code()));
        if (userProject == null) {
            addUserProject(user.getUser_id(), Integer.parseInt(user.getProject_code()));
            userProject = securityUserProjectService.getUserProject(user.getUser_id(), Integer.parseInt(user.getProject_code()));
        }
        if (dlpasswordMatched(oldPwd, userProject, user)) {
            // 检查新密码强度
            if (isSimplePwd(newPwd)) {
                return new UCResponse(1, "新密码太简单，建议使用大小写字母、数字和特殊字符");
            }

            dlchangePwd(userProject, newPwd, currentTime);
            logger.info("用户修改密码成功： userId:" + user.getUser_id());
            return new UCResponse(0, "修改密码成功");
        } else {
            return new UCResponse(2, "修改密码失败，旧密码错误");
        }
    }

    @Override
    public UCResponse reInputPassword(String phone, String msgCode, String newPassword) {
        UCResponse response = verifyMessageCode(phone, msgCode, GlobalConstants.ValidatePhoneType.FORGETPSW.getKey(), false);
        if (response.getRetCode() != 0) {
            return new UCResponse(-1, response.getRetInfo());
        }
        logger.info("用户修改请求密码： phone:" + ThreeDESEncrypt(phone));

        int appCode;
        try {
            List<SMSHistory> smsHistories = smsHistoryMapper.getAppcodeByPhoneAndKeywords(phone, msgCode);
            if (smsHistories.size() > 0) {
                appCode = Integer.parseInt(smsHistories.get(0).getVcProjectId());
            } else {
                appCode = 0;
            }
        } catch (NumberFormatException e) {
            appCode = 0;
        }
        logger.info("未传递appcode，更改密码：appcode=" + appCode);
        response = reInputPassword(phone, appCode, newPassword);
        if (response.getRetCode() == 0) {
            redisTemplate.delete(CommonConstant.MSG_CATCH + GlobalConstants.ValidatePhoneType.FORGETPSW.getValue() + phone);
        }
        return response;
    }

    @Override
    public UCResponse reInputPassword(int appCode, String phone, String msgCode, String newPassword) {
        UCResponse response = verifyMessageCode(phone, msgCode, GlobalConstants.ValidatePhoneType.FORGETPSW.getKey(), false);
        if (response.getRetCode() != 0) {
            return new UCResponse(-1, response.getRetInfo());
        }
        logger.info("用户修改请求密码： phone:" + ThreeDESEncrypt(phone));
        response = reInputPassword(phone, appCode, newPassword);
        if (response.getRetCode() == 0) {
            redisTemplate.delete(CommonConstant.MSG_CATCH + GlobalConstants.ValidatePhoneType.FORGETPSW.getValue() + phone);
        }
        return response;
    }

    private UCResponse reInputPassword(String userId, int appCode, String newPassword) {
        String currentTime = TimeUtil.getCurrentTime();
        UC_UserInfo user = getUser(userId);
        if (user == null) {
            return new UCResponse(2, "该账号未注册");
        }
        if (isSimplePwd(newPassword)) {
            return new UCResponse(1, "密码太简单，建议使用大小写字母、数字和特殊字符");
        }
        boolean indPwd;
        if (appCode != 0) {
            indPwd = appMemoryInfo.getAppIndependentPwd(appCode);
        } else {
            indPwd = false;
        }
        int projCode = getProjCode(appCode);
//        SecurityUserProject userProject = securityUserProjectService.getUserProject(user.getUser_id(), projCode);
//        if (userProject == null) {
//            return new UCResponse(3, "用户在该项目中授权");
//        }
        if (indPwd) {
            SecurityUserProject userProject = securityUserProjectService.getUserProject(user.getUser_id(), projCode);
            if (userProject == null) {
                addUserProject(user.getUser_id(), projCode);
                userProject = securityUserProjectService.getUserProject(user.getUser_id(), projCode);
            }
            dlchangePwd(userProject, newPassword, currentTime);
        } else {
//             修改密码
            changePwd(user, newPassword, currentTime);
        }
        if (projCode == 0) {
            redisTemplate.delete(USER_PWD_ERROR_TIMES + user.getUser_id());
        } else {
            redisTemplate.boundHashOps(USER_PWD_ERROR_TIMES + user.getUser_id()).delete(String.valueOf(projCode));
        }
        logger.info("用户重置密码成功： userId:" + user.getUser_id());
        return new UCResponse(0, "重置密码成功");
    }


    @Override
    public UCResponse logout(String token) {
//        log4j.info("测试用：用户退出登录：token：" + token);
        String userId = verifyToken(token);
        if (userId != null) {
            UC_UserInfo user = getUser(userId);
            user.setToken("");
            user.setToken_time(TimeUtil.getCurrentTime());
            updateUser(user);
            stringRedisTemplate.delete(token);
            logger.info("用户退出登录：" + user.getUser_id());
        }
        return new UCResponse(0, "退出登录成功");
    }


    @Override
    public UCResponse changePhone(String token, String msgCode, String newPhone) {
        UCResponse response = verifyMessageCode(newPhone, msgCode, GlobalConstants.ValidatePhoneType.RESETPHONE.getKey(), false);
        if (response.getRetCode() != 0) {
            return new UCResponse(-1, response.getRetInfo());
        }
        // 验证新手机号合法性
        if (isNotPhonePattern(newPhone)) {
            return new UCResponse(2, "手机号不合法");
        }
        // 验证新手机号是否注册
        if (isPhoneExist(newPhone) || isUserExist(newPhone)) {
            return new UCResponse(3, "手机号已注册");

        }
        String userId = verifyToken(token);
        if (userId == null) {
            return new UCResponse(401, "会话已失效");
        }
//        newPhone = ThreeDESEncrypt(newPhone);
        UC_UserInfo user = getUser(userId);

        user.setPhone(ThreeDESEncrypt(newPhone));
        updateUser(user);
        redisTemplate.delete(CommonConstant.MSG_CATCH + GlobalConstants.ValidatePhoneType.FORGETPSW.getValue() + newPhone);
        try {
            UserInfo userInfo = userInfoService.getUserInfoByUserId(userId);
            userInfo.setMobile(newPhone);
            userInfoService.updateUserInfo(userInfo);
        } catch (BusinessException e) {
            logger.info("旧手机号码未注册");
        }
        logger.info("用户更换手机号成功：" + user.getUser_id() + "新手机号：" + ThreeDESEncrypt(newPhone));
        return new UCResponse(0, "手机号修改成功");
    }

    @Override
    public UCResponse changePhoneByOpenId(String openId, String msgCode, String newPhone) {
        logger.info("根据openId修改手机号码,openId:" + openId);
        UCResponse response = verifyMessageCode(newPhone, msgCode, GlobalConstants.ValidatePhoneType.RESETPHONE.getKey(), false);
        if (response.getRetCode() != 0) {
            return new UCResponse(-1, response.getRetInfo());
        }
        // 验证新手机号合法性
        if (isNotPhonePattern(newPhone)) {
            return new UCResponse(2, "手机号不合法");
        }
        // 验证新手机号是否注册
        if (isPhoneExist(newPhone) || isUserExist(newPhone)) {
            return new UCResponse(3, "手机号已注册");
        }
        SecurityUserProject securityUserProject = securityUserProjectService.getUserProjectByOpenId(openId);
        if (securityUserProject == null) {
            return new UCResponse(401, "openId不存在");
        }
//        newPhone = ThreeDESEncrypt(newPhone);
        UC_UserInfo user = getUser(securityUserProject.getUserId());
        user.setPhone(ThreeDESEncrypt(newPhone));
        updateUser(user);
        redisTemplate.delete(CommonConstant.MSG_CATCH + GlobalConstants.ValidatePhoneType.FORGETPSW.getValue() + newPhone);
        try {
            UserInfo userInfo = userInfoService.getUserInfoByUserId(securityUserProject.getUserId());
            userInfo.setMobile(newPhone);
            userInfoService.updateUserInfo(userInfo);
        } catch (BusinessException e) {
            logger.info("旧手机号码未注册");
        }
        logger.info("用户更换手机号成功：" + user.getUser_id() + "新手机号：" + ThreeDESEncrypt(newPhone));
        return new UCResponse(0, "手机号修改成功");
    }

    @Override
    public UCResponse setPermission(String userId, int appCode, String roleName) {
        logger.info("用户授权应用，userId：" + userId + "，应用id：" + appCode + "，授权角色：" + roleName);
        UC_UserInfo user = getUser(userId);
        if (user == null) {
            return new UCResponse(1, "账号未注册");
        }
        Application application = getAppInfoService.getAppByCode(appCode);

        if (application == null) {
            logger.info("应用未注册，授权结束");
            return new UCResponse(-1, "应用未注册");
        }

        // 判断角色是否存在
        if (getRoleId(roleName, appCode) == 0) {
            logger.info("角色不存在，授权结束");
            return new UCResponse(-3, "角色不存在");
        }

        addRelation(user.getUser_id(), appCode, roleName);
        // 返回OpenId
        SecurityUserProject userProject = securityUserProjectService.getUserProject(userId, Integer.parseInt(application.getProj_code()));
        JSONObject param = new JSONObject();
        if (userProject != null) {
            param.put("openId", userProject.getOpenId());
        }
        // 删除权限缓存
        redisTemplate.delete(USER_PERMISSION_KEY + user.getUser_id() + ":" + appCode);
        logger.info("授权成功结束");
        return new UCResponse(0, "授权成功", param);
    }

    @Override
    public UCResponse sendMsgCode(String phone, int appCode, String type, String picCode) {
        // 校验图形验证码
        UCResponse response = verifyPictureValidateCode(phone, type, picCode);
        if (response.getRetCode() != 0) {
            return new UCResponse(response.getRetCode(), response.getRetInfo(), response.getParam());
        }
        try {
            sendMessage(phone, appCode, type);
        } catch (BusinessException ex) {
            logger.error(ex.getMessage());
            return new UCResponse(ex.getCode(), ex.getMessage());
        }
        return new UCResponse(0, "获取验证码成功，请查看您的手机短信");
    }

    @Override
    public UCResponse sendMsgCode(String phone, int appCode, String type) {
        try {
            sendMessage(phone, appCode, type);
        } catch (BusinessException ex) {
            logger.error(ex.getMessage());
            return new UCResponse(ex.getCode(), ex.getMessage());
        }
        return new UCResponse(0, "获取验证码成功，请查看您的手机短信");
    }

    private void sendMessage(String phone, int appCode, String type) throws BusinessException {
        if (isNotPhonePattern(phone)) {
            throw new IllegalPatternException(641, "手机号不合法");
        }
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            throw new UnknownAppException(641, "应用未注册");
        }

        if (GlobalConstants.ValidatePhoneType.REGIST.getKey().equals(type)) {
//            boolean indPwd = appMemoryInfo.getAppIndependentPwd(appCode);
            UC_UserInfo user = getUser(phone);
            SecurityUserProject userProject = null;
            if (user != null) {
                userProject = securityUserProjectService.getUserProject(user.getUser_id(), Integer.parseInt(application.getProj_code()));
            }

            if (userProject != null && (isPhoneExist(phone) || isUserExist(phone))) {
                throw new RepeatedAccountException(641, "该账号已注册");
            }
        } else if (GlobalConstants.ValidatePhoneType.FORGETPSW.getKey().equals(type)
                || GlobalConstants.ValidatePhoneType.LOGIN.getKey().equals(type)
                || GlobalConstants.ValidatePhoneType.BINDPATIENTVISIT.getKey().equals(type)) {
            if (isPhoneNotExist(phone) && isUserNotExist(phone)) {
                throw new UnknownAccountException(641, "账号未注册");
            }
        } else if (GlobalConstants.ValidatePhoneType.RESETPHONE.getKey().equals(type) ||
                GlobalConstants.ValidatePhoneType.BINDTHIRDAPP.getKey().equals(type)) {
            //DO nothing
        } else if ("99".equals(type)) {
            //DO nothing
        } else {
            throw new IllegalPatternException(643, "type类型错误");
        }
        String code;
        try {
            String templateId = MTKEY_INVITE_MESSAGE_CODE;
            Map<String, String> smsParams = new HashMap<String, String>();
            code = getSMSVerificationCode(GlobalConstants.ValidatePhoneType.getValue(type), phone, 6);
            smsParams.put("code", code);
            List<String> paramList = new ArrayList<>();
            paramList.add(code);

            if (!smsClient.sendMessage(String.valueOf(appCode), new SmsInfo(phone, phone, "你的验证码是:" + code, templateId, smsParams, paramList))) {
                throw new BusinessException(642, "获取验证码失败");
            }
        } catch (SendSmsFailException e) {
            throw new BusinessException(642, e.getMessage());
        }
    }

    /**
     * 从缓存中获取短信验证码，验证码有效缓存为30m,如果失效会生成新的验证码并返回
     *
     * @param tag   短信验证码标志，忘记密码或注册时应当不同，实际情况可能存在bug
     * @param phone 手机号码
     * @param i     验证码长度
     * @return 指定长度的验证码
     */
    public String getSMSVerificationCode(String tag, String phone, int i) {

        if (StringUtils.isNotBlank(phone)) {

            MsgCache msgCache = (MsgCache) redisTemplate.opsForValue().get(CommonConstant.MSG_CATCH + tag + phone);

            /**
             * 如果验证码失效，则生成新的验证码并返回
             */
            if (msgCache == null ||
                    StringUtils.isEmpty(msgCache.getMsgNo())) {

                msgCache = new MsgCache();
                msgCache.setMsgNo(RandomStringUtils.randomNumeric(i));
                msgCache.setLength(i);
                msgCache.setPhone(phone);
                msgCache.setTag(tag);
                msgCache.setErrorTimes(0);
                redisTemplate.opsForValue().set(msgCache.getKey(), msgCache, msgTimeout, TimeUnit.SECONDS);
            }
            return msgCache.getMsgNo();
        } else {
            throw new cn.ucmed.common.rubikexception.BusinessException(-1, "获取验证码失败，电话号码不正确");
        }
    }

    @Override
    public UCResponse verifyMessageCode(String phone, String msgCode, String type, boolean invalidWhenVerify) {
        if (mastermsgcode != null && !mastermsgcode.isEmpty() && mastermsgcode.equals(msgCode)) {
            logger.info(ThreeDESEncrypt(phone) + "通过万能验证码验证");
            return new UCResponse(0, "万能验证码验证成功");
        }
        String tag = GlobalConstants.ValidatePhoneType.getValue(type);
        if (StringUtils.isNotBlank(phone) && StringUtils.isNotBlank(msgCode)) {
            MsgCache msgCache = (MsgCache) redisTemplate.opsForValue().get(CommonConstant.MSG_CATCH + tag + phone);
            if (msgCache != null &&
                    msgCache.getMsgNo() != null &&
                    msgCache.getMsgNo().equals(msgCode) &&
                    msgCache.getErrorTimes() < msgErrorTimes) {

                if (invalidWhenVerify) {
                    redisTemplate.delete(CommonConstant.MSG_CATCH + tag + phone);
                }
                return new UCResponse(0, "验证成功");
            }
            if (redisTemplate.hasKey(CommonConstant.MSG_CATCH + tag + phone)) {
                int errorTimes = msgCache.getErrorTimes() + 1;
                if (errorTimes >= msgErrorTimes) {
                    redisTemplate.delete(CommonConstant.MSG_CATCH + tag + phone);
                } else {
                    msgCache.setErrorTimes(errorTimes);
                    long expire = redisTemplate.getExpire(CommonConstant.MSG_CATCH + tag + phone);
                    redisTemplate.opsForValue().set(msgCache.getKey(), msgCache, expire, TimeUnit.SECONDS);
                }
            }
            return new UCResponse(-1, "验证码失效或者输入不正确,请重新获取");
        } else {
            return new UCResponse(-2, "验证验证码失败，电话号码或短信验证码为空");
        }
    }

    @Override
    public UCResponse generatePictureValidateCode(String phone, String type) {
        return captchaService.getImageCaptcha(phone, type);
    }

    @Override
    public UCResponse generatePictureValidateCode(String phone, String type, int length) {
        return captchaService.getImageCaptcha(phone, type, length);
    }

    @Override
    public UCResponse generatePictureValidateCode(String phone, int appCode, String type, int length) {
        if (!getAppInfoService.isAppExists(appCode)) {
            return new UCResponse(641, "应用未注册");
        }
        return captchaService.getImageCaptcha(appCode, phone, type, length);
    }

    @Override
    public UCResponse verifyPictureValidateCode(String phone, String type, String picCode) {
        return captchaService.verifyImageCaptcha(phone, type, picCode);
    }

    @Override
    public UCResponse getUserInfo(String token) {
//        log4j.info("查询用户信息：" + token);
        UserInfo userInfo;
        try {
            userInfo = userInfoService.getUserInfo(token);
        } catch (BusinessException e) {
            logger.info("查询用户信息" + e.getMessage() + "：token：" + token);
            return new UCResponse(e.getCode(), e.getMessage());
        }
        activeToken(token);
        return new UCResponse(0, "查询成功", userInfo);
    }

    @Override
    public UCResponse updateUserInfo(String token, UserInfo userInfo) {
        String userId = verifyToken(token);
        if (userId == null) {
            return new UCResponse(401, "会话已失效");
        }
        userInfo.setUser_id(userId);
        try {
            userInfoService.updateUserInfo(userInfo);
        } catch (IllegalArgumentException e) {
            return new UCResponse(412, e.getMessage());
        }
        activeToken(token);
        return new UCResponse(0, "更新成功");
    }

    @Override
    public User getUserByOpenId(String openId) throws UnknownAccountException {
        SecurityUserProject userProject = securityUserProjectService.getUserProjectByOpenId(openId);
        if (userProject == null) {
            throw new UnknownAccountException(HttpStatus.UNAUTHORIZED.value(), "账号未注册");
        }
//        UC_UserInfo user = getUser(userProject.getUserId());
        User userDto = new User();
        try {
            UserInfo user = userInfoService.getUserInfoByUserId(userProject.getUserId());

            if (user == null) {
                throw new UnknownAccountException(HttpStatus.UNAUTHORIZED.value(), "账号未注册");
            }

            userDto.setPhone(user.getMobile());
            userDto.setUserId(user.getUser_id());
            userDto.setRealName(user.getThe_name());
            userDto.setIDCard(user.getCard_id());
        } catch (BusinessException e) {
            logger.info("查询用户信息错误!");
        }
        return userDto;
    }

    @Override
    public List<UserPush> getUserPushByOpenId(List<String> openIds) throws UnknownAccountException {
        List<UserPush> userPushList = new ArrayList<>();
        for (String openId : openIds) {
            try {
                User user = getUserByOpenId(openId);
                JcUserPush jcUserPush = jcUserPushMapper.selectByOpenId(openId);
                UserInfo userInfo = getJcUserInfoDao().getUserInfoByUserId(user.getUserId());
                UserPush userPush = new UserPush();
                userPush.setPushId(jcUserPush.getPushId());
                userPush.setIdCard(ThreeDESDecrypt(userInfo.getCard_id()));
                userPush.setPhone(user.getPhone());
                userPush.setRealName(userInfo.getThe_name());
                userPushList.add(userPush);
            } catch (UnknownAccountException e) {
                logger.error(openId, e);
                userPushList.add(new UserPush());
                continue;
            }
        }
        return userPushList;
    }

    @Override
    public UCResponse updateUserInfoByOpenId(String openId, UserInfo userInfo) {
        logger.info("根据用户中心openId修改用户信息,openId:" + openId);
        SecurityUserProject securityUserProject = securityUserProjectService.getUserProjectByOpenId(openId);
        if (securityUserProject == null) {
            return new UCResponse(401, "openId不存在");
        }
        userInfo.setUser_id(securityUserProject.getUserId());
        try {
            userInfoService.updateUserInfo(userInfo);
        } catch (IllegalArgumentException e) {
            return new UCResponse(412, e.getMessage());
        }
        return new UCResponse(0, "更新成功");
    }


    public UCResponse registrationUserInfoJudge(Application application, String userId, String phone, String password, int appCode, String roleName, String address, String email) {
        UCResponse ucResponse = registrationUserInfoJudge(application, userId, phone, password, appCode, roleName);
        if (ucResponse.getRetCode() != 0) {
            return ucResponse;
        }
        if (!isEmail(email)) {
            return new UCResponse(8, "邮箱格式不正确");
        }

        return new UCResponse(0, "可以注册");
    }

    public UCResponse registrationUserInfoJudge(Application application, String userId, String phone, String password, int appCode, String roleName) {
        if (!whiteAndBlackListService.isPassWhitList(appCode, userId)) {
            return new UCResponse(6, "此用户不在白名单内，无法注册");
        }

        if (!whiteAndBlackListService.isPassBlackList(appCode, userId)) {
            return new UCResponse(7, "此用户在黑名单内，无法注册");
        }


        if (application == null) {
            return new UCResponse(-1, "应用未注册");
        }

        // 判断角色是否存在
        if (!roleService.isExists(appCode, roleName)) {
            return new UCResponse(-3, "角色不存在");
        }

        // 用户名合法性
        if (isNotUsernamePattern(userId)) {
            return new UCResponse(4, "用户名不合法");
        }

        // 手机号合法性
        if (isNotPhonePattern(phone)) {
            return new UCResponse(5, "手机号不合法");
        }

        // 弱密码
        if (isSimplePwd(password)) {
            return new UCResponse(3, "密码太简单，建议使用大小写字母、数字和特殊字符");
        }

        return new UCResponse(0, "ok");
    }

    public UCResponse alreadyRegistered(Application application, String userId, String phone, int appCode, String roleName, String password, boolean dlpwd) {
        UC_UserInfo user = getUser(userId);
        if (user == null) {
            user = getUser(phone);
        }
        String retUserId = user.getUser_id();
        JSONObject retJson = new JSONObject();
        retJson.put(USERID, retUserId);
        // 返回OpenId
        SecurityUserProject userProject = securityUserProjectService.getUserProject(user.getUser_id(), Integer.parseInt(application.getProj_code()));
        if (userProject != null) {
            retJson.put("openId", userProject.getOpenId());
        } else if (dlpwd) {
            //独立密码项目添加挂靠关系
            dladdRelation(user.getUser_id(), appCode, roleName, password);
            int uid = getUid(userId);
            if (uid == 0) {
                uid = getUid(phone);
            }
            userProject = securityUserProjectService.getUserProject(user.getUser_id(), Integer.parseInt(application.getProj_code()));
            retJson.put("openId", userProject.getOpenId());
            retJson.put("UID", uid);
            logger.info("独立密码项目注册： " + retJson);
            return new UCResponse(0, "独立密码项目注册成功", retJson);
        }
        logger.info("registration service output:账号已注册 " + retJson);
        return new UCResponse(1, "该账号已注册，请使用原账号密码登录", retJson);
    }

    public void registerSuccessMessage(Application application, String retUserId, String phone, int appCode, SecurityUserProject userProject) {
        JSONObject messageJson = new JSONObject();
        messageJson.put(USERID, retUserId);
        messageJson.put(PHONE, phone);
        messageJson.put(APPCODE, appCode);
        messageJson.put("proj_code", Integer.parseInt(application.getProj_code()));
        messageJson.put("create_time", TimeUtil.getCurrentTime());
        messageJson.put("open_id", userProject.getOpenId());
        rocketMQUtil.registerMessage(messageJson);
    }

    @Override
    public UCResponse verifyUserToken(String token) {
        String TokenInfo = getTokenInfo(token);
        if (TokenInfo == null) {
            return new UCResponse(401, "会话已失效");
        }
        activeToken(token);
        return new UCResponse(0, "会话正常", TokenInfo);
    }


    @Override
    public UCResponse getOpenIdByToken(String token) {
        String userId = verifyToken(token);
        logger.info("获取所有openId，token：" + token);
        if (userId == null) {
            return new UCResponse(401, "会话已失效");
        }
        List<SecurityUserProject> userProjects = securityUserProjectService.getOpenIdByUserId(userId);
        if (userProjects.size() < 1 || userProjects == null) {
            return new UCResponse(-2, "用户未授权任何应用");
        }
        activeToken(token);
        return new UCResponse(0, "获取成功", userProjects);
    }

    @Override
    public UCResponse getOpenIdByToken(int appCode, String token) {
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            return new UCResponse(-1, "应用未注册");
        }
        String userId = verifyToken(token);
        logger.info("appCode：" + appCode + "获取openId，token：" + token);
        if (userId == null) {
            return new UCResponse(401, "会话已失效");
        }
        SecurityUserProject userProject = securityUserProjectService.getUserProject(userId, getProjCode(appCode));
        if (userProject == null) {
            return new UCResponse(5, "用户未授权，是否确定使用该应用？");
        }
        userProject.setPassword(null);
        userProject.setSecuritykey(null);
        activeToken(token);
        return new UCResponse(0, "获取成功", userProject);
    }

    @Override
    public UCResponse resetCache(String password) {
        if (!password.equals("resetgo")) {
            return new UCResponse(-1, "后台用的，别瞎来");
        }
        AppMemoryInfo.appTokenlife = securityApplicationMapper.seclectAllAppTokenLife();
        AppMemoryInfo.proIndependentPwd = securityProjectMapper.seclectAllProIndependentPwd();
        AppMemoryInfo.appIndependentPwd = securityProjectMapper.seclectAllAppIndependentPwd();
        Set<String> keys = redisTemplate.keys(ROLES_CACHE + "*");
        for (String key : keys) {
            redisTemplate.delete(key);
        }
        keys = redisTemplate.keys(APP_CACHE + "*");
        for (String key : keys) {
            redisTemplate.delete(key);
        }
        return new UCResponse(0, "重置成功");
    }

    @Override
    public UCResponse downloadRegisteredPhoneExcel(String startTime, String endTime, Integer appCode, Integer mouth) {
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            return new UCResponse(-1, "应用未注册", "0");
        }
        Integer projCode = null;
        try {
            projCode = Integer.valueOf(application.getProj_code());
        } catch (Exception e) {
            logger.error("项目id转换错误" + e.getMessage());
        }
        if (projCode == 0) {
            return new UCResponse(-1, "应用未注册", "0");
        }
        if (startTime == null || startTime.isEmpty() || endTime == null || endTime.isEmpty()) {
            return new UCResponse(-2, "开始或结束时间不能为空", "0");
        }
        if (mouth > 0) {
            if (!limitThereMouth(startTime, endTime, mouth)) {
                return new UCResponse(-3, "跨度不能超过" + mouth + "个月或日期格式错误", "0");
            }
        }
        List<UserInfoExcelDTO> userInfoExcelDTOS = userInfoService.listUserInfoWithinTimeByProjCode(startTime, endTime, projCode);
        if (userInfoExcelDTOS == null) {
            return new UCResponse(-3, "这段时间该项目没有注册用户", "0");
        }
        String projCodeDES = ThreeDESEncrypt(projCode.toString());
        return new UCResponse(0, "生成excel文件成功", hostAddress + URLConstants.REGIST_PHONE_EXCEL + "?projCode=" +
                projCodeDES + "&startTime=" + startTime + "&endTime=" + endTime);
    }


    @Override
    public void downloadRegisteredPhoneExcel(String startTime, String endTime, String projCode, HttpServletResponse response) {
        projCode = ThreeDESDecrypt(projCode);

        // 验证项目id
        String projName = securityProjectMapper.getProjNameByProjCode(Integer.valueOf(projCode));
        if (projName == null) {
            return;
        }
        List<UserInfoExcelDTO> userInfoExcelDTOS = userInfoService.listUserInfoWithinTimeByProjCode(startTime, endTime, Integer.valueOf(projCode));
        userInfoExcelDTOS.forEach(userInfoExcelDTO -> userInfoExcelDTO.setMobile(ThreeDESDecrypt(userInfoExcelDTO.getMobile())));
        UserInfoExcelDTO userInfoExcelDTO = new UserInfoExcelDTO();
        JSONObject json = new JSONObject();
        JSONObject columns = new JSONObject();
        Class<? extends UserInfoExcelDTO> rtClass = userInfoExcelDTO.getClass();
        //获取所有属性
        Field[] fields = rtClass.getDeclaredFields();
        Arrays.stream(fields).forEach(field -> {
            //获取是否可访问
            boolean flag = field.isAccessible();
            try {
                //设置该属性总是可访问
                field.setAccessible(true);
                columns.put(field.getName(), field.get(userInfoExcelDTO));
            } catch (IllegalAccessException e) {
                logger.info("field获取参数名失败：" + e.getMessage());
            }
            //还原可访问权限
            field.setAccessible(flag);
        });
        json.put("columns", columns);
        json.put("rows", userInfoExcelDTOS);

        Workbook wb = ExcelUtil.timesheetDownloadExcel("注册量报表", json, 25);
        try {
            String fileName = new String((projName + "注册量报表(" + startTime + "-" + endTime + ").xls").getBytes("gb2312"), "ISO8859-1");
            ExcelUtil.output(response, wb, fileName);
        } catch (UnsupportedEncodingException e) {
            logger.info("生成文件名失败：" + e.getMessage());
        }
        logger.info(projName + " 下载注册量报表");
    }

    @Override
    public UCResponse listRegisteredPhone(Integer pageNum, Integer pageSize, String startTime, String endTime, Integer appCode, Integer mouth) {
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            return new UCResponse(-1, "应用未注册", "0");
        }
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return new UCResponse(-2, "开始或结束时间不能为空", "0");
        }
        if (mouth > 0) {
            if (!limitThereMouth(startTime, endTime, mouth)) {
                return new UCResponse(-3, "跨度不能超过" + mouth + "个月", "0");
            }
        }
        Integer projCode = null;
        try {
            projCode = Integer.valueOf(application.getProj_code());
        } catch (Exception e) {
            logger.error("项目id转换错误" + e.getMessage());
        }
        if (projCode == null || projCode == 0) {
            return new UCResponse(-1, "应用未注册", "0");
        }

        JSONObject retJson = new JSONObject();
        retJson.put("pageNum", pageNum);
        retJson.put("pageSize", pageSize);
        pageNum = (pageNum - 1) * pageSize;
        List<UserInfoExcelDTO> userBusinessInfos = userInfoService.listUserInfoWithinTimeByProjCodeLimitPage(startTime, endTime, projCode, pageNum, pageSize);
        if (userBusinessInfos == null) {
            return new UCResponse(-3, "这段时间该项目没有注册用户", "0");
        }
        userBusinessInfos.forEach(userInfoExcelDTO -> {
            userInfoExcelDTO.setMobile(ThreeDESDecrypt(userInfoExcelDTO.getMobile()));
        });

        retJson.put("rows", userBusinessInfos);
        retJson.put("rowsLength", userBusinessInfos.size());

        return new UCResponse(0, "获取成功", retJson);
    }

    @Override
    public UCResponse countRegisteredPhone(String startTime, String endTime, Integer appCode, Integer pageSize, Integer mouth) {
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            return new UCResponse(-1, "应用未注册", "0");
        }
        if (StringUtils.isEmpty(startTime) || StringUtils.isEmpty(endTime)) {
            return new UCResponse(-2, "开始或结束时间不能为空", "0");
        }
        if (mouth > 0) {
            if (!limitThereMouth(startTime, endTime, mouth)) {
                return new UCResponse(-3, "跨度不能超过" + mouth + "个月", "0");
            }
        }
        Integer projCode = null;
        try {
            projCode = Integer.valueOf(application.getProj_code());
        } catch (Exception e) {
            logger.error("项目id转换错误" + e.getMessage());
        }
        if (projCode == null || projCode == 0) {
            return new UCResponse(-1, "应用未注册", "0");
        }

        Integer total = userInfoService.countRegisteredPhone(startTime, endTime, projCode);
        if (total == null) {
            return new UCResponse(-3, "这段时间该项目没有注册用户", "0");
        }

        JSONObject retJson = new JSONObject();
        retJson.put("total", total);
        retJson.put("pages", Math.ceil(total * 1.0 / pageSize));

        return new UCResponse(0, "获取成功", retJson);
    }

    @Override
    public NUCResponse<Boolean> checkUserInProject(String phone, Integer appCode) {
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            return NUCResponse.createByError(-1, "应用未注册");
        }
        UC_UserInfo user = getUser(phone);
        if (user == null) {
            return NUCResponse.createByError(4, "账号未注册");
        }
        SecurityUserProject userProject = securityUserProjectService.getUserProject(user.getUser_id(), Integer.parseInt(application.getProj_code()));
        if (userProject == null || !userProject.getProjCode().toString().equals(application.getProj_code())) {
            return NUCResponse.createBySuccess(false);
        }
        return NUCResponse.createBySuccess(true);
    }

    @Override
    public NUCResponse<UserProjectInfoVO> getUserInProject(String phone, Integer appCode) {
        Application application = getAppInfoService.getAppByCode(appCode);
        if (application == null) {
            return NUCResponse.createByError(-1, "应用未注册");
        }
        UC_UserInfo user = getUser(phone);
        if (user == null) {
            return NUCResponse.createByError(4, "账号未注册");
        }

        SecurityUserProject userProject = securityUserProjectService.getUserProject(user.getUser_id(), Integer.parseInt(application.getProj_code()));
        if (userProject == null || !userProject.getProjCode().toString().equals(application.getProj_code())) {
            return NUCResponse.createByError(5, "用户未授权");
        }
        UserProjectInfoVO userProjectInfoVO = new UserProjectInfoVO();
        userProjectInfoVO.setUserId(userProject.getUserId());
        userProjectInfoVO.setOpenId(userProject.getOpenId());
        userProjectInfoVO.setCreateTime(userProject.getCreateTime());
        return NUCResponse.createBySuccess(userProjectInfoVO);
    }

    private boolean limitThereMouth(String startTime, String endTime, Integer mouth) {
        if (startTime.length() != endTime.length()) {
            return false;
        }
        try {
            DateTimeFormatter df = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            LocalDateTime localStartTime = LocalDateTime.parse(startTime, df);
            LocalDateTime localEndTime = LocalDateTime.parse(endTime, df);
            if (localStartTime.plusMonths(mouth).plusDays(1).isBefore(localEndTime)) {
                return false;
            }
        } catch (Exception e) {
            logger.warn("日期格式出错：" + e.getMessage());
            return false;
        }
        return true;
    }

}
