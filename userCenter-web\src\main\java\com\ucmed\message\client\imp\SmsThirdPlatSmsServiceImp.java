package com.ucmed.message.client.imp;


import cn.ucmed.common.net.HttpClient;
import cn.ucmed.rubik.sms.view.SmsInfo;
import com.alibaba.fastjson.JSON;
import com.ucmed.dto.MessageBody;
import com.ucmed.dto.MessageResult;
import com.ucmed.message.client.SmsThirdService;
import com.ucmed.message.model.TSmsGateway;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * Created by ucmed on 2017/2/20.
 */
@Service("smsThirdPlatSmsService")
public class SmsThirdPlatSmsServiceImp implements SmsThirdService {
    private static final Logger LOG = Logger.getLogger(SmsThirdPlatSmsServiceImp.class);
    private static final int RESULTCODE_SUCCESS = 100; //      提交成功
    private HttpClient httpClient;
    @Value("${platsms.http.url}")
    private String platSmsHttpUrl;

    @Override
    public MessageResult sendMessage(TSmsGateway tSmsGateway, String platformHospitalId, List<SmsInfo> lstSmsInfo) {
        LOG.debug("SmsThirdPlatSmsServiceImp-sendMessage:" + platformHospitalId);
        setSingleHttpClient();
        MessageBody mess;  //请求参数
        MessageResult messageResult = null;  //返回参数
        boolean sendSuccess = true;   //返回发送成功与否
        //    String messageResultStr;  //返回消息 字符
        for (SmsInfo smsInfo : lstSmsInfo) {
            mess = new MessageBody();
            mess.setToken(tSmsGateway.getPlatsmsToken());
            mess.setPhone(smsInfo.getReceiverMobile());
            mess.setContent(smsInfo.getContent());

/*            messageResultStr = httpClient.sendSynchronousRequest(Json.getJson().toJson(mess));
            LOG.warn(Json.getJson().toJson(mess) + "messageResultStr:" + messageResultStr);
              messageResult = JSON.parseObject(messageResultStr, MessageResult.class);
            */
            messageResult = SendSmsApi.sendMessage(mess);
            LOG.warn(JSON.toJSON(mess) + "messageResultStr:" + messageResult);
            // sendSuccess &= messageResult.getResultCode() == RESULTCODE_SUCCESS;
        }
        return messageResult;
    }

    private void setSingleHttpClient() {
        if (httpClient == null) {
            httpClient = new HttpClient(platSmsHttpUrl);
        }
    }
}
