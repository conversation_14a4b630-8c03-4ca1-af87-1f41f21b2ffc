package com.ucmed.service;

import com.ucmed.bean.Patient;
import com.ucmed.bean.UC_UserInfo;
import com.ucmed.common.service.CommonService;
import com.ucmed.dao.JcUserPatientDao;
import com.ucmed.util.JsonFormat;
import com.ucmed.util.TimeUtil;
import net.sf.json.JSONArray;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

import static com.ucmed.common.constant.CommonConstant.PATIENTID;
import static com.ucmed.common.constant.CommonConstant.USERID;

/**
 * 就诊人服务实现类
 * Created by QIUJIAHAO on 2016/9/27.
 */
@Service
@Transactional
public class PatientServiceImpl extends CommonService implements PatientService {
    @Autowired
    private JcUserPatientDao jcUserPatientDao;

    public String addPatient(JSONObject rcv) {
        String currentTime = TimeUtil.getCurrentTime();
        String user_id = rcv.getString(USERID);
        //用户是否存在
        UC_UserInfo user = getUser(user_id);
        if(user == null) {
            return JsonFormat.retFormat(1, "账号未注册");
        }
        user_id = user.getUser_id();
        Patient patient = new Patient();
        patient.setUser_id(user_id);
        patient.setCreate_time(currentTime);
        patient = setPatient(patient, rcv);
        jcUserPatientDao.addPatient(patient);
        return JsonFormat.retFormat(0, "添加就诊人信息成功");
    }

    public String updatePatient(JSONObject rcv) {
        int patient_id = rcv.getInt(PATIENTID);
        String currentTime = TimeUtil.getCurrentTime();
        //就诊人是否存在
        Patient patient = jcUserPatientDao.getPatientById(patient_id);
        if(patient == null) {
            return JsonFormat.retFormat(1, "没有该就诊人信息，请检查patient_id");
        }
        patient = setPatient(patient, rcv);
        patient.setUpdate_time(currentTime);
        jcUserPatientDao.updatePatient(patient);
        return JsonFormat.retFormat(0, "修改就诊人信息成功");
    }

    public String getPatientInfo(JSONObject rcv) {
        String user_id = rcv.getString(USERID);
        //判断用户是否存在
        UC_UserInfo user = getUser(user_id);
        if(user == null) {
            return JsonFormat.retFormat(1,"账号未注册");
        }
        user_id = user.getUser_id();
        List<Patient> patients = jcUserPatientDao.getPatientByUser(user_id);
        if(patients == null) {
            return JsonFormat.retFormat(2, "该用户没有就诊人信息");
        }
        return JsonFormat.retFormat(0, "查询成功", JSONArray.fromObject(patients));
    }

    /**
     * 将传入的值set到对象
     */
    private Patient setPatient(Patient patient, JSONObject rcv) {
        if(rcv.containsKey("patient_name")) {
            patient.setPatient_name(rcv.getString("patient_name"));
        }
        if(rcv.containsKey("patient_sex")) {
            patient.setPatient_sex(rcv.getString("patient_sex"));
        }
        if(rcv.containsKey("patient_birthday")) {
            patient.setPatient_birthday(rcv.getString("patient_birthday"));
        }
        if(rcv.containsKey("patient_card_type")) {
            patient.setPatient_card_type(rcv.getString("patient_card_type"));
        }
        if(rcv.containsKey("patient_card_id")) {
            patient.setPatient_card_id(rcv.getString("patient_card_id"));
        }
        if(rcv.containsKey("patient_medicare")) {
           patient.setPatient_medicare(rcv.getString("patient_medicare"));
        }
        if(rcv.containsKey("patient_medicare_id")) {
            patient.setPatient_medicare_id(rcv.getString("patient_medicare_id"));
        }
        if(rcv.containsKey("patient_city_id")) {
            patient.setPatient_city_id(rcv.getString("patient_city_id"));
        }
        if(rcv.containsKey("patient_address")) {
            patient.setPatient_address(rcv.getString("patient_address"));
        }
        if(rcv.containsKey("patient_w_chat")) {
            patient.setPatient_w_chat(rcv.getString("patient_w_chat"));
        }
        if(rcv.containsKey("patient_mobile")) {
            patient.setPatient_mobile(rcv.getString("patient_mobile"));
        }
        if(rcv.containsKey("patient_e_mail")) {
            patient.setPatient_e_mail(rcv.getString("patient_e_mail"));
        }
        return patient;
    }
}
