package com.ucmed.controller;

import com.ucmed.authc.AuthenticationService;
import com.ucmed.authc.EnumProductKey;
import com.ucmed.bean.SimpleToken;
import com.ucmed.bean.SimpleTokenParam;
import com.ucmed.bean.UCResponse;
import com.ucmed.bean.UC_UserInfo;
import io.swagger.annotations.*;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

@Controller
@RequestMapping(value = "/authentication")
@Api(value = "鉴权", description = "鉴权接口")
public class AuthenticationController {
    @Autowired
    @Qualifier("userCenterAuthenticationServiceImpl")
    AuthenticationService authenticationService;

    @ApiOperation(
            value = "插入信息",
            notes = "插入信息\n"
    )
    @RequestMapping(value = "/init", method = RequestMethod.POST)
    public ResponseEntity<String> init(@RequestBody SimpleTokenParam simpleTokenParam) {
        try {
            SimpleToken simpleToken = new SimpleToken(simpleTokenParam.getTokenKey(), EnumProductKey.valueOf(simpleTokenParam.getProductKey()), simpleTokenParam.getValue());
            simpleToken.setTimeOut(simpleTokenParam.getTimeOut());
            authenticationService.init(simpleToken);
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(0, "操作成功")).toString();
            return createResponseEntity(responseJson);
        } catch (Exception e) {
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(-1, "操作失败", e.getMessage())).toString();
            return createResponseEntity(responseJson);
        }
    }

    @ApiOperation(
            value = "获取信息",
            notes = "获取信息\n"
    )
    @RequestMapping(value = "/authenticate", method = RequestMethod.POST)
    public ResponseEntity<String> authenticate(@RequestBody SimpleTokenParam simpleTokenParam) {
        try {
            SimpleToken simpleToken = new SimpleToken(simpleTokenParam.getTokenKey(), EnumProductKey.valueOf(simpleTokenParam.getProductKey()), simpleTokenParam.getValue());
            simpleToken.setTimeOut(simpleTokenParam.getTimeOut());
            Object authenticate = authenticationService.authenticate(simpleToken);
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(0, "操作成功", authenticate)).toString();
            return createResponseEntity(responseJson);
        } catch (Exception e) {
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(-1, "操作失败", e.getMessage())).toString();
            return createResponseEntity(responseJson);
        }
    }


    @ApiOperation(
            value = "更新",
            notes = "更新\n"
    )
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public ResponseEntity<String> update(@RequestBody SimpleTokenParam simpleTokenParam) {
        try {
            SimpleToken simpleToken = new SimpleToken(simpleTokenParam.getTokenKey(), EnumProductKey.valueOf(simpleTokenParam.getProductKey()), simpleTokenParam.getValue());
            simpleToken.setTimeOut(simpleTokenParam.getTimeOut());
            authenticationService.update(simpleToken);
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(0, "操作成功")).toString();
            return createResponseEntity(responseJson);
        } catch (Exception e) {
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(-1, "操作失败", e.getMessage())).toString();
            return createResponseEntity(responseJson);
        }
    }

    @ApiOperation(
            value = "保存",
            notes = "保存\n"
    )
    @RequestMapping(value = "/save", method = RequestMethod.POST)
    public ResponseEntity<String> save(@RequestBody SimpleTokenParam simpleTokenParam) {
        try {
            SimpleToken simpleToken = new SimpleToken(simpleTokenParam.getTokenKey(), EnumProductKey.valueOf(simpleTokenParam.getProductKey()), simpleTokenParam.getValue());
            simpleToken.setTimeOut(simpleTokenParam.getTimeOut());
            authenticationService.save(simpleToken);
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(0, "操作成功")).toString();
            return createResponseEntity(responseJson);
        } catch (Exception e) {
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(-1, "操作失败", e.getMessage())).toString();
            return createResponseEntity(responseJson);
        }
    }

    @ApiOperation(
            value = "获取token用户中心信息",
            notes = "获取token用户中心信息\n"
    )
    @RequestMapping(value = "/authenticateToken", method = RequestMethod.POST)
    public ResponseEntity<String> authenticateToken(String token) {
        try {
            Object authenticate = authenticationService.authenticate(token);
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(0, "操作成功", authenticate)).toString();
            return createResponseEntity(responseJson);
        } catch (Exception e) {
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(-1, "操作失败", e.getMessage())).toString();
            return createResponseEntity(responseJson);
        }
    }

    @ApiOperation(
            value = "清除",
            notes = "清除\n"
    )
    @RequestMapping(value = "/clear", method = RequestMethod.POST)
    public ResponseEntity<String> clear(@RequestBody SimpleTokenParam simpleTokenParam) {
        try {
            SimpleToken simpleToken = new SimpleToken(simpleTokenParam.getTokenKey(), EnumProductKey.valueOf(simpleTokenParam.getProductKey()), simpleTokenParam.getValue());
            simpleToken.setTimeOut(simpleTokenParam.getTimeOut());
            authenticationService.clear(simpleToken);
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(0, "操作成功")).toString();
            return createResponseEntity(responseJson);
        } catch (Exception e) {
            String responseJson = null;
            responseJson = JSONObject.fromObject(new UCResponse(-1, "操作失败", e.getMessage())).toString();
            return createResponseEntity(responseJson);
        }
    }

//    @ApiOperation(
//            value = "注册？？？？",
//            notes = "注册？？？？\n"
//    )
//    @RequestMapping(value = "/registration", method = RequestMethod.POST)
//    public ResponseEntity<String> clear(@RequestBody UC_UserInfo uc_userInfo) {
//        try {
//            String registration = authenticationService.registration(uc_userInfo);
//            String responseJson = null;
//            responseJson = JSONObject.fromObject(new UCResponse(0, "操作成功", registration)).toString();
//            return createResponseEntity(responseJson);
//        } catch (Exception e) {
//            String responseJson = null;
//            responseJson = JSONObject.fromObject(new UCResponse(-1, "操作失败", e.getMessage())).toString();
//            return createResponseEntity(responseJson);
//        }
//    }


    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }


}
