#------------------------HttpConnection配置----------------
#最大连接数 
MAX_TOTAL_CONNECTIONS = 400
#获取连接的最大等待时间 
WAIT_TIMEOUT = 60000
#每个路由最大连接数
MAX_ROUTE_CONNECTIONS = 400
#连接超时时间
CONNECT_TIMEOUT = 10000
#读取超时时间
READ_TIMEOUT = 40000
#------------------------HttpConnection配置----------------
#------------------------短信请求配置-----------------------
#请求地址
#msg.url=http://localhost:8140/msg/send/
#msg.url=http://msg.zwjk.com:8888/api/exec/1.htm
msg.url=http://msg.config.zhuojian:8140/msg/send
#访问的key
msg.key=ZW5sNWVWOWhibVJ5YjJsaw==


Dubbo.properties.file=dubbo.properties
#需要特殊判断的角色
special_role = 医生

#设置连接总数
http.maxTotal=500
#设置每个主机最大的并发数
http.defaultMaxPerRoute=100
#设置创建连接的最长时间
http.connectTimeout=2000
#从连接池中获取到连接的最长时间
http.connectionRequestTimeout=500
#数据传输的最长时间
http.socketTimeout=6000
#空闲时间(用于定期清理空闲连接)
http.maxIdleTime = 1
# 移动远程第三方用户接口地址
ylxz.domain = http://ylxz.zwjk.com/ThirdPartyUserCenter/


# 短信配置
sms.account=zODxeVBh3uj2hQCSu1EIpw==
sms.password=sbqwyvP8ux58XAYQZlpFRaozONWwpab8O71htxEt6es=
sms.cm.host=**************
sms.cm.port=9080
sms.ws.host=**************
sms.ws.port=9070

# 测试手机号
testphone =

# 新platSms http url 配置
#platsms.http.url=http://localhost:8140/msg/send
#platsms.http.url=http://smsplat.zwjk.com:8888/sms/send/
platsms.http.url=http://msg.config.zhuojian:8140/msg/send

# 测试手机短信默认验证码
testSMSValdate=
# 短信验证码失效时间
SMSValdate_timeout=1800
# 短信验证码错误次数
SMSValdate_error_times=5

# 测试图形验证码
testValidateCode=
# 图形验证码失效时间
validateCodeExpireTime=1
# 图形验证码失效次数
validateCodeExpireCount=5

# 域名地址
host =https://hlwyy-xiangya.zwjk.com/yh/
cookie.domain= ucmed.cn
# rocketMQ
rocketmq.producerGroup = ucmed
rocketmq.namesrvAddr = uc-mq.config.zhuojian:9876
rocketmq.topic = userCenterUCMED
rocketmq.statistic.topic = userActiveStatistic



# B端+C端用户中心资源变量
BdUrl = http://************:8083
BdAppId = hzyhzx1417ff460e2f81be0d5f7b84006ebe3b
bcsmstype = "bc"


#万能验证码
master_msgcode = zmkm66

#用户验证码输入错误次数
pwd.errorLockTimes = 5

spring.application.name = userCenterUCMED-test
