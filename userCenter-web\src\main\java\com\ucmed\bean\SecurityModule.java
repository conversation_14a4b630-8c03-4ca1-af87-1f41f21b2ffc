package com.ucmed.bean;

import java.io.Serializable;

/**
 * Created by QIUJIAHAO on 2016/11/15.
 */
public class SecurityModule implements Serializable{

    private static final long serialVersionUID = -2472109333474769848L;
    private int module_id;
    private int app_code;
    private String module_name;
    private String module_url;
    private String module_desc;
    private int parent_id;
    private int is_leaf;
    private int valid;
    private String oper_date;
    private String oper_user;

    public int getModule_id() {
        return module_id;
    }

    public void setModule_id(int module_id) {
        this.module_id = module_id;
    }

    public int getApp_code() {
        return app_code;
    }

    public void setApp_code(int app_code) {
        this.app_code = app_code;
    }

    public String getModule_name() {
        return module_name;
    }

    public void setModule_name(String module_name) {
        this.module_name = module_name;
    }

    public String getModule_url() {
        return module_url;
    }

    public void setModule_url(String module_url) {
        this.module_url = module_url;
    }

    public String getModule_desc() {
        return module_desc;
    }

    public void setModule_desc(String module_desc) {
        this.module_desc = module_desc;
    }

    public String getOper_date() {
        return oper_date;
    }

    public void setOper_date(String oper_date) {
        this.oper_date = oper_date;
    }

    public String getOper_user() {
        return oper_user;
    }

    public void setOper_user(String oper_user) {
        this.oper_user = oper_user;
    }

    public int getParent_id() {
        return parent_id;
    }

    public void setParent_id(int parent_id) {
        this.parent_id = parent_id;
    }

    public int getIs_leaf() {
        return is_leaf;
    }

    public void setIs_leaf(int is_leaf) {
        this.is_leaf = is_leaf;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }
}
