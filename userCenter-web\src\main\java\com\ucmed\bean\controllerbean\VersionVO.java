package com.ucmed.bean.controllerbean;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/01/08 13:51
 */
@Data
public class VersionVO {
    @ApiModelProperty(value="主版本及次版本信息（如：v3.0.0）", name="version", required = true, example="0")
    private String version;

    @ApiModelProperty(value="Git提交id", name="submit_id", required = true, example="0")
    private String submit_id;

    @ApiModelProperty(value="项目id", name="update_time", required = true, example="0")
    private String update_time;

    @ApiModelProperty(value="项目id", name="about_url", required = true, example="0")
    private String about_url;

    @ApiModelProperty(value="产品名称", name="product_name", required = true, example="0")
    private String product_name;

    private Integer return_code;

    private String return_msg;

}
