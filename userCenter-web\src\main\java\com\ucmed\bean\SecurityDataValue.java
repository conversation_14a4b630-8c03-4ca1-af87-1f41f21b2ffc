package com.ucmed.bean;

import java.io.Serializable;

/**
 * Created by XXB-QJH-1303 on 2016/11/16.
 */
public class SecurityDataValue implements Serializable{
    private static final long serialVersionUID = -1911582308686303268L;
    private int datavalue_id;
    private int template_id;
    private int app_code;
    private String value;
    private String datavalue_desc;
    private String oper_date;
    private String oper_user;
    private int valid;

    public int getDatavalue_id() {
        return datavalue_id;
    }

    public void setDatavalue_id(int datavalue_id) {
        this.datavalue_id = datavalue_id;
    }

    public int getApp_code() {
        return app_code;
    }

    public void setApp_code(int app_code) {
        this.app_code = app_code;
    }

    public String getValue() {
        return value;
    }

    public void setValue(String value) {
        this.value = value;
    }

    public String getDatavalue_desc() {
        return datavalue_desc;
    }

    public void setDatavalue_desc(String datavalue_desc) {
        this.datavalue_desc = datavalue_desc;
    }

    public String getOper_date() {
        return oper_date;
    }

    public void setOper_date(String oper_date) {
        this.oper_date = oper_date;
    }

    public String getOper_user() {
        return oper_user;
    }

    public void setOper_user(String oper_user) {
        this.oper_user = oper_user;
    }

    public int getValid() {
        return valid;
    }

    public void setValid(int valid) {
        this.valid = valid;
    }

    public int getTemplate_id() {
        return template_id;
    }

    public void setTemplate_id(int template_id) {
        this.template_id = template_id;
    }
}
