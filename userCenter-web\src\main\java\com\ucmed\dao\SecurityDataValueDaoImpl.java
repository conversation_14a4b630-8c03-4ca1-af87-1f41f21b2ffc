package com.ucmed.dao;

import com.ucmed.bean.SecurityDataValue;
import com.ucmed.common.dao.BaseDaoImpl;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

import static com.ucmed.common.constant.CommonConstant.DATAVALUEID;
import static com.ucmed.common.constant.TableName.SECURITYDATAVALUE;
import static com.ucmed.common.constant.TableName.SECURITYMODULE;
import static com.ucmed.common.constant.TableName.SECURITYROLEDATAVALUE;

/**
 * Created by QIUJIAHAO on 2016/11/15.
 */
@Repository
public class SecurityDataValueDaoImpl extends BaseDaoImpl implements SecurityDataValueDao {

    @Override
    public List<SecurityDataValue> getDataValueByAppCode(int appCode) {
        String sql = "SELECT datavalue_id,template_id,app_code,value,datavalue_desc,valid FROM " + SECURITYDATAVALUE + " WHERE app_code = ?";

        List<SecurityDataValue> result = getJdbcTemplate().query(sql, new Object[]{appCode}, new BeanPropertyRowMapper<>(SecurityDataValue.class));
        if(result.size() == 0)
            return null;
        return result;
    }

    @Override
    public SecurityDataValue getDataValueById(int dataValueId) {
        String sql = "SELECT datavalue_id,template_id,app_code,value,datavalue_desc,valid " +
                "FROM " + SECURITYDATAVALUE + " " +
                "WHERE datavalue_id = ?";

        List<SecurityDataValue> result = getJdbcTemplate().query(sql, new Object[]{dataValueId}, new BeanPropertyRowMapper<>(SecurityDataValue.class));
        if(result.size() == 0)
            return null;
        return result.get(0);
    }

    @Override
    public int updateDataValue(final SecurityDataValue dataValue) {
        String sql = "UPDATE " + SECURITYDATAVALUE + " SET "
                + "app_code = ?,value = ?,datavalue_desc = ?,oper_date = ?,valid = ? "
                + "WHERE datavalue_id = ?";
        try {
            return getJdbcTemplate().update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setInt(1, dataValue.getApp_code());
                    preparedStatement.setString(2, dataValue.getValue());
                    preparedStatement.setString(3, dataValue.getDatavalue_desc());
                    preparedStatement.setString(4, dataValue.getOper_date());
                    preparedStatement.setInt(5, dataValue.getValid());
                    preparedStatement.setInt(6, dataValue.getDatavalue_id());
                }
            });
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public int addDataValue(final SecurityDataValue dataValue) {
        final String sql = "INSERT INTO " + SECURITYDATAVALUE + "(app_code, value, datavalue_desc, oper_date) " +
                "VALUES(?, ?, ?, ?)";
        KeyHolder keyHolder = new GeneratedKeyHolder();
        getJdbcTemplate().update(new PreparedStatementCreator() {
            @Override
            public PreparedStatement createPreparedStatement(Connection connection) throws SQLException {
                PreparedStatement ps = connection.prepareStatement(sql, PreparedStatement.RETURN_GENERATED_KEYS);
                ps.setInt(1, dataValue.getApp_code());
                ps.setString(2, dataValue.getValue());
                ps.setString(3, dataValue.getDatavalue_desc());
                ps.setString(4, dataValue.getOper_date());
                return ps;
            }
        }, keyHolder);
        return (int)keyHolder.getKeys().get(DATAVALUEID);
    }

    @Override
    public int deleteDataValueById(int dataValueId) {
        String sql = "DELETE FROM " + SECURITYDATAVALUE + " WHERE datavalue_id = ?";
        try {
            return getJdbcTemplate().update(sql, new Object[]{dataValueId});
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public int addDataValueToRole(final int roleId, final int dataValueId, final int appCode, final String time) {
        String sql = "INSERT INTO " + SECURITYROLEDATAVALUE + "(role_id,datavalue_id,valid,oper_user,oper_date) VALUES(?,?,?,?,?)";
        try {
            return getJdbcTemplate().update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setInt(1, roleId);
                    preparedStatement.setInt(2, dataValueId);
                    preparedStatement.setInt(3, 1);
                    preparedStatement.setString(4, String.valueOf(appCode));
                    preparedStatement.setString(5, time);
                }
            });
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public int removeDataValueRromRole(int roleId, int dataValueId) {
        String sql = "DELETE FROM " + SECURITYROLEDATAVALUE + " WHERE role_id = ? AND datavalue_id = ?";
        return getJdbcTemplate().update(sql, new Object[]{roleId, dataValueId});
    }
}
