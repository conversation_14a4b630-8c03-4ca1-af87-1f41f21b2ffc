<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:tx="http://www.springframework.org/schema/tx"
       xsi:schemaLocation="http://www.springframework.org/schema/beans http://www.springframework.org/schema/beans/spring-beans.xsd http://www.springframework.org/schema/tx http://www.springframework.org/schema/tx/spring-tx.xsd">

    <!--Spring & MYBATIS -->
    <bean id="mybatisSqlSessionFactory" class="org.mybatis.spring.SqlSessionFactoryBean">
        <property name="dataSource" ref="dataSource"/>
        <property name="configLocation" value="classpath:META-INF/spring/mybatis/mybatis-config.xml"/>
        <!--<property name="mapperLocations">-->
            <!--<array>-->
                <!--<value>classpath*:META-INF/xml/*Mapper.xml</value>-->
            <!--</array>-->
        <!--</property>-->
        <!--<property name="plugins">-->
            <!--<array>-->
                <!--<bean class="com.github.pagehelper.PageHelper">-->
                    <!--&lt;!&ndash; 这里的几个配置主要演示如何使用，如果不理解，一定要去掉下面的配置 &ndash;&gt;-->
                    <!--<property name="properties">-->
                        <!--<value>-->
                            <!--dialect=PostgreSQL-->
                            <!--reasonable=true-->
                            <!--autoRuntimeDialect=true-->
                        <!--</value>-->
                    <!--</property>-->
                <!--</bean>-->
            <!--</array>-->
        <!--</property>-->
    </bean>

    <bean class="tk.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.ucmed"/>
        <!-- 3.2.2版本新特性，markerInterface可以起到mappers配置的作用，详细情况需要看Marker接口类 -->
        <property name="markerInterface" value="tk.mybatis.mapper.common.Mapper"/>
        <property name="sqlSessionFactoryBeanName" value="mybatisSqlSessionFactory"/>

        <!-- 通用Mapper通过属性注入进行配置，默认不配置时会注册Mapper<T>接口
        <property name="properties">
            <value>
                mappers=tk.mybatis.mapper.common.Mapper
            </value>
        </property>
        -->
    </bean>

    <!--Transaction Manager -->
    <bean id="mybatisTransactionManager"
          class="org.springframework.jdbc.datasource.DataSourceTransactionManager">
        <property name="dataSource" ref="dataSource"/>
    </bean>

    <tx:annotation-driven transaction-manager="mybatisTransactionManager"/>

    <!-- Mapper接口所在包名，Spring会自动查找其下的Mapper -->
    <bean id="mapperScannerConfigurer" class="org.mybatis.spring.mapper.MapperScannerConfigurer">
        <property name="basePackage" value="com.ucmed.mapper"/>
        <property name="sqlSessionFactoryBeanName" value="mybatisSqlSessionFactory"/>
    </bean>

</beans>