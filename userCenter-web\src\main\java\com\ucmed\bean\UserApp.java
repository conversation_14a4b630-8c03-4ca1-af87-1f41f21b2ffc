package com.ucmed.bean;

import org.apache.commons.lang.StringUtils;

import java.io.Serializable;

/**
 * Created by XXB-QJH-1303 on 2017/6/13.
 */
public class UserApp implements Serializable{

    private static final long serialVersionUID = -6246920850586397515L;

    private int tId;
    private int appCode;
    private String userId;
    private String operDate;
    private String openId;

    public int gettId() {
        return tId;
    }

    public void settId(int tId) {
        this.tId = tId;
    }

    public int getAppCode() {
        return appCode;
    }
    public void setAppCode(int appCode) {
        this.appCode = appCode;
    }

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getOperDate() {
        return operDate;
    }

    public void setOperDate(String operDate) {
        this.operDate = operDate;
    }

    public String getOpenId() {
        return openId;
    }

    public void setOpenId(String openId) {
        this.openId = openId;
    }
}
