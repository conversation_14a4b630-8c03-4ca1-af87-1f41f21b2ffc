package com.ucmed.bean;

import java.util.ArrayList;
import java.util.List;

public class JcUserExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public JcUserExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andUidIsNull() {
            addCriterion("uid is null");
            return (Criteria) this;
        }

        public Criteria andUidIsNotNull() {
            addCriterion("uid is not null");
            return (Criteria) this;
        }

        public Criteria andUidEqualTo(Integer value) {
            addCriterion("uid =", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotEqualTo(Integer value) {
            addCriterion("uid <>", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThan(Integer value) {
            addCriterion("uid >", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidGreaterThanOrEqualTo(Integer value) {
            addCriterion("uid >=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThan(Integer value) {
            addCriterion("uid <", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidLessThanOrEqualTo(Integer value) {
            addCriterion("uid <=", value, "uid");
            return (Criteria) this;
        }

        public Criteria andUidIn(List<Integer> values) {
            addCriterion("uid in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotIn(List<Integer> values) {
            addCriterion("uid not in", values, "uid");
            return (Criteria) this;
        }

        public Criteria andUidBetween(Integer value1, Integer value2) {
            addCriterion("uid between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andUidNotBetween(Integer value1, Integer value2) {
            addCriterion("uid not between", value1, value2, "uid");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNull() {
            addCriterion("user_id is null");
            return (Criteria) this;
        }

        public Criteria andUserIdIsNotNull() {
            addCriterion("user_id is not null");
            return (Criteria) this;
        }

        public Criteria andUserIdEqualTo(String value) {
            addCriterion("user_id =", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotEqualTo(String value) {
            addCriterion("user_id <>", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThan(String value) {
            addCriterion("user_id >", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdGreaterThanOrEqualTo(String value) {
            addCriterion("user_id >=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThan(String value) {
            addCriterion("user_id <", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLessThanOrEqualTo(String value) {
            addCriterion("user_id <=", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdLike(String value) {
            addCriterion("user_id like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotLike(String value) {
            addCriterion("user_id not like", value, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdIn(List<String> values) {
            addCriterion("user_id in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotIn(List<String> values) {
            addCriterion("user_id not in", values, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdBetween(String value1, String value2) {
            addCriterion("user_id between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andUserIdNotBetween(String value1, String value2) {
            addCriterion("user_id not between", value1, value2, "userId");
            return (Criteria) this;
        }

        public Criteria andPasswordIsNull() {
            addCriterion("password is null");
            return (Criteria) this;
        }

        public Criteria andPasswordIsNotNull() {
            addCriterion("password is not null");
            return (Criteria) this;
        }

        public Criteria andPasswordEqualTo(String value) {
            addCriterion("password =", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotEqualTo(String value) {
            addCriterion("password <>", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordGreaterThan(String value) {
            addCriterion("password >", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordGreaterThanOrEqualTo(String value) {
            addCriterion("password >=", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLessThan(String value) {
            addCriterion("password <", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLessThanOrEqualTo(String value) {
            addCriterion("password <=", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordLike(String value) {
            addCriterion("password like", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotLike(String value) {
            addCriterion("password not like", value, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordIn(List<String> values) {
            addCriterion("password in", values, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotIn(List<String> values) {
            addCriterion("password not in", values, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordBetween(String value1, String value2) {
            addCriterion("password between", value1, value2, "password");
            return (Criteria) this;
        }

        public Criteria andPasswordNotBetween(String value1, String value2) {
            addCriterion("password not between", value1, value2, "password");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andLatesttimeIsNull() {
            addCriterion("latesttime is null");
            return (Criteria) this;
        }

        public Criteria andLatesttimeIsNotNull() {
            addCriterion("latesttime is not null");
            return (Criteria) this;
        }

        public Criteria andLatesttimeEqualTo(String value) {
            addCriterion("latesttime =", value, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeNotEqualTo(String value) {
            addCriterion("latesttime <>", value, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeGreaterThan(String value) {
            addCriterion("latesttime >", value, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeGreaterThanOrEqualTo(String value) {
            addCriterion("latesttime >=", value, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeLessThan(String value) {
            addCriterion("latesttime <", value, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeLessThanOrEqualTo(String value) {
            addCriterion("latesttime <=", value, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeLike(String value) {
            addCriterion("latesttime like", value, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeNotLike(String value) {
            addCriterion("latesttime not like", value, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeIn(List<String> values) {
            addCriterion("latesttime in", values, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeNotIn(List<String> values) {
            addCriterion("latesttime not in", values, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeBetween(String value1, String value2) {
            addCriterion("latesttime between", value1, value2, "latesttime");
            return (Criteria) this;
        }

        public Criteria andLatesttimeNotBetween(String value1, String value2) {
            addCriterion("latesttime not between", value1, value2, "latesttime");
            return (Criteria) this;
        }

        public Criteria andFailnumIsNull() {
            addCriterion("failnum is null");
            return (Criteria) this;
        }

        public Criteria andFailnumIsNotNull() {
            addCriterion("failnum is not null");
            return (Criteria) this;
        }

        public Criteria andFailnumEqualTo(Integer value) {
            addCriterion("failnum =", value, "failnum");
            return (Criteria) this;
        }

        public Criteria andFailnumNotEqualTo(Integer value) {
            addCriterion("failnum <>", value, "failnum");
            return (Criteria) this;
        }

        public Criteria andFailnumGreaterThan(Integer value) {
            addCriterion("failnum >", value, "failnum");
            return (Criteria) this;
        }

        public Criteria andFailnumGreaterThanOrEqualTo(Integer value) {
            addCriterion("failnum >=", value, "failnum");
            return (Criteria) this;
        }

        public Criteria andFailnumLessThan(Integer value) {
            addCriterion("failnum <", value, "failnum");
            return (Criteria) this;
        }

        public Criteria andFailnumLessThanOrEqualTo(Integer value) {
            addCriterion("failnum <=", value, "failnum");
            return (Criteria) this;
        }

        public Criteria andFailnumIn(List<Integer> values) {
            addCriterion("failnum in", values, "failnum");
            return (Criteria) this;
        }

        public Criteria andFailnumNotIn(List<Integer> values) {
            addCriterion("failnum not in", values, "failnum");
            return (Criteria) this;
        }

        public Criteria andFailnumBetween(Integer value1, Integer value2) {
            addCriterion("failnum between", value1, value2, "failnum");
            return (Criteria) this;
        }

        public Criteria andFailnumNotBetween(Integer value1, Integer value2) {
            addCriterion("failnum not between", value1, value2, "failnum");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNull() {
            addCriterion("create_time is null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIsNotNull() {
            addCriterion("create_time is not null");
            return (Criteria) this;
        }

        public Criteria andCreateTimeEqualTo(String value) {
            addCriterion("create_time =", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotEqualTo(String value) {
            addCriterion("create_time <>", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThan(String value) {
            addCriterion("create_time >", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeGreaterThanOrEqualTo(String value) {
            addCriterion("create_time >=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThan(String value) {
            addCriterion("create_time <", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLessThanOrEqualTo(String value) {
            addCriterion("create_time <=", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeLike(String value) {
            addCriterion("create_time like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotLike(String value) {
            addCriterion("create_time not like", value, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeIn(List<String> values) {
            addCriterion("create_time in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotIn(List<String> values) {
            addCriterion("create_time not in", values, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeBetween(String value1, String value2) {
            addCriterion("create_time between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andCreateTimeNotBetween(String value1, String value2) {
            addCriterion("create_time not between", value1, value2, "createTime");
            return (Criteria) this;
        }

        public Criteria andLoginTimesIsNull() {
            addCriterion("login_times is null");
            return (Criteria) this;
        }

        public Criteria andLoginTimesIsNotNull() {
            addCriterion("login_times is not null");
            return (Criteria) this;
        }

        public Criteria andLoginTimesEqualTo(Integer value) {
            addCriterion("login_times =", value, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andLoginTimesNotEqualTo(Integer value) {
            addCriterion("login_times <>", value, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andLoginTimesGreaterThan(Integer value) {
            addCriterion("login_times >", value, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andLoginTimesGreaterThanOrEqualTo(Integer value) {
            addCriterion("login_times >=", value, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andLoginTimesLessThan(Integer value) {
            addCriterion("login_times <", value, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andLoginTimesLessThanOrEqualTo(Integer value) {
            addCriterion("login_times <=", value, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andLoginTimesIn(List<Integer> values) {
            addCriterion("login_times in", values, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andLoginTimesNotIn(List<Integer> values) {
            addCriterion("login_times not in", values, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andLoginTimesBetween(Integer value1, Integer value2) {
            addCriterion("login_times between", value1, value2, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andLoginTimesNotBetween(Integer value1, Integer value2) {
            addCriterion("login_times not between", value1, value2, "loginTimes");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeIsNull() {
            addCriterion("pass_change_time is null");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeIsNotNull() {
            addCriterion("pass_change_time is not null");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeEqualTo(String value) {
            addCriterion("pass_change_time =", value, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeNotEqualTo(String value) {
            addCriterion("pass_change_time <>", value, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeGreaterThan(String value) {
            addCriterion("pass_change_time >", value, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeGreaterThanOrEqualTo(String value) {
            addCriterion("pass_change_time >=", value, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeLessThan(String value) {
            addCriterion("pass_change_time <", value, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeLessThanOrEqualTo(String value) {
            addCriterion("pass_change_time <=", value, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeLike(String value) {
            addCriterion("pass_change_time like", value, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeNotLike(String value) {
            addCriterion("pass_change_time not like", value, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeIn(List<String> values) {
            addCriterion("pass_change_time in", values, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeNotIn(List<String> values) {
            addCriterion("pass_change_time not in", values, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeBetween(String value1, String value2) {
            addCriterion("pass_change_time between", value1, value2, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andPassChangeTimeNotBetween(String value1, String value2) {
            addCriterion("pass_change_time not between", value1, value2, "passChangeTime");
            return (Criteria) this;
        }

        public Criteria andLockIsNull() {
            addCriterion("lock is null");
            return (Criteria) this;
        }

        public Criteria andLockIsNotNull() {
            addCriterion("lock is not null");
            return (Criteria) this;
        }

        public Criteria andLockEqualTo(String value) {
            addCriterion("lock =", value, "lock");
            return (Criteria) this;
        }

        public Criteria andLockNotEqualTo(String value) {
            addCriterion("lock <>", value, "lock");
            return (Criteria) this;
        }

        public Criteria andLockGreaterThan(String value) {
            addCriterion("lock >", value, "lock");
            return (Criteria) this;
        }

        public Criteria andLockGreaterThanOrEqualTo(String value) {
            addCriterion("lock >=", value, "lock");
            return (Criteria) this;
        }

        public Criteria andLockLessThan(String value) {
            addCriterion("lock <", value, "lock");
            return (Criteria) this;
        }

        public Criteria andLockLessThanOrEqualTo(String value) {
            addCriterion("lock <=", value, "lock");
            return (Criteria) this;
        }

        public Criteria andLockLike(String value) {
            addCriterion("lock like", value, "lock");
            return (Criteria) this;
        }

        public Criteria andLockNotLike(String value) {
            addCriterion("lock not like", value, "lock");
            return (Criteria) this;
        }

        public Criteria andLockIn(List<String> values) {
            addCriterion("lock in", values, "lock");
            return (Criteria) this;
        }

        public Criteria andLockNotIn(List<String> values) {
            addCriterion("lock not in", values, "lock");
            return (Criteria) this;
        }

        public Criteria andLockBetween(String value1, String value2) {
            addCriterion("lock between", value1, value2, "lock");
            return (Criteria) this;
        }

        public Criteria andLockNotBetween(String value1, String value2) {
            addCriterion("lock not between", value1, value2, "lock");
            return (Criteria) this;
        }

        public Criteria andTokenIsNull() {
            addCriterion("token is null");
            return (Criteria) this;
        }

        public Criteria andTokenIsNotNull() {
            addCriterion("token is not null");
            return (Criteria) this;
        }

        public Criteria andTokenEqualTo(String value) {
            addCriterion("token =", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotEqualTo(String value) {
            addCriterion("token <>", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenGreaterThan(String value) {
            addCriterion("token >", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenGreaterThanOrEqualTo(String value) {
            addCriterion("token >=", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenLessThan(String value) {
            addCriterion("token <", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenLessThanOrEqualTo(String value) {
            addCriterion("token <=", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenLike(String value) {
            addCriterion("token like", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotLike(String value) {
            addCriterion("token not like", value, "token");
            return (Criteria) this;
        }

        public Criteria andTokenIn(List<String> values) {
            addCriterion("token in", values, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotIn(List<String> values) {
            addCriterion("token not in", values, "token");
            return (Criteria) this;
        }

        public Criteria andTokenBetween(String value1, String value2) {
            addCriterion("token between", value1, value2, "token");
            return (Criteria) this;
        }

        public Criteria andTokenNotBetween(String value1, String value2) {
            addCriterion("token not between", value1, value2, "token");
            return (Criteria) this;
        }

        public Criteria andTokenTimeIsNull() {
            addCriterion("token_time is null");
            return (Criteria) this;
        }

        public Criteria andTokenTimeIsNotNull() {
            addCriterion("token_time is not null");
            return (Criteria) this;
        }

        public Criteria andTokenTimeEqualTo(String value) {
            addCriterion("token_time =", value, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeNotEqualTo(String value) {
            addCriterion("token_time <>", value, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeGreaterThan(String value) {
            addCriterion("token_time >", value, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeGreaterThanOrEqualTo(String value) {
            addCriterion("token_time >=", value, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeLessThan(String value) {
            addCriterion("token_time <", value, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeLessThanOrEqualTo(String value) {
            addCriterion("token_time <=", value, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeLike(String value) {
            addCriterion("token_time like", value, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeNotLike(String value) {
            addCriterion("token_time not like", value, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeIn(List<String> values) {
            addCriterion("token_time in", values, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeNotIn(List<String> values) {
            addCriterion("token_time not in", values, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeBetween(String value1, String value2) {
            addCriterion("token_time between", value1, value2, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andTokenTimeNotBetween(String value1, String value2) {
            addCriterion("token_time not between", value1, value2, "tokenTime");
            return (Criteria) this;
        }

        public Criteria andWechatIdIsNull() {
            addCriterion("wechat_id is null");
            return (Criteria) this;
        }

        public Criteria andWechatIdIsNotNull() {
            addCriterion("wechat_id is not null");
            return (Criteria) this;
        }

        public Criteria andWechatIdEqualTo(String value) {
            addCriterion("wechat_id =", value, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdNotEqualTo(String value) {
            addCriterion("wechat_id <>", value, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdGreaterThan(String value) {
            addCriterion("wechat_id >", value, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdGreaterThanOrEqualTo(String value) {
            addCriterion("wechat_id >=", value, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdLessThan(String value) {
            addCriterion("wechat_id <", value, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdLessThanOrEqualTo(String value) {
            addCriterion("wechat_id <=", value, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdLike(String value) {
            addCriterion("wechat_id like", value, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdNotLike(String value) {
            addCriterion("wechat_id not like", value, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdIn(List<String> values) {
            addCriterion("wechat_id in", values, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdNotIn(List<String> values) {
            addCriterion("wechat_id not in", values, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdBetween(String value1, String value2) {
            addCriterion("wechat_id between", value1, value2, "wechatId");
            return (Criteria) this;
        }

        public Criteria andWechatIdNotBetween(String value1, String value2) {
            addCriterion("wechat_id not between", value1, value2, "wechatId");
            return (Criteria) this;
        }

        public Criteria andValidIsNull() {
            addCriterion("valid is null");
            return (Criteria) this;
        }

        public Criteria andValidIsNotNull() {
            addCriterion("valid is not null");
            return (Criteria) this;
        }

        public Criteria andValidEqualTo(Integer value) {
            addCriterion("valid =", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidNotEqualTo(Integer value) {
            addCriterion("valid <>", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidGreaterThan(Integer value) {
            addCriterion("valid >", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidGreaterThanOrEqualTo(Integer value) {
            addCriterion("valid >=", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidLessThan(Integer value) {
            addCriterion("valid <", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidLessThanOrEqualTo(Integer value) {
            addCriterion("valid <=", value, "valid");
            return (Criteria) this;
        }

        public Criteria andValidIn(List<Integer> values) {
            addCriterion("valid in", values, "valid");
            return (Criteria) this;
        }

        public Criteria andValidNotIn(List<Integer> values) {
            addCriterion("valid not in", values, "valid");
            return (Criteria) this;
        }

        public Criteria andValidBetween(Integer value1, Integer value2) {
            addCriterion("valid between", value1, value2, "valid");
            return (Criteria) this;
        }

        public Criteria andValidNotBetween(Integer value1, Integer value2) {
            addCriterion("valid not between", value1, value2, "valid");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyIsNull() {
            addCriterion("securitykey is null");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyIsNotNull() {
            addCriterion("securitykey is not null");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyEqualTo(String value) {
            addCriterion("securitykey =", value, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyNotEqualTo(String value) {
            addCriterion("securitykey <>", value, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyGreaterThan(String value) {
            addCriterion("securitykey >", value, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyGreaterThanOrEqualTo(String value) {
            addCriterion("securitykey >=", value, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyLessThan(String value) {
            addCriterion("securitykey <", value, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyLessThanOrEqualTo(String value) {
            addCriterion("securitykey <=", value, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyLike(String value) {
            addCriterion("securitykey like", value, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyNotLike(String value) {
            addCriterion("securitykey not like", value, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyIn(List<String> values) {
            addCriterion("securitykey in", values, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyNotIn(List<String> values) {
            addCriterion("securitykey not in", values, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyBetween(String value1, String value2) {
            addCriterion("securitykey between", value1, value2, "securitykey");
            return (Criteria) this;
        }

        public Criteria andSecuritykeyNotBetween(String value1, String value2) {
            addCriterion("securitykey not between", value1, value2, "securitykey");
            return (Criteria) this;
        }

        public Criteria andUcmedIdIsNull() {
            addCriterion("ucmed_id is null");
            return (Criteria) this;
        }

        public Criteria andUcmedIdIsNotNull() {
            addCriterion("ucmed_id is not null");
            return (Criteria) this;
        }

        public Criteria andUcmedIdEqualTo(String value) {
            addCriterion("ucmed_id =", value, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdNotEqualTo(String value) {
            addCriterion("ucmed_id <>", value, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdGreaterThan(String value) {
            addCriterion("ucmed_id >", value, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdGreaterThanOrEqualTo(String value) {
            addCriterion("ucmed_id >=", value, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdLessThan(String value) {
            addCriterion("ucmed_id <", value, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdLessThanOrEqualTo(String value) {
            addCriterion("ucmed_id <=", value, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdLike(String value) {
            addCriterion("ucmed_id like", value, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdNotLike(String value) {
            addCriterion("ucmed_id not like", value, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdIn(List<String> values) {
            addCriterion("ucmed_id in", values, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdNotIn(List<String> values) {
            addCriterion("ucmed_id not in", values, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdBetween(String value1, String value2) {
            addCriterion("ucmed_id between", value1, value2, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andUcmedIdNotBetween(String value1, String value2) {
            addCriterion("ucmed_id not between", value1, value2, "ucmedId");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNull() {
            addCriterion("description is null");
            return (Criteria) this;
        }

        public Criteria andDescriptionIsNotNull() {
            addCriterion("description is not null");
            return (Criteria) this;
        }

        public Criteria andDescriptionEqualTo(String value) {
            addCriterion("description =", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotEqualTo(String value) {
            addCriterion("description <>", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThan(String value) {
            addCriterion("description >", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionGreaterThanOrEqualTo(String value) {
            addCriterion("description >=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThan(String value) {
            addCriterion("description <", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLessThanOrEqualTo(String value) {
            addCriterion("description <=", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionLike(String value) {
            addCriterion("description like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotLike(String value) {
            addCriterion("description not like", value, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionIn(List<String> values) {
            addCriterion("description in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotIn(List<String> values) {
            addCriterion("description not in", values, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionBetween(String value1, String value2) {
            addCriterion("description between", value1, value2, "description");
            return (Criteria) this;
        }

        public Criteria andDescriptionNotBetween(String value1, String value2) {
            addCriterion("description not between", value1, value2, "description");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}