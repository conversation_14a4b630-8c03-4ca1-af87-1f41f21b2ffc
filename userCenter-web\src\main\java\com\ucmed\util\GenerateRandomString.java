package com.ucmed.util;

import java.util.Random;

/**
 * Created by HUANGYIXIN on 2016/7/22.
 */
public class GenerateRandomString {

    public static String generateRandomString(String head, int length){
        String base = "0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        String time = TimeUtil.getTime();
        time = time.substring(2, time.length());
        sb.append(head).append(time);
        for(int i = 0; i < length; i++){
            int index = random.nextInt(base.length());
            sb.append(base.charAt(index));
        }
        return sb.toString();
    }
}
