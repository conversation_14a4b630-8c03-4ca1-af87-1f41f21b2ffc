<?xml version="1.0" encoding="UTF-8"?>

<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
	http://www.springframework.org/schema/beans/spring-beans.xsd">

    <bean id="rocketmqProducer" class="org.apache.rocketmq.client.producer.DefaultMQProducer" init-method="start"
          destroy-method="shutdown">
        <property name="producerGroup" value="${rocketmq.producerGroup}"/>
        <property name="namesrvAddr" value="${rocketmq.namesrvAddr}"/>
        <property name="sendMessageWithVIPChannel" value="false"/>
    </bean>
</beans>
