package com.ucmed.dao;

import java.util.List;

import com.ucmed.bean.AppAndRole;
import com.ucmed.bean.Application;
import com.ucmed.bean.Role;

public interface GetAppInfoDao {
	/**
	 * 获取用户注册的应用程序信息
	 */
	List<Application> getApps(String user_id) ;
	
	/**
	 * 获取用户注册的角色信息
	 */
	List<AppAndRole> getRoles(String user_id);
	
	/**
	 * 获取某应用程序的所有角色信息
	 */
	List<Role> getRolesByApp(String app_code);
	
	/**
	 * 获取用户在某应用程序的所有角色信息
	 */
	List<Role> getRolesByAppAndUser(String app_code, String user_id);

    /**
     * 根据appcode查应用
     * @param app_code
     * @return
     */
    Application getAppByCode(int app_code);

    /**
     * 查询项目下的应用
     * @param projCode
     * @return
     */
	List<Application> getAppsByProjCode(int projCode);
}
