package com.ucmed.dao;

import com.ucmed.bean.Role;
import com.ucmed.common.dao.BaseDaoImpl;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.stereotype.Repository;

import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

import static com.ucmed.common.constant.TableName.SECURITYROLE;
import static com.ucmed.common.constant.TableName.SECURITYUSERROLE;

/**
 * security_role
 * Created by QIUJIAHAO on 2016/8/16.
 */
@Repository
public class SecurityRoleDaoImpl extends BaseDaoImpl implements SecurityRoleDao {

    public Role getRoleByRoleId(int roleId) {
        String sql = "SELECT "
                + "role_id,app_code,role_name,role_desc,valid,oper_user,oper_date "
                + "FROM " + SECURITYROLE + " "
                + "WHERE role_id = ?";
        List<Role> result = getJdbcTemplate().query(sql, new Object[]{roleId}, new BeanPropertyRowMapper<>(Role.class));
        if(result.size() == 0)
            return null;
        return result.get(0);
    }

    public int getRoleId(int appCode, String roleName) {
        String sql = "SELECT "
                + "role_id "
                + "FROM " + SECURITYROLE + " "
                + "WHERE app_code = ? and role_name = ?";
        List<Integer> list = getJdbcTemplate().queryForList(sql, new Object[]{appCode, roleName}, Integer.class);
        if(list.size() == 0) {
            return 0;
        }
        return list.get(0);
    }

    public int updateRole(final Role role) {
        String sql = "UPDATE " + SECURITYROLE + " SET "
                + "app_code = ?,role_name = ?,role_desc = ?,valid = ?,oper_user = ?,oper_date = ? "
                + "WHERE role_id = ?";
        try {
            return getJdbcTemplate().update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setInt(1, role.getAppCode());
                    preparedStatement.setString(2, role.getRoleName());
                    preparedStatement.setString(3, role.getRoleDesc());
                    preparedStatement.setString(4, role.getValid());
                    preparedStatement.setString(5, role.getOperUser());
                    preparedStatement.setString(6, role.getOperDate());
                    preparedStatement.setInt(7, role.getRoleId());

                }
            });
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    public int deleteRoleByRoleId(int roleId) {
        String sql = "DELETE FROM " + SECURITYROLE + " WHERE role_id = ?";
        try {
            return getJdbcTemplate().update(sql,new Object[]{roleId});
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    public int addRole(final Role role) {
        String sql = "INSERT INTO " + SECURITYROLE + "(app_code, role_name, role_desc, oper_user, oper_date) " +
                "VALUES(?, ?, ?, ?, ?)";
        try {
            return getJdbcTemplate().update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setInt(1, role.getAppCode());
                    preparedStatement.setString(2, role.getRoleName());
                    preparedStatement.setString(3, role.getRoleDesc());
                    preparedStatement.setString(4, role.getOperUser());
                    preparedStatement.setString(5, role.getOperDate());
                }
            });
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    /**
     * 用户是否注册角色
     * @param user_id
     * @param role_name
     * @return
     */
    public int isUserInRole(String user_id, String role_name) {
        String sql = "SELECT COUNT(1) FROM " + SECURITYROLE + " WHERE role_id IN(SELECT role_id FROM " + SECURITYUSERROLE + " WHERE user_id=?) AND role_name=?";
        return getJdbcTemplate().queryForObject(sql, new Object[]{user_id, role_name}, Integer.class);
    }

    /**
     * 查询app下的所有角色信息
     *
     * @param appCode
     * @return
     */
    @Override
    public List<Role> queryRoleByAppCode(int appCode) {
        String sql = "SELECT role_id, app_code, role_name, role_desc, valid, oper_user, oper_date FROM " + SECURITYROLE + " WHERE app_code = " + appCode;
        try{
            return getJdbcTemplate().query(sql, new BeanPropertyRowMapper(Role.class));
        }catch (DataAccessException e){
            e.printStackTrace();
            return null;
        }

    }

    @Override
    public List<Role> queryUserRole(String userId, int appCode) {
        String sql = "SELECT u.role_id, r.role_name, r.app_code, r.role_desc FROM security_user_role u INNER JOIN security_role r ON u.role_id = r.role_id AND u.user_id = ? AND r.app_code = ?";
        try{
            return getJdbcTemplate().query(sql, new Object[]{userId, appCode}, new BeanPropertyRowMapper(Role.class));
        }catch (DataAccessException e){
            e.printStackTrace();
            return null;
        }
    }
}
