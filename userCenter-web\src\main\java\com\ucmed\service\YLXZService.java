package com.ucmed.service;

import net.sf.json.JSONObject;

/**
 * 移动远程第三方用户对接接口
 * Created by XXB-QJH-1303 on 2017/5/15.
 */
public interface YLXZService {

    String registration(JSONObject rcv);
    String login(JSONObject rcv);
    String reInputPassword(JSONObject rcv);
    String changePwd(JSONObject rcv);
    String logout(JSONObject rcv);
    String getUserInfo(JSONObject rcv);
    String getHospitalList(JSONObject rcv);
    String isUserExist(JSONObject rcv);
    String sendVerifyCode(JSONObject rcv);
    String verifyCodeLogin(JSONObject rcv);

}
