package com.ucmed.sso.ssocotroller;

import com.ucmed.bean.UCResponse;
import com.ucmed.service.UserService;
import com.ucmed.sso.SingleSignOn;
import net.sf.json.JSONObject;
import org.apache.log4j.Logger;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Controller;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;

@Controller
public class SsoController {

    private static Logger log4j = Logger.getLogger(SsoController.class.getName());
    @Value("${cookie.domain}")
    String cookieDomain;
    @Autowired
    UserService userService;
    @Autowired
    SingleSignOn singleSignOn;
    @RequestMapping(value = "/Sso/SsoVerify", method = RequestMethod.GET)
    public String SsoVerify(HttpServletRequest request, String ssocallbackurl, String ssologinurl){
        try {
            ssocallbackurl = URLDecoder.decode(ssocallbackurl, "UTF-8");
            ssologinurl = URLDecoder.decode(ssologinurl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }

        UCResponse ucResponse = singleSignOn.SsoVerify(ssocallbackurl, ssologinurl, request.getCookies());
        if (ucResponse.getRetCode() == 0){
            return "redirect:" + ucResponse.getParam();
        } else {
            return "redirect:" + ssocallbackurl;
        }
    }

    @RequestMapping(value = "/Sso/SsoPushToken", method = RequestMethod.GET)
    public ResponseEntity<String> SsoPushToken(HttpServletRequest request, HttpServletResponse response, String ssocallbackurl, String ssoToken){
        String responseJson = null;
        if (ssocallbackurl == null || ssocallbackurl.isEmpty()){
            responseJson = JSONObject.fromObject(new UCResponse(1, "再见")).toString();
            return createResponseEntity(responseJson);
        }
        try {
            ssocallbackurl = URLDecoder.decode(ssocallbackurl, "UTF-8");
        } catch (UnsupportedEncodingException e) {
            log4j.info("编码不正确");
        }
        Cookie cookie = new Cookie("CSSOCenterToken", ssoToken);
        cookie.setPath("/");
        cookie.setDomain(cookieDomain);
        response.addCookie(cookie);
        try {
            response.sendRedirect(ssocallbackurl);
        } catch (IOException e) {
            log4j.info("重定向失败");
        }
        return createResponseEntity(responseJson);
    }


    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }
}


