package com.ucmed.controller;

import com.ucmed.bean.NUCResponse;
import com.ucmed.bean.controllerbean.VersionVO;
import com.ucmed.service.VersionService;
import net.sf.json.JSONObject;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.CrossOrigin;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import java.io.IOException;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/01/08 13:55
 */
@RestController
@RequestMapping("/version")
@ApiIgnore
public class VersionController {
    @Autowired
    VersionService versionService;

    @CrossOrigin
    @RequestMapping(value = "/getInfo", method = RequestMethod.GET)
    public ResponseEntity<VersionVO> getInfo() {
        VersionVO version = null;
        try {
            version = versionService.getVersion();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return createResponseEntity(version);
    }

    private <B> ResponseEntity<B> createResponseEntity(B body) {
        MultiValueMap<String, String> headers = new HttpHeaders();
        headers.set("Content-Type", "application/json; charset=utf-8");
        return new ResponseEntity<B>(body, headers, HttpStatus.OK);
    }
}
