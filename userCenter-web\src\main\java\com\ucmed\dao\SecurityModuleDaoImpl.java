package com.ucmed.dao;

import com.ucmed.bean.SecurityModule;
import com.ucmed.common.dao.BaseDaoImpl;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.PreparedStatementCreator;
import org.springframework.jdbc.core.PreparedStatementSetter;
import org.springframework.jdbc.support.GeneratedKeyHolder;
import org.springframework.jdbc.support.KeyHolder;
import org.springframework.stereotype.Repository;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.List;

import static com.ucmed.common.constant.CommonConstant.MODULEID;
import static com.ucmed.common.constant.TableName.SECURITYMODULE;
import static com.ucmed.common.constant.TableName.SECURITYROLEMODULE;

/**
 * Created by QIUJIAHAO on 2016/11/15.
 */
@Repository
public class SecurityModuleDaoImpl extends BaseDaoImpl implements SecurityModuleDao {
    @Override
    public List<SecurityModule> getModuleByAppCode(int appCode) {
        String sql = "SELECT module_id,app_code,module_name,module_url,module_desc,parent_id,is_leaf,valid FROM " + SECURITYMODULE + " WHERE app_code = ?";

        List<SecurityModule> result = getJdbcTemplate().query(sql, new Object[]{appCode}, new BeanPropertyRowMapper<>(SecurityModule.class));
        if(result.size() == 0)
            return null;
        return result;
    }

    @Override
    public SecurityModule getModuleById(int moduleId) {
        String sql = "SELECT module_id,app_code,module_name,module_url,module_desc,parent_id,is_leaf,valid " +
                "FROM " + SECURITYMODULE + " " +
                "WHERE module_id = ?";

        List<SecurityModule> result = getJdbcTemplate().query(sql, new Object[]{moduleId}, new BeanPropertyRowMapper<>(SecurityModule.class));
        if(result.size() == 0)
            return null;
        return result.get(0);
    }

    @Override
    public int updateModule(final SecurityModule module) {
        String sql = "UPDATE " + SECURITYMODULE + " SET "
                + "app_code = ?,module_name = ?,module_desc = ?,module_url = ?,oper_date = ?,parent_id = ?, is_leaf = ?, valid = ? "
                + "WHERE module_id = ?";
        try {
            return getJdbcTemplate().update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setInt(1, module.getApp_code());
                    preparedStatement.setString(2, module.getModule_name());
                    preparedStatement.setString(3, module.getModule_desc());
                    preparedStatement.setString(4, module.getModule_url());
                    preparedStatement.setString(5, module.getOper_date());
                    preparedStatement.setInt(6, module.getParent_id());
                    preparedStatement.setInt(7, module.getIs_leaf());
                    preparedStatement.setInt(8, module.getValid());
                    preparedStatement.setInt(9, module.getModule_id());
                }
            });
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public int addModule(final SecurityModule module) {
        final String sql = "INSERT INTO " + SECURITYMODULE + "(app_code, module_name, module_url, module_desc, oper_date,parent_id,is_leaf,valid) " +
                "VALUES(?, ?, ?, ?, ?, ?, ?, ?)";
        KeyHolder keyHolder = new GeneratedKeyHolder();
        getJdbcTemplate().update(new PreparedStatementCreator() {
            @Override
            public PreparedStatement createPreparedStatement(Connection connection) throws SQLException {
                PreparedStatement ps = connection.prepareStatement(sql, PreparedStatement.RETURN_GENERATED_KEYS);
                ps.setInt(1, module.getApp_code());
                ps.setString(2, module.getModule_name());
                ps.setString(3, module.getModule_url());
                ps.setString(4, module.getModule_desc());
                ps.setString(5, module.getOper_date());
                ps.setInt(6, module.getParent_id());
                ps.setInt(7, module.getIs_leaf());
                ps.setInt(8, module.getValid());
                return ps;
            }
        }, keyHolder);
        return (int)keyHolder.getKeys().get(MODULEID);
    }

    @Override
    public int deleteModuleById(int moduleId) {
        String sql = "DELETE FROM " + SECURITYMODULE + " WHERE module_id = ?";
        try {
            return getJdbcTemplate().update(sql, new Object[]{moduleId});
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public int addAuthorityToRole(final int roleId, final int moduleId, final int appCode, final String time) {
        String sql = "INSERT INTO " + SECURITYROLEMODULE + "(role_id,module_id,valid,oper_user,oper_date) VALUES(?,?,?,?,?)";
        try {
            return getJdbcTemplate().update(sql, new PreparedStatementSetter() {
                @Override
                public void setValues(PreparedStatement preparedStatement) throws SQLException {
                    preparedStatement.setInt(1, roleId);
                    preparedStatement.setInt(2, moduleId);
                    preparedStatement.setInt(3, 1);
                    preparedStatement.setString(4, String.valueOf(appCode));
                    preparedStatement.setString(5, time);
                }
            });
        } catch (DataAccessException e) {
            e.printStackTrace();
            return 0;
        }
    }

    @Override
    public int removeAuthorityRromRole(int roleId, int moduleId) {
        String sql = "DELETE FROM " + SECURITYROLEMODULE + " WHERE role_id = ? AND module_id = ?";
        return getJdbcTemplate().update(sql, new Object[]{roleId, moduleId});
    }
}
